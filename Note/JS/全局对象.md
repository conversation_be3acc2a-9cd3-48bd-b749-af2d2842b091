---
aliases: 
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---
| **全局对象**        | **功能描述**                                                                        |
| --------------- | ------------------------------------------------------------------------------- |
| **Math**        | 提供数学相关的常量和函数，例如 Math.PI、Math.sin()  <br>、Math.round() 等。                        |
| **Date**        | 提供日期和时间处理的功能，例如 Date.now()、Date.parse() 等。                                      |
| **String**      | 提供字符串操作的方法，例如 String.fromCharCode()，以及原型方法如 str.toUpperCase()。                  |
| **Array**       | 提供数组操作的方法，例如 Array.isArray()，以及原型方法如 arr.push()。                                |
| **Object**      | 提供对象操作的方法，例如 Object.keys()、Object.assign() 等。                                   |
| **Function**    | 提供函数相关的操作，例如 Function.prototype.call()  <br>、Function.prototype.apply()  <br>等。 |
| **JSON**        | 提供 JSON 数据的序列化和反序列化方法，例如 JSON.stringify()、JSON.parse()。                         |
| **Promise**     | 提供异步操作的处理，例如 Promise.resolve()、Promise.all() 等。                                 |
| **[[Symbol]]**  | 提供 Symbol 类型的创建和操作，例如 Symbol.for()、Symbol.keyFor() 等。                           |
| **[[Reflect]]** | 提供反射操作，例如 Reflect.apply()、Reflect.defineProperty() 等。                           |
| **[[Proxy]]**   | 提供创建代理对象的功能，用于拦截和自定义对象操作。                                                       |
| **[[Error]]**   | 提供错误处理和调试信息，例如 Error.prototype.message、Error.prototype.stack 等。                 |



共同特点
全局访问：这些对象都可以通过 globalThis 访问，例如 globalThis.Math、globalThis.Date 等。
功能封装：它们都封装了特定的语言功能，通过静态方法或原型方法供开发者调用。


[[浏览器全局对象]]