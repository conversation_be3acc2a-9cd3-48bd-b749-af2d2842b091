---
aliases:
  - Proxy
  - 代理
  - Traps
  - 陷阱
tags:
  - ES6
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---
JavaScript中的代理（Proxy）是一个强大的工具，允许你拦截和自定义对象的操作，适用于数据验证、日志记录、权限控制等场景。与Reflect API结合使用，代理可以实现灵活且可维护的元编程模式。尽管功能强大，但需注意性能开销和陷阱实现的正确性。

- 代理是一个对象(handler)，它包装了另一个对象（称为**目标对象** target），并允许你定义在对目标对象执行操作时触发的自定义行为。这些自定义行为是通过所谓的“**陷阱（traps）**”来定义的，
- 陷阱是代理对象上的方法，用于拦截各种操作。

- **基本语法**：
  ```javascript
  const proxy = new Proxy(target, handler);
  ```
  - `target`：要包装的目标对象。
  - `handler`：一个包含陷阱方法的对象，用于定义自定义行为。

---

#### 2. 代理的用途

代理在以下场景中非常有用：

- **数据验证**：在设置属性值时验证输入数据，确保其符合特定条件。
- **日志记录**：记录对对象的操作（如属性访问或修改），便于调试或审计。
- **权限控制**：限制对某些属性或方法的访问，实现权限管理。
- **虚拟化**：创建虚拟对象，在访问时才真正创建或计算值。[[虚拟化]]
- **API包装**：包装第三方API，提供更友好的接口或额外的功能。

---

#### 3. 常见的陷阱（Traps）

代理通过定义陷阱来拦截不同的操作。以下是一些常见的陷阱：

| 陷阱                     | 描述                               | 示例                             |
|--------------------------|------------------------------------|----------------------------------|
| `get(target, property, receiver)` | 拦截属性访问                       | `proxy.name`                     |
| `set(target, property, value, receiver)` | 拦截属性赋值                       | `proxy.name = 'John'`            |
| `has(target, property)`   | 拦截 `in` 操作符                   | `'name' in proxy`                |
| `deleteProperty(target, property)` | 拦截 `delete` 操作                 | `delete proxy.name`              |
| `apply(target, thisArg, argumentsList)` | 拦截函数调用（如果目标是函数）     | `proxy()`                        |
| `construct(target, argumentsList, newTarget)` | 拦截 `new` 操作（如果目标是构造函数） | `new proxy()`                    |

---

[[Proxy EG]]
[[Reflect API 与 Proxy 对象的关系]]