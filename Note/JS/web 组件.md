---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
aliases:
  - Web Components
---
Web组件是一种让开发者能够为Web创建自定义元素的技术。以下是关于Web组件的详细介绍：

### 核心技术
- **自定义元素（[[Custom Elements]]）**：允许开发者创建新的HTML元素，并定义其行为和样式。通过`customElements.define()`方法可以注册自定义元素，比如创建一个名为`<my-button>`的自定义按钮元素。
- **影子DOM（[[Shadow DOM]]）**：可以创建一个独立的DOM树，与主文档的DOM树隔离，实现样式和脚本的封装，避免全局命名空间的污染，确保组件的内部结构不受外部样式的影响。
- **HTML模板（[[HTML Templates]]）**：提供了一种机制，允许开发者将HTML片段存储起来，以便在需要时重复使用，可通过`<template>`标签来定义模板。
- **插槽（Slots）**：允许开发者创建一个可插入内容的占位符，以便在不同的组件中使用，使得组件具有更好的灵活性和可扩展性。

### 优势
- **浏览器原生支持**：是浏览器内置技术，无需额外框架支持，减少代码量，提高性能，确保组件的一致性和可靠性。
- **独立性和稳定性**：得到浏览器厂商支持，不太可能因商业原因被废弃，为开发者提供稳定的技术基础。
- **性能优势**：可利用浏览器提供的可靠API，构建快速且一致执行的组件，还能通过渐进增强的方式优雅处理组件加载失败的情况。
- **广泛支持**：主流浏览器如Chrome、Firefox、Safari和Edge等都支持Web组件。
- **与框架无关**：可以跨任何框架工作，无论使用React、Angular、Vue还是纯HTML，都能轻松集成，具有高度通用性。
- **互操作性**：可以在项目、团队甚至跨组织之间轻松共享，促进协作和标准化。

### 应用场景
- **设计系统**：有助于确保多个应用程序和框架之间的一致性，如Adobe、Microsoft和Google等公司已在其设计系统中使用。
- **跨框架项目**：适合使用多个框架的项目，或者需要切换框架而不重写整个UI的情况。
- **跨团队的可重用性**：为公司不同团队从事的不同项目提供了一种跨项目共享UI元素的标准化方法。

### 举例
以下是一个简单的Web组件代码示例：
```html
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>web components example</title>
</head>

<body>
  <my-button></my-button>
  <script>
    // 定义自定义元素
    class MyButton extends HTMLElement {
      constructor() {
        super();
        // 创建影子DOM
        const shadowRoot = this.attachShadow({ mode: 'open' });
        // 创建按钮元素并设置样式
        const button = document.createElement('button');
        button.textContent = 'Click me!';
        const style = document.createElement('style');
        style.textContent = `
          button {
            background-color: #4caf50;
            border: none;
            color: white;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            padding: 10px 24px;
            border-radius: 4px;
          }
        `;
        // 将样式和按钮添加到影子DOM
        shadowRoot.appendChild(style);
        shadowRoot.appendChild(button);
      }
    }
    // 注册自定义元素
    customElements.define('my-button', MyButton);
  </script>

</body>

</html>
```
