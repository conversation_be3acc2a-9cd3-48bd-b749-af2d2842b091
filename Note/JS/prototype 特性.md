---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---
JavaScript 对象的 prototype 特性是其原型继承机制的核心。每个对象都有一个 __proto__ 属性（即隐式原型），它指向其构造函数的 prototype 属性（显式原型）。通过原型链，对象可以继承属性和方法。

[[Object.getPrototypeOf 和 Reflect.getPrototypeOf()]]
[[Object.setPrototypeOf]]
[[obj.isPrototypeOf()]]

`__proto__`  已经废弃


以下是不同方式创建对象时的原型设置:

1. 使用对象字面量创建

```javascript
const obj = {
  name: '<PERSON>',
};
```
原型是 `Object.prototype`

2. 使用构造函数创建

```javascript
const obj = new Apple();
```
原型是 `Apple.prototype`

3. 使用 Object.create 创建

```javascript
const obj = Object.create(null);
```
原型是 `null`
```javascript
const obj = Object.create(Apple);
```
原型是 `Apple` 
obj 是 Apple 的子类的实例

```javascript
const obj = Object.create(Apple.prototype);
```
原型是 `Apple.prototype` 而 obj 就是 Apple 的实例


4. 使用 class 创建

```javascript
const obj = new class {
    constructor() { 
        this.name = 'John';
    }
};

```
```javascript
class Apple {
    constructor() {
        this.name = 'Apple';
    }
}

const app = new Apple();
```
原型是 `Apple.prototype` 而 app 是 Apple 的实例




