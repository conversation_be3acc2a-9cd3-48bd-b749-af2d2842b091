---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---
# Shadow DOM

## 一、Shadow DOM 基本概念

### 1.1 什么是 Shadow DOM

Shadow DOM 是 Web Components 标准的核心部分，它允许将隐藏的 DOM 树附加到常规 DOM 树中的元素上。这个隐藏的 DOM 树具有以下特点：

- 它的根节点称为 Shadow Root
- Shadow Root 下的内容不会在主文档的 DOM 中显示
- 可以将其视为一个"DOM 中的 DOM"，<mark style="background: #FFB86CA6;">有自己独立的作用域</mark>
- 允许将标记结构、样式和行为封装起来，与页面上的其他代码隔离

### 1.2 为什么需要 Shadow DOM

在 Shadow DOM 出现之前，我们面临这些问题：

- CSS 样式全局性导致的样式冲突
- HTML 结构复杂度增加，难以组织和维护
- JavaScript 对组件内部实现的过度暴露

Shadow DOM 通过提供组件封装解决了这些问题，使我们可以：

- 创建独立的组件，内部结构对外隐藏
- 防止样式泄漏，组件样式不影响外部，外部样式也不影响组件
- 简化 DOM 结构，提高代码可维护性

### 1.3 [[Shadow DOM 与 iframe 的区别]]

虽然 iframe 也可以实现隔离，但 Shadow DOM 有明显优势：

- 轻量级：不需要加载完整的文档
- 与主文档共享 JavaScript 上下文
- 性能更好，资源消耗更低
- 能够继承部分样式
- 更易与主文档进行通信

## 二、Shadow DOM 核心概念

### 2.1 关键术语

- **Shadow Host**: 常规 DOM 节点，Shadow DOM 会附加到这个元素上
- **Shadow Root**: Shadow DOM 树的根节点
- **Shadow Tree**: Shadow Root 内部的 DOM 树
- **Shadow Boundary**: Shadow DOM 与主 DOM 的边界，样式和选择器不能跨越这个边界
- **Light DOM**: 组件使用者提供的内容，可以通过 slot 插入到 Shadow DOM 中

### 2.2 Shadow DOM 模式

Shadow DOM 有两种模式：

- **Open mode**: 外部 JavaScript 可以通过 `element.shadowRoot` 访问 Shadow DOM
    
    ```javascript
    element.attachShadow({ mode: 'open' });
    ```
    
- **Closed mode**: 外部无法访问 Shadow DOM，`element.shadowRoot` 返回 null
    
    ```javascript
    element.attachShadow({ mode: 'closed' });
    ```
    

## 三、创建和使用 Shadow DOM

### 3.1 基本创建方法

```javascript
// 选择宿主元素
const host = document.getElementById('host');

// 创建 Shadow Root
const shadowRoot = host.attachShadow({ mode: 'open' });

// 在 Shadow DOM 中添加内容
shadowRoot.innerHTML = `
  <style>
    p { color: red; }
  </style>
  <p>这是 Shadow DOM 内部的内容</p>
`;
```

### 3.2 使用 template 元素

```html
<template id="my-template">
  <style>
    p { color: blue; }
  </style>
  <p>模板内的内容</p>
</template>

<div id="host"></div>

<script>
  const host = document.getElementById('host');
  const shadowRoot = host.attachShadow({ mode: 'open' });
  const template = document.getElementById('my-template');
  
  // 克隆模板内容到 Shadow DOM
  shadowRoot.appendChild(template.content.cloneNode(true));
</script>
```

### 3.3 在自定义元素中使用 Shadow DOM

```javascript
class MyElement extends HTMLElement {
  constructor() {
    super();
    
    // 创建 Shadow DOM
    const shadow = this.attachShadow({ mode: 'open' });
    
    // 添加内容
    shadow.innerHTML = `
      <style>
        :host {
          display: block;
          border: 1px solid #ccc;
          padding: 10px;
        }
        p { color: green; }
      </style>
      <p>这是自定义元素的内部实现</p>
    `;
  }
}

// 注册自定义元素
customElements.define('my-element', MyElement);
```

使用：

```html
<my-element></my-element>
```

## 四、Shadow DOM 中的样式

### 4.1 样式封装

Shadow DOM 的一个主要特性是样式隔离：

- Shadow DOM 内的样式不会影响外部元素
- 外部的样式表不会影响 Shadow DOM 内的元素（除了可继承属性）

```html
<style>
  /* 这个样式不会影响 Shadow DOM 内的 p 元素 */
  p { color: blue; }
</style>

<div id="host"></div>

<script>
  const host = document.getElementById('host');
  const shadow = host.attachShadow({ mode: 'open' });
  
  shadow.innerHTML = `
    <style>
      /* 这个样式只影响 Shadow DOM 内的 p 元素 */
      p { color: red; }
    </style>
    <p>Shadow DOM 内部的段落</p>
  `;
</script>
```

### 4.2 特殊选择器

Shadow DOM 提供了特殊的 CSS 选择器：

- **:host**: 选择 Shadow 宿主元素
- **:host()**: 当宿主元素匹配特定选择器时才应用样式
- **:host-context()**: 当宿主元素或其祖先元素匹配选择器时应用样式
- **::part()**: 选择标记为 part 的元素
- **::slotted()**: 选择通过 slot 插入的元素

```css
/* 选择宿主元素 */
:host {
  display: block;
  border: 1px solid black;
}

/* 当宿主元素有特定类名时应用样式 */
:host(.highlighted) {
  background-color: yellow;
}

/* 当宿主元素位于特定祖先元素内时应用样式 */
:host-context(.dark-theme) {
  color: white;
  background-color: #333;
}

/* 选择标记为 header 的部分 */
::part(header) {
  font-weight: bold;
}

/* 选择插入到 content 插槽的元素 */
::slotted(p) {
  color: green;
}
```

### 4.3 CSS 自定义属性（变量）跨越边界

CSS 自定义属性（变量）可以跨越 Shadow 边界，这为主文档与 Shadow DOM 之间的样式通信提供了机制：

```html
<style>
  /* 全局定义变量 */
  :root {
    --main-color: blue;
  }
  
  #host {
    --element-background: lightgray;
  }
</style>

<div id="host"></div>

<script>
  const host = document.getElementById('host');
  const shadow = host.attachShadow({ mode: 'open' });
  
  shadow.innerHTML = `
    <style>
      /* 使用主文档定义的变量 */
      p {
        color: var(--main-color);
        background-color: var(--element-background, white);
      }
    </style>
    <p>此段落使用外部定义的颜色变量</p>
  `;
</script>
```

## 五、内容分发与 Slot

### 5.1 基本 Slot 用法

Slot 允许将 Light DOM（用户提供的内容）插入到 Shadow DOM 中：

```html
<!-- 自定义元素的定义 -->
<script>
  class UserCard extends HTMLElement {
    constructor() {
      super();
      const shadow = this.attachShadow({ mode: 'open' });
      
      shadow.innerHTML = `
        <style>
          .card {
            border: 1px solid #ccc;
            padding: 10px;
          }
          h2 {
            margin-top: 0;
          }
        </style>
        <div class="card">
          <h2><slot name="username">未知用户</slot></h2>
          <slot name="avatar">
            <img src="default-avatar.png" width="50" height="50">
          </slot>
          <div>
            <slot>无附加信息</slot>
          </div>
        </div>
      `;
    }
  }
  
  customElements.define('user-card', UserCard);
</script>

<!-- 使用自定义元素 -->
<user-card>
  <span slot="username">张三</span>
  <img slot="avatar" src="zhang-san.jpg" width="50" height="50">
  <p>前端开发者</p>
  <p>喜欢编写组件</p>
</user-card>
```

### 5.2 命名 Slot

上面的例子展示了命名 Slot，让我们更详细地了解它：

- 使用 `name` 属性定义命名 Slot
- 使用 `slot` 属性将内容分配到特定的 Slot
- 未命名的 Slot 作为默认 Slot，接收所有未分配的内容

### 5.3 Slot 事件

Shadow DOM 提供了与 Slot 相关的事件：

- **slotchange**: 当 Slot 的分配内容改变时触发

```javascript
class SlotExample extends HTMLElement {
  constructor() {
    super();
    const shadow = this.attachShadow({ mode: 'open' });
    
    shadow.innerHTML = `
      <slot name="example"></slot>
    `;
    
    // 监听 slot 变化
    const slot = shadow.querySelector('slot');
    slot.addEventListener('slotchange', (e) => {
      console.log('Slot 内容已更改', slot.assignedNodes());
    });
  }
}

customElements.define('slot-example', SlotExample);
```

### 5.4 获取 Slot 内容

可以通过以下方法获取 Slot 的分配内容：

```javascript
// 获取所有分配的节点
const assignedNodes = slot.assignedNodes();

// 获取分配的元素（排除文本节点等）
const assignedElements = slot.assignedElements();

// 包括默认内容的选项
const withFallback = slot.assignedNodes({ flatten: true });
```

## 六、Shadow DOM 事件处理

### 6.1 事件重定向

Shadow DOM 中发生的大多数事件会被重定向，看起来像是来自宿主元素：

- 事件的 `target` 被调整为指向宿主元素
- 原始 `target` 可以通过 `event.composedPath()` 获取

```javascript
const host = document.getElementById('host');
const shadow = host.attachShadow({ mode: 'open' });

shadow.innerHTML = `
  <button>Shadow DOM 内的按钮</button>
`;

// 在主文档中添加事件监听器
document.addEventListener('click', (event) => {
  console.log('事件目标:', event.target);
  console.log('事件路径:', event.composedPath());
});
```

### 6.2 事件冒泡与 composed 标志

并非所有事件都能跨越 Shadow 边界冒泡，这由事件的 `composed` 标志决定：

- 大多数 UI 事件（如 `click`、`focus`、`blur`）的 `composed: true`
- 一些特定事件（如 `slotchange`）的 `composed: false`

自定义事件需要明确设置 `composed` 属性才能跨越边界：

```javascript
// Shadow DOM 内部触发自定义事件
const event = new CustomEvent('my-event', {
  bubbles: true,     // 允许冒泡
  composed: true,    // 允许穿越 Shadow 边界
  detail: { message: '来自 Shadow DOM 的问候' }
});

shadowRoot.querySelector('button').dispatchEvent(event);

// 主文档中监听
host.addEventListener('my-event', (e) => {
  console.log('收到自定义事件:', e.detail.message);
});
```

## 七、访问和遍历 Shadow DOM

### 7.1 访问 Shadow Root

```javascript
// 对于 open 模式
const shadowRoot = element.shadowRoot;

// 对于 closed 模式，无法直接访问
// 但可以在创建时保存引用
const div = document.createElement('div');
const closedShadowRoot = div.attachShadow({ mode: 'closed' });
// 保存引用以便后续访问
div._shadowRoot = closedShadowRoot;
```

### 7.2 判断元素是否有 Shadow Root

```javascript
function hasShadowRoot(element) {
  return !!element.shadowRoot;
}
```

### 7.3 处理嵌套的 Shadow DOM

遍历包含 Shadow DOM 的 DOM 树需要特殊处理：

```javascript
function getAllTextContent(node) {
  let text = '';
  
  // 处理节点本身
  if (node.nodeType === Node.TEXT_NODE) {
    text += node.textContent;
  }
  
  // 检查是否有 Shadow Root
  if (node.shadowRoot) {
    // 遍历 Shadow DOM
    for (const child of node.shadowRoot.childNodes) {
      text += getAllTextContent(child);
    }
  }
  
  // 遍历子节点
  for (const child of node.childNodes) {
    text += getAllTextContent(child);
  }
  
  return text;
}
```

## 八、Shadow DOM 的实际应用

### 8.1 创建可重用的 UI 组件

```javascript
class ToggleSwitch extends HTMLElement {
  constructor() {
    super();
    const shadow = this.attachShadow({ mode: 'open' });
    
    // 初始状态
    this.isOn = false;
    
    // 创建 DOM 结构
    shadow.innerHTML = `
      <style>
        :host {
          display: inline-block;
        }
        .switch {
          position: relative;
          width: 60px;
          height: 34px;
        }
        .slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: .4s;
          border-radius: 34px;
        }
        .slider:before {
          position: absolute;
          content: "";
          height: 26px;
          width: 26px;
          left: 4px;
          bottom: 4px;
          background-color: white;
          transition: .4s;
          border-radius: 50%;
        }
        .switch.on .slider {
          background-color: #2196F3;
        }
        .switch.on .slider:before {
          transform: translateX(26px);
        }
      </style>
      <div class="switch">
        <span class="slider"></span>
      </div>
    `;
    
    // 获取元素引用
    this.switch = shadow.querySelector('.switch');
    
    // 添加事件监听
    this.switch.addEventListener('click', () => this.toggle());
  }
  
  toggle() {
    this.isOn = !this.isOn;
    this.updateUI();
    
    // 触发自定义事件
    this.dispatchEvent(new CustomEvent('change', {
      bubbles: true,
      composed: true,
      detail: { isOn: this.isOn }
    }));
  }
  
  updateUI() {
    if (this.isOn) {
      this.switch.classList.add('on');
    } else {
      this.switch.classList.remove('on');
    }
  }
  
  // 设置默认开关状态
  attributeChangedCallback(name, oldValue, newValue) {
    if (name === 'checked' && newValue !== null) {
      this.isOn = true;
      this.updateUI();
    }
  }
  
  static get observedAttributes() {
    return ['checked'];
  }
}

customElements.define('toggle-switch', ToggleSwitch);
```

使用：

```html
<toggle-switch></toggle-switch>
<toggle-switch checked></toggle-switch>

<script>
  document.querySelector('toggle-switch').addEventListener('change', (e) => {
    console.log('开关状态变更:', e.detail.isOn);
  });
</script>
```

### 8.2 封装第三方库

Shadow DOM 可以用来封装第三方库，防止样式冲突：

```javascript
class ChartWidget extends HTMLElement {
  constructor() {
    super();
    const shadow = this.attachShadow({ mode: 'open' });
    
    shadow.innerHTML = `
      <style>
        :host {
          display: block;
          width: 100%;
          height: 300px;
        }
        .chart-container {
          width: 100%;
          height: 100%;
        }
      </style>
      <div class="chart-container"></div>
    `;
    
    this.container = shadow.querySelector('.chart-container');
  }
  
  connectedCallback() {
    if (typeof Chart === 'undefined') {
      // 加载 Chart.js 库
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
      script.onload = () => this.initChart();
      document.head.appendChild(script);
    } else {
      this.initChart();
    }
  }
  
  initChart() {
    const ctx = document.createElement('canvas');
    this.container.appendChild(ctx);
    
    this.chart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['红', '蓝', '黄', '绿', '紫', '橙'],
        datasets: [{
          label: '示例数据',
          data: [12, 19, 3, 5, 2, 3],
          backgroundColor: [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 206, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
            'rgba(153, 102, 255, 0.2)',
            'rgba(255, 159, 64, 0.2)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }]
      }
    });
  }
}

customElements.define('chart-widget', ChartWidget);
```

## 九、Shadow DOM 性能考虑

### 9.1 性能优势

- **样式计算隔离**: 减少样式计算范围
- **减少全局选择器复杂度**: 降低选择器匹配时间
- **降低重绘成本**: 变更可以局限在组件内

### 9.2 性能注意事项

- **过度使用会增加内存消耗**: 每个 Shadow Root
- **多层嵌套会增加事件传播路径**: 影响事件处理效率
- **跨边界事件和样式会有额外开销**: composed 事件和 CSS 变量

### 9.3 优化建议

- **缓存 DOM 引用**: 避免重复查询
- **合理使用虚拟 DOM**: 减少实际 DOM 操作
- **批量 DOM 操作**: 使用 DocumentFragment
- **避免过深的嵌套**: 保持合理的组件颗粒度

```javascript
// 优化示例：批量添加内容
function addItemsOptimized(shadowRoot, items) {
  const fragment = document.createDocumentFragment();
  
  items.forEach(item => {
    const div = document.createElement('div');
    div.textContent = item;
    fragment.appendChild(div);
  });
  
  // 一次性添加所有元素
  shadowRoot.querySelector('.container').appendChild(fragment);
}
```

## 十、浏览器支持与 Polyfill

### 10.1 浏览器支持情况

Shadow DOM (v1) 在现代浏览器中得到广泛支持：

- Chrome 53+
- Firefox 63+
- Safari 10+
- Edge 79+

### 10.2 检测支持

```javascript
function supportsShadowDOM() {
  return !!HTMLElement.prototype.attachShadow;
}

if (supportsShadowDOM()) {
  // 使用原生 Shadow DOM
} else {
  // 使用 polyfill 或替代方案
}
```

### 10.3 ShadyDOM Polyfill

对于不支持 Shadow DOM 的浏览器，可以使用 ShadyDOM polyfill：

```html
<script src="https://unpkg.com/@webcomponents/shadydom@1.9.0/shadydom.min.js"></script>
<script src="https://unpkg.com/@webcomponents/shadycss@1.11.0/shadycss.min.js"></script>

<script>
  // 检查是否需要使用 polyfill
  if (!supportsShadowDOM()) {
    // 应用 ShadyDOM
    ShadyDOM.inUse = true;
    ShadyDOM.flush();
  }
</script>
```

## 十一、Shadow DOM 最佳实践

### 11.1 设计原则

- **专注点分离**: 保持结构、样式和行为的分离
- **最小化外部依赖**: 降低对全局状态的依赖
- **合理暴露 API**: 提供必要的接口，隐藏实现细节
- **符合原生行为**: 遵循平台原生组件的行为模式

### 11.2 编码最佳实践

- **使用模板**: 利用 `<template>` 元素定义结构
- **避免操作 Light DOM**: 尽量通过 slot 接收内容
- **合理使用 CSS 变量**: 为用户提供样式定制点
- **命名约定**: 使用明确的前缀避免名称冲突

```javascript
class BestPracticeElement extends HTMLElement {
  constructor() {
    super();
    this._shadowRoot = this.attachShadow({ mode: 'open' });
    
    // 使用私有成员存储状态
    this._state = {
      value: 0
    };
    
    this._render();
  }
  
  // 提供公共方法
  increment() {
    this._state.value++;
    this._updateValue();
  }
  
  // 私有方法使用下划线前缀
  _updateValue() {
    this._valueElement.textContent = this._state.value;
    
    // 触发事件通知变化
    this.dispatchEvent(new CustomEvent('value-changed', {
      bubbles: true,
      composed: true,
      detail: { value: this._state.value }
    }));
  }
  
  _render() {
    const template = document.getElementById('best-practice-template');
    this._shadowRoot.appendChild(template.content.cloneNode(true));
    
    // 缓存常用元素引用
    this._valueElement = this._shadowRoot.querySelector('.value');
    this._button = this._shadowRoot.querySelector('button');
    
    // 添加事件监听
    this._button.addEventListener('click', () => this.increment());
    
    // 初始化显示
    this._updateValue();
  }
}

customElements.define('best-practice-element', BestPracticeElement);
```

### 11.3 可访问性考虑

- **语义化标签**: 在 Shadow DOM 中使用正确的 HTML 语义
- **ARIA 属性**: 提供必要的 ARIA 角色和属性
- **键盘导航**: 确保组件可以通过键盘操作
- **无障碍焦点**: 维护合理的焦点行为

```javascript
class AccessibleTabs extends HTMLElement {
  constructor() {
    super();
    const shadow = this.attachShadow({ mode: 'open' });
    
    shadow.innerHTML = `
      <style>
        :host { display: block; }
        [role="tablist"] {
          display: flex;
          border-bottom: 1px solid #ccc;
        }
        [role="tab"] {
          padding: 8px 16px;
          border: none;
          background: none;
          cursor: pointer;
        }
        [role="tab"][aria-selected="true"] {
          border-bottom: 2px solid blue;
          font-weight: bold;
        }
        [role="tabpanel"] {
          padding: 16px;
          display: none;
        }
        [role="tabpanel"][active] {
          display: block;
        }
      </style>
      
      <div role="tablist"></div>
      <slot></slot>
    `;
    
    this.tabList = shadow.querySelector('[role="tablist"]');
    this.tabs = [];
    this.panels = [];
  }
  
  connectedCallback() {
    // 处理 Light DOM 中的面板
    const panels = Array.from(this.children);
    
    panels.forEach((panel, index) => {
      // 创建选项卡
      const tab = document.createElement('button');
      tab.setAttribute('role', 'tab');
      tab.setAttribute('aria-selected', index === 0 ? 'true' : 'false');
      tab.setAttribute('id', `tab-${index}`);
      tab.setAttribute('aria-controls', `panel-${index}`);
      tab.textContent = panel.getAttribute('title') || `选项卡 ${index + 1}`;
      
      // 设置面板
      panel.setAttribute('role', 'tabpanel');
      panel.setAttribute('id', `panel-${index}`);
      panel.setAttribute('aria-labelledby', `tab-${index}`);
      panel.setAttribute('tabindex', '0');
      
      if (index === 0) {
        panel.setAttribute('active', '');
      }
      
      this.tabList.appendChild(tab);
      this.tabs.push(tab);
      this.panels.push(panel);
      
      // 添加事件
      tab.addEventListener('click', () => this.selectTab(index));
      tab.addEventListener('keydown', (e) => this.handleKeyDown(e, index));
    });
  }
  
  selectTab(index) {
    // 更新选项卡状态
    this.tabs.forEach((tab, i) => {
      tab.setAttribute('aria-selected', i === index ? 'true' : 'false');
      if (i === index) {
        tab.focus();
      }
    });
    
    // 更新面板状态
    this.panels.forEach((panel, i) => {
      if (i === index) {
        panel.setAttribute('active', '');
      } else {
        panel.removeAttribute('active');
      }
    });
  }
  
  handleKeyDown(event, index) {
    let newIndex;
    
    switch (event.key) {
      case 'ArrowRight':
        newIndex = (index + 1) % this.tabs.length;
        break;
      case 'ArrowLeft':
        newIndex = (index - 1 + this.tabs.length) % this.tabs.length;
        break;
      case 'Home':
        newIndex = 0;
        break;
      case 'End':
        newIndex = this.tabs.length - 1;
        break;
      default:
        return;
    }
    
    event.preventDefault();
    this.selectTab(newIndex);
  }
}

customElements.define('accessible-tabs', AccessibleTabs);
```

## 十二、Shadow DOM 常见问题与解决方案

### 12.1 样式穿透问题

**问题**: 需要从外部样式化 Shadow DOM 内部元素

**解决方案**:

1. 使用 CSS 变量

```css
/* 外部样式 */
my-element {
  --my-element-text-color: blue;
  --my-element-background: #f0f0f0;
}
```

```css
/* Shadow DOM 内部样式 */
:host {
  display: block;
}
p {
  color: var(--my-element-text-color, black);
  background-color: var(--my-element-background, white);
}
```

1. 使用 part 属性

```html
<div part="header">标题</div>
```

```css
/* 外部样式 */
my-element::part(header) {
  font-size: 24px;
  color: purple;
}
```

### 12.2 外部资源加载

**问题**: Shadow DOM 内部引用的样式表和脚本如何加载

**解决方案**:

```javascript
// 创建并附加样式表
const linkElem = document.createElement('link');
linkElem.setAttribute
```