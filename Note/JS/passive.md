
`passive` 是 `addEventListener` 的 `options` 对象中的一个重要配置项，**主要作用是提升滚动、触摸等事件的性能表现**。以下是它的核心作用和原理：

---

### **核心作用**
1. **消除滚动阻塞**  
   当监听 `touchstart`、`touchmove`、`wheel` 等事件时，浏览器默认会等待事件处理函数执行完毕，**以确定是否调用了 `event.preventDefault()`**（这会阻止默认滚动行为）。如果事件处理函数耗时较长，会导致滚动/触摸操作的延迟（如页面卡顿）。  
   **`passive: true`** 会明确告诉浏览器：**此事件监听器不会调用 `preventDefault()`**，因此浏览器无需等待，直接执行默认滚动行为，消除延迟。

2. **性能优化**  
   对于频繁触发的事件（如 `scroll`、`touchmove`），设置 `passive: true` 可使浏览器跳过检查 `preventDefault()` 的步骤，减少主线程的计算负担，显著提升流畅度。

---

### **实际场景**
- **移动端滚动优化**  
  移动浏览器（如 Chrome）已默认对 `touchstart` 和 `touchmove` 事件启用 `passive: true`，但显式声明可确保代码意图清晰，并兼容旧版本。
  
- **避免控制台警告**  
  未使用 `passive: true` 且事件处理函数未调用 `preventDefault()` 时，现代浏览器会输出警告（如：`[Violation] Added non-passive event listener to a scroll-blocking event`）。通过 `passive: true` 可消除此警告。

---

### **代码示例**
```javascript
// 优化滚动性能的写法
element.addEventListener("touchmove", handleTouchMove, { passive: true });

// 如果仍需阻止默认行为（如禁止页面滚动），则不能使用 passive: true
element.addEventListener("touchmove", (e) => {
  e.preventDefault(); // 若 passive: true，此处会抛出错误！
}, { passive: false });
```


---

### **注意事项**
- **与 `preventDefault()` 冲突**  
  如果事件处理函数中调用了 `event.preventDefault()`，但 `passive` 被设为 `true`，浏览器会抛出 `TypeError` 错误（因违反了 passive 的承诺）。

- **浏览器兼容性**  
  现代浏览器普遍支持 `passive`，但旧版浏览器需检测支持性。可通过特性检测工具（如 Modernizr）或以下代码判断：
```javascript
  let passiveSupported = false;
  try {
    const opts = Object.defineProperty({}, "passive", {
      get() { passiveSupported = true; }
    });
    window.addEventListener("test", null, opts);
  } catch (e) {}
```


---

### **总结**
**`passive: true` 是一种性能优化手段**，适用于无需阻止默认行为的高频事件（如滚动、触摸）。它能显著提升用户体验，但需确保事件处理函数中不调用 `preventDefault()`。在移动端开发中，优先对滚动相关事件使用此配置。