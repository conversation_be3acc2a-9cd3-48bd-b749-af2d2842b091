---
aliases:
---
Shadow DOM 和 iframe 都是网页开发中用于封装和隔离内容的技术，但它们有很大的区别：

### Shadow DOM

- **定义**：Shadow DOM 是 Web Components 规范的一部分，允许将一个隐藏的、独立的 <mark style="background: #FFB86CA6;">DOM 树附加到常规 DOM 元素</mark>上
- **用途**：主要用于创建可重用的组件，隔离 CSS 样式和 DOM 结构
- **隔离范围**：仅隔离 DOM 结构和 CSS 样式，不隔离 JavaScript 上下文
- **性能**：轻量级，性能开销较小
- **访问方式**：可以通过 JavaScript 轻松访问主文档和 Shadow DOM 之间的内容
- **样式影响**：内部样式不会泄漏到外部文档，外部样式也不会渗透到 Shadow DOM 内部（除非使用 CSS 自定义属性）

### iframe

- **定义**：iframe 是一个内联框架元素，可以将一个完全独立的文档嵌入到当前文档中
- **用途**：嵌入第三方内容，如广告、视频播放器或独立应用
- **隔离范围**：完全隔离，包括 DOM、CSS、JavaScript 执行环境和网络请求
- **性能**：相对较重，每个 iframe 都需要额外的资源和内存
- **访问方式**：存在[[同源策略限制]]，不同源的 iframe 之间通信需要特殊方法（如 postMessage）
- **样式影响**：完全独立的样式环境，互不影响

### 主要差异

1. **隔离程度**：iframe 提供更完整的隔离（包括 JavaScript 执行环境），而 Shadow DOM 主要<mark style="background: #FFB86CA6;">隔离 DOM 和 CSS</mark>
2. **加载方式**：iframe 加载完整的 HTML 文档，Shadow DOM 只是当前文档的一部分
3. **安全性**：iframe 受同源策略限制，Shadow DOM 与主文档共享同一安全上下文
4. **用途场景**：Shadow DOM 适合组件化开发，iframe 适合嵌入独立内容

选择使用哪种技术取决于您的具体需求：组件化开发通常选择 Shadow DOM，而需要完全隔离的第三方内容则更适合使用 iframe。