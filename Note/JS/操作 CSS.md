
# JavaScript操作CSS的方法

根据您的个人知识库，JavaScript提供了几种操作CSS样式的方法。以下是主要的CSS操作技术：

## 1. 使用CSS类

最简单的影响文档样式的方式是添加或删除CSS类名：

```javascript
// 隐藏元素
element.classList.add("hidden");
// 显示元素
element.classList.remove("hidden");
```

## 2. 操作行内样式

每个Element对象都有style属性，指向CSSStyleDeclaration对象，可以直接设置元素的样式：[[element.style]]

```javascript
// 设置多个样式属性
tooltip.style.position = "absolute";
tooltip.style.left = x + "px";
tooltip.style.top = y + "px";
```

## 3. CSS属性命名约定

JavaScript中的CSS属性名与CSS中的有所不同：

- CSS中连字符的属性在JavaScript中变成驼峰式
- 例如：`border-left-width` → `borderLeftWidth`
- 例如：`font-family` → `fontFamily`


## 4. 设置样式值注意事项

在使用style属性时：
- 所有值必须是字符串
- 通常需要包含单位（如"px"、"pt"等）
- 示例：`e.style.marginLeft = "300px"` 而非 `e.style.marginLeft = 300`


## 5. 其他设置样式的方法

```javascript
// 使用getAttribute和setAttribute
element.setAttribute("style", "display:block; border:1px solid black");

// 使用cssText属性
element.style.cssText = "display:block; border:1px solid black";
```


## 6. 获取计算样式

元素的计算样式是浏览器根据所有适用的样式规则导出的实际使用的属性值，同样以CSSStyleDeclaration对象表示。


## 7. 元素几何信息

```javascript
// 获取元素大小和位置（视口坐标）
let rect = element.getBoundingClientRect();
```

## 8. 影子DOM中的CSS

影子DOM提供了CSS样式的封装，其中定义的样式对该子树是私有的，不会影响外部DOM元素。
