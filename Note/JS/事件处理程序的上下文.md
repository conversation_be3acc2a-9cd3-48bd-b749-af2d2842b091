---
alias: handler this
---

## 事件处理程序中的`this`

- **定义**: 在事件处理程序中，`this`通常指向触发事件的DOM元素(注意箭头函数)。
- **示例**:

  ```javascript
  let button = document.querySelector("button");
  button.addEventListener("click", function(event) {
    console.log(this); // 输出 button 元素
  });

  button.onclick = function(event) {
    console.log(this); // 输出 button 元素
  }

  let obj = {name: "<PERSON>"}
  let button = document.querySelector("button");
  button.addEventListener("click", () => {
    console.log(this); // 输出 window 对象
  })
  ```


## 改变`this`的值

有几种方法可以改变事件处理程序中的`this`值：

1. **使用`bind()`方法**:

   ```javascript
   let obj = { name: "<PERSON>" };
   let button = document.querySelector("button");
   button.addEventListener("click", function(event) {
     console.log(this.name);
   }.bind(obj));
   ```

   在这个例子中，`this`指向`obj`对象。

2. **使用箭头函数**:

   ```javascript
   let obj = { name: "John" };
   let button = document.querySelector("button");
   button.addEventListener("click", () => {
     console.log(this); // this 指向当前的执行上下文
   });
   ```

   箭头函数不具有自己的`this`上下文，它会继承外层的执行上下文。

3. **显式赋值**:

   ```javascript
   let obj = { name: "John" };
   let self = obj;
   let button = document.querySelector("button");
   button.addEventListener("click", function(event) {
     console.log(self.name);
   });
   ```

   在这个例子中，使用变量`self`来捕获外层函数的`this`值。

## 执行上下文

JavaScript的执行上下文（Execution Context）决定了代码的执行环境。每次调用函数时，都会创建一个新的函数执行上下文，其中包括了函数的参数、局部变量以及`this`对象[2][5]。在事件处理程序中，执行上下文由事件触发的元素决定。

### 总结

| 方法            | 描述                                                                 | 示例用途                                               |
|-----------------|----------------------------------------------------------------------|----------------------------------------------------------|
| `bind()`        | 创建一个新函数，其`this`值固定为指定对象。                         | 确保`this`指向特定对象。                               |
| 箭头函数        | 继承外层的执行上下文。                                             | 简化代码，避免`this`指向问题。                         |
| 显式赋值        | 使用变量捕获外层函数的`this`值。                                    | 处理嵌套函数中的`this`指向问题。                       |

这些方法在处理事件处理程序的上下文时非常有用，帮助开发者更好地控制和理解代码的行为。
