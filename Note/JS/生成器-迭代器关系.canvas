{"edges": [{"fromNode": "d511d46aba1d305e", "fromSide": "bottom", "id": "ee6885cd6c2b8668", "label": "返回", "styleAttributes": {"arrow": "triangle-outline"}, "toNode": "919b7fd8acc9c9ef", "toSide": "right"}, {"fromNode": "c80cc0fd40dbdfcb", "fromSide": "right", "id": "ed9ba52711402392", "label": "必须实现", "styleAttributes": {"arrow": "triangle-outline"}, "toNode": "d511d46aba1d305e", "toSide": "left"}, {"fromNode": "0cf3d8f931ee975d", "fromSide": "right", "id": "6814d94b2f33c803", "label": "可迭代协议（Iterable Protocol）", "styleAttributes": {"arrow": "triangle-outline"}, "toNode": "c80cc0fd40dbdfcb", "toSide": "right"}, {"fromNode": "0cf3d8f931ee975d", "fromSide": "right", "id": "b10f129733495d03", "label": "迭代器协议（Iterator Protocol）", "styleAttributes": {"arrow": "triangle-outline"}, "toNode": "919b7fd8acc9c9ef", "toSide": "right"}, {"fromNode": "919b7fd8acc9c9ef", "fromSide": "left", "id": "1ae58b0fce17e860", "label": "定义并使用next", "styleAttributes": {"arrow": "triangle-outline"}, "toNode": "bdfdf5aef25c832d", "toSide": "right"}, {"fromNode": "bdfdf5aef25c832d", "fromSide": "right", "id": "c9e10a42ab3ae4bc", "label": "语法糖处理", "styleAttributes": {"arrow": "triangle-outline"}, "toNode": "c80cc0fd40dbdfcb", "toSide": "left"}, {"fromNode": "0cf3d8f931ee975d", "fromSide": "left", "id": "e372651aad61786a", "label": "使用", "styleAttributes": {"arrow": "triangle-outline"}, "toNode": "bdfdf5aef25c832d", "toSide": "right"}, {"fromNode": "d511d46aba1d305e", "fromSide": "right", "id": "92e3c37c73fd433b", "label": "特殊", "styleAttributes": {"arrow": "triangle-outline"}, "toNode": "6ce4f854e90c415b", "toSide": "left"}, {"fromNode": "919b7fd8acc9c9ef", "fromSide": "right", "id": "b8325acc93777a7b", "styleAttributes": {}, "toNode": "3b80d73fd123fd8e", "toSide": "left"}, {"fromNode": "3b80d73fd123fd8e", "fromSide": "right", "id": "4cbd10210b000623", "label": "必须返回", "styleAttributes": {}, "toNode": "6ce4f854e90c415b", "toSide": "bottom"}, {"fromNode": "0cf3d8f931ee975d", "fromSide": "right", "id": "530d8d6500c7f922", "styleAttributes": {}, "toNode": "fdebbf3778ae8a3a", "toSide": "left"}, {"fromNode": "fdebbf3778ae8a3a", "fromSide": "left", "id": "6650aad3d6f262cc", "label": "实现", "styleAttributes": {}, "toNode": "3b80d73fd123fd8e", "toSide": "right"}], "nodes": [{"file": "Note/JS/生成器.md", "height": 220, "id": "0cf3d8f931ee975d", "styleAttributes": {}, "type": "file", "width": 350, "x": 400, "y": -280}, {"height": 280, "id": "919b7fd8acc9c9ef", "styleAttributes": {}, "text": "迭代器 Iterator\n- **定义**：一个具有 \n    \n    next()\n    \n     方法的对象\n- **返回格式**：\n    \n    next()\n    \n     必须返回 \n    \n    { value: any, done: boolean }\n    \n- **职责**：跟踪迭代状态，产生序列中的值\n- **关系**：由可迭代对象的 \n    \n    Symbol.iterator\n    \n     方法返回\n\n```javascript\nconst iterator = {\n  next() {\n    return { value: '下一个值', done: false };\n    // 迭代完成后返回 { done: true }\n  }\n};\n```", "type": "text", "width": 350, "x": 400, "y": 120}, {"file": "Note/JS/迭代器方法.md", "height": 272, "id": "bdfdf5aef25c832d", "styleAttributes": {}, "type": "file", "width": 340, "x": -200, "y": -306}, {"file": "Note/JS/Symbol.iterator.md", "height": 210, "id": "d511d46aba1d305e", "styleAttributes": {}, "type": "file", "width": 340, "x": 960, "y": -640}, {"file": "Note/JS/Symbol.asyncIterator.md", "height": 210, "id": "6ce4f854e90c415b", "styleAttributes": {}, "type": "file", "width": 340, "x": 1440, "y": -640}, {"file": "Note/JS/可迭代对象 Iterable.md", "height": 280, "id": "c80cc0fd40dbdfcb", "styleAttributes": {}, "type": "file", "width": 350, "x": 400, "y": -660}, {"file": "异步迭代器.md", "height": 400, "id": "3b80d73fd123fd8e", "type": "file", "width": 400, "x": 1160, "y": 60}, {"file": "Note/JS/异步生成器.md", "height": 400, "id": "fdebbf3778ae8a3a", "type": "file", "width": 400, "x": 1900, "y": -380}]}