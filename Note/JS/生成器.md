---
aliases:
  - Generator
  - 惰性求值
  - yield
---
JavaScript 中的生成器（Generator）是一种特殊的函数，它允许函数在执行过程中暂停和恢复，逐步生成多个值，而不是一次性返回所有结果。生成器函数通过 `yield` 关键字生成值，并通过 `next()` 方法驱动执行 

---

### 1. **生成器的基本概念**
- **生成器函数**：生成器函数是在普通函数的基础上添加一个星号 `*`，例如：
  ```javascript
  function* generatorFunction() {
    yield 1;
    yield 2;
    return 3;
  }
  ```
  - `yield`：用于生成值，并暂停函数的执行。
  - `return`：用于结束生成器，返回最后一个值。

- **生成器对象**：调用生成器函数会返回一个生成器对象，该对象实现了迭代器协议，可以通过 `next()` 方法获取生成的值 [citation:1][citation:3]。

---

### 2. **生成器的使用方法**
#### 示例代码：
```javascript
function* countUpTo(n) {
  let i = 1;
  while (i <= n) {
    yield i;
    i++;
  }
}

const gen = countUpTo(3);
console.log(gen.next().value); // 输出 1
console.log(gen.next().value); // 输出 2
console.log(gen.next().value); // 输出 3
console.log(gen.next().done);  // 输出 true（生成器结束）
```

#### 工作原理：
1. 调用生成器函数 `countUpTo(3)`，返回一个生成器对象 `gen`。
2. 调用 `gen.next()`：
   - 第一次调用：执行到第一个 `yield`，返回 `{ value: 1, done: false }`。
   - 第二次调用：从 `yield` 恢复执行，返回 `{ value: 2, done: false }`。
   - 第三次调用：返回 `{ value: 3, done: false }`。
   - 第四次调用：生成器结束，返回 `{ value: undefined, done: true }`。

---

### 3. **生成器的应用场景**
- **惰性计算**：按需生成数据，避免一次性计算大量数据，节省内存和性能 [citation:3][citation:4]。
- **状态机管理**：生成器可以轻松管理复杂的状态转换逻辑 [citation:5]。
- **异步操作**：结合 `async/await`，生成器可以简化异步流程的控制 [citation:6][citation:7]。

---

### 4. **生成器的优势**
- **代码简洁**：通过 `yield` 和 `next()`，生成器可以清晰地表达迭代逻辑。
- **资源高效**：按需生成数据，避免一次性加载大量数据。
- **状态管理**：生成器可以轻松维护函数的执行状态，适用于复杂的状态转换场景 [citation:1][citation:3]。

---

### 5. **总结**
生成器是 JavaScript 中一个强大且灵活的特性，适用于需要按需生成数据或管理复杂状态的场景。通过 `yield` 和 `next()`，生成器能够以简洁的方式实现惰性计算和状态管理，提升代码的可读性和性能 [citation:1][citation:3][citation:4]。

理解生成器的基本概念和使用方法，将帮助你在处理复杂数据流或状态转换时更加得心应手。