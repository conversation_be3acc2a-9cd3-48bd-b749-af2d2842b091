---
tags:
  - ES6
---

## `Symbol.hasInstance`

1.  **作用**：`Symbol.hasInstance` 是一个内置的 Symbol 值，用于自定义 `instanceof` 操作符的行为。它允许你定义一个函数，该函数确定对象是否被视为某个构造器的实例。

2.  **使用方式**：当使用 `instanceof` 运算符时，如果右侧的操作数（通常是一个构造函数）定义了 `Symbol.hasInstance` 方法，那么该方法会被调用，左侧的操作数会作为参数传递给这个方法。该方法的返回值会被转换为布尔值，并作为 `instanceof` 运算的结果。

    ```javascript
    class MyArray {
      static [Symbol.hasInstance](instance) {
        return Array.isArray(instance);
      }
    }

    console.log([] instanceof MyArray); // true
    ```

3.  **与传统行为的区别**：如果右侧的操作数没有定义 `Symbol.hasInstance` 方法，并且它是一个函数，那么 `instanceof` 运算符会按照传统的原型链查找方式来判断左侧的对象是否是右侧函数的实例。

    ```javascript
    function Person() {}
    let p = new Person();
    console.log(p instanceof Person); // true
    ```

4.  **属性特性**：`Symbol.hasInstance` 属性是不可写、不可枚举、不可配置的。如果需要修改其行为，可以使用 `Object.defineProperty()` 方法。

    ```javascript
    function MyObject() {}

    Object.defineProperty(MyObject, Symbol.hasInstance, {
      value: function () {
        return false;
      },
    });

    let obj = new MyObject();

    console.log(obj instanceof MyObject); // false
    ```

5.  **总结**：`Symbol.hasInstance` 提供了一种自定义 `instanceof` 操作符行为的途径，允许开发者根据需要调整实例检查的逻辑。