

`element.style` 是 JavaScript 中用于操作元素内联样式的属性，返回一个 [[CSSStyleDeclaration]] 对象。通过该属性可以动态修改元素的样式，但仅针对内联样式（即通过 HTML `style` 属性直接定义的样式）。以下是关键点： ^daca84

1. **设置样式**：通过驼峰命名法直接设置属性，例如 `element.style.backgroundColor = "red"`。这种方式不会覆盖其他内联样式，适合修改单个属性。

2. **覆盖全部内联样式**：可通过 `element.style.cssText = "color: blue; font-size: 14px;"` 直接赋值字符串，这会完全替换原有的内联样式。

3. **属性命名规则**：CSS 短横线命名（如 `background-color`）需转换为驼峰形式（如 `backgroundColor`），而部分属性（如 `float`）需用 `cssFloat` 或 `styleFloat` 访问。

4. **重置样式**：将属性设为 `null` 或空字符串可移除样式，例如 `element.style.color = ""`。

5. **优先级与限制**：`element.style` 设置的样式优先级等同于内联样式，但无法获取外部或嵌入样式表定义的样式。若需读取最终计算样式，应使用 `window.getComputedStyle()`。[[Computed Style]]

示例：点击按钮修改背景色：
```javascript
element.style.backgroundColor = 'skyblue';  // 
```