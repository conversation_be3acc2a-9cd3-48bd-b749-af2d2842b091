### JavaScript访问器属性

访问器属性是JavaScript中的一种特殊属性，它们通过getter和setter方法来控制属性的访问和修改。与直接存储值的数据属性不同，访问器属性允许在访问或修改属性时执行自定义逻辑，如验证输入、计算值或触发其他操作。

#### 定义和作用
访问器属性由两个可选的函数组成：
- **getter**：当访问属性时调用，返回属性的值。
- **setter**：当设置属性时调用，处理赋值逻辑。

通过访问器属性，可以在属性访问时动态计算值或在属性修改时执行额外的逻辑，从而实现更灵活和受控的属性管理。

#### 示例
以下是一个使用访问器属性的示例：

```javascript
const person = {
  firstName: 'John',
  lastName: 'Doe',
  get fullName() {
    return `${this.firstName} ${this.lastName}`;
  },
  set fullName(value) {
    const parts = value.split(' ');
    this.firstName = parts[0];
    this.lastName = parts[1];
  }
};

console.log(person.fullName); // 输出: <PERSON> [citation:5]
person.fullName = 'Jane <PERSON>';
console.log(person.firstName); // 输出: Jane
console.log(person.lastName); // 输出: Smith
```

#### 访问器属性与数据属性的区别
- **数据属性**：直接存储值，可以通过`value`和`writable`属性控制。
- **访问器属性**：通过getter和setter函数来定义，没有`value`或`writable`属性，而是使用`get`和`set`函数。

#### 使用场景
访问器属性适用于以下场景：
1. **动态计算**：当属性值需要根据其他属性动态计算时。
2. **数据验证**：在设置属性时执行输入验证。
3. **封装内部状态**：隐藏内部实现细节，提供更友好的接口。

#### 优势
- **动态性**：属性值可以动态计算，无需预先存储。
- **控制逻辑**：可以在访问或修改属性时执行自定义逻辑。
- **封装性**：隐藏内部实现，提供更灵活的接口。

通过合理使用访问器属性，可以提升代码的灵活性和可维护性，特别是在需要复杂逻辑或动态行为的场景中。