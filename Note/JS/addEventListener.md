`addEventListener` 是用于为 DOM 元素注册事件监听器的标准方法，属于 `EventTarget` 接口。其核心功能如下：  

1. **多事件处理**：可为同一元素的同一事件绑定多个处理函数，按添加顺序依次执行。  
2. **参数控制**：  
   - 第三个参数可为布尔值 `useCapture`（控制事件在捕获或冒泡阶段触发），或 `options` 对象（支持 `passive`（优化滚动性能）、`once`（仅触发一次）等配置）。  
3. **与 `on` 事件的区别**：  
   - `on` 事件（如 `onclick`）仅能绑定单个处理函数，而 `addEventListener` 支持多个。  
   - 提供更精细的事件阶段控制和扩展选项。  
4. **兼容性**：作为 W3C 标准，现代浏览器广泛支持，但旧版 IE 不兼容。  

示例语法：  
```javascript
target.addEventListener("click", handler, {
    passive: true, // 优化滚动性能
    once: true, // 仅触发一次
    capture: true, // 在捕获阶段触发
    signal: AbortSignal // 取消事件
});  
```
推荐优先使用 `addEventListener` 以实现更灵活的事件管理。

### 参数
- `eventType`: 事件类型，如 `click`, `mouseover`, `keydown` 等。
- `handler`: 事件处理函数，可以是匿名函数或具名函数。
- `options`: 可选参数对象，支持以下属性：
  - `passive`: 布尔值，表示事件处理函数是否不会调用 `preventDefault()` 方法。[[passive]]
  - `once`: 布尔值，表示事件处理函数是否仅触发一次。
  - `capture`: 布尔值，表示事件处理函数是否在捕获阶段触发。
  - `signal`: `AbortSignal` 对象，表示事件处理函数是否可以被取消。

### 使用场景
- 需要为同一事件绑定多个处理函数时。
- 需要控制事件在捕获或冒泡阶段触发时。
- 需要取消事件时。