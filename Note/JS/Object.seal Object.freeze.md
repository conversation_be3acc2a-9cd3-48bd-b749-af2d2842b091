---
aliases:
  - 密封
  - 冻结
---
`Object.seal()` 和 `Object.freeze()` 都是 JavaScript 中用于操作对象的方法，但它们的功能有所不同：

1. **`Object.seal()`**：
   - 将对象标记为密封（sealed），防止添加新属性。
   - 禁止删除现有属性。
   - <mark style="background: #FFB86CA6;">允许修改现有属性的值</mark>。
   - 密封的对象可以被冻结（freeze）。

2. **`Object.freeze()`**：
   - 将对象标记为冻结（frozen），防止任何修改。
   - 禁止添加、删除或<mark style="background: #FFB86CA6;">修改属性</mark>。
   - 冻结的对象不能被密封，也不能再被修改。

**主要区别**：
- `seal()` 允许修改现有属性的值，而 `freeze()` 不允许。
- `freeze()` 是更严格的限制，对象完全不可变。

两者都会返回被操作的对象，并且不会改变对象的引用。