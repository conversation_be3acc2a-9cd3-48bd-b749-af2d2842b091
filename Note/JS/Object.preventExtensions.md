---
aliases:
  - 对象的可扩展性
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---


Reflect.isExtensible()
Reflect.preventExtensions()

[[Object.seal Object.freeze]]

Keys:
- 调用Object.preventExtensions()只会影响对象本身的可扩展能力。如果给一个不可扩展对象的原型添加了新属性,则这个不可扩展对象仍然会继承这些新属性
- 当一个对象不可扩展时，确实不能添加新属性，但对于已有属性，在其 `writable` 为 `true` 的情况下是可以修改的。 
- 一旦使用 `Object.preventExtensions()` 方法把对象设置成不可扩展，并没有直接的方法能将其重新设置为可扩展
	- 可以通过创建一个新对象，把原对象的属性复制过去 [[复制对象]]

1. **如何将对象设置为不可扩展**：
在 JavaScript 中，可以使用 `Object.preventExtensions()` 方法将一个对象设置为不可扩展。例如：
```javascript
let myObject = {
    name: "Alice",
    age: 30
};

// 将对象设置为不可扩展
Object.preventExtensions(myObject);

// 尝试添加新属性
myObject.gender = "female";
console.log(myObject.gender); // 输出：undefined，说明新属性添加失败
```
在上述代码中，`Object.preventExtensions(myObject)` 将 `myObject` 设置为不可扩展，之后尝试添加新属性 `gender` 是不会成功的，`myObject.gender` 的值为 `undefined`。

1. **修改已有属性**：
当对象不可扩展时，只要属性不是只读（`configurable` 为 `true` 且 `writable` 为 `true`），就可以对已有属性进行修改。例如：
```javascript
let myObject = {
    name: "Alice",
    age: 30
};

Object.preventExtensions(myObject);

// 修改已有属性
myObject.age = 31;
console.log(myObject.age); // 输出：31，说明已有属性可以被修改
```
在这段代码中，`myObject` 是不可扩展的对象，但是我们成功地修改了它已有的 `age` 属性的值。

1. **属性的特性（attribute）影响**：
属性具有 `configurable`（能否被删除或能否修改特性）、`writable`（能否被修改）、`enumerable`（能否在 `for...in` 循环中被枚举）等特性。如果一个属性的 `writable` 为 `false`，即使对象是可扩展的，也不能修改该属性的值；对于不可扩展的对象，如果属性 `writable` 为 `true`，则可以修改属性值。例如：
```javascript
let myObject = {
    name: "Alice",
    age: 30
};

Object.preventExtensions(myObject);

// 将 age 属性设置为只读
Object.defineProperty(myObject, "age", {
    writable: false
});

// 尝试修改只读属性
myObject.age = 31;
console.log(myObject.age); // 输出：30，说明只读属性不能被修改
```
在上述代码中，我们将 `age` 属性设置为只读（`writable` 为 `false`），即使对象 `myObject` 不可扩展，也无法修改 `age` 属性的值。

