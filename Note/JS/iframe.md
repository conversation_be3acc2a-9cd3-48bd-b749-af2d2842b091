### 定义与基本概念
iframe即内联框架，是一种HTML元素，用于在当前HTML页面中嵌入另一个HTML页面。可将其看作网页上用于展示其他在线内容的“窗口”。

[[Shadow DOM 与 iframe 的区别]]
### 主要特点
- **独立的文档上下文**：有自己独立的DOM树、window对象和全局作用域，与父页面相互隔离。
- **可跨域加载内容**：在设置了适当的CORS（跨域资源共享）后，能加载不同源的内容。
- **安全隔离性较好**：能在一定程度上实现父页面与嵌入内容间的安全隔离，避免相互干扰。

### 常用属性
- **src**：指定要嵌入的HTML文档的URL。
- **width** 和 **height**：设置iframe的宽度和高度，单位可以是像素或百分比。
- **allowfullscreen**：规定是否允许iframe以全屏模式显示。
- **sandbox**：对iframe中的内容进行安全限制。

### 使用场景
- **嵌入第三方内容**：如在网站中嵌入YouTube视频、Google Maps地图、社交媒体帖子等。
- **广告展示**：很多网站的侧边栏或横幅广告常通过iframe展示，方便广告商更新内容。
- **开发复杂应用功能**：如开发单登录系统、所见即所得（WYSIWYG）文本编辑器等。

### 示例代码
基本的iframe使用代码如下：
```html
<iframe src="https://example.com" width="500" height="300" title="示例iframe"></iframe>
```

### 注意事项
- **跨域限制**：受同源策略限制，跨域访问iframe内容时，需通过`window.postMessage`等方法进行跨域通信。
- **性能问题**：加载完整新文档可能影响页面加载速度，使用时要考虑性能优化。
- **浏览器兼容性**：不同浏览器对iframe的支持和处理可能有差异，需进行兼容性测试。
