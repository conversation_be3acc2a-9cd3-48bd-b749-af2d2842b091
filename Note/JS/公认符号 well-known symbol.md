---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---
是的，您对“公认符号”（well-known symbols）的理解是正确的。 它们是 `Symbol` 工厂函数的一组预定义属性，每个属性都代表一个特定的语言行为或操作。 这些符号允许 JavaScript 引擎和开发者之间进行更深层次的交互，可以自定义对象的行为。

以下是一些常见的公认符号及其用途：

` @@iterator`   [[Symbol.iterator]] 指定一个对象默认的迭代器。 `for...of` 循环会使用这个符号来获取对象的迭代器。
` @@asyncIterator`  [[Symbol.asyncIterator]] 指定一个对象默认的异步迭代器。 `for await...of` 循环会使用这个符号来获取对象的异步迭代器。
` @@hasInstance`  [[Symbol.hasInstance]] 一个方法，用于确定一个构造器对象是否认可一个对象是它的实例。  `instanceof` 运算符会使用这个符号。
` @@toStringTag`  [[Symbol.toStringTag]] 一个字符串，决定`Object.prototype.toString()` 返回的类型标签。注意与类自定义toString 的区别
` @@toPrimitive`  [[Symbol.toPrimitive]] 一个方法，用于将对象转换为原始值。
` @@species`  [[Symbol.species]] 一个构造器属性，用于创建派生对象 让其构造函数为特定类型 用于让返回的类型也属于这个特定类型。
[[Symbol 模式匹配符号]] Symbol.match、Symbol.matchAll和Symbol.replace是用于自定义正则表达式行为的特殊符号属性，它们为开发者提供了对字符串匹配机制进行深度控制的能力
`@@unscopables` [[Symbol.unscopables]]是一个内置的 Symbol，它用于自定义对象的属性在 `with` 语句中的可见性
### 举例说明 `Symbol.iterator`:

```javascript
const myIterable = {
  data: [1, 2, 3],
  [Symbol.iterator]: function* () {
    yield* this.data;
  }
};

for (const item of myIterable) {
  console.log(item); // 输出 1, 2, 3
}
```

在这个例子中，`Symbol.iterator` 定义了 `myIterable` 对象的迭代器。  `for...of` 循环使用这个迭代器来遍历 `myIterable.data` 数组。

### 总结:

公认符号是 `Symbol` 的预定义属性，它们代表了 JavaScript 语言内部的一些核心机制。 通过使用这些符号，开发者可以自定义对象的行为，使其与 JavaScript 引擎更好地集成。 它们提供了一种强大的方式来扩展和定制 JavaScript 的功能。

[[Symbol]]