这段内容主要讲解了在文档对象模型（DOM）中，使用`Element`属性和`Node`属性来遍历文档结构的相关知识，以下为详细讲解：

### 使用Element属性遍历文档结构 [[Element]]
 - **parentNode属性**：用于引用元素的父节点，父节点可以是另一个`Element`对象或者`Document`对象。比如在HTML文档中，一个`<p>`标签元素可能是`<div>`标签元素的子元素，那么这个`<div>`就是`<p>`的父节点，可通过`parentNode`获取。
 - **children属性**：是一个`NodeList`集合，它包含了该元素的所有子元素，但不包括文本节点（如标签之间的文字）和注释节点。例如，`<div>`标签内有多个`<span>`标签，这些`<span>`标签可以通过`<div>`的`children`属性获取。
 - **childElementCount属性**：用于获取元素所有子元素的个数。比如统计一个`<ul>`标签下有多少个`<li>`子元素，可以使用该属性。 
 - **firstElementChild和lastElementChild属性**：分别引用元素的第一个子元素和最后一个子元素。比如在一个`<nav>`标签内有多个`<a>`链接子元素，通过这两个属性可以方便获取第一个和最后一个`<a>`标签。
 - **previousElementSibling和nextElementSibling属性**：分别引用元素左侧紧邻的同辈元素和右侧紧邻的同辈元素。例如，在一排并列的`<div>`元素中，一个`<div>`可以通过这些属性找到相邻的其他`<div>`元素。

### 使用Node属性遍历文档结构 [[Node]]
 - **parentNode属性**：和`Element`中的类似，用于获取当前节点的父节点。对于没有父节点的节点（如文档的根节点`Document`对象），该属性值为`null`。 
 - **childNodes属性**：是一个只读的`NodeList`对象，与`Element`的`children`不同，它包含了节点的所有子节点，不仅有元素子节点，还包括文本节点和注释节点。 
 - **firstChild和lastChild属性**：获取当前节点的第一个子节点和最后一个子节点，如果没有子节点，则属性值为`null` 。和`firstElementChild`、`lastElementChild`的区别在于，`firstChild`和`lastChild`会包含所有类型的子节点。
 - **previousSibling和nextSibling属性**：用于获取当前节点的前一个同辈节点和后一个同辈节点，这些节点也包括文本节点和注释节点等所有类型。 



兄弟节点 vs 兄弟元素节点
兄弟节点：指的是在DOM树中，拥有相同父节点的所有节点。
兄弟元素节点：指的是在DOM树中，拥有相同父节点的所有元素节点。
主要区别：兄弟节点包括所有类型的节点（元素、文本、注释等），而兄弟元素节点只包括元素节点。

```html
复制
<div id="parent">
    <p>第一段</p>
    <!-- 这是一个注释 -->
    文本节点
    <span>一个span元素</span>
</div>
```

<p>、注释节点、文本节点和<span>都是兄弟节点。
但只有<p>和<span>是兄弟元素节点。
