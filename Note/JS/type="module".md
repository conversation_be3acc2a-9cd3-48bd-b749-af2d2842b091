## `type="module"` 脚本标签

在HTML中，使用``标签可以将JavaScript脚本作为ES6模块执行。这意味着脚本将被视为模块，支持使用`import`和`export`语句来导入和导出模块。

### 主要特点

1. **支持ES6模块语法**：可以使用`import`和`export`语句来导入和导出模块[3][4]。
2. **异步加载**：模块脚本会异步加载，不会阻塞页面渲染，类似于设置了`defer`属性[6][8]。
3. **严格模式**：模块脚本自动启用严格模式，无需显式声明`"use strict"`[6]。
4. **作用域**：模块代码运行在自己的作用域中，不会污染全局变量[5][6]。

### 使用方法

1. **内嵌模块代码**：
   ```html
   
     // JavaScript 模块代码
     import { sayHi } from './say.js';
     document.body.innerHTML = sayHi('<PERSON>');
   
   ```

2. **引入外部模块文件**：
   ```html
   
   ```

3. **回落机制**：对于不支持模块的浏览器，可以使用`nomodule`属性提供回落脚本：
   ```html
   
   
   ```

### 示例

假设有一个名为`hello.js`的模块文件：
```javascript
export function sayHello(name) {
  return `Hello, ${name}!`;
}
```

在HTML中使用`type="module"`引入并调用这个模块：
```html



  
  Document


  
    import { sayHello } from './hello.js';
    document.body.innerHTML = sayHello('Alice');
  


```