
事件捕获阶段（从根到目标）
目标处理阶段（实际触发）
事件冒泡阶段（从目标到根）

好的，以下是基于你提供的 HTML 结构，当点击 `child2` 和 `parent2` 时分别会发生的情况，我们假设添加了一些常见的 `click` 事件监听器来进行演示（你可以根据实际需求修改和扩展）：

```html
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <div id="grand">
    <div id="parent">
      <button id="child1">点击我</button>
      <button id="child2">点击我</button>
      <button id="child3">点击我</button>
    </div>
    <div id="parent2">
    </div>
  </div>
  <script>
    const grand = document.getElementById('grand');
    const parent = document.getElementById('parent');
    const parent2 = document.getElementById('parent2');
    const child1 = document.getElementById('child1');
    const child2 = document.getElementById('child2');
    const child3 = document.getElementById('child3');

    // grand 元素的捕获阶段监听器
    grand.addEventListener('click', () => console.log('grand 元素（捕获阶段）'), { capture: true });
    // grand 元素的冒泡阶段监听器
    grand.addEventListener('click', () => console.log('grand 元素（冒泡阶段）'), { capture: false });

    // parent 元素的捕获阶段监听器
    parent.addEventListener('click', () => console.log('parent 元素（捕获阶段）'), { capture: true });
    // parent 元素的冒泡阶段监听器
    parent.addEventListener('click', () => console.log('parent 元素（冒泡阶段）'), { capture: false });

    // parent2 元素的捕获阶段监听器
    parent2.addEventListener('click', () => console.log('parent2 元素（捕获阶段）'), { capture: true });
    // parent2 元素的冒泡阶段监听器
    parent2.addEventListener('click', () => console.log('parent2 元素（冒泡阶段）'), { capture: false });

    // child1 元素的捕获阶段监听器
    child1.addEventListener('click', () => console.log('child1 元素（捕获阶段）'), { capture: true });
    // child1 元素的冒泡阶段监听器
    child1.addEventListener('click', () => console.log('child1 元素（冒泡阶段）'), { capture: false });

    // child2 元素的捕获阶段监听器
    child2.addEventListener('click', () => console.log('child2 元素（捕获阶段）'), { capture: true });
    // child2 元素的冒泡阶段监听器
    child2.addEventListener('click', () => console.log('child2 元素（冒泡阶段）'), { capture: false });

    // child3 元素的捕获阶段监听器
    child3.addEventListener('click', () => console.log('child3 元素（捕获阶段）'), { capture: true });
    // child3 元素的冒泡阶段监听器
    child3.addEventListener('click', () => console.log('child3 元素（冒泡阶段）'), { capture: false });
  </script>
</body>

</html>
```

### 点击 `child2` 时的情况：
1. **事件捕获阶段**：
    - 事件从 `document` 开始，传播到 `grand` 元素，因为 `grand` 元素设置了 `capture: true` 的监听器，所以会执行 `grand` 元素上捕获阶段的监听器，控制台输出 `grand 元素（捕获阶段）`。
    - 继续向下传播到 `parent` 元素，执行 `parent` 元素上捕获阶段的监听器，控制台输出 `parent 元素（捕获阶段）`。
    - 到达 `child2` 元素，执行 `child2` 元素上捕获阶段的监听器，控制台输出 `child2 元素（捕获阶段）`。
2. **目标处理阶段**：
    - 到达目标元素 `child2`，执行 `child2` 元素上设置的 `capture: false`（冒泡阶段）的监听器，控制台输出 `child2 元素（冒泡阶段）`。
3. **事件冒泡阶段**：
    - 事件从 `child2` 向上冒泡到 `parent` 元素，执行 `parent` 元素上冒泡阶段的监听器，控制台输出 `parent 元素（冒泡阶段）`。
    - 继续冒泡到 `grand` 元素，执行 `grand` 元素上冒泡阶段的监听器，控制台输出 `grand 元素（冒泡阶段）`。

### 点击 `parent2` 时的情况：
1. **事件捕获阶段**：
    - 事件从 `document` 开始，传播到 `grand` 元素，执行 `grand` 元素上捕获阶段的监听器，控制台输出 `grand 元素（捕获阶段）`。
    - 继续向下传播到 `parent2` 元素，执行 `parent2` 元素上捕获阶段的监听器，控制台输出 `parent2 元素（捕获阶段）`。
2. **目标处理阶段**：
    - 到达目标元素 `parent2`，执行 `parent2` 元素上设置的 `capture: false`（冒泡阶段）的监听器，控制台输出 `parent2 元素（冒泡阶段）`。
3. **事件冒泡阶段**：
    - 事件从 `parent2` 向上冒泡到 `grand` 元素，执行 `grand` 元素上冒泡阶段的监听器，控制台输出 `grand 元素（冒泡阶段）`。

总结来说，点击 `child2` 时会按照捕获阶段、目标处理阶段、冒泡阶段的顺序依次执行相关元素的监听器并输出相应信息；点击 `parent2` 时也是类似的过程，只是涉及的元素不同。 
