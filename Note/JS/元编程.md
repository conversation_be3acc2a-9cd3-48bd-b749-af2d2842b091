[](Reflect.md)[](公认符号%20well-known%20symbol.md)---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---

核心思想：编写能够操作其他程序（或自身）的代码，实现 "代码的代码"（Code that manipulates code）。
```js
// 典型特征：在运行时动态修改程序行为
const obj = { name: "DeepSeek" };
const proxy = new Proxy(obj, { 
  get(target, key) { 
    return target[key] + "[PROXIED]"; // 动态修改属性访问
  }
});
console.log(proxy.name); // "DeepSeek[PROXIED]"
``` 


[[prototype 特性]]
[[公认符号 well-known symbol]]
[[模版标签]]
[[Reflect]]
[[Proxy]]
