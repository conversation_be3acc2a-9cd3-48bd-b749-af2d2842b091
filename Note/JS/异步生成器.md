---
aliases:
  - async function*
---
# 异步生成器

异步生成器函数是 JavaScript 中结合异步操作和生成器特性的重要功能，以下是详细讲解：

组合::[[生成器]] ,[[Async_Await]]

---

### 一、基本定义

```javascript
async function* asyncGenerator() {
  // 可同时使用 await 和 yield
  yield await fetchData1();
  yield await fetchData2();
}

// 等价于
let count = 0;
const asyncGenerator = {
  [Symbol.asyncIterator]() {
    return {
      next() {
        if(count === 0) {
          count++;
          return {
            value: fetchData1(),
            done: false
          }
        }
        if(count === 1) {
          count++;
          return {
            value: fetchData2(),
            done: false
          }
        }
        return {
          value: undefined,
          done: true
        }
      }
    }
  }
}
```

- **双重特性**：同时支持 `async/await` 和生成器 `function*`
- **语法特征**：函数名前同时包含 `async` 和 `*`
- **返回值**：返回异步生成器对象（同时实现 `AsyncIterator` 和 `AsyncIterable` 协议）

---

### 二、执行机制

#### 1. 生成器阶段

```javascript
const generator = asyncGenerator()
const res = await generator.next()
```

- 调用时不立即执行函数体
- 返回的生成器对象需要通过 `next()` 方法驱动

#### 2. 迭代阶段

```javascript
for await (const data of asyncGenerator()) {
  console.log(data)
}
```

- 每次迭代自动调用 `generator.next()`
- 返回值为 `Promise<{ value, done }>` 对象
- `for await...of` 会自动解析 Promise

---

### 三、核心特点

| 特性                | 说明                          |
|---------------------|-----------------------------|
| **异步产出值**       | 每个 `yield` 可等待异步操作完成 |
| **顺序执行**         | 严格执行 `yield` 顺序         |
| **流式处理**         | 支持处理实时数据流             |
| **内存高效**         | 无需等待所有异步操作完成       |

---

### 四、对比分析

```javascript
// 普通生成器（同步）
function* syncGenerator() {
  yield syncOperation()
}

// 异步生成器
async function* asyncGenerator() {
  yield await asyncOperation()
}
```

|           | 普通生成器           | 异步生成器                  |
| --------- | --------------- | ---------------------- |
| **迭代协议**  | Iterable        | AsyncIterable          |
| **返回值类型** | { value, done } | Promise<{value, done}> |
| **遍历方式**  | for...of        | for await...of         |
| **执行时机**  | 同步执行            | 可包含异步等待                |

---

### 五、典型应用场景

1. **分页数据加载**

```javascript
async function* paginatedFetcher() {
  let page = 1
  while(true) {
    const res = await fetch(`/api/data?page=${page}`)
    const data = await res.json()
    if(!data.hasMore) break
    yield data
    page++
  }
}
```

1. **实时数据流处理**

```javascript
async function* streamProcessor() {
  const stream = getReadableStream()
  for await (const chunk of stream) {
    yield processChunk(chunk)
  }
}
```

1. **多任务流水线**

```javascript
async function* dataPipeline() {
  const rawData = await fetchRawData()
  yield* transformData(rawData)  // 委托其他生成器
  yield await finalizeProcess()
}
```

---

### 六、注意事项

1. **错误处理**：必须用 `try...catch` 包裹

```javascript
async function* safeGenerator() {
  try {
    yield await fetchData()
  } catch (err) {
    console.error('Fetch failed:', err)
  }
}
```

1. **并发控制**：多个 `yield` 默认串行执行，需要并行时可结合 `Promise.all`

```javascript
async function* parallelYields() {
  const [res1, res2] = await Promise.all([fetchA(), fetchB()])
  yield res1
  yield res2
}
```

1. **浏览器兼容**：需要 ES2018+ 环境支持

---

这种模式特别适合需要 **逐步处理异步数据** 的场景，相比传统的回调或 Promise 链式调用，能更好地保持代码的线性逻辑和可读性。
