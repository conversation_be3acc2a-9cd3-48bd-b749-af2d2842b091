---
aliases:
  - 反射
  - Reflect Api
tags:
  - ES6
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---

**Reflect API** 是 JavaScript 中一个强大的工具，用于元编程和处理 Proxy 对象。它提供了一种<mark style="background: #BBFABBA6;">一致的、函数式的方式来执行属性访问、修改和函数调用等操作</mark>，这些操作可以被拦截和自定义。通过将 Reflect 与 Proxy 结合使用，开发者可以为日志记录、验证或动态对象操作等场景创建复杂且易于维护的代码。

### Reflect API 是什么？

**Reflect API** 是 JavaScript 中的一个[[全局对象]]，提供了一系列静态方法，用于执行常见操作，例如访问属性、设置值、检查属性是否存在、删除属性以及调用函数。这些操作是 JavaScript 的基础，并且可以被 Proxy 对象拦截，因此 Reflect 成为了 **元编程**（编写在运行时操作或检查其他代码的代码）的重要工具。

与传统操作符（例如 `obj[prop]` 或 `delete obj[prop]`）不同，Reflect 方法提供了一种函数式、标准化的方式来执行这些操作。它们特别设计为与 Proxy 陷阱（traps）无缝协作，确保使用的一致性和简便性。

---
### Reflect API 如何工作？

Reflect API 由 [[静态方法]] 组成，这些方法反映了 JavaScript 操作符和内置函数的行为。以下是一些最常用的方法：
### Reflect API 方法概览（按常见程度排序）

Reflect API 是 JavaScript 中的一个内置对象，提供了一系列静态方法，用于执行对象的常见操作，如属性访问、设置、删除等。这些方法在元编程和 Proxy 对象中特别有用。以下表格列出了所有 Reflect API 的方法，并按照其在开发中的常见程度排序，从最常用到最不常用。

| 方法                                   | 参数                                             | 描述                                                                                      |
| ------------------------------------ | ---------------------------------------------- | --------------------------------------------------------------------------------------- |
| [[Reflect.get]]                      | `target`, `propertyKey`, `[receiver]`          | 从对象中获取属性的值。可选的 `receiver` 参数用于在属性是 getter 时指定 `this` 的上下文。                              |
| [[Reflect.set]]                      | `target`, `propertyKey`, `value`, `[receiver]` | 设置对象属性的值。可选的 `receiver` 参数用于在属性是 setter 时指定 `this` 的上下文。                                |
| [[Reflect.has]]                      | `target`, `propertyKey`                        | 检查对象上是否存在某个属性，类似于 `in` 操作符。                                                             |
| [[Reflect.deleteProperty]]           | `target`, `propertyKey`                        | 删除对象上的属性，类似于 `delete` 操作符。                                                              |
| [[Reflect.apply]]                    | `target`, `thisArgument`, `argumentsList`      | 使用指定的 `this` 上下文和参数列表调用函数。                                                              |
| [[Reflect.construct]]                | `target`, `argumentsList`, `[newTarget]`       | 使用 `new` 操作符调用构造函数，类似于 `new target(...argumentsList)`。                                  |
| [[Reflect.defineProperty]]           | `target`, `propertyKey`, `attributes`          | 定义对象上的新属性或修改现有属性，类似于 `Object.defineProperty()`。                                         |
| [[Reflect.getOwnPropertyDescriptor]] | `target`, `propertyKey`                        | 获取对象自有属性的描述符，类似于 `Object.getOwnPropertyDescriptor()`。                                   |
| [[Reflect.getPrototypeOf]]           | `target`                                       | 获取对象的原型，类似于 `Object.getPrototypeOf()`。                                                  |
| [[Reflect.setPrototypeOf]]           | `target`, `proto`                              | 设置对象的原型，类似于 `Object.setPrototypeOf()`。                                                  |
| [[Reflect.isExtensible]]             | `target`                                       | 检查对象是否可扩展，类似于 `Object.isExtensible()`。                                                  |
| [[Reflect.preventExtensions]]        | `target`                                       | 阻止对象扩展，类似于 `Object.preventExtensions()`。                                                |
| [[Reflect.ownKeys]]                  | `target`                                       | 返回对象自有属性的键数组，类似于 `Object.getOwnPropertyNames()` 和 `Object.getOwnPropertySymbols()` 的组合。 |
|                                      |                                                |                                                                                         |

### 说明
- **最常用**：`Reflect.get()` 和 `Reflect.set()` 是访问和修改对象属性的核心方法，特别是在 Proxy 的陷阱（traps）中频繁使用。
- **常见操作**：`Reflect.has()` 和 `Reflect.deleteProperty()` 提供了属性检查和删除的简便方式，常用于动态对象操作。
- **函数调用**：`Reflect.apply()` 和 `Reflect.construct()` 用于灵活的函数和构造函数调用，在元编程场景中较为实用。
- **属性与原型管理**：`Reflect.defineProperty()`、`Reflect.getOwnPropertyDescriptor()` 等方法适用于需要精确控制对象属性的情况。
- **较少使用**：`Reflect.isExtensible()`、`Reflect.preventExtensions()` 和 `Reflect.ownKeys()` 在特定场景（如对象密封或键枚举）中使用，频率相对较低。

这些方法涵盖了 Reflect API 的全部功能，您可以根据具体需求选择合适的方法，并在 JavaScript 项目中结合 Proxy 或其他元编程技术加以应用。

---

### Reflect API 的主要用例

Reflect API 在以下场景中表现出色：

1. **元编程**  
   它使开发者能够动态地检查或操作对象，支持高级模式，如日志记录、验证或对象观察。

2. **Proxy 陷阱**  
   在使用 Proxy 对象时，Reflect 方法通常在陷阱内部使用，以调用操作的默认行为，从而在不需要重新实现基本功能的情况下进行自定义。

3. **一致性**  
   Reflect 提供了一种统一的、基于函数的方式来执行操作，这些操作原本需要操作符（例如 `.` 或 `[]`）或关键字（例如 `delete`），从而提高了代码的可读性和可维护性。


[[Reflex EG]]
[[Reflect API 与 Proxy 对象的关系]]

