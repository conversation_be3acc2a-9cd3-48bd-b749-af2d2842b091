
### Reflect API 的实际应用示例

以下是一些实际示例，说明 Reflect API 的工作原理：

#### 示例 1：基本属性访问和修改
```javascript
const obj = { a: 1 };

// 访问属性
console.log(Reflect.get(obj, 'a'));  // 输出: 1

// 修改属性
Reflect.set(obj, 'a', 2);
console.log(obj.a);  // 输出: 2
```

#### 示例 2：检查属性是否存在
```javascript
const obj = { a: 1 };

// 检查属性是否存在
console.log(Reflect.has(obj, 'a'));  // 输出: true
console.log(Reflect.has(obj, 'b'));  // 输出: false
```

#### 示例 3：调用函数
```javascript
function greet(name) {
  console.log(`Hello, ${name}!`);
}

// 使用 Reflect.apply 调用函数
Reflect.apply(greet, null, ['Alice']);  // 输出: Hello, <PERSON>!
```

#### 示例 4：将 Reflect 与 Proxy 结合使用
```javascript
const target = { a: 1 };
const handler = {
  get(target, prop, receiver) {
    console.log(`获取属性: ${prop}`);
    return Reflect.get(target, prop, receiver);  // 默认行为
  },
  set(target, prop, value, receiver) {
    console.log(`设置属性: ${prop} 为 ${value}`);
    return Reflect.set(target, prop, value, receiver);  // 默认行为
  }
};

const proxy = new Proxy(target, handler);

console.log(proxy.a);  // 输出: 获取属性: a \n 1
proxy.a = 2;           // 输出: 设置属性: a 为 2
console.log(target.a); // 输出: 2
```

在这个 Proxy 示例中，陷阱记录每个操作，同时使用 Reflect 保持默认行为，展示了这两个 API 如何协同工作。

---

### 总结

**Reflect API** 是 JavaScript 中一个强大的工具，用于元编程和处理 Proxy 对象。它提供了一种一致的、函数式的方式来执行属性访问、修改和函数调用等操作，这些操作可以被拦截和自定义。通过将 Reflect 与 Proxy 结合使用，开发者可以为日志记录、验证或动态对象操作等场景创建复杂且易于维护的代码。

欲了解更多详情，请查看 [MDN 上的 Reflect 文档](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Reflect) 和 [Proxy 文档](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy)。