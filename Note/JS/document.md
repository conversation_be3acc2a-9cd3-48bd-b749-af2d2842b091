---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---

Document对象在DOM中扮演着核心角色，它是DOM树的根节点，代表整个HTML或XML文档，并提供了对文档内容、结构和元数据的操作入口。通过Document对象，开发者可以访问和修改文档中的元素、属性和样式，实现动态生成和操作网页内容。

具体来说，Document对象提供了多种方法和属性来操作文档：
1. **访问和操作元素**：可以通过`document.getElementById()`、`document.querySelector()`等方法获取特定元素，也可以使用`document.createElement()`创建新元素。
2. **修改文档内容**：可以使用`innerHTML`或`textContent`属性修改元素的内容，或者通过`appendChild()`、`insertBefore()`等方法调整元素的顺序。
3. **操作样式和属性**：可以通过`style`属性直接修改元素的CSS样式，也可以通过`setAttribute()`和`removeAttribute()`方法修改或删除HTML属性。
4. **事件处理**：可以通过`addEventListener()`为元素添加事件监听器，响应用户的交互操作。
5. **获取文档信息**：Document对象还提供了元数据属性，如`document.characterSet`、`document.compatMode`、`document.domain`等，用于获取文档的字符集、兼容模式和域名等信息。

Document对象是DOM操作的核心，通过它可以高效地访问、修改和操作网页文档的内容和结构，为网页开发提供了强大的工具。


[[querySelectorAll]]