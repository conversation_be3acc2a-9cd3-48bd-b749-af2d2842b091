```js
class MyObject {
  get [Symbol.toStringTag]() {
    return "MyCustomObject";
  }

  constructor() {
    this.name = "MyCustomObject";
    this.data = "data";
  }

  toString() {
    return `[class ${this.name} ${this.data}]`;
  }
}

const obj = new MyObject();
console.log(Object.prototype.toString.call(obj)); // "[object MyCustomObject]"

console.log(obj.toString()); // "[class MyCustomObject]"

obj.toString = function () {
  return `[object ${this.name} ${this.data}]`;
};

console.log(obj.toString()); // "[object MyCustomObject data]"





```  


Symbol.toStringTag 用于修改 Object.prototype 的 toString 返回类型