- **核心概念**：`Symbol.asyncIterator` 定义 JavaScript 异步迭代行为，让对象可用 `for await...of` 异步迭代。异步可迭代对象需实现此方法，其返回异步迭代器对象，含 `next()` 方法且返回 Promise。
- **实现步骤**：定义 `Symbol.asyncIterator` 方法返回异步迭代器对象，实现 `next()` 方法返回 `{ value, done }` 形式的 Promise。
- **示例**：`delayedResponses` 对象通过 `Symbol.asyncIterator` 实现异步迭代延迟响应。
- **与 `Symbol.iterator` 区别**：方法返回值、`next()` 返回值、循环语法不同。 [[Symbol.iterator]]
- **总结**：`Symbol.asyncIterator` 是异步迭代关键，`next()` 返回 Promise 支持 `await` 异步操作。 


