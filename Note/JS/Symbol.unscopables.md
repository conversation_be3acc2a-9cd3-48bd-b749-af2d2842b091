在 JavaScript 中，`Symbol.unscopables` 是一个内置的 Symbol，它用于自定义对象的属性在 `with` 语句中的可见性。`with` 语句在现代 JavaScript 开发中并不常用，因为它可能会导致代码的可读性和可维护性变差，不过 `Symbol.unscopables` 本身为开发者提供了一种控制对象属性在 `with` 语句作用域内是否可见的方式。[[with 语句]]

### 基本原理
当一个对象具有 `Symbol.unscopables` 属性时，该属性的值应该是一个对象，其中的键是要排除在 `with` 语句作用域之外的属性名，对应的值为 `true`。当使用 `with` 语句时，对象中那些在 `Symbol.unscopables` 对象里被标记为 `true` 的属性将不会被添加到 `with` 语句的作用域中。

### 示例代码
```javascript
// 创建一个对象
const myObject = {
    prop1: 'value1',
    prop2: 'value2',
    [Symbol.unscopables]: {
        prop2: true // 将 prop2 排除在 with 语句作用域之外
    }
};

// 使用 with 语句
with (myObject) {
    console.log(prop1); // 输出: value1
    console.log(prop2); // 这里会报错，因为 prop2 被排除在 with 语句作用域之外
}
```

### 内置对象中的应用
在 JavaScript 的一些内置对象（如 `Array`）中，也使用了 `Symbol.unscopables` 来避免某些方法在 `with` 语句中造成混淆。例如：
```javascript
const arr = [1, 2, 3];
console.log(Array.prototype[Symbol.unscopables]);
// 输出包含如 'copyWithin', 'entries', 'fill', 'find', 'findIndex', 'includes', 'keys' 等属性，值都为 true

with (arr) {
    // 下面这行代码会报错，因为 'includes' 被排除在 with 语句作用域之外
    console.log(includes(1)); 
}
```
在这个例子中，`Array.prototype` 上的 `Symbol.unscopables` 对象包含了一些数组方法，这些方法不会在 `with` 语句中被视为数组对象的属性，从而避免了可能的命名冲突和混淆。

### 注意事项
- **不推荐使用 `with` 语句**：由于 `with` 语句会影响代码的可读性和可维护性，并且可能导致意外的变量作用域问题，在现代 JavaScript 开发中通常不建议使用。
- **`Symbol.unscopables` 的作用范围**：`Symbol.unscopables` 仅影响 `with` 语句中对象属性的可见性，不会影响对象属性在其他地方的正常使用。 