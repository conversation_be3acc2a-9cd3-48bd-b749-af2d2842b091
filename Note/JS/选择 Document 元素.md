[[querySelectorAll]]
[[Element#Element 获取节点方法]]

### 其他选择元素的方法
2. **常用方法及示例**
    - **`getElementById()`**：通过id属性查找元素，参数为id值（不带#），返回单个Element对象，如 `let sect1 = document.getElementById("sect1");` 。
    - **`getElementsByName()`**：查找具有指定name属性的所有元素，返回NodeList，如查找表单复选框 `let colors = document.getElementsByName("color");` 。
    - **`getElementsByTagName()`**：按标签名查找元素，在document或Element对象上都可调用 。在document上调用查找文档中所有匹配标签元素，在Element对象上调用查找其后代中匹配标签元素，返回NodeList，如 `let headings = document.getElementsByTagName("h1");` 和 `let subheads = sect1.getElementsByTagName("h2");` 。
    - **`getElementsByClassName()`**：根据类名查找元素，document或Element对象上均可调用，返回NodeList，如 `let tooltips = document.getElementsByClassName("tooltip");` 和 `let sidebars = sect1.getElementsByClassName("sidebar");` 。
3. **与querySelectorAll()区别**：老式选择方法（除 `getElementById()` ）返回的NodeList是“活的”，其length属性和元素会随文档内容或结构变化而变化；而 `querySelectorAll()` 返回的NodeList是“静态”的 。 