---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---
对象属性描述符



属性描述符分为两种类型：
- **数据描述符**：具有 `value` 和 `writable` 属性。
  - `value`：属性的值。
  - `writable`：一个布尔值，表示该属性的值是否可以被修改。如果为 `false`，则该属性是只读的。
- **存取描述符**：具有 `get` 和 `set` 属性。
  - `get`：一个函数，作为该属性的 getter 方法，当访问该属性时会调用此函数。
  - `set`：一个函数，作为该属性的 setter 方法，当给该属性赋值时会调用此函数。

两种类型的描述符都有以下可选属性：
- `configurable`：一个布尔值，表示该属性的描述符是否可以被修改，以及该属性是否可以从对象中删除。如果为 `false`，则不允许进行这些操作。
- `enumerable`：一个布尔值，表示该属性是否可以在 `for...in` 循环和 `Object.keys()` 等方法中被枚举。 [[可枚举属性]]

---
- 如果对象不可扩展,可以修改其已有属性,但不能给它添加新属性。[[Object.preventExtensions]]
- 如果属性不可配置,不能修改其configurable或enumerable 特性。
- 如果访问器属性不可配置,不能修改其获取方法或设置方法,也不能把它修改为数据属性。
- 如果数据属性不可配置,不能把它修改为访问器属性。
- 如果数据属性不可配置,不能把它的writable特性由false修改为true,但可以由true修改为false
- 如果数据属性不可配置且不可写,则不能修改它的值。不过,如果这个属性可配置但不可写,则可以修改它的值(相当于先把它配置为可写,然后修改它的值,再把它配置为不可写)
```js
const obj = {}; // 使用 Object.defineProperty 定义一个可配置但不可写的属性
Object.defineProperty(obj, 'prop', 
{ value: 10, configurable: true, writable: false }); // 先将属性配置为可写 

Object.defineProperty(obj, 'prop', { writable: true }); // 修改属性的值 
obj.prop = 20; // 再将属性配置为不可写 
Object.defineProperty(obj, 'prop', { writable: false }); console.log(obj.prop); // 输出: 20，说明值已被修改
```

每个对象属性都有以下两种描述符之一：
1. **数据描述符**：定义属性的值
   ```javascript
   {
     value: 'DeepSeek',  // 属性值
     writable: true,     // 是否可修改
     enumerable: true,   // 是否可枚举（出现在 for-in 循环中）
     configurable: true  // 是否可删除或修改描述符
   }
   ```
2. **存取描述符**：通过 getter/setter 控制属性 [[访问器属性]]
   ```javascript
   {
     get() { return this._name }, 
     set(v) { this._name = v.toUpperCase() },
     enumerable: true,
     configurable: true
   }
   ```