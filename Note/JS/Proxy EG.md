---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---
 代理的基本示例

以下是一个简单示例，展示如何使用代理记录属性访问和修改：

```javascript
const target = { a: 1, b: 2 };

const handler = {
  get(target, property, receiver) {
    console.log(`正在访问属性 "${property}"`);
    return Reflect.get(target, property, receiver);
  },
  set(target, property, value, receiver) {
    console.log(`正在设置属性 "${property}" 为 ${value}`);
    return Reflect.set(target, property, value, receiver);
  }
};

const proxy = new Proxy(target, handler);

console.log(proxy.a); // 输出: 正在访问属性 "a" \n 1
proxy.b = 3;          // 输出: 正在设置属性 "b" 为 3
```

在这个示例中，代理拦截了对 `target` 对象的属性访问和修改，并在控制台记录了相应的操作。

---

#### 5. 代理的注意事项和常见陷阱

使用代理时，需要注意以下几点：

- **性能**：代理会引入额外开销，特别是在频繁访问或修改属性的场景中。需要权衡其好处与性能影响。
- **陷阱的实现**：陷阱的实现需要小心，以免破坏对象的正常行为。例如，在 `get` 陷阱中，若未返回正确值，可能导致意外行为。
- **不可拦截的操作**：并非所有操作都能被代理拦截，例如 `typeof` 操作符和 `instanceof` 操作符。
- **代理的嵌套**：若目标对象本身也是代理，陷阱调用顺序可能变得复杂，需避免无限循环或意外行为。
- **Reflect API**：代理常与Reflect API一起使用，提供默认行为。例如，`Reflect.get(target, property, receiver)` 等同于 `target[property]`。

---

#### 6. 代理与Reflect API的关系

Reflect API 是一组静态方法，提供标准化的方式来执行对象操作，这些操作与代理的陷阱相对应。在代理的陷阱中，Reflect API 常用于执行默认行为，确保代理行为与原始对象一致。例如，在上述示例中，`Reflect.get` 和 `Reflect.set` 用于执行默认的属性访问和设置操作。

---

#### 7. 代理的实际应用示例

##### 示例1：数据验证

```javascript
const validator = {
  set(target, property, value) {
    if (property === 'age' && (typeof value !== 'number' || value < 0)) {
      throw new TypeError('年龄必须是一个非负数');
    }
    return Reflect.set(target, property, value);
  }
};

const person = new Proxy({}, validator);
person.age = 25; // 成功
person.age = -5; // 抛出错误: TypeError: 年龄必须是一个非负数
```

##### 示例2：虚拟化（惰性加载）

```javascript
const lazyLoader = {
  get(target, property) {
    if (!(property in target)) {
      target[property] = `虚拟属性 ${property} 被访问`;
    }
    return target[property];
  }
};

const virtualObj = new Proxy({}, lazyLoader);
console.log(virtualObj.someProperty); // 输出: 虚拟属性 someProperty 被访问
```