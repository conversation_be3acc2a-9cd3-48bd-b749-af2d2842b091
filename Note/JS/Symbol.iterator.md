

`Symbol.iterator` 是 JavaScript 中定义对象可迭代行为的核心符号，它是实现**迭代协议**的关键。以下是其核心特性与应用场景：

---

### **1. 核心作用**

- `Symbol.iterator` 是一个内置的 `Symbol` 值，用于定义对象的默认迭代器。
- 任何实现了 `Symbol.iterator` 方法的对象被称为**可迭代对象**，可以通过 `for...of`、`[...spread]` 等语法遍历。

---

### **2. 基本实现**

一个合法的 `Symbol.iterator` 方法需要返回一个**迭代器对象**，该对象必须包含 `next()` 方法。

#### 示例：自定义可迭代对象

```javascript
const customIterable = {
  data: [10, 20, 30],
  [Symbol.iterator]() {
    let index = 0;
    return {
      next: () => {
        if (index < this.data.length) {
          return { value: this.data[index++], done: false };
        } else {
          return { done: true };
        }
      }
    };
  }
};

// 使用 for...of 遍历
for (const value of customIterable) {
  console.log(value); // 输出 10, 20, 30
}
```

---

### **3. 内置可迭代对象**

JavaScript 中许多内置对象已实现 `Symbol.iterator`：


| 对象类型 | 迭代行为 |
| :-- | :-- |
| **数组** | 遍历元素值 |
| **字符串** | 遍历 Unicode 字符 |
| **Map** | 遍历键值对 `[key, value]` |
| **Set** | 遍历元素值 |
| **NodeList** | 遍历 DOM 节点 |

#### 示例：字符串的迭代

```javascript
const str = "hello";
const iterator = str[Symbol.iterator]();

console.log(iterator.next().value); // "h"
console.log(iterator.next().value); // "e"
```

---

### **4. 生成器与 Symbol.iterator**

生成器函数（`function*`）返回的生成器对象**同时实现了可迭代协议和迭代器协议**，其 `Symbol.iterator` 方法直接返回自身。

#### 示例：生成器的迭代行为

```javascript
function* numberGenerator() {
  yield 1;
  yield 2;
  yield 3;
}

const gen = numberGenerator();

// 生成器对象同时是迭代器和可迭代对象
console.log(gen === gen[Symbol.iterator]()); // true

// 使用 for...of 遍历
for (const num of gen) {
  console.log(num); // 1, 2, 3
}
```

---

### **5. 手动调用迭代器**

可以通过直接操作迭代器控制遍历过程：

```javascript
const array = [100, 200, 300];
const iterator = array[Symbol.iterator]();

let result = iterator.next();
while (!result.done) {
  console.log(result.value); // 100, 200, 300
  result = iterator.next();
}
```

---

### **6. 常见应用场景**

1. **自定义数据结构**
为链表、树等结构实现迭代逻辑。
1. **惰性计算**
生成器逐步产生值，节省内存。
1. **异步迭代**
结合 `Symbol.asyncIterator` 实现异步遍历（如流数据处理）。

---

### **总结**

- `Symbol.iterator` 是定义对象如何被迭代的标准方式。
- 可迭代对象通过返回迭代器实现 `for...of` 的自动遍历。
- 生成器对象天然支持迭代协议，简化了迭代逻辑的实现。
