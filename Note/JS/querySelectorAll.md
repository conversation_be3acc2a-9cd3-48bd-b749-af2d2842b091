[[CSS 选择器]]

`querySelectorAll()`是DOM（Document Object Model）中的一个方法，用于在文档中查找与指定CSS选择器匹配的所有元素。以下是关于它的详细解释：
- **语法**：`document.querySelectorAll(selectors)`，其中`selectors`是一个字符串，表示一个或多个CSS选择器，多个选择器之间用逗号分隔。除了`document`对象可以调用外，任意`Element`对象也能调用，在调用对象的范围内查找匹配元素。
- **返回值**：该方法返回一个`NodeList`对象，它是一个包含所有匹配元素的类数组集合。可以通过索引（从0开始）来访问其中的元素，例如`nodeList[0]`获取第一个匹配元素。但`NodeList`并非真正的数组，若要使用数组方法，需转换，如`Array.from(nodeList)`。  如果没有匹配到 nodeList.length = 0
  
  [[NodeList]]
  
- **示例**
```javascript
// 获取文档中所有class为"example"的元素
let elements = document.querySelectorAll('.example');
for (let i = 0; i < elements.length; i++) {
    console.log(elements[i]);
}
```
```html
<ul>
    <li class="example">元素1</li>
    <li class="example">元素2</li>
</ul>
```
上述代码中，`querySelectorAll('.example')`获取所有class为"example"的元素，通过循环可以对每个匹配元素进行操作。
- **注意事项**：`querySelectorAll()`返回的是调用时匹配的元素集合，文档后续变化不会自动更新`NodeList`，若文档结构改变，需再次调用获取最新集合。 