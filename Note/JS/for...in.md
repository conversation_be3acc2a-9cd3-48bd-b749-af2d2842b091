在 JavaScript 中，`for...in` 语句是一种用于遍历对象可枚举属性的循环结构。下面将详细介绍 `for...in` 的使用方法、特点及注意事项。

### 语法
```javascript
for (variable in object) {
  // 代码块
}
```
- `variable`：在每次迭代中，会被赋值为对象的一个可枚举属性名（字符串类型）。
- `object`：要遍历的对象。

### 示例代码
#### 遍历普通对象
```javascript
const person = {
  name: '<PERSON>',
  age: 30,
  occupation: 'Engineer'
};

for (let key in person) {
  console.log(key + ': ' + person[key]);
}
```
在上述代码中，`for...in` 循环会遍历 `person` 对象的每个可枚举属性，`key` 依次为 `'name'`、`'age'` 和 `'occupation'`，并将属性名和对应的属性值打印到控制台。

#### 遍历数组
```javascript
const numbers = [10, 20, 30];

for (let index in numbers) {
  console.log('Index: ' + index + ', Value: ' + numbers[index]);
}
```
当使用 `for...in` 遍历数组时，`index` 会是数组元素的索引（字符串类型），可以通过该索引访问数组元素。

### 特点
- **遍历可枚举属性**：`for...in` 会遍历对象自身的可枚举属性以及继承的可枚举属性。可枚举属性是指那些 `enumerable` 特性为 `true` 的属性。
- **属性顺序不确定**：对于对象的属性，`for...in` 遍历的顺序是不确定的，不同的 JavaScript 引擎可能会有不同的实现。对于数组，虽然通常会按索引顺序遍历，但这并不是规范所保证的。

### 注意事项
- **不适合遍历数组**：由于 `for...in` 会遍历数组的可枚举属性，包括可能添加到数组原型上的属性，而且遍历顺序不确定，所以不建议使用 `for...in` 来遍历数组。推荐使用 `for` 循环、`for...of` 循环或数组的迭代方法（如 `forEach`）来遍历数组。
```javascript
Array.prototype.customProperty = 'Custom Value';
const array = [1, 2, 3];

for (let index in array) {
  console.log(index); // 可能会输出 '0', '1', '2', 'customProperty'
}
```
- **过滤原型链上的属性**：如果只想遍历对象自身的属性，可以使用 `hasOwnProperty` 方法来过滤掉继承的属性。
```javascript
const person = {
  name: 'John',
  age: 30
};

for (let key in person) {
  if (person.hasOwnProperty(key)) {
    console.log(key + ': ' + person[key]);
  }
}
```

综上所述，`for...in` 主要用于遍历对象的可枚举属性，但在使用时需要注意其特点和适用场景。