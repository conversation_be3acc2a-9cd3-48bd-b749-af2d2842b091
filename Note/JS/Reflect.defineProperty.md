---
canvas:
  - "[[DOM 接口层次结构关联.canvas]]"
---
`Reflect.defineProperty` 是 JavaScript 中 `Reflect` 对象的一个静态方法，它提供了一种在对象上定义新属性，或者修改现有属性的属性描述符的方式。这个方法和 `Object.defineProperty` 功能类似，但有一些不同的返回值和行为特点。

### 语法
```javascript
Reflect.defineProperty(target, propertyKey, attributes)
```

### 参数
- **`target`**：需要定义属性的目标对象。
- **`propertyKey`**：要定义或修改的属性的名称，可以是字符串或者 `Symbol` 类型。
- **`attributes`**：属性描述符对象，用于描述属性的配置，例如 `value`（属性的值）、`writable`（是否可写）、`enumerable`（是否可枚举）、`configurable`（是否可配置）等。

### 返回值
`Reflect.defineProperty` 方法返回一个布尔值，表示属性是否成功定义。如果属性成功定义，返回 `true`；否则返回 `false`。

### 示例代码

#### 定义一个新属性
```javascript
const obj = {};
const result = Reflect.defineProperty(obj, 'newProperty', {
    value: 'Hello, World!',
    writable: true,
    enumerable: true,
    configurable: true
});

if (result) {
    console.log('属性定义成功');
    console.log(obj.newProperty); // 输出: Hello, World!
} else {
    console.log('属性定义失败');
}
```

#### 修改现有属性的描述符
```javascript
const person = {
    name: 'John'
};

// 修改 name 属性为不可写
const isModified = Reflect.defineProperty(person, 'name', {
    writable: false
});

if (isModified) {
    console.log('属性描述符修改成功');
    person.name = 'Jane'; // 尝试修改 name 属性
    console.log(person.name); // 输出: John，因为属性不可写
} else {
    console.log('属性描述符修改失败');
}
```

### 与 `Object.defineProperty` 的区别
- **返回值**：`Object.defineProperty` 返回传入的目标对象，而 `Reflect.defineProperty` 返回一个布尔值，表示操作是否成功。
- **错误处理**：`Object.defineProperty` 在操作失败时会抛出异常，而 `Reflect.defineProperty` 只是返回 `false`，不会抛出异常，这使得错误处理更加方便。

```javascript
const nonExtensibleObj = {};
Object.preventExtensions(nonExtensibleObj);

// 使用 Object.defineProperty，会抛出异常
try {
    Object.defineProperty(nonExtensibleObj, 'newProp', { value: 42 });
} catch (error) {
    console.log('Object.defineProperty 抛出异常:', error.message);
}

// 使用 Reflect.defineProperty，返回 false
const defineResult = Reflect.defineProperty(nonExtensibleObj, 'newProp', { value: 42 });
console.log('Reflect.defineProperty 结果:', defineResult); // 输出: false
```

通过上述示例可以看出，`Reflect.defineProperty` 提供了一种更简洁、更安全的方式来定义和修改对象的属性。