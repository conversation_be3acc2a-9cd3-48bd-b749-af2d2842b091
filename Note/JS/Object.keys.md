在 JavaScript 中，`Object.keys()` 是一个非常有用的静态方法，它用于返回一个由指定对象的所有<mark style="background: #FF5582A6;">可枚举属性</mark>组成的数组，这些属性为对象自身的属性（不包括原型链上的属性），并且数组中属性名的排列顺序和使用 `for...in` 循环遍历该对象时返回的属性顺序一致。
[[Object.getOwnPropertyNames() - Object.keys()]]


### 语法
```javascript
Object.keys(obj)
```

- **参数**：
  - `obj`：要返回其可枚举属性的对象。

- **返回值**：
  返回一个由给定对象的所有可枚举属性组成的字符串数组。

### 示例代码

#### 1. 基本使用
```javascript
const person = {
    name: 'John',
    age: 30,
    city: 'New York'
};

const keys = Object.keys(person);
console.log(keys); // 输出: ['name', 'age', 'city']
```

#### 2. 处理数组
```javascript
const arr = ['a', 'b', 'c'];
const arrKeys = Object.keys(arr);
console.log(arrKeys); // 输出: ['0', '1', '2']
```
在处理数组时，`Object.keys()` 返回的是数组元素的索引，这些索引会被转换为字符串类型。

#### 3. 处理类数组对象
```javascript
const arrayLike = {
    0: 'apple',
    1: 'banana',
    2: 'cherry',
    length: 3
};
const arrayLikeKeys = Object.keys(arrayLike);
console.log(arrayLikeKeys); // 输出: ['0', '1', '2', 'length']
```

#### 4. 原型链上的属性不会被包含
```javascript
function Person(name) {
    this.name = name;
}
Person.prototype.age = 30;

const p = new Person('Alice');
const pKeys = Object.keys(p);
console.log(pKeys); // 输出: ['name']
```
这里 `age` 是原型链上的属性，`Object.keys()` 不会将其包含在返回的数组中。

### 应用场景
- **遍历对象属性**：可以使用 `Object.keys()` 结合 `forEach()` 或 `for...of` 循环来遍历对象的属性。
```javascript
const student = {
    name: 'Tom',
    grade: 'A',
    score: 90
};

Object.keys(student).forEach(key => {
    console.log(`${key}: ${student[key]}`);
});
```
- **检查对象是否为空**：可以通过判断 `Object.keys()` 返回数组的长度是否为 0 来确定对象是否为空。
```javascript
const emptyObj = {};
if (Object.keys(emptyObj).length === 0) {
    console.log('对象为空');
}
```