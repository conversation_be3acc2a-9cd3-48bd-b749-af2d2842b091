---
aliases: [事件处理顺序, EventHandler]
tags: [JavaScript, 事件处理, 事件处理顺序, 事件处理程序, 事件绑定, 事件监听, 事件处理函数]
---


### 事件传播阶段与执行顺序
- **捕获阶段**：当事件发生时，首先会从最外层的文档对象开始，向目标元素进行捕获传播。在这个过程中，如果在沿途的元素上绑定了在捕获阶段触发的事件处理程序，那么这些处理程序会按照从外到内的顺序依次执行。例如，在`document`、`html`、`body`等元素到目标元素的路径上，若有元素设置了`addEventListener`且第三个参数为`true`（表示在捕获阶段触发），则会先执行这些元素上的事件处理程序。
- **目标阶段**：当事件到达目标元素后，会执行目标元素上的事件处理程序，无论这个事件处理程序是在捕获阶段还是冒泡阶段绑定的，都会在目标阶段执行。如果目标元素上有多个事件处理程序，它们会按照绑定的顺序依次执行。
- **冒泡阶段**：事件在目标元素上执行完后，会开始从目标元素向文档的根节点进行冒泡传播。在这个过程中，沿途元素上绑定的在冒泡阶段触发的事件处理程序会按照从内到外的顺序依次执行。例如，从目标元素开始，到它的父元素、祖父元素等，直到`document`对象，如果这些元素上有`addEventListener`且第三个参数为`false`（默认值，表示在冒泡阶段触发）或者使用`on`方式绑定的事件处理程序，都会在冒泡阶段执行。

[[事件传播机制]]]

### 同一元素上不同绑定方式的执行顺序
一般优先执行addEventListener绑定的，然后是on方式绑定的

- **`addEventListener`绑定的多个同类事件处理程序**：按照它们被添加的顺序执行。例如：
```javascript
const button = document.getElementById('myButton');
button.addEventListener('click', function() {
  console.log('第一个点击事件处理程序');
});
button.addEventListener('click', function() {
  console.log('第二个点击事件处理程序');
});
```
上述代码中，点击按钮时会先输出“第一个点击事件处理程序”，再输出“第二个点击事件处理程序”。
- **`addEventListener`与`on`方式绑定的同类事件处理程序**：通常`addEventListener`绑定的事件处理程序会先执行，但不同浏览器可能存在差异。例如：
```javascript
const button = document.getElementById('myButton');
button.addEventListener('click', function() {
  console.log('addEventListener绑定的点击事件处理程序');
});
button.onclick = function() {
  console.log('onclick绑定的点击事件处理程序');
};
```
在大多数情况下，会先输出“addEventListener绑定的点击事件处理程序”，再输出“onclick绑定的点击事件处理程序”。

| 注册方式                 | 描述                        | 示例用途                |
| -------------------- | ------------------------- | ------------------- |
| `addEventListener()` | 按照注册顺序调用事件处理程序。           | 支持多个事件处理程序，灵活性高。    |
| 对象属性（如`onclick`）     | 按照注册顺序调用事件处理程序，但会覆盖前面的赋值。 | 简单的事件处理，但不支持多个处理程序。 |
