跨源资源共享（CORS）是一种借助HTTP头部来实现的安全机制，它能让服务器指明哪些外部源

（包含域、协议、端口）有权访问其资源，以此突破浏览器同源策略的限制。下面为你详细介绍它的

核心机制和功能：

1\. 关键机制

◦ 同源策略的补充：浏览器为防止恶意攻击（像XSS攻击），默认会阻止跨源请求。CORS则提供

了一种服务器和浏览器协商的方式，在保证安全的基础上，让合法的跨源请求得以实现。

◦ 请求类型

◦ 简单请求：当请求使用GET、POST或HEAD方法，并且头部信息符合安全列表规定（比如

Content-Type只能是text/plain等特定类型）时，就属于简单请求。这种请求浏览器会直接发送，服务

器依据Access-Control-Allow-Origin响应头来决定是否允许该请求。

◦ 预检请求：如果请求涉及PUT、DELETE等方法，或者包含自定义头部，浏览器会先发送一个

OPTIONS请求，向服务器确认是否有权限进行实际请求。只有在服务器返回Access-Control-Allow-

Methods和Access-Control-Allow-Headers等相关头部信息，表明允许之后，浏览器才会发送实际请

求。

◦ 核心HTTP头部

◦ Origin：这个头部由浏览器自动添加，用于明确请求的来源。

◦ Access-Control-Allow-Origin：服务器通过该头部指定允许访问资源的来源。它可以是具体的某个

源，如https://example.com ，也可以使用通配符*表示允许所有来源访问。不过，当请求包含凭据

（如Cookie、HTTP认证信息）时，不能使用通配符。

◦ Access-Control-Expose-Headers：服务器通过这个头部，指定客户端可以访问的非简单响应头，

让客户端能获取更多响应信息。

2\. 安全考量

◦ 风险控制：CORS如果配置错误，比如不当使用通配符，可能会造成数据泄露。为了避免这种情

况，最好明确指定可信的来源，对于敏感接口，更要谨慎设置，避免开放跨源访问。

◦ 凭据管理：如果请求需要携带Cookie或HTTP认证信息，服务器需要设置Access-Control-Allow-

Credentials: true ，同时要严格限制允许的来源，以确保安全。

3\. 应用场景

◦ 跨子域API调用：常见的场景如前端部署在www.example.com ，后端API在api.example.com ，此

时就需要配置CORS，使得前端能够跨子域访问后端API。

◦ 第三方资源集成：当网站使用外部字体库或者视频平台API时，这些资源所在的服务端需要启用

CORS支持，这样网站才能顺利获取和使用这些资源。

CORS通过精确的头部协商机制，既保障了安全，又拓展了Web应用的功能边界，是构建现代分布式

Web服务必不可少的基础技术。
