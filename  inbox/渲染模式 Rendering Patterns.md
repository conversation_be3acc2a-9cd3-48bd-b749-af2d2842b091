我想在哪里以及如何渲染内容？”是应该在 Web 服务器、构建服务器、边缘还是直接在客户端渲染？是应该一次性渲染、部分渲染还是渐进式渲染？

[[岛屿架构 Islands Architecture]]
![[Pasted image 20250314115600.png]]

|                          | [[SSR]]                                              | [[SSR with Hydration]]                                                            | [[Progressive Streaming]]                                                              | [[Static Hydration]]                                                                | [[Static Generation]]                                                    | [[ISR]]                                                                                             | [[CSR]]                                                                                                |
| ------------------------ | ---------------------------------------------------- | --------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------- | ------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------ |
| HTML generated on        | Server                                               | Server                                                                            | Server                                                                                 | Server                                                                              | Build Server                                                             | Build Server                                                                                        | Client                                                                                                 |
| JavaScript for Hydration | No Hydration                                         | JS for all components to be loaded for hydration                                  | JS is streamed with HTML                                                               | JS is loaded progressively                                                          | Minimal JS                                                               | Minimal JS                                                                                          | No Hydration but JS for all components is required for rendering and interactivity                     |
| SPA Behaviour            | Not Possible                                         | Limited                                                                           | Limited                                                                                | Limited                                                                             | Not Possible                                                             | Not Possible                                                                                        | Extensive                                                                                              |
| Crawler Readability      | Full                                                 | Full                                                                              | Full                                                                                   | Full                                                                                | Full                                                                     | Full                                                                                                | Limited                                                                                                |
| Caching                  | Minimum                                              | Minimum                                                                           | Minimum                                                                                | Minimum                                                                             | Extensive                                                                | Extensive                                                                                           | Minimum                                                                                                |
| TTFB                     | High                                                 | High                                                                              | Low and consistent across page sizes                                                   | High                                                                                | Low                                                                      | Low                                                                                                 | Low                                                                                                    |
| TTI : FCP                | TTI = FCP                                            | TTI > FCP                                                                         | TTI > FCP                                                                              | TTI > FCP                                                                           | TTI = FCP                                                                | TTI = FCP                                                                                           | TTI >> FCP                                                                                             |
| Implemented Using        | Server side scripting languages like PHP             | React for Server, Nextjs                                                          | React for Server (React 16 onwards)                                                    | Full fledged React solution under development                                       | Nextjs                                                                   | Nextjs                                                                                              | CSR frameworks like React, Angular etc.                                                                |
| Suitable For             | Static content pages like news or encyclopedia pages | Mostly static pages with interactive components. E.g., comments section of a blog | Mostly static pages that can be streamed in chunks. E.g., search results listing pages | Interactive pages where activation of some components may be delayed. E.g., Chatbot | Static content that does not change often. About Us, Contact us websites | Huge quantities of static content that may change frequently. Blog listing or Product listing pages | Highly interactive apps where user experience is critical. E.g., Social media messaging and commenting |

### 网页关键指标

| 缩写   | 英文全称                     | 中文释义                           |
| ---- | ------------------------ | ------------------------------ |
| TTFB | Time To First Byte       | 首字节时间，即客户端接收到页面内容第一个字节所需的时间    |
| FCP  | First Contentful Paint   | 首次内容绘制时间，即浏览器在导航后渲染第一块内容所需的时间  |
| LCP  | Largest Contentful Paint | 最大内容绘制时间，即加载并渲染页面主要内容所需的时间     |
| TTI  | Time To Interactive      | 可交互时间，即从页面开始加载到能够快速响应用户输入的时间   |
| CLS  | Cumulative Layout Shift  | 累积布局偏移，用于衡量视觉稳定性，避免意外的布局偏移     |
| FID  | First Input Delay        | 首次输入延迟，即从用户与页面交互到事件处理程序能够运行的时间 |
![[渲染模式 Rendering Patterns 2025-03-14 13.00.00.excalidraw]]
[[静态渲染 Static Rendering]]
[[SSR]]



- **设计模式 (Design Patterns)**：解决软件设计中常见问题的可重用方案。文档中列举了命令模式、工厂模式等。
- **渲染模式 (Rendering Patterns)**：决定 Web 应用内容在何处以及如何生成和呈现给用户的策略。
- **客户端渲染 (Client Side Rendering, CSR)**：在用户的浏览器中执行 代码来渲染页面内容。
- **服务器端渲染 (Server Side Rendering, SSR)**：在服务器上生成完整的 HTML 页面，然后发送给客户端。
- **静态渲染 (Static Rendering)**：在构建时预先生成 HTML 页面，并在用户请求时直接提供。
- **混合渲染 ( Rendering)**：结合了客户端渲染和服务器端渲染的特点。
- **构建时 (Build Time)**：应用程序打包和部署的阶段。
- **请求时 (Request Time)**：用户向服务器发送请求并等待响应的阶段。
- **首屏加载时间 (Time To First Byte, TTFB)**：浏览器接收到服务器响应的第一个字节所花费的时间。
- **首次内容绘制 (First Contentful Paint, FCP)**：浏览器首次在屏幕上绘制任何内容的时间。
- **最大内容绘制 (Largest Contentful Paint, LCP)**：浏览器首次在屏幕上绘制最大可见内容元素的时间。
- **核心网页指标 (Core Web Vitals, CWV)**：Google 衡量用户体验的关键指标，包括 LCP、FID (First Input Delay) 和 CLS (Cumulative Layout Shift)。
- **开发者体验 (Developer Experience, DX)**：开发人员在构建和维护应用程序时的整体体验。
- **用户体验 (User Experience, UX)**：最终用户与应用程序交互时的整体体验。
- **骨架屏 (Skeleton Component)**：在实际内容加载之前显示的占位 UI，用于改善用户感知加载速度。
- **CDN (Content Delivery Network)**：分布在全球各地的服务器网络，用于更快地向用户提供静态资源。
- **边缘网络 (Edge Network)**：一种更靠近用户的分布式服务器基础设施。
- **水合 (Hydration)**：客户端 代码将静态 HTML 转换为可交互的动态 Web 应用的过程。
- **Bundle Splitting**：将 代码分割成更小的块，以便按需加载。
- **动态导入 (Dynamic Import)**：在运行时按需加载 模块。
- **Tree Shaking**：移除未使用的 代码，以减小 bundle 大小。
- **列表虚拟化 (List Virtualization)**：只渲染长列表中可见的部分，提高性能。
- **Islands** ：将静态 HTML 页面分解为小的、独立的交互式组件（“岛屿”）。
- **PRPL Pattern**：一种优化 Web 应用加载和交互的模式：Push (推送关键资源), Render (渲染初始路由), Pre-cache (预缓存剩余资源), (按需路由)。
- **API 路由 (API )**：服务器端处理 API 请求的端点。
- **Webhook**：当特定事件发生时，一个应用程序向另一个应用程序发送的自动 HTTP 请求。
- **无服务器函数 (Serverless Functions)**：在无服务器环境中运行的按需执行的代码单元。
- **冷启动 (Cold Boot)**：无服务器函数在一段时间不活动后首次被调用时启动所需的时间。
- **HTTP 流式传输 (HTTP Streaming)**：允许服务器在整个响应生成完成之前逐步发送数据给客户端。
- **React 服务端组件 (React Server Components)**：允许在服务器上渲染 React 组件，减少客户端 负载。
