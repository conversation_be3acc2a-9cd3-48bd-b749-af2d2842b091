{"main": {"id": "6089c7faf2d60ac7", "type": "split", "children": [{"id": "5681f19b9bc1c177", "type": "tabs", "children": [{"id": "0ce691ab923c87c5", "type": "leaf", "pinned": true, "state": {"type": "markdown", "state": {"file": "同源策略限制.md", "mode": "source", "source": false}, "pinned": true, "icon": "lucide-file", "title": "同源策略限制"}, "group": "98e2a1c70bad56f9"}]}, {"id": "a1f4700919bcf6fc", "type": "tabs", "children": [{"id": "05fe88048a61dff1", "type": "leaf", "state": {"type": "localgraph", "state": {"file": "用户代理样式表.md", "options": {"collapse-filter": false, "search": "", "localJumps": 3, "localBacklinks": true, "localForelinks": true, "localInterlinks": true, "showTags": false, "showAttachments": false, "hideUnresolved": true, "collapse-color-groups": false, "colorGroups": [], "collapse-display": true, "showArrow": false, "textFadeMultiplier": 0, "nodeSizeMultiplier": 1, "lineSizeMultiplier": 1, "collapse-forces": false, "centerStrength": 0.831578947368421, "repelStrength": 13.7894736842105, "linkStrength": 1, "linkDistance": 30, "scale": 2.8402010007808856, "close": true}}, "icon": "lucide-git-fork", "title": "Graph view"}}, {"id": "82b00671390c5eee", "type": "leaf", "state": {"type": "canvas", "state": {"file": "Untitled.canvas", "viewState": {"x": 859.9336156317934, "y": 32.84986285596864, "zoom": -1.1660636082162106}}, "icon": "lucide-layout-dashboard", "title": "Untitled"}}, {"id": "0b9bbf48be0692c7", "type": "leaf", "pinned": true, "state": {"type": "excalidraw", "state": {"file": "Note/JS/Drawing 2025-03-01 14.47.44.excalidraw.md"}, "pinned": true, "icon": "lucide-file", "title": "Plugin no longer active"}}, {"id": "7a8e451ed75e6d6a", "type": "leaf", "state": {"type": "excalidraw", "state": {"file": "Note/JS/Drawing 2025-03-01 14.47.44.excalidraw.md"}, "icon": "lucide-file", "title": "Plugin no longer active"}}, {"id": "dc9706c7104e3b5d", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Note/JS/web 组件.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "web 组件"}}, {"id": "0d780cedc03e0db4", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Gradient Descent 梯度下降.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "icon": "lucide-file", "title": "Gradient Descent 梯度下降"}}, {"id": "950fa309eb43a782", "type": "leaf", "state": {"type": "markdown", "state": {"file": "HTTPS.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "HTTPS"}}], "currentTab": 5}, {"id": "a050dc3c38e0cd5f", "type": "tabs", "children": [{"id": "338b4561a07b8141", "type": "leaf", "state": {"type": "localgraph", "state": {"file": "用户代理样式表.md", "options": {"collapse-filter": false, "search": "", "localJumps": 3, "localBacklinks": true, "localForelinks": true, "localInterlinks": false, "showTags": false, "showAttachments": false, "hideUnresolved": false, "collapse-color-groups": true, "colorGroups": [], "collapse-display": true, "showArrow": false, "textFadeMultiplier": 0, "nodeSizeMultiplier": 1, "lineSizeMultiplier": 1, "collapse-forces": true, "centerStrength": 0.518713248970312, "repelStrength": 10, "linkStrength": 1, "linkDistance": 250, "scale": 2.204932988871646, "close": true}}, "icon": "lucide-git-fork", "title": "Graph view"}}]}, {"id": "49394ae81c3304b8", "type": "tabs", "children": [{"id": "28151094604dc83a", "type": "leaf", "pinned": true, "state": {"type": "markdown", "state": {"file": "同源策略限制.md", "mode": "source", "source": false}, "pinned": true, "icon": "lucide-file", "title": "同源策略限制"}, "group": "98e2a1c70bad56f9"}]}], "direction": "vertical"}, "left": {"id": "a9c1135f8b50fd5e", "type": "mobile-drawer", "children": [{"id": "17c7ab63bf6728ae", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "byModifiedTime", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "16599c57bc8fc2b8", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "96b6f9576eb0d20d", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "3030a0a680bc82de", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}, {"id": "ec8afcf133d7a554", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}, "icon": "lucide-archive", "title": "All properties"}}], "currentTab": 0}, "right": {"id": "fd28a007cb6ea322", "type": "mobile-drawer", "children": [{"id": "63864c11c92a4490", "type": "leaf", "state": {"type": "backlink", "state": {"file": "用户代理样式表.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": true, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks"}}, {"id": "4c150c48dc16032b", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "如何绕过同源策略的限制.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links"}}, {"id": "2233520d5ac665b3", "type": "leaf", "state": {"type": "outline", "state": {"file": "如何绕过同源策略的限制.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline"}}], "currentTab": 0}, "left-ribbon": {"hiddenItems": {"markdown-importer:Open format converter": false, "random-note:Open random note": false, "switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "0d780cedc03e0db4", "lastOpenFiles": ["动态导入 Dynamic Import.md", "2025-03-02.md", "Untitled.md", "Note/JS/web 组件.md", "Note/JS/Drawing 2025-03-01 14.47.44.excalidraw.md", "Note/JS/iframe.md", "用户代理样式表.md", "Note/JS/GetComputedStyle Basic.md", "Note/JS/Computed Style.md", "Note/JS/CSSStyleDeclaration.md", "Note/JS/ShadowDOM.md", "XSS攻击.md", "HTTPS.md", "Untitled.canvas", "Note/JS/element.style.md", "Note/JS/操作 CSS.md", "Note/JS/操作 DOM.md", "Note/JS/DOM.md", "Note/JS/未命名.canvas", "同源策略限制.md", "为什么不受同源限制？.md", "HTTP头部.md", "CORS.md", " inbox/多信息文本.txt", " inbox", "和豆包的对话_0301.txt", "JSONP和CORS的区别.md", "JSONP.md", "如何绕过同源策略的限制.md", "WebSocket.md", "WebSocket和WebRTC的区别.md"]}