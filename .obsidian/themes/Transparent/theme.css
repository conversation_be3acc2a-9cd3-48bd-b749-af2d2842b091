/* 
This code was created by <PERSON><PERSON><PERSON><PERSON>.
For more details, visit:
https://github.com/Oczko24/Obsidian-transparent

Your feedback and suggestions are greatly appreciated! 
I’m working to make this as polished and functional as possible. Thanks for your support!
*/


/* vars */
:root {
  --tr: Transparent;
  --set-blur: 80px;
  --kolorboku: #ffffff;
  /* there is background */
  --background-image-base64: url("data:image/jpg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAcHBwcIBwgJCQgMDAsMDBEQDg4QERoSFBIUEhonGB0YGB0YJyMqIiAiKiM+MSsrMT5IPDk8SFdOTldtaG2Pj8ABBwcHBwgHCAkJCAwMCwwMERAODhARGhIUEhQSGicYHRgYHRgnIyoiICIqIz4xKysxPkg8OTxIV05OV21obY+PwP/CABEIAPUBcAMBIgACEQEDEQH/xAAbAAADAQEBAQEAAAAAAAAAAAAEBQYDAgEAB//aAAgBAQAAAAD8FYfE84vs1pjed4eZo/0XYKX7P2lf0ZNVpJqkAGnv1GbmLNPrxNkNAyVaP3nUvRpmHoxDUtbr8/8A0RtCLhqI+WcGfFzDl6oYTadVfYKxQA6LhsBJgakszlvwj0lxFkNcrVWPLEONHUexLLiW1C0mEtyvVfPFclru4x0j/utnJCvJifr6hY5U8wxXj6F+0cOU1Xr2LUTRPRmy+J4OZHOqxtJt/meD+VPcCCFNhC8ZxrLUDE9aQiwpG3oqQH3v7NeU+GC6OTGAZOg3M1X+N5Zb74/EGcCCoGjkp/Hbq7dRUr5ApCcqfD2CiVo1pyrxeL60JlbSsD48nFz7yYdu1bNotwI0ndSGzKdH4Uu1pfQy/vta7WOJsrk9xmmZSx591+clbJndJjYqdMOvpfV7hiGXko0M+44RCUoJSpRo5F+6pZ2mTMVGptDKRx95ySwCXGaofXSgKxFiL1D3kGw37x2i388ITpVzDQ1XQo9t1trORFJUSbVU1YC/AMQvAP0X78otk49VrD8EbO1onqJn67nsWg55gfqhpgotsvE1PImlq8qxHsW47/Mraa5av5tykSUmsm64KnbiNYj004ywcrQ8qIA3ECmmE9YMA9kiqD0uboV4+VZNYmMMxkghBkrQpmWJTMJhyKQoP4CclmAz1goJyFXF3ErzYyPT1lLzlTwFoDp3L+NkrlQYVoUbOUzORYyLrytVe4pyC9U5t8hibyUtQpu+PXiR+zkSdW51KXsAr0vN2I23m/lL1ZSoPePWYXLgVmSksJjZrPWS1lLImx4h4ynN9Hgfd15jlYQoGCHZk5YfOdJtRUEFCU676fweY755JMsnKTjjfb49JTx1M92lyA33rGTZz7j74bKhTrN75AaHPq6KiUKyV/wLJATnTlJWHcfn+j9tvzx5O2oyjj7uin7P8+x6+UVLSab6BKm7JOBg2A7xezxZzAGbdcfVs1QTTMSqihCx2mqbrcAh25nCppjwG4+qJJb9XxLjZSYHwBUpqcvsKe5quGCFz3NGAcr8qAgyZoImslqFaYUqb9pBG4DvJdi+yIy7PYAAIK59gunbZhMp230+jpjvmk/HUqxyhYsTZii+mKhQ/lcenw2prMkAsp3J5rB6lD8nHa80ouMz8a0hn6o/7cj2cZtQOTd1fQb87XzaJp01Emp4o7rXw2bNegP/AM5KYvo1lNWqha3GbADLrSVIP0mmSPtw9AUMfeRDtymXO0l+qyIzIOPYd9DkupWhcLp8j1C1BYi4G9JT1FrrPW01XDbqtJ9zRdUEfwkxoNzkOITtwgFXl/Ky1ietVAGFoaBDovrNGgmZC7z3tec+P68nH+ww265KLcmK4ZpS6rUmY9X3+enFCFzhnrf6qVrO/qZMn0ac2HyQAtJ3s3AmhnjcQMFmSJk+0QqKkZCCWL14+UOj/ZVrRyZQTg9Znz+grJxyC52A3Vpn2xiIchctNFeJ2Rsk1y2zXh2C6c6MZauOFJE9Tl1qDttH054qpYmL0Ix07zQulR6ZopZeBjMQAbEkQR5m0n0WJXNLVjPPzv08775lHTT8cV7XS32Kh6hPSC8cnPB8tmi5wt3aM3/5tFN32tPAMXAbUHGdDfKbGdAOoZ1h2gvZRfsP2nYrzP/EABoBAAMBAQEBAAAAAAAAAAAAAAIDBAEABQb/2gAIAQIQAAAA4tBuPT5XqnkqbZSokN8U/qat2IrQis5KEVQ2edXPVKigOx7ki106qvPD0HYhXAZJbO1pnFKzbpsa4MetDVDxlqPS87t54JPi0UnSkH8OmtW0hhxt5ydEtPTHsBhys3jmYtKsow2ZWUqdp0DE1LmoFQ2p4wOmHqZWNXjHzYbJ6MzhUSKSTrdLkqocCwpSp8wTY0j3aEswLh5LUCNEs9ZDgODMqRrFt4l9rPOwxY3zusFdGxeih7obs40SVIo2bk1LPiwtE1u3mSOQuo/Omp5br1zG3jMQFa1+R9Rnk+pNNewGoLpq2F3/xAAYAQEBAQEBAAAAAAAAAAAAAAACAQMABP/aAAgBAxAAAADODh2Wvo8fTR46w7cN9cs1lVilnzy2w1Hoy1GzklxKhPomW78oWjSQp1yBO+tGGkE5FuLqB2nn2tczRQ7n0dy7unXs3c9h0Udskk6k6TuOhrXTmDM9HcpSiqxyQqlIfW5l8JpOil5cgjyHSRInleqmpfGyXNDliozau1WZ7uVPZ3o8r3Lu3pkG6x6jtcGDrlVGxRy5429OkU0M7mVc5qgoc6uKnWsu6eTtRzzN5GvPun//xAAnEAADAAIDAAIDAAIDAQEAAAABAgMEEgARExQiBSEjJDEVMjNCQf/aAAgBAQABCAFG1/1df2DyfYyAeToqyqOROl2Tk+0pPhkla2VhKAR3mhLjUA/5E04813rFh0WANSfTso6HLmeWjonbKf8Ap1ixpkYE0m+Hj1WgaFh/tkoGvsUJ+UnePP8Ap29ZgUmvP9Fu8bybFK0rANVeTQWnuaPjkM0qwAovImbU142Bkz76sxW7DkQzuq8ZVy8YFrTXHbHnAFp5o1/ItXH8RzG9lWfbbPseJNe3KFWDWJpQghhBB8ghoTDvUEYEwj60xpzTcj+bftm7C8/YqvD+vUcP2SVCVBKqJV/T8gd1qvMX9se6H+wfmUh+Ypa0ZTMxynW51R9chWFPj5FD6nHdSqnEnjgR5lZRnBNI96OU+qdHk26oCZMfRu9Q/upMu5KVHkmveQC2N6L+Nc9FGyoSCtWUWFmZWB7qCoKierUwJmqLawHnlNxasCqrkjVVYqC1f1kTOSFDB30rF6N6AhDYQaSKw/m55Ruwo5LdQnWhjb+cQGQghEqKpQglW52eAjvnXZbvzbyGs8ZnQUI3Rf3jJ26AzJD8x+xejKha+NRTSOLbHOhctRGUg+p4GKZjONmrOTHAVnHbZgvQnuGPBCd33ldlYEdM/Mcs2p5OrfMbYNAQuRj0LUYE/wB8bvmNZpxdh9sjAZln9X5u3lGi7vWCcUyt5F+iEyRwMSshwTw9Z+g/GGF/SW9fR+Y/7yjw2wya96Yhla8QN0YnTVJuRTcDUjuZJxrjzVuVav2LByfTacC/bOJFD+yl1KpxaNqVMVp9XK/cuvIsfVOHFs5qyn1lNQz9zrPquO7B1T4eRIhqTabVdxkv62GmARKN7cN32PUrv8xAbOy0J5kK9MhOhKm7KuK1JANxBR7miZwInSs4/wA6PyHY9pthgNi5ANrHyiqqaf2IRS+KF4qnpZ8SxXHRlUOb1QY4OzFi3clqozmrNtzWisGPsY5H1y6Gq/XHm43D388a05q5SgkspGE4BaXkJR65gqr97ZzLOCgJM7gAJZdZktr9D8W16UNqQKU7B86J3QVK32KYVHopNJiGVDmYx2oD3R1kTPI6pj8yrkUgj3THg38a4PZ3SRWuI8lKsChGJRFk7lqdt9jL/HWxX90FOVb79tjpqZaXLUFFPgVUsyUHqG5+L19KBp4mFbpuZ6zfHnsnq4ExaoWhqtVRduixWk35p0ag49jKVByqFDqe+pz4mLTJY0CTWUTTl8oPAAmAv5+nwwKCSTTQKxyC743RWc5ogOWs2KyRmKlH4SukeTEmyeqZWVtJfIE9PtDFm0hYpjY9AlG6wu2bmU2r0QZTHsHkWTyqnNQT1y6rRUrwLfJxZ9TsPduQmFpZOUBEfRsJ+jVQ42canJ2xtDjtM4z8oQ2kxg1x+vDlsSsmFDAewflIrOg6C1G7NG02lPqUUjAqO8aQahzaYrj+FmQ/joll27SgybBv5uTrKbcDYy+YocO18vQeE4468mkq4d24DPx6MqBAxyrWC5m6Xis5rXmOQLPJnns94hcaqrvzJyDcgt6axHJiropWkK117B+vXIVCm2zHCVVpZ8jDfzbn5UCYlWYjDIk6zpCkbdUof2o5Q74SPz8fkGcKrwt/XsKQ2U6gYt39Z8x8cPleZH4+tb7BvxZM1neuKuNAgBCywpOUzHKPePmtlUeZTsUpqEyK+veLcml1tlhVWaJarf4vWTj5Cba1168IZqMuDNTENSaLwGVZKts2TpPzYYRSU2tVsxSu1r5L/pgaH+i1sPMK9CzYPfJszln5lb+O3Mf0tYsuV1BAqzcliGy8Vsd22lA0nRFiTjgrxj3pYxwfx159kz88kKJ41a47qQpWQJyWVivUq/8AQKZISRkZB2p6I3fn5HE0SN2NWBbsY7sM2B5RxIt54wia+h/VNHPtb2dmWlAQi5RmpVeamWKSjvj/AHPLQn03UM6uyYz9N4TXjgKuMtCQjovJ3zlyfM0wQhZiW+R643IYVGAlz7JlKvKmihGZqSqNrPle5ROVxZeSpxse8W+M1JN5L2sMuiOr/wDGZRHS0hiAefDIY8y4pVnBHMYguF5crqPbsKhpLDalFovA7vidcxa160kMTIDhhkU8Kpp+R7e6ryFXUKqVkPSNlO32R8doI+PtkYWC8afHWZSGQrH/AHz01pjtwHHXasb5Y+E+uITkY6JwZePOmpnYqaHmeX9XXgqX/Dq3Fo7NPUsfS3HmPWTLTIPxF2zye5avbfOWvAzUJ6+LlSGT1GpS/pw6+k6F1DjIbg+84cp+PyQ7ry0KxvN29pCQDh8UB3UlKhaNfKpI+bMfkjuMk/qS5snnbG58fqQDwVg+wt0JlRhf+hDY2O8nr4nqQVmrQxis0xaP3Fmzz3bngbTmtKV0jHWRAhkyBAektM2RiUQDMIxGYwJpHrlAVoVOHBMsqtslcYq4jWbymwb8Sf06cykb5LOs6v8AFLcXa2lHEyMXInwUHcjwNs9SQg9weWBkkpvaW8++HXbH0ysvHnjKOZeSHxm1/wD0grNytZqtCxKkbrJSDejZMG5SlMbCbf7d6CZu2hWouTtMSzbwTlKYsgJUys0nXWQXI/XM9vUSosqa5CbdNqzcimP6jZcauxtDIJeR5k99Ky4D7sZnISvv3SlreqAZcVcvMbn4+wE7tq65dS9V4pFMUBsIpsV5ftirhRMymy0iUSpP5QyaOKw/Hg/ZuGgSH9Jtpi5HeOxbE15mUHX622mqmdunowx5qz/vIontQczKeS4wUY2xky/NT1ed87wjj28mdG2PMWlmxWVH9KMdNj416gm3lQxMgl1ZgLVfuGCIntaDKh3w1pVxrBFpoVzMNXPIG876zqlVA0I0H1xQjYqCkCjW++VkFXOuMd6Mhn9mM2lYjLDHJmrXKnI66BW9OvjUGUdZmYhZoRVheBy8h3THE5TLUxW1uhU1mTBWyO5NkTIoGjiHmWZ9zQw/lOK8vP3pNjlKs8cR5OjIJ6+DXABvNY5iBaxbZu9x7TJt9aOGymV0hzCyNZcf8fRLv5/laNPFlA42N/EUbCoIpWDFWx1Zm82Ut0xKwCKWhT7c00vsFz6IjniUx382f8djzRqsPZHyKK+Qs3VnNP3AdYz0IChtkm78N5gqOFxt3zII9C3I01ovDQrZStj1Vhw1cWVgF/pUPSr6+JR1qYgZLDyFAbJJZsMol5Fkgzek+Ws+ybZVC7FirnyXmXTacnWveQEedO2mj8tMCrTa03EkLQI+I3WbotG4zRNFfkfL164xUZOhuNPrydP8PksjKVR3nP7NrzHrLx7pA46ru/5Ok+9Z+lTMGY/H37GxxpRg/TCzDpSpf6LLBhESemb8z0Xz6/m3VMhWaSsITO8mhN0l9hhO8n1eim/LpObMqsFbGk/D332LqxupfvdhwDpxtap7kxyxP0Ytjk6pvjWU4dENUOnfMKcHXqw8xtNLzK365THHxZ2VD2ePscUd2tpaRXMoEwhrkUHnjNyreqQPMFz5ZEzkvs3DT/FA5Bh/9Wb/ACtyULWqvDMwlEtivPw83sMW2Q/nQMuq8DtIedM3z9G6/HKFx+Z5uIHi5DJRa8h8S2txeMWD2VF/x8dXyreKdCFQ9LLxlZmmCssWRm0833R7ryNVWD90n3P1HQY7EwGk0akytOi1GcDn6+vHKs47YqOurK7AOEU6owwIlg45eIW0wrf4mQeoHXKccye5Odseu0zLiBWY9Toxx3jzLZ3uzMFR8NJnQrOpYMPjJ0CY0cis1bI1BQJMz4mnZm1ldZksQG1qM30BSfJS3TkcKLuRzLilc+eoxLZAn3TDVbLvIp8hgQsGUJQzOLXXmNhpjqyhWs3spx5wfFDUuVf3C4y6ujtFw5pzFr0+0RRXyqqwMemWKvCs9eIsF+zENuhU4pble13FHKk/r/8AOiF/f7mQuOhMhPHaU2fGgAHhgNJ6ZAp+QJrj4zc30rhvzw+QajlMS+JtuIUa9FXYLQa1xip+SMq5V4Nx8elbPymJ5iiqi4CqKUrlYuSDy3SuG5IBqqpWa0kVNQIjF0pmFpCdMWoTIeT2zoByi5X49Gjj6jIt5oUe+9v64NFaq7WefzEQ5Mnd+i97pjRVp5HpioplRWw7gzsxCqszrj98x3/orcondSZjv5TlRYq4ImXQgjJFaFacxUqqm3FyqBexky9oinJYhawmxaLjnoy0OuPHJvjVEzhVZuY/oiZEqYnYsxWrMcJl59u1DZVqKVRcuzWwpkyZPjI7ZKoG7R/tGoNFPiBxh5yDtKg/uONGrA6lTJGWcJx9pzycfCxfsZTYuMjW9GbUcyd075CXlrl0ySnx26w8o/G1bDo852QsP+1ORIR2DEF6prVFEDThY0WfLnxM1RSq+oosUZAeaDRQITHoFZbEVyOJ/wCpJAOxPJxGimmFf45PefibFmjt8c+QSs6/yeOHZdyju+53L7PtzCjSWK9Ww4UyK9tl4ZSFWQA+iHmQQRXh76A5iL3OdeZeVa0V9HqPhHkrfRjy/U5U6mUYtwZCNJi346nWZ6U/J3oumv2OOeKmzLRoZGrt3Jm9NgA5+3DAWjJTciOMsQalZ68lZFSvIePbuyJLyoxYf0WgyUMy7NcXvLZMNG+22a/2mvJ02CqTjnqbljqg1HS0U8X60bRU/HtJy9J+ZJHx17PFzJSRYoMwK3XL4NDPdJlttUGVWFGAyZLk61hD8a1S/pSKY+M2v/IfrU4uf9DNuvLKrTlZ6tkUOKFZPJZ49Eh/i0s9LstT35r3UlE8jlMWE+4fsBuTO0LHn1FHAf8AdE4FZltPgFFCKcfDyLu3WJhQNylseeMjFVy8p2yP1n1v6FqUH0BMfsSrJQ9W6n2A/MbByH825l9+ZZpXZcHy4W6lJhmjq3MWQrTVi+GwEwzjoclGtkXWksjHSodVJJUuAS44YFtdK9dlVVi7gFb2m6xmmLU1fjYx/m/PSyHrj5G0WZjkRYHrMrj0dvLFnVjsKsjrzCamVAw5TrEhUhcqtmVaMcWpf1fGn1PrJ9FI9Eh8jHWfIgxs0mh29ulpQjdSWbzTvApRHa3KCr5P2yMpVRBN8ihyWJ+U7WbhJLEjJdnmqEuwPXFCqA3B0tKKXB83ScMp0JHLULSTgb9MRlYzeMqJV9pyVUuVWgM1LxRxkof/AE5+Mf8Am8+fkHKLBUFO6nna9k8+LEbMUx5LvQKslXySuOqzcxUiob5CUhLsTFPToLlwyqowWf4moDUpbEoq7L+NhSS9s8w1UmRbF31U/BmKinhpk0W03EiiDGzo+TPH8naDoWUUmssfXIOMyzpwZOPRwlMvGmrOq60ITaC0hM2nG9qjnUpXXVGx8wXShgcY5Jp/9ctlN54yBOl+zTnP5CjjBpX/AGh/arzDWdRROVx5kFuUoA9usMtZEQE6ztip8ah3cyAmECTRrNaSQwHjt32WPGn/ANhwn6vs+KSmy6FHVGktN/2+RkI/IXlX91d8vrWUJKneXQNiBGkgxknqeHFlr3i49cpbutBbD9u+ZuOk5LlSgY2mVf4/vCceZIWVJApgZCDrmXN0HTVZToFE3qkG5XFy6t0gnkika1yPjxP1sMRcMecHIJYZN3a/bi7Rr6cySr45s/65kdeispL/AK2Fv8hW5lzC2Zuf9etZURZ3YY9v42HCwbY8xaoqsvDlRK/o+/k/PQhY9x9vvRfLJ8a0dCFRkFe3EaCfm9Spln0Mya2fcaqZxQb8TLgAUoc6F5+Jq3lcdZDLDF8zO1kQ5CjLX47bJm1LzVL5GJ/9ZZn2+uLlZNEdQ2HkzSXnXDpp1zKCJkSmpNbL50rCvo54uDkeCVbGsZYWrClG80pazL3Jr+iMyMu3UAfxs4fKX2r8Te9Uf7O68yWq+InFHZCmuKK48vJMe1lFlVXWytyejzAaXZoZm8WiJy5iW0uOrrJKtrjoGoqkmaK6SlUk9coxdXHApkicyskt9eI5Wx7orIB23a1PBOr99KKzRfTFaddpnLwPilWljiooPOsPR5qaYrnanJzpRFiJYebV6SQYN8Y29Jq2RAq/9uvtMwm06rh2MrtTkrjIy282F6UWpjl/mZQ1l3W4yC3rfKfpIZl/v7nJFdoF0Vdhy2G81koq14QijY/nro1kboc+PlNNdXzLY2P8ZViempQRG6x4vj8pFmrZOQlFpPpJEMZspL8o70QhpP5uCMzuzzovmwu3EYy9LccyOrJM6X/eRUvQU5b+uPFuUX978YjVTx+z03MAwxyrXze8ivcoypjWSjr5XRmX5J3PaVkskK5WVNqQbmTScIFInKL4sGbLq9MX9SaelCMS2P5PtSWJSK8diET1xLYSwYJk2xMpqzEb2m31a4e2UeCqymE4l0r5Y9x+MwK2XvNwcW8to4aGwrv+Tyo92HJwk1aKpXIjjUJSlXwUbiY/y3oHycXwk25UtPab0ytuO2Yg0XG6ak+RFMilEWm5KowA/wB8alSCsnx8ia0ekW7JXlABJm5H0E/YM37XqdFabJxemDjnX/qD39eJjNoveNji/R5rjCgXhgAVolvTzTmHenkzDKqz1AambaVlIycoWghJ++IrGbatROQoEfn4+hZ2VshXaiqJmmPeUUzsULtkxl0J5G8bfCionkXlRvY42WqY7tVcjDFOxb37+VHEKGnYy6SYPrjqxoh5EqByjir+dMy+eoJprT7bY9yuXtzNX4+PqJZX84qJ0kFCclXysSudO3pu+OjMunMGqd0L3z5gXSKlRQE0P1K8kT59mfe/ZVigeZ7/AHwH/fJr2GDQ1WRJlQvJxydi2NN6vWWSUWGQ9Qik/i7Fcg7bKtGPDDAZv658knOXi5cQXqdG9wvIJMgosN5WAXGzG9O3OdDvU5d4rPXG/GTS1nFK5hd1Vr5L1KhvjW+O/PFXiU5HMPusOZjhKI08mpNqnnqwLhYejyq/LUf1Ctkb/wBhR+vafID+y8/IUY01OH9sexadGCORkletlxc0M2Ir5DN8hRxWo9qq2ttvMTw1xoClbhHFZrsVivC7friNv2vLd7ngnXYAKSpYNmBEA1xv/RRwqaO0eSwfEsXzWmMYaYra1HMhGmjCiLPIxYnmUagpJ0YLB2E0peq8/wDOsVOcQlJ649+rrzIiiyRlxaS7/pKWLPt4PgvTQ8thZCs7ci6ic6NhVn6ZG0o461SlMnAllOtZwwnkoAwvxiq9q8zVjDGUSpViGHMgiuOm2GqnENVGJGqe0cuIlRVbGvabOsKmCqrrZAOgGfoqRtluO+LQbq/JvoWfmTk+tehiEvRUGb8e36n5oh+8ml/6TP0JbhyGVR3l9NOVeWoW74jgMDxKrNz50lXveRZvsDN9WHGb6d8g7ea6vY1o5fYSgFqmX6LGSPQsXXlWN2cCdCLIeDAvWrPwYM1fVo49E9Kzb/k5IGbCznfVaN450vZ4+gyrNPMjGs5jkapjR6bJs1cmaSgn5FJs5z8idbIqnuVUYt0Iz6gzJhN1hXGtFOZv6oxFTK1dcpmsGDTSto/Uq22p92xvLRivzG6FT4U1+iNz5GPNQITsysWHpd/0cfxRQ1blQf0/71CmdIpqbKxbvnX++T7VNTCFh92zMVP2VxsMh9ntDG/9GTqerqmTJ2JpnIFy9SO0VG4Kts/EsTdGKSJozcerRSLAfD62pPKwpUCrNmSzkZKz+R/ExyZMyHFyq6FVhE/JGZUZEqTbkunorKbE39HzGD5jlyBsvC4GKo5QdYiA4dFWnZyqHeS8xciokm2Y6fI7VLtCjlTjigJFjNcqitl4Oo95w77KgwR0Y8rKkxqUH7/XZOx52WYlu1og2TKWSdLTLZ1kS7MOiMPGXJoUPksiQmPZoN3zK+gNALP2BzC7ISh3Pqy8jHfIBJ/D42QyFvybGlSvFPTDmDH5GQic+OJvljkJ7ouzY06tVXz8b4d9Fwr2/Y5gwORCq8zsYYVTJMhwuI3MPQRUnCUPkURrBse90WtGAU8xOqpozyA/fMTEV8K78ouriZxv07Dl3/evMDdmhpmIUvrwMCJcK6436sNijlciqQx15iL3Z+K3V3XmR+z+8WW02PFQNPjfXUjHRGb7U/2DwIdXPP/EADoQAAEDAgUCBAMHBAICAwEAAAEAAhESIQMiMUFRYXEygZGxQqHBBBNSYtHh8BAjcvEzgkOSU6Kywv/aAAgBAQAJPwHa4RFxJ6KI4QkW3Vw4EDzV4N/JGlszPAQggERr5oyXiI9kYBaGFXIa2kg8fRODeOl0ZPKtNpTG1Bt5OknjkIQQnBxD6o3jzRDXYbpaW3sPhKE5XfIoxx1W9vlCia9NNURRiPNKsZNkRLdOk8J7ZD7+SjWq/RTUPESZkI2m/uszHO8MdExoa50zsP0WWq1O0GyBgcLGa2XQ7myfME6nU6KBn0Hoiac3TVUDCO+szsnwD842TocYKymNO/ZfE4O+XVSAWWPG+qkg8WlPBduqq5twjPZDVbQtCVq2x7bIyakJduenC+JpmTEK1x81z6QjlxWi/eyeSKocYj+lsyoc+kt6272sh8V06h5qy8905uY0vaWwULiWuHSVBdtDhbutrhD4qgOFTLH1t4hCSXdd0S5pdSRx2WUvitumbdTAEg9llZQf/Y7ITVT8k4AWsbJ/9y8g9dh1X2il0w0AVFGstbDSvjNQHUhO1cYGvmjN07QVnsVSaKSBtpGynI6BbY/6RuNSdFJe4gz0WwWJQSyD0Bsjfe/6I1u1v0Rph2vXhSYdqjouEYE8I5TBiPJZSJEbmOFEO80YDrf0ZVTNPmvEw1ArF+7HiLdnH6FNh8psXKMMZ807wuI8PKwmVideP3TbYZmBwV9qcHvF4bZOmDEpsj26J0F3F1mN6hpI/ZYDRT4RpCMZquVNTXQXfMJti25PpBTdGhveEaY1RiH2KFeLVFvw6/RNzBlus/oU2fPgoRG/zX2aoz4rj2TqmRUAbObtmXaDvCFc7gLCeymzhpojDoEh147K7iZQObTyMLxT/PILFbDanNixVUgwA1FpbVe3yKdqEaG8wnAFMIJPhjqiTUw/JSA5lhyeFobjuFoCvHVLW/W6Dhngg7u1XhOYDSKtlSKhLW9Bum0tq8W3yVQF43UgQ1pH+KAhzcs7HlEglwv8k6WYvn6oEH9EfE0FMzkdvLqm6Cforiu5jwzyvibQ6NGxv5qCS0rLLT8leWbX6ynQGt07ymRA9FaXfwrWioeX7Jtw7xcRsm3cHRfVWgb3TZxAdRyN/RNbJFiLeH9FoXFP0Puhcs8+kppApzdOhTiWBtzzKdphtLu51hYVTnuveLdECAWZSXcpsgEJ7i6qL6IVEq1XwzZCJ1TnSwRI9ZunERNx0WIJGYMG8W8gm2B7xHdNEbiYyndEhgABcrRMX162R8FQv2lWEU/yVmaJBkaT3TAGmDczE7LFaCfGOybEu+avztEFNl7XQ0/5K55lOJpFI7m6cJGG0g+tkLOI/wDtv6pkhz9zE3i6pEknLwPdBsOiRq5vryue8LZrgfXVPJouSN45X2iK5zRa3ZRSJzTLfnutWQCqgC63ndC4aL+afo++gX4hFk003gzOqN9U5rW03P8ApOrMBrenVNE3zcwjBGVO+GptX7LEh8S2L+apDfvPRAUt8WIZGt9lUXfiJ13WEMOfCF+KpvN0IEaDdEtZ7dk4Qeq8TJncLHpcdmxYjdVPHhJqlZh8DiLtC0cxNJfyL/yVendS2nDkbymyMKKqdV0EOv5ItacJwKqvLQeZ5XxMNjBHdeXVHQi36prTAMJoBeyL+uyEYrG+MmRV+ifvmaL+YCfzVVeZCbcO12+aOHQMO8wSY3MINH93NFpkKHfdzZ3uFhtoaaesFYVJrMjkcp0E+phWF2yf51TZLMoJEE9TC1PXhQ94vdxj/wCu6wmMiPyshZiW5W0/O+ibFO8xZQXE5RwjALhMXnqpsLJ4J6WneE2pjzBEoBmG2LlHCcAOZK5XiEUlVveTeNIUtpymbeH3X4kJIa0weiY0sdp8RM/osGiHQJMfRMa1lus9VjUvc3wu/wDz6rCoa8UwmUlro9ETbXgIVAkSvQX6I/8AK3bkhFttajA01WIWzu3+bp9LBLXE/DCxZAMMLdb99UXmdctjH6ppqDhcdOeyu/VjuN1JwiCZI8PVfiBF9fRYeLFMmBOnfZD+04QW8zZMa0TIDT7rNXBLedossLE+73MXUFk6735WE6A6ztk8j8G8nhHNMVcf6UEtfaN5ATsPBcM15rv2T2OYHeIXqi/dYriJnmf9KugOAcdh0MIVOpsVhzDonhWgXMSiCHMG83FpKdmEFbvn+FSGkGU4GXWG/nCZceEmydIdxosWllLad4WNiyfjpt5p9tP5CE0g37d1YOdY9k+bC2qOYR5KqrxNJyuLvJaSL90b6uOswjtqnfJOpJaI2WJXiPcBSTEW5CBaWNMunKOiojfZ3l3T8SAG2pga6dgsO0mQNipLKSTtEotirzN9VhUOAykZau6bL5BOt+yuA6ByEz/G3zVMnLPVO/u4YsAObx3XifejbvZOZSJrLNp55RsYvF7XT3NdVV5JrQ4TfTRNa/7u4B04T3NIcfCPcpwaw2AsY7/qvtRGbdvtCeBWQ7unNLmk2iHJvAzZR6p7bCInZOe2cs1Q0wsUvaHaCwKdlboov4SsSYFh81imxuOJWHWK9zpKNmPJLeQn0Ck2DZnumVQ2Z5VvxT7JsNtZMsLg9NYRIaZrI2cE+SNTGllcPMPaRAQ+7xQ2Q0EuBjUd0RBFQOgPT+gOXbsVd251udk4TbKLR1C1abTz1hAPeG6m87wnQD9ViyRgndc0+ipJp37ronZ6hl01TRuPmrAH0P7qAHQddO6w4NTRXYEjhDJeG1fJHdZg93ojmDjDvym6flY0tMeK6bU52ZkHKU11VOYjQE/sgX/d3Y5pLfVMaXtnPVURO6xqcRzBYyfRUkW6iP1TXYTW3+9dp5JhxQbxBzTyvssBx+HWdRqg5r6iD5JsgXA57LQJshzfZYoIY1zTP5rpxIINu/CLTUf+11UJNjOyM3tPTdB0xlExHUqdYPWCpGcf6Vi4NaZsfOU6qLz1KF4i+88IhxaNONkLrEoDN+Z2QLQykRsgQwvdvqQqR8Q6ppgXPRC7BB+ibleP4FiANZiVQryIO3Hut4814HM9lqJJHcp9z4RrMXVyG38lUXS2LIWey+2i42TJc12kccQpFUztr3VxUIjZGAMMamFizdw1q8XPAREjDAseEKrXHIHKYaWjYeHomVgPtyfXZCwOjTx9F4Dq2ZRogW9ouvCMnputGuMwm2AEHTdYj3Yl5Rqw/iE6bbqSWP07rwENtwQF4rUnqE0XdePiRoIOmlPRDORWDpfhOLSGCDyr0ON9YlED+bp0usLXTbx7KSKWz7LKMPPiRrf908xiXB3stQy6NxZvfVNzGkP38ViqiC6Bf5leJmJI9F/5MJpWpFu4VhlRNTfOOvks8W1TQC3Nxcqk4TS4k6UzeCmVtNJoN6dpWDRLgam7tPdWIHqnyTTO2ndaN8lbwzsntbtGt01rXgimRujSXHX5Kp+JTIqFu6YQR8VXuAqi7fU+YVJfucUW8k4Mi3QnhqEuHmgwU+ISDc/zRMbBdH5mlYhDZLobvSvxKf8AjBn/ABsozDupzzEQL8rnunZbOadYUTVEKoCgbIGfvbbWddYgBDoo36lXbGo3KqrD6YF0bmfmjJ+8e09itaR8lYiWnVABtFE8TuvG558kXDDe3X8zdgr0PieevdWzeqykB1TuGi6sALO57rUvAHVWJbDr7hShmGGJVmEAH2TnH7z/AI3dOu6uW6z7hNMOt3VM6SNp9whckTNlRQ7tpOy/+TTrom3LQ6YuSDCe1wmppE3lOdLn+JXJGfaeqeWPLraeDX5pshp1q57alMdhluVsn4RzOqDQ8HxC0hDMDZ0TEo1DwzxP1RABbLROjlrKc7w6arlbEwAhBHyRnTRMhuI6xHTZWDCZE6poGT4R79lY2uNi3lHO41HhNm4rTZOyi2m6IzNajNL5UWPshmAqDeyfS0J1sXw/qqZa4tjcQYTI0q6IzofVaOaZtuE45emqa2aS109+qZEWQvVHlwE2llE1f422VMnNOkboENuBHvdCo889laYtM6JlbnhrS3e2ygYJxNax6LOWjwh2YItDSJDNN+FhUw7unuOpEaLDbQyQ2jYFQKX3Gs9fNNmptJ51WK4u0aOvmvsxFerdBAWM2kiqmfeUZbPEImQ5TX4T5bJuh4QH5nN33TjN9k71H6q4CaZLARedURSzDJAO88KmA4a6ieE4GknzWYUnLKt93pT6R2R1v27p9nGL7Rt/QRD7lRkAGlvmvigt6TsrUMNu6A/usLZ6hbt/gUFbH+FfhM++60yx2PHRGSLCU4Z70o1M3HPqnsa74XH2KcSTtxtC8AaBB3tvHC1446J1J35AK8LYhwOoUlw8XQouBwtTN77qGOIJfO9o9VjMc2LX2PdP8Bs3ryrDEaY3uF4qdfoIVTMV+UbipTNF0NSPknzFi3cIG6x2se3ge/VYgg7okXsjrqpndOk8qbMzKnwX51hODS5hiV8Ti0/5aIWyx1HmnWc05tJlOlwKkgaL0TnWMi6vfVVAltQQIIyzGnROOTE8rhQIcF4NeyLgS+ktidlLiAYgSnEzpPAWtnR21WE4NZudyb6lf2nNjY7prsI4TrnmOZToY/SBosQUM+K9x56pzczoy7LFdGIPEenKbLQ3SfqE2Q53i/EwrEc6vMDOn8lOLWeBtQubJtQZ4RoVcBtTQeoWy1ZJCFxoDsdN1mh2vyWFhuYwkg7hYVZPO4bYSnurHlB13V6ojr0ssOKXSwi890M86cf08kDK8RdT5FYjSDY0iaat1ita7DdfiG6hAODXVgOTdCb6+VkzbRPgB0kzM/uuWweVaLieuwRhv3g/co5QbjhyyikG1k4NwjD9efqpe0jL5X9k7O0CkOin5C6EODfE0R5HmU+b9bwtDqmk0mYHRYljVG1ioe0t70kQrgmkVG0dV4GuM9h5J9ybRvunOAZLemulkKqQYPUWvCbIB7zKtoJFtQnhow9OLbK0A1DnZGXyQDM9YViyItpKiWtdfkfsvid7ISBcqA2dJ/l0LDVWunR1THFpPi7J7WNbYSnkib9FDX28wURBEz2WAxw9PKytfRMqzVHpHEqAY03Nui3u7dM1tM6T9E4SzHgwgOddJ7ogMLb+i2054t0UgNuXfmbsmkNDtZ0JQIFY+YQu1xE90zM+J7rQX/ZNnCFN9hNk/wD5I0RNBPiBT5DHZifianACnVfDuhqBfk/p0WEwufhiDxTqfNO8UGTMnc9FpcaIf5Djqj4sOPPReaMQy1+U+SbiY7HRH4bjXRaTI3QNJEjz7I0nNBC3sjl3V4/oeDqnwDov+Im8381hwPiHXookCJUvDvi1g9EwucKmgDYnutRqrJ8Bwyt+vdZWjU6TvZYstDbj4jfdOkkT+ya2nK8Gr9N5RlMrLBkGsR3Q/u3m1+6fmr8P1VojW83RBzj+XQHwuAC0DpWZp8X+KxYZTSGjT5JwyE/NaQLcrwmUb9gfdAm+qn7yjJ1A2K+8EtvJtrujl19UWnNp057oaNI+auQ4XHXlWqgptLqhAWCXZZJbtJ3QMExPE3RNm6Hb+kUVG3dWh0hDIblDxT6QmOEaOm59U7IYWKAwaHX2TZLZzpz6o/8A0sRrw0mdjZak2RLYcT9E3M7xtnflOGE1gzO19FiteCAHRtOkLD8IixiOykgmZAmB5pmVs0jRYROEcMQ7iUW1udNR2AT3zVmqNK1IiYTbaIXt/pRdvtsnQ5mFfyOlkBFqkcpb2V5HJsUACdekK2Y27ptgYc7hYjmEbbrCa5sES+//AF6QrDDgCE2Kop/WyvK+IWJ5C31Cdl3REA+I7d1iFxqh3kqXAsJLT6IgXvTt3Q1aCPNWAEuPRYBDiMp2/hC/EYKDn3vA4TaZ03TuCL8L3RiTbsd1dw1d+LqnwDvwsP4teZWSl2psmsxXfilMYxwmbbLDBaHWndOoYHNamUCUMoP81V9jOvZYoawNzeaooJjDLfdY7gBqsLviTcr7O4tjKdfNNcHESKtr7dFi4QJNpvG6bJFTTfXoE6hhmD5aLy21WibImCdYTKjs2k59+6yjCxL7319JR8TrpzrNzumPP1RuSjm2bFwIHPK2UZWhwCa0ghXHi59lJyuDtwnTOvkphuWDwU1xYPF+VSYmSdAtTFk9xcPEOFBBtbRGKzAssStkSD2UQWx6qwiPRfbTQRORqx3EW2iSVFJs6dT1UzpId8rrDoiJOlSd6cJzXACzZj16KBScoGjk9stN4dVCIePy9VV/jHt1Tct5Ok7bbr7MxgZVc+GpBoY/8ObtHbdOaykAij4u3dXD6oBuKT7rCNA0DenTopMgQUKXyXW/dYM4hjxGqyyMJ0+EBFznDQkRpspDSMp5hGpx12pWPoQJO0onNMuLee6wqcWLOHija3uhfYg2M/0eYDZQI46pwdk0j3Qghym2qguLbjT58KXAfONlYF1h2T4vJ3Xhm40JTTS3UjnhMtiNn8UwhE87BYuo4UUgRonVRoe10ZJhPbPeBzuixhje9vJYtIHxbdhKdUdIilojdGRGhuKlj4UmDCpbhARk/Ei573tqALo8kIbqZxM3lCfhtjXMU6luEMwp0A/VOxgxo3iSUcR+DiZmz8O2bqqzQZadUHy34WiTdDEyaVWjoVitDCZPZPa8WuCi52ULDxHsb4wPZfYvuoPaPVAMohxggz1WKRFUCOe6YanaVHX00RjSL/JVEg76qczrHodk4te4f8cW7oL4mtJ/KTZZo+itB2TSQ7MDPKcARuvETm2twEHTYikwpW+8xCwhW4Gozrf6oOoe75hbCpYbyXDVqa6mLOq52V3Rp1WA1vIaIFlummidrLBa9ogxpT1TbGG0+ISVS+RDg2RdMgic0aT2WKH6TGll9oGIHnRo0HmsJsCwfN7pj8Vn4nO1d0T5FV5yz0KwfHvf4eeyi9Okn3VRjDpHG9j3T2k/hFlj1OcZAa3fhaNHGp68lYha4NE7Fw4Tbfm4HdfZ8j3AMPPZAmHwNR7IljdCHbN1jzQBa6AI+EDZxKGhKYCKXbap4ojTnhU1cOzB3ZCNwtKuFY3WLOM1kU89uqEtHUeLhMOu4TZg380CQ90AfVYlXxCOv81Qqucp3XhP1/RWl2qaam6WmRz3UkE6K9Om6lpiXeSdUHRPHzXYqwqy9lsUCYT3sG37JzTJ0geqxHPa6bxoeCn0k7/VfaA7ygw5VCDcPsbJrnn8s78phy2dt7psPYLbgysQBrCHdY6INcGZg3/FZfvLkN8IcEWuww6rNe/PmsNr8R9TxekAi9k8Oz02PssLCB3qOYdF4i+oyee6k0t/60i8Dun1hx8BHF5Cdk3k6fqViB1W6Bc/EG4tpseiYGGg3GsFEEmYP4Y+UFa+E9VhOJOXT9Uyh1Piqj/adD9fVOAOI3K7vumOow7uvGbS6bfCJh3wU6FqdAP+06DGxhGp4jP0CdugSXWiM2X9UII99FiE4g0Pfsm0QOZRyu9k29PHCfBaY39URf3RANN+64WGZd8ROzuFjN+7d6plg7XUQnvBc7MQdeEIImpYALHCX5fkmReAfopJxWuMi2iNT3WJ46oaGC/qEXB1Mc2KFZeQHA9/qsD7oTkp/mqw3WzEVX7eawoOgGh7SFSwslrI/L+qu951aatLyi+HDz+fCwP7jgLccApuI00xx7rHiCJAyyOOiw/7wNnWDX9CscNAPg7X30TD94WxX9ESGOFJIvB2PmscFsZqG3n83RYUE2Adln1TZ8Of4RFiZ91jYOLzS41J8vwWgxvT+y2zF4tY6Kuh9gEBBO99UB4qhOnzQgsmfromyJTqrClsc2THsJcNeqFjFl8Ttk1mlNTtuoU6Rqhb4Sei9eFt/TG+HwR7JsU+I89FUMUiwJtdYpBDqXdwgHkkzAWG0lhs3/LlfDPn5hOa7KKbbLBzNtUNJVWncStDEdelkYaRfc9+4VV/h7Kq1Xzuqxh4gJc511j1F3iqOvZWNOVzb3BTQXllTwQJk7L7O37wavimPXUppcGHKNiT7BYVMaOEpr6YzBsC2um/dYQpcbg/y6bn+I/ontYQdT7rFJB4GTN+VNhzhzpF5WIaC6LW9ULB0FWni6pzOqAF4TaZIDjO1xC+zNc8zDnX/hUwR+6Y4WsYsv8AyOI8wmipvNvIwm3I119JWnCIAn+aqaQbLflNMTrx/S8tVghL+/zT3TB0hMaaRvfTugQZ8IH6KxiD/NkKmkGppvKmGu07lMgnwuq1HKxamEnS3mgR59NU3XKWzyjr/wC09E7QG/dSAWyTKwKw6zxN/mhS1rqSPqUYFBf3jZNsx+WLbLbQ87J5pmWg7zuiBQ7O52ybVSMju3flWllQE6dl8d/VOsReOCpOzc0a8Ilr202WIXOAbHSUNW37wqbG5doi6kdvlCuG/LqEDEmHax2T/Fej8NkKwGlsfhPKcAQ4khotKuHgpp+80jcysEl/yvsUAKRUNNeFi6mY7IqTa39GGo6LWFTJFwOPNCR2lPlxnWyx2gRctvEL7RWKuP1W8hBsk7FYlLoLQdddk2AzTrP6qqssplbDXpyE4ZWwSDwvAIg6ynWOqxWOdXmDT6arDmbE7Jzhl/yAhPYzFAv31GiwszdtZTzLB4eOhV2OmfNMFA8JdwOAFim3AP8A/RQaMY3G9vzJ7HR4fy3vZSCXyWnbzQsd024Goi/dMcXtEA++qdQRaIlpKDMwjKVMU3/W6FLjSSnSGusOQjA1VTg3eJhaiFqW0uM8p2W0X+ahzQKjb3nVQCB4WAQtOl06g+EzdNpdMDp1uhc8/RMLSbd1oYQ7rBDcvjO8pvputzdCULkmVFjvb+SvxNobMpud1TfVMDTNJiybNL6k34Rf/FbEKlrDmaR+14VREiQN+xTpJ+DSfXfomgNF6/ED3TnOxAXWjVMECALwVhC3nE8KWuYCaZvm7pjqGy5zomEWguwSWu/1ysJwIMy7p7ptoGm3ZeH1lPBBBqHRXnE+ib8PpG9kAZTdXNJ/hTYdF9phYYeQI6tTLnROgzU6nborNcVcj2CaHilMibOc7dGJ22KFhrsmgyTYq4Qm3oq58RGy02Qkp409J909oHwnmUGMgwZOq0A+fCw3RHv+ia2nUx9VgNhppqDedvNNgRlbqia6vZGKtVcWt9EMgJ+WydAB8QPyKxHj+3Wdv4U7FIxdXEzCbW02OGQg5oMjDpvusauph+d/dS5obZvxNgKPu6bCfKETLcRzWj4aXcwvuzTu2ag3iyxcQYjDcXMj6Ko1aE/sjP4k5pykEb91rrP0WvXbqmgglfZycNmr9Y/VODgON5RtssQSH3gG226BgGOoVYFN6x7IWflJ7oQ9jg3UXlAxqm6X9Frqtd06DV4isLDBgAFOcSJuUU4gTsrS1Zvu3WnRG+IJOiNjeO6cZJhNacs3Eo6YdgiQcoJCsA2YC2ToTjlBI9UZpNgRZfh13TyYAgp5u0z6LELewT3GgiChd7ZneOE2Y6oCH+RCebSEdRfqhcTDuJutc/yToOE8R1lOJAe4K8NcULUj5p9LS0mNU6YdHCaJI1TjnNR/6rUiSnFzTh3adCpgBCzgr5QnabLYT6/0Ere6cv/EACIQAQEBAQEBAQEBAQEAAwEAAAERIQAxQVFhcYGRscHRof/aAAgBAQABPxBkidCBdHmQNOBiSc01VFOIn3lMiUAgRY0+W8KqqIfDRLObtp0fKpK8toEAVT+MzrOoEoRzXx4uxnaqmjYe+PBtNBjEJ8HlPC4ABW08YYiXiJCqCRH695QCDUNW/q8Q0AysqS28BppLhSBqKBwGD6yVr+I3j5bQYApU4UUCAoA6mq154qXhByhTgDSCIBU/E5uIoYNG6HKImHVUijxmQ9X+5X5WdEzQ/wACDvMPILppVRPZ0sIJTBBzbzAMM/qSxObq9HATaD47eS0ECHWwfvtZzKxCj4fv9czhBCIyRhZXj/Bqx4Fx+kDq+HqiwGVeFOwgLL+/V486Q4KNeQnPjFk8AVjmLTWLEfc1ZHkHTESD+TgamzKoUsH5PpxjsCSgn4/281xUNRsTBOO5gyEM0TmQWiSRIOCXkAhCy1pDpBuFPi7Qg3sK0Cj4Yv8AbxuwNNT32vC00EY1UCcKlLQU9weYgAjonzcnOoiOkgjHNI0Ib7066U9QLUR9XpFFgRoWcTIAvg+p9Thki0a1SmjJ+l5YqQJPAK/BvNCxlgR2S9EChcFvVYIEng3wnS7SjSEDbSa9O8OCBLJ980TacHlgkMgYAkl670AgBMnB14GWioZ45jeBSQioGPmCp/HjWP7KKz/7ec6kBKmv79ybyNoHngFhPCUTooBNCoEv5OeKCL2BqvjY9RclJVZj6fTs6Y5IRXWynQGMorUIf4Qo8uaAWVv1vhm8IHm/QorHal3jLRY9g8wZw5jFYomXqyEIUiEaHQhb0SAj25Cyig9E9H2FC+sTk5skPmCJ9icZAwwEIAzjCAYSaQ5/3luoKiCjIHSYEhJo1z3H68ZwhaZ7XkyythREIdvMhAosD9o12ciIpWA/hP8AbHTv3iweL8LJzDUFEOX94+n+HSmyU56nWORTVd+hzWo9CAXR09gknULB/P1x6mpsbACH/wA75wMrGsmib2vCqDZIO9VfQFwfpX7xtfBVb68jIEcqEqNrpCdPwFhfbTnMLQ/ZG3PzicJKoApJ59e9nxS2SvtqLzWDgW2pP5OGzAsbPLJXTgwUIwImVdeelWg06TG0V9oU/RpxNGBYSiJh+O8HIyEIArfl4eFkVVXkfQ9eSKCg1Z8ljzANMIpbaXgnMFTRaC/jzYLGBRXG0GgNG0YnI2BxDzF4JuTXqBVbu8EgOE7IPrI8IlJFhieknMFVyGmsU5xk7NSArVAvHVTJQqgHCVogxYVUf7OYqAo0jLGfXgRKQKo/EmPKQAkAo8XyXjqoAnswF+d9wgpBoi6mC4JEYtt8A4kIkNBcSflODWEEWT7P8eJlCJulEgaTntgl/wBNHOPUwRJtZnPzUEHWPPxgjREgzklAgg+7HPycVrMQ9ZE/k84yKII3Aw/ZCdh2hwsQbefo0C+i2GA5M0hKoSp59jyfGrAhKPH7ALLQFT8x5tIgLFLpH4eOh6NDFmqy/adGisWNOu8CFAhSIjEToAghfAgqcx2ANdfEj8TjxaSJBF84wBah4Q49nRtAIiDzxOrFUFJ6KDr1sVPBSx4dW2WXaMTcBz2IUCU065pMdNdRrAzrAJSASiD9UnDVSYItJW/Xr3Alo4XzeXSgEExT2pzt8k7JoRn6mcn1KKMCvLiYnxRdc7HIQEoXQ0EHjhZiigAP+jAnSXJTCMf+g611WoFrPe94lVCU/UBc6+BEElaKi1mXqgxBRgIH+nxOQCYoF19OUQAFm7OIE5zjmGlMOJBaHqDiJ0CjKWCbZ5vAU+CkRcD6OL0qcEPNH7z8QGAKQlW80TbBfFcj8iZwsgVXqEJ9HaUALYYnFIVxJQFUOJZID2KeDx+soAE+A8ZOYkyLyprIh0ZsUHQW4xvw4bxROFJ6WZHrOkVAxp7NTk5JrgFE8M4qIdXosj6ThcwVxVNWcJi6g0D5LoM3hMlEFp5PpeVI3IEfh/KnM9pKNF/OqbUNDIByMmbzimgsTQwf+tJwCGhSQCg/44zyprgCACE6NSDjW/QvAf5IrR4yEeBuSsBL/wD0b4nQfEGDVEeHiRA4lZWD5JQOensDBIulKt4nxoSApHKTXkrxVgo1t8RB+5vKvYwtiDwY+8PZVAyhFh/49I00CIAVsNOeQheKLd35h0cDUZo8SQ5a26QPyLxkQBXVDYcLpAhJIx/bznTPGPHjCKq0m+T3cHlhlahq/Sfo5huA4RSKsjKThjhG/UkgfSclRFRCAALXkgNJkqpeRpiwPS+o/l5+QF6RoqE8L5yxUxCAR1fA6AYEUpB9XTaFCtNu+e8rawBQF7NZLQAZr0nJpWpe+KTzM8OkAIFUUX1j5j2GsICYMjPrwBzxEVXH/gd6agtEH3/0UpM57KTKCiasTceWaYNCI+y1YyHERGW0trFcewE02ackL1li1YPaHEWWFEC4KnAItM0rHOBbQFj9oDz7esbDJWl02ZOp1wlNQimN6uUZKoXE84FAFoGY39BUoccpgBUAGyWjpOJgYRURCIwzpSGCSCz8FZzywIjKhvAWLaQtPOB3SjLABAu052q1JVAxWpxQQggYOCckcBVIbv8A8cZC5SATEi8Rwqq3wqecAMFHtrn6VzQ2kqgLCDFvR9ZBwh6wD/dqnX7YU8EWz8Ulh0paFGoD6Fl+PLVRepTPRHmS+G9SfMecDJY0BQRKTrlQHAiOk44XpsxCBfW8OChoJH0OENUcjULwVBogTI7T+TjVLieHoOL7a9NFRLwQfPU9vSi+bInSyDk+40Zed5peurZ4HgDpMfX2P1CDrcyEE1JwY1AQaABX6jl7kQDG+FF0Tx45hEElnXPa+8jmBAEJfPPb4cRGyFgQ6RgDVRBLBfzltQxjzwTx09ObV1ikIIWvJ2jVFRfQr8jnjzwFf/EnHYMDKEAqLxq0w8UGyLnA0KiEkXSQ4/kbLAhmkSidf5QSwVT0AzUl6XFYOCLEgwOs1eN4SQEUCM9h3r7HgkI/0ni6nMyWCaGFJH1eDBE7KBT06hZ9JanDTLLLxLT2MV5kbUog94qQEbP5N9i9OpO1QWNh3vFHZqynD3ODU2q2p6YcOLol35eHJjdn7CDK8EYVK/rMy8LGUiWQq06vgIsPTi9At+IWJYOM1IrPEy5xKgu0rWbafPJz+BYNX9h7b0cxqIofUcIgMVObtJr1O0StLH7+PDsosOlHw5oBZjNfDikn6KmLCtS8qQSB9X5FbyhaVDQZ5Xi4ZASxff8At4ymKHFFYTiQRAJTF9SQnAtEAxgjJvl6TAboQ18fE4s8H4ZV4WEZBFoeO8filRA65XsK/YFV6FZ1xBvgmGvqj1dkmBf8iHVPJOI6gQ2BPWYPPGYWWC+3OVLCi4fAv9OXGEOtIAA5gkSAhLViE4RgL1ikzxnW+6SiCeQ7Vy6tEoz6yD7xV0UwuGGeshXgExNCIlKO/gGdO1W8MKCji9EgGvVEkcV5RgB8B0yGZRETLPjCnIZA0gjxdwzHmKSjVDFRaxRvDYFgtqKS93OTPUYT960cvI3DLdFUil5TjjYoFRChXiphRUh+sKXTmvlFvwPglg3ls/QcqlSa8pSxCsQ6IpTjpYFKkZX3HgStIZKuyS83ENEtF+HuvLQcJeCoileTug4aImrZ0uIUUBVsOofhKgCXDyXnGxqA27tw8LylAzCwCOfpwPCaU9H9M8L2/mUAoH2HDxCYAGUVKNd5pLktKJaWWnXtooJ1C5y4L4CBk/o8DeqQRUt58siwBoN3XiB0eJSRAQaJhx5VdgALfySY8oxpCQE2gcpkCXGpojVE9eMsI/1MKkjzRo02vv8AT3o+C0FYoJePDoUss0vCwERuEPSv+7y2kX27Lf3kjqsIJ+HdpzBgCnsdJZ4p122mkvgmu3nrmEOKPFwleGBKVqVlvVQkgAqAVDwPEK9Rj8HiGGTbol3jaUDZFaA+zjQ7AinA8qRAOAYjZHvRuAXAN+RcD4xMNdfS9PzvQQpqErHmrpAhVcYWHKBVGqIfUhAenLsT0iEkK8BgovYoV8LzThkagzqt5LAyEguIcrsjqEDLjraIiUtRmf0u8/fQUWlWc2SgDafxagcy3FRFptci+0Y4GX8xx45BSSyi5QnIiqJVKghy1bFIKYAxxx7f4RRpqN8ZyC0JrRHQE162DTgEEQCyfTruOoJPQ3ZyXBEY0Y/AHCvLguGr3/fDnoQyP2/d/eewFrLCp0aQEIItEXV3XoFKprww/Aee5gipFFOaSCQapy8wiulED2tvCkIgVuirHwU5J/QcolPhyAmcr+6368EhruMaWv8Ay+c5Qvth68zr4i3cPJn6HM02ZYURH6HNIjVylTPvwdnEpHtgQfR2nB6g0iIfq+xOCMJQHzuF7ahAkwHtuDiCgigyMGBr0zL+BI/fd4SKnQADH2C3jEjl0Wg9TjZ0AIAi8Y0hB+6k/RHiLZPSFMx0hKlqACWnoc8IAkQx92CfhyjZ6AEURLOOCIxlqto0ubnSXRUoW+P6Ht73QoAuzlXBDBEX4kG/Hi63EsGnleUEaLIUjoiMbyfoEnQ4bpgCaeBwuJDbw/TsyAabF/E6pGQirkq3bytGjYr38RDhQQAFCH6f81Oi4KIUACAP28MKA18S/r682Y0PYC+k+D02bSKsYfQqdLVIjwnq8wdwA0DBN9HW0FUKgWB9ON6SjJJzwUINYmhHjiZuDAlE4EAChG7PpOAed+ahUeKxR8sJF/8ABGXoLKlxImL05pOfqLvvCvWFEFWr+cMQSJVT+IczatwEBo9bY7lDgfa3rG1EBh9OIMSYs1HpjxVkiAeh+8ktbqIE5eRzUBxiz4fLy6lGgKhHVK+9ZmCYaqb/AGc6eJwh0Y81BroiWqDHvvmiJLEFtBk6UtQBCFxRBT2+jxUkCoUV1D04BcAIJAcEdOzKVViCroPNSK+gD+70ehUGIVAkm5wjTKVChraFfnknGDdAVG6yhHw6DADUXVfN+/rwWwJQfaV6IgQiOjfOSLpKCJc8M5s3yO0x6NWpGiqnz3yPF5E1GocakBKRp8Li8KosuFGCn9XOM8e9HWQ576jw8as9K14FwYCVbvsyJxXNRMIJy/HiRoujqQo++28WZEDHFQceiFCir4/7hJzmAEDUlIeAsoijURAJPaTjpjqKg34DeIzSoSCZ63j/AIO0Y1m8Yc8niAEaQL0hTlGuLeZYKQkRaF2cThaMQgQW7c7Wfmj7X70aSJWrqAc5COhLEqextimk+2QROkswU6qm01OoHGlZIf8AKfhOlWpCowuX6c9AQBFH/W8M1jSiWF1Enw5gQBrRRXxwxmqdkPFAIV6FHgJAVBOGyMdOKIhy6BsBqAqfb0mcXYwCvo8TruQ6XPdQOdrgBrQqNDkiWZCvQ2DwBsYXAb512YAX4h7fscQH0UH3oHESVjZUQP7zypn0+pdkiPARQDT0UEDSwGM/jwgH8ioh4lDqdRj9wvWf20eOjVjfjwGwk1C+JeTghfSw1Aeo5xgICIrijbR9eq6lwfXgnKHG64PhJ4fJzAdix+i+SU+Qc4gJJrURdBSycf1FqqgY5ybiNTYOcb4kEfR8PBPyKfbBQ9XiXdiIW4B6Edhzx1ijwuivxfhzeVDySf8Ais4ZiaAeoKLPjzshVgEGjRnvxDk0k6jJCZ+qdQ/ET9F0YZ14CoarUL9SPj01min/ALS/bG7zyAXpNAk/0XiZJLoRqKKT4dqwijwCqv00OjxIDqSKThqWaKiAxxB3hIkWUAeCPB0zqUUQRmznoLtVYVIOuZQJkBqcsvIF0Af1bxawhQWly79305w8UDwlLxb3QaUIcRJnSQgxfyONKKgDDHidt4EhSQgoEF40lUooA67ReVV6EDsQUB06sVIFHiJAGokAaz+ucelCoCqgQGnAeQI3wvt4l9YooYBD9jzeUSm+D5OcgFBvk3x4tgQMCGfJeixWECxE/DlqnGBDafTJwBLSEKdX2PPAiyFCm06nm0iCrDgwCyEVFVK1OPGAZClCMF8U5Hkqm4XXSSwvofr1PuVB29SKgIqfsAPP3n0xiqPEOWRVWGp8UzK9NHuU77HSUqMPIjHl9ELiEoYZ4ee9FA1AYZf1yWlSiEU0pVxKosFY09WXWLzhWjUc8f8AwnHVNgYgMrb27nqkcNwh4dF6tBWKD5tLzQgIlUhRyS6CFMeBqRl4CCJBUYC6+TqupKibgRizn0TFYAjSo5eqRW4b5fk/pOw8UFjVTxV4DtiEpYXTHIBK0nqOSdns5kHDIpQVxzbFIYL0OCEFlviMUTe3poKCiR6MEYr1AjeMxQy1oik52CpQRHggg8wvuZwQCMLF30Poc59nFJkrvzzrxAxewDCPCbFlpPt5yWTTip+csGzbzZprOVpN6EfZwUwLUpEWD7vE2blgntfSHH8mNKar/KN4twiLKfbQcarECrAvvzGcCCFqRIyB/h10W7ry8tJqKR6ny8AkFM+hrf3DqqthhAngHJ+wA0qoQvKxyJUpn/GGc5QP+YPt/wBJyyUgYrtpOMqkMDUa5xwRWWRo3z146EBMn/Jxo5AXroP0Hjmqjeb4WecmYxGiLch5HB4SoIodD0J8Hkg21SCbdFqTgAijqItgPFuQoaEEhWONgWFVA+n9Pt4w0vHV2H0HGdeITUSmFI062hdpUAw/QiPEVCghAfb4ziAIX+2Hi5rhroaZKcI1IYFworPOZcMYIs8jmB19NxpcLRPEvRrZIDQaBe9d9UPakE8YqQFw8VT3jpAw/kUVxOTDFoCBFAIuPNGCtPSHojdnagCzAkOlLdOjaDp4sf5LjwBwqjn794qU+Me7fPc5iGBkPkPOINWtbks3qtssIpiSNJx4qhBEgaVeZgzMBPH9vb1hAJhkAOmJWB8ffvFNXgCNyX3lQQLE0ArScpIag9wn9HkcqAzxXPTObpWDkiUi+iuPCUQgNLijOcGxXzLQBvLGBrlRYrPouoUAKkuxEqfXj4Hn5hYwAHIg4GqmE3raiRXzfeTZWYUa/wDsYHvI1XMoDhp0gtW1BFgU5jAIk0aEcwdIsYBJWFbO32Y2wIfUhOI4Q4NLBQwR9ecmB4e7UP8A0nNM6I48HLdOSMAFBEg4wnEk1hFSIh5Ni03oVF5l0FQSOKXfMvMypTAGpvIbqgaA3/Xh0DGRfJHPgp1fJ9vy3iE4It2+3111iqqgSAUhzQIArYlnH8Qs8e69mQAJVQUUvXvNZ6qbILg8eACoYpfPpHraG0NsG1NnEaMgnnr1C84EwxQgcFh+VlhzeEbKofzeTuwooP8AR6U40iyltI+D1rOKwkVYhB8uVeCQGgKoYpN/RzlTUEqiU59IKUqKr9XgO/EsC732cNoigfxKAEoQeFNUgKh/9GV4bmKgqKnNRZZWoka/1L2jVhXzweW08587MaisKfGPYVK5SmkUc7IQhMEwsl6PUFEYCrvPFPKggdQoUuiQ+Hkqd7FAZ9fk95043i99/pnHcIB+wBE/I8HAD7CKgwTwcTgR2UAIqPwN4ovIfobhxlGiswEgLaq8TN6Qg7TGsWdROeQx4vch+qjy+y9icyxVhgLknPKwosawZzZbzkSujn8eHYCyoMfYZeZDw6VuRLyhstk+ICOfedBKhFETmhaCgNMyqdDoarf1vl6sDzQV/wAmXm+knhcfZwiQBYMKwHGclZJiZRajR6SDQS1XXesitpFKUp/FleMML4ERquNJWgVNM+c5LUK56HvEDoA2B40YVgPW4EhDhIEY9IUqlWiI8Q9DikmYMarUM4hUDIQDECKl48YCiUDBb5wquvICYxKG86+RNFhbXpeagTNB8Rg4FIQoij/QQmXmKB7AiJ/BzIfIfie358aHPRcQzEt2l3B45SJCrq46KlYZWtPScMICoW095xJALMYjE/8ATe8KphMGMhymcVAQKmZQdJEmvc52irIfRP6MpzuT2iBGYl45vWPf65VCIROQyHLDOKVG0IRMf6x5TadEABWcmgEAKDpsP9OSM7IyX+J5+IzjwgHgLbxNnOHMiJUHQz6HN8BvhStDoqo0XzzzgvJWS0F+yJnExIdmuvzyWcmTVADGmgc0KIBuUfx5z+5gMull4Koo6ERTP9nK1+SrTyh8ceIAUMU8QTkxWANlEWZOU/Y4l/DN5YCiQe6/zldZAtVSOjYUlW0xfBvJIGsQ/jxHZMMBStZ+I8wSOAX08R48aMgCExHV0t0yYcLBOoFwZJRBmUXRanWZYyVdTg16YgBBn2PByCrKydmC4AmL+rCdbAEODYEDy/ErT0MV/TjVuKtpAjiWgYekPECPF2HxeJ7nIAMmgUS/f9M4MzDTCmvjeO2giIwBeMdk3RBa8HyVRGiQZz0Gh1KWxIg2hw1uOJwMHyvOZCZdIFh9LeEpIej29MZ0JJE4apBCibXiz8Iz+eceAtRBI0Rxft5CaMSU0bz9m9DqtD+kE5rVUsIIAPLS9dVRoiUFvX+PO9MWGN5hG0rV3D6BxGB1ITH0jR5E8oKof1s4LVUCiaPB6oK8UZHnoxIBWUo/jH5y2ciAc9K+nM0XqOn64bhgKKcGAhMN+LdfTvCy1oDjAOhrCnx2S8Pi9EAEy3zxeQ0oMUiTL4cTAtEjHfyBxkQ5ERYSreKEXWzX/IHmHn5hSDiVNc45wdIqt4hCEvBSQybYevYzXt0xIV8RO+FlayW58Dk0oEFBItEu9QvECiqJ8HLMyEFQvo0vJvAUQNMTmLNSi4ar14kh5WRArJxFSKwmsTeOCQEKXpeNKAqJMCD5CXkcDCaF6kmAh076z8S+P7npweHDQQU1qWq5aFBPWr2BQii0GGqjpwqF9I5+n86bFTie4+/mPXUEMcLo3PJxGVGGCw2PxHjZk9kZR8+JwisETEBSZzz9SB3yukjyR24UeGxSVCFofgvCwIA/r6ryRRqSBwnMArCmgJfPhQ41b3WqpmkTtUck9VKnPHI8OQRRnAHV8rRRyvzff1kBesKXT7DIVhdnTmaDpH3/AIucGgwmJnqImHLyNBSo09PYR4xCyrSH9sZHnVIY+H6vvBRK0oLPHPDiFtSjNqBy9GdQrPFPl5kaYghBdlZXFnsVCeevC/DhI/hgKIV4yq4CCTE3VVrgIoqRKkX9Ac9zYGAl8GV4cRFRERD8KgBt4/lpmhWuQeUyoFOj/fHmThmHyIEBgUnT4cARYUdErxfYKnga+L24oxBQLZ+7xZMzVkfd+vOEcAV9xFZZSPS19ICKYgMcvP2UhlRYvxPeAIWe4b+CcNoorcd4PoQZT/IUetX8I0h3f9HmhAASlc8qFO+JLS+TqbYsSyaX74edoZ0Kie344DEIiCDcUe+TeNAUAGah7xa5ZA0LEzy8rDZxApiG6PWsJBKCMrh0SUlVAPwb6cemir4R9TfL02EaI4KS1ScqMKimCOnMSqAL6A8fkeImUKQo/wBOSNDUE20/HAkbSnTavp/hzzCTJoagxR/HrIqEDADhlOXJY442ZziWghBRtG3G8U3OFEWJhP7QnPgxEBQCoS3mNOCoCNFUJxxoyBhT9b9tV4yxaN0X+ZXMTdWhftLYdIFtCj9DQFPP0sAUfxGE5C0ewqeB9m/DqdIAGiTDlyY0FI9qSGmHHlgUlaWpwgoALjFsTfzDi9SArCwHYiCYU/0rj+3ooAhgGA+RZozklQSKgYYNNb7ecMqoCAVZAOLhCqQUaf1Z1JAq5UD3iwjWku7AsOdDIBOWw0C3qWiHOJFDzTaEH8ynKQUSRYQYHPlULDJ8hOeguhOYq6u9Gzqgn09n2Ly9IgUB4v8Ao8gBoTQVjv8ATknFX631ziYIpYtH9njy28tG0hPfHgT4AqOi/ThOmytz+4z1Oo7y6aPt/wDLOawGkMYhC2PCPQKnI28BKlQEF0MsMQWCPAnMQkFesoK2gzPwexnDsVpQAyt8Ul4CLEQjZnzJTs6HJCIw83LDlMPCgg8TZThhftMAPPA8IKy2G+sXXrYiRjGxh2cJRk4sJFeGwBJUoYzKUrjsxsUD7tdDizWl6GIBxrh4SfVK7tlfS8kraQBA+sSI8a4hipWqtbTlcCSKTAYi9eEsUVUj6w3gT6A9KrReY0FQiKmI1ep2neQnAbKOdCXIgsCE4Lygi2CkeSIRApatf7Pnw5TAIVUqkcRLt04jVCEbzUg1LCaPz8pwyC/M1YS50tFKX3zziEC1ITbVaC6cbM4LBAeLaws6mKFSH925vJwBDUh/+EOKERJtCHA+iNIKrc+yYuNQmMa1kn148JGzSnxPnGWYRH4Gk5448tB1QDOWvEIYxb9+04FFL78fYcVomQaxFJ8Q7SpBpUX0mh8eRgosB+LsnQoomiCH4nvE4N0OPd0Hu04nDKNND1OH36A2Q8ATTmJBEeSaclOJocA1XZWPi7Siy1rIvVtEgdIKmE6ANEVA6kZC8lOwTgrh45jYIBP0BaIc1WwjQskHCHQrCkrc/PI9+6khVsiMcHixHt+RvjTg1Lki4gL1RNDRUtQzMl7ZT6tD4OkyjCyI2qp14CZYKInDFc6hhjRECT4noHIgIFSeHIlQXOVDQQqCemft5YeKwSh9o8fzWp7WLrF4uDQFRcIvvBLIEYWhb/8A304fnqoiH3ePnm2BTaqeLiZnCM26PAIl3bspryycyeCzCcAjJkhOjpWc8KhknM1AtG20Q+4Zys+LrH6LLvjxxA1SgEnleQogEytH5z9x9LX0n3VpxRgFakFFf+vPg7AFCY1hvKn+xVVbX+28AgKFb9PxnvQYh0JAU5iBAe+nzkyAEnvg95MWOn6DkXhUf+f39nA9FTKaCDqpNHX6xW8+gBf2orOZbg2WGwNymnKwMohFbn9888OER5AozxLnul7Mhiloer4cc6G8gCaZXlrBQZIw/FOqGBHEUvGc6SdKAsNySdMrU9AC5NnBhKNTYspLbyh4CCAP0n3hFGBlEWKKy8MCSGny4GAjlKpQUN1XBxKUM9fGorpVKilBLAPIrQgQCi+Ck4jBDGBHE+ri4UM7gyVONyUiKVYBTxCcwiDAwVdHZ7azhFhdOHOiQ7KDSE55cuUAaR+F2da0HjRiIiT7wqWw8xaCMJ0HQVWq356uQsAZ4CMDl+w5ybFG3QK8C1onIo+2XnwZYgxkX0ccK08q+0+ukR4C/S+colKAhioC48EUABLwVAiH4Z0CBoggT5JDiioYlBM5qyAAWqHvK9gBF4qph6nGrAW0fDbwwpJEJ7Mj3nVt8PHmG1LFbKoIVOedmgWqPIcOqekXT9ahHpk1j92ycXRWJrL4ss/pzUg1GCKIOLwu5KI3NFj2CX/iItiHRd0ENNVOACJQ6QMc+O2cEkUSVEGw1OK0UiMFVEfU5/GICfr2hJQ4zIU0ZVD+NLnT0rTSiPngceBQSxISTikkm4AhQRIv8ZyWHy3z+khMOdM5raD2YD48UyIAoClZeBgCxQBDaPtvBEF5yDQX2N0HFUGhFBWDF8XjtCgSgTIHgIUqtdKI8hOmEWKtVT0Yc0aYlUTRHmTl8DamPgO81AJRYZh65yNW6Wg3N3x6qqSYJpQHqoS9IqADRJbxJ21Kj8hOUEUAA4M9WcYGEKsRBNmrwiM+DgUt83hTKgtICVKelJ1sAjaAKHH5K0fP8edBQICxn84ZrL2KVL985wQgolBa+SzhmAYMUkq/jeEKKcvtMDtk3gvMHPVPQOBvhivPpHGE6IlXN1pz59YkQLKcNJkuEA9nArQiRptj8XCoDgAEfjxZySMIFPqzmYewUPoLk8FwBDTgsBjGrdA+HX9qhEUX1fOnKCipQ0qfs5qg0MiNS8UKjQgBZjGPBgMogjgxexonBAJC/KgjdZOJ0jBUD74EMViQCj/CachP4RqCBUzrIGbEADxFvfaoxKamIXp6GALFALvoB19iwJF5BKc3EaEw9AsnMcFHyUFOgdWHCFDQ+7zCqVVj6+ydAPKsYH7nrzKDQqCCYfpUdvGpYDSG/qVLxxAKCosBV6OSMNFVsGAepwmLRKI2r94RzOKVoWsjfhxDF9iVYVxhwKUJzU1/V5rXSsFZR3zhphACLBeod1RT0kE6JddBkrwkxp6UcaclQ4AD/fef2QmGom/3OGgau5N+4de3QAKrPn+HBogQj6N5hY0BqnI112BEUjkMVwHH5dY8eoYwQ9Eo3hAniEhSioW9NSTU9KenDbF8KAEUIbzkZIBcwk4klJAz0qrrwSLMokX3TyEzhg+SgmGPBjaiSCnB3wZeHQlBuR8vyHb53gZonjw4hGBquIV/yhxDIO1jPnn5MePWSpb1i+p68Q7dK+irAtnV6NKTBjzEUAiirP4R4AnODanEgAQr50EFPteMWC2xW+2AHXhSaCbytl5riFcXcJR6cVymAtXwFnNihFD9JE+5J14SNIQlFPztELAAFgnofU58GJIqS36U45LAsgBdPRvNe01mSewsnNHSLUb76ycHI9RVQxnPtBCARuzqaDC2QF14TWYn2h8nBcBiGrQjcvKSDMNFCKZ0/ETACfoUcCVagV+6p18agFYwj4jPeRCMRAH+T8T3pcEQFlTHFIEERRR+rioCKXYMN8ePnKnJgYf5eaxxq5asJCYPC2vv04YAIqDVTjDc5mi0o2nMwvaFmnHC1GvI+chyllExf7fOKLEAZiOzleg4WgLiQI0IZIWb68YylysIRk4sS2AyNP37LxzjQQ/HhEqJlx2ygPFnOp4gqNMYSdXMlRoBAWivnHEr6wJJ+jhZqitgsH0jwnJ0IVfVOcCmsEQ8et7D4E2mMXpZYSCqw2tTToojlaiyUx5S5A8hBJ6OOsAfUS6g6IJpGADlDrwJg9A2Vz/OHYAHuAaM8eWVFDdiWRp6Sw01j/bePj3DAr5CCN2kOiaqxChoFJLJnJAzjMHcbzhKnloP8nLCDVONYKMWHKhF2SAOz/j1jaBSep9vY69TqW/n3OVRRBLD6HDLIRJ4fE4WtHXRSv20t4nBMUJPX+vPZ0lfcC8EwzULU8zAmKIE48xoDVAPl4wKB+aHIuU7RRD03g8aXTRhm50y0iLWffwXiDCBIyv6eiYR3ZXUkt5KBMBgouQ9IchooGj7eOZfQkXnrc6PhQcWTKolGV2chiDX6Ej/AEdLsWRc+hSzkKyBTY3wKfjyQVHtMPiyuOE+VACZJmqkrzxAwpLKr5JcOzq1cQFcdOG0gQ+lwU+jhLxkgoSzngJWKVYKNXkpRMj4aHiOVWmHYAEW8OJooFIfyuZxIRm0gTkitpYEivm9upfCAaBP4zhDjTBMG/8ATXpNFADR6IEeKx3YEknj+R6PQogEjPs9nRqiKNCyjzsgAjMFnzHo5UV7k8RxUg6Ige548h3wRDHKnU5FAJg+DH+xedCVUQGKn8vtnJbaSjlznSk0J3DTby4BKhPld526q6DehBLQKRAPK/ObwgZX+q+j27Mg+kmTh/ILEvEaS5Zio11ETeQEpULVnpwcmhIgXcJ0rclehXvF87m/14JQgUOmvDYzyMLesCuQXsSEAABCeAc7j+jqE9+y8njHxW8ZBGwDwqK1fLh+9ZrYgCcpOGptOYxLKH6cchWjHbymNWm0p+5Z1IWGVfF5K5nywXxHoQmnxThgqHv0fXifZG+gUTltKnX5ZOsbhQQex5yxzWZ2lAAU+znJjwXZIvM5MnzSH3r48FAgJ+divIg+y2cxujBbKQDjdO8/XnrJW0FSThDQkAeAg+o5w3Xs/U96lqQ5o48JfEq/ZaZOqtGdfPXmyPt7/gBH9nek2RoN7//EACgRAAICAgIBBAICAwEAAAAAAAECABEDIRIxQQQTIlEycUJhFCOBEP/aAAgBAgEBPwCjyB8QqDB1+9GCxfdCHyPFGYiAAL7EYWv/AAzDlUgg6Ij5soz8Uui1zHVk13qow7/UxZewfEushNmmOrlFcxUsexVRya4k7A7iepAZuZFbGo95wrL8SOjMaDgbazRFmcB7YWgT5gUlSFlXVxiQXpqJAqJ6l1ZuTkgEXUsKNzkvg9wk8qBrcU6aO4IoUT5EOypsqLsVOa1YN6mQhVVgLbQqewyZDksHVgfUxWDZawwlijRBMyUciC6v6NR1F2NURCgLmtEEG4xBrox8KsT+9VFCgH7hGdH+DEqTTAxkJVgKsiLi4jlzIOrFzlfEgamUBmUURvUfEnvAAbJ3qHK1jQoxnfkoQbI7MyPxpnOwaBEb1YU/BTdUdTCCSpJs1CoNg/2ItKAbodHcZWUfJrqiIX5YyfoxrpK19y6Y66sRcyvlIYcSOr8wZNsDGZ3ZyrEXqxMGUhWUW1EgRHoIaI5GqMN8gBG+PyAu7sCZ/UBOATszGquxLAlgZyy82A0ACRFa7fuotcuZY32BA2LgrAjiIXfna1x8GFsea0ogjuxE9Gpu8hsE9GDA6Yl45DyW9zBm52GG1GzCoKEHdtMi0hPkCDI/BjdAjUPqM7EAOBXiJnyMQjDZs3AVeyVs9A31UYtxtVB+7lhnDAVvYEQMl8aqrIMRweBXdGyTMjhyQG2N6hyZSihTX2aswMo4+4oBl4yQbH9EQ5cShgWHcXIvEqNFtCx3EQpjPLZ7JirWMnsDxEZWdqArREpkDMoFNFAQIx7o3Ba24HZowU9JdEi6mA5V5BiSAwokzK6rj5N1W67mJceSibsNYuN6dHzMrGgNicODhq6sRcYpq8sSI9gDxqIiqQfBM9QSrYwq2TYnpC7o/MAHrQiJs01kR/dT8CKo9zEffW28GrEb09H8qhwqtUtsDuowLgOykAVVGKS2OyNnxFo8xZFTBzos1UOvuo20IrYFiI3MLY3UW6AP3MTn3DyUACwDK/MDruMxyBVqiOoiH3FJJ0TMxHuAno+YF5qYrKAF8jcy/Nda1OBtN2BMqB3Kua18TPSD28NtZN9kzGxGV/iNnUZ76NT07DnkoEMRB7ysS45CZc4bkEUigNzC7HHx7vUog8SQaMReJrexMVcCPq4tGj/UI2K8iEfAECogBtWE93hyTiTXmWg4H70DHUAgx1IKDwTqIzrxvfdxE/2NClBtnWtTH4P9bnqGAXkF5C5jIyYwNqQAaMwp/ts2fo3GyO2UohCi6sxzk9L82tgbNw+vGRFK2tmmEwu74/xAA6MLDGtLYJvoRObgurC7GouctYKkET3Bjolu4lVrr/xs/GxxJIJqJ6jkHyLZA7UjYiE5EutkEGB8ZpCw5UDUdgdDZE5e5xC+JzU6JF3Pd2GCmhomKbVjejuFrIA7jFzib40RZFTFldHDOSAwoAzGcnJtivoTKh51vq7mZXyogBq+42C0qqPIdRU4KVmVByI+xHwXVHoDVzM5S3VeTDVdXMeRM68+IJPj6qJk4Kysa+rmNOO7u5lckuQQFUndWZ6Uo65GU7PcxAFTqoApyqAotTsxEdWZtnZFRPgt15uWxx5Dx2u56d3yYwxULZ6j43Y2rHxaxVAJJmQ8+Kj7IMbD8KYXuxF4hyQDZq45sj7IMRaQsTZB6gUsggYHo3uN8ib3qLlDkiiCNbHcONctBiSO66oxsJxEHEQPLAzNlxWA1EEQZ8ukQgKdA3ZEw4mqnsnuyIFxo7KvbdiHLTNjP5WKiBsTW4u4uVXavP1OQKlR2PERdtvvxG+CEio+d9BALPmZMWZjZckMBYB6M4BGx0NVRj6UCHRqu/MUZTkBYih1UUBX2asmDaUDRmJUUsLJ3C7nMU0BcPd/Qi5OS/HJcJyBwAwOtg+Y55kowoDxeooOLIDj3Vbqf57q1k2CfrQhXI+VnJ3WiInpkrmHbvez2IT71Ak+QZh9OgzWHszJiUUxYjZnNcNFXFEGxcbN7gT4mmNRsPzIGhWwIiNjcDl8G8d0YwFrYuOp3uEghd7Ijc+ZIOgRqZEtNdiKXW6AFkXcIZAzWLOgTMQyAszsGNjqBtWdV3G9NhVgUIBu+IMXHjZ/byM3PxMeHFhJW9HZEUY1XZve57fpxzW1JbYExqMPRJI0QTHzUGZFro0R3MLO/wCWqHVTHk4FuteAZnLuu1BIIJFwYRkrmnGtgTmpVACL6mRC2Xl7hUUBUOWynAAkN1czZCSpVqqL6jJewOGv2IRth99RELY2DGzFYe33RI3EUDkKJ/uOCSFA1V3MQAWqrxEH9RkQZmz8zYFfYmVhkKvZVgBRrRj4hkRW6o/c/wAQVj47C2aJ7i4VBVilHlMmMEN32D3Pb45eDMSBbbMx0mai7ANtTcPFCxC2xIEcs6Bgh5fvdz/eMiXkYV1fRmPGTnLGzXQ6qepsEbvrUxIwJCAKzGxZ6ntsiGzyI3OVhXVAQDOTPyIWiBq4jD2wbE97KMpRcVjVkwrTjwDF7aIvdas3Bq4oV0L/AHue1jxksW2dqomP54GtamdSQhDsourExqURRfLfZmQjYBo1ZAj4VzoSR2O4npUxLzBZq7F3cHqcL5SWtVGgDDmXivtkGZTZZXU6FrMPqcS4wO2Aogw+qxZMgxILaxupRQr8ezWvEb1CryUggXVwU2Eca1RuIyk3RnsAOHBAFHULFLpQbEKZFX3XNnuphyh1viQTujA/yOv+QO1gMtA+RF48aEtELGx/3dz38ft8zofUb1CsmkN/3AxzMiFStb0YuEAkiKvBSPFTPmzqR2VNgBRAShPuYweQBBYVD6VCwyIwRjsTMXL2WC8frzETgSzKxO9nqYCHzcigUgWKFRh+Pm4yAo+tkx3KYlPAG9VdQM4xAqoB7AMXIMmvobFQMmRQym+PctMlAk68RkBOgL+5w4vysm5zyM2kpQYxK42I1qYvkj8t6Ex7UX9zP+eSeoz5ceZODldCen3gx/qZGPMi/AnrHdcq0xEVRk9KjOLOpiY+863qWSjb/nE3nYeOImbSWJmYhF35E/g0cD2Y34Y5h7P7Mw6GWp/NP1D3G/Ez+In/xAAnEQACAgICAQQCAwEBAAAAAAAAAQIRITESQQMQIjJRYYETQnFikf/aAAgBAwEBPwCSwa70MxWvSSzojhq/s8kOLVaFGH8fJ9JEspEI2Th9IxKCxlI3400l+SFbJeLXHZGvHaeUxv3aMcuVv8CavOfRZ4jgqTSSfpQ0sD2iqFVPFuqOLvLIu5NXg53FRzskk8JVQrVPJFvjJkROoK8qtCi0zm0PLT6L8TVd7RGWf2Nu8LAo5eSKpfoTlxKWTpiynSI+J1kni0XrI8339Cp6VXsqmlYuxK/GiUKhjKY46aIxSSvJKKw7StZHG+WU6yL4sinVN6PH43bbWCTaSSxaKjS++zFqL7JPpIdpsxQoyg0+mS81aiqP5IynrDrB5YJf4y6l+iOZHCLr7R/D40srY4RUcaOFadL6HFp1LAk1CuiTTw7HDd4vVCTVNrDwKMU3f6tjTd8ZWhKSHCTppMcXyTY3bLHhFpuKb0PPKOqK0h3d7SJqL4tKnRFO6RK4v9HOXBMc1KNLeyLeL6RJclq8k3g8KVu3SPOlGUUnZNqo4FGD2mifsars/kwrjgU3m9MTq0mVk1RLjeD+1jVW7G9jVreX0dRNJjdxkQXtZqQ730yLatHJ0yLeGv2eT3TpEk2o2yqWTyRdK3aRcGklhkIpU2xpW8C0Mexi0L5DbqzjySdlN2Ii9jp2N+0TRL6IXbzRJOMmyUsJUYSum8EGpqqpoXgqWiSSnsq2Wk6q0NL79MjsUUzgk4xZ5UoSpa2hqSTYkzCuzjk4r7/R3oS2ziuayOMXhK/sl/gngj7RTz94OxPBGRCKbpsnHi6+uzekOytfbJ2nH8ErdMuk87WiTukd0VmOcE0otqLE0llHSE6TYpZJO0hIe9CedDEV3ZlCafyEn1gpbZJ/WBW1nor22YloccFVlj6wLZGMct/+EJQX9cibdiRQ6obsxdjzkpcEzI4/80cd9CjWbsq1kfiTWqE0opfkc3dUhLim6Jzk401SIyeqMyTuOfs403nSFL2/mxyUlrK7F36UYENo2/SheSVaOUuKkkvySk5K+0NtvVFzw6dIlbEspNjSWu2eSMMU2RUU7Tawc+N1kreDlUcRsjHbeMEe8XY4KtlZG8qvVffY9+l+1RrBHGlYpOLYp5le32OW6laoi6YmnG6yTk3BfjZl1kTp7we1p+3I37SFZOSqmrVUWnJYpDSv5OzFrJTtihHjbln6Mn0fXplSo5TeKtdj9slki94slsoT4vDHOUsHGXFJbKdvkhXinQ4n8bUU7x6KN0P5MoUrTVCSbNulrRKNPdorFlD2bwcXdChT3gqluxt+kYxY1aVfumKbWHlEVGtXY6ddDWNmRbRGKcnmirezjQ4tOn2aLOqEoq85H8kS6/0ZD4oglxeB7YtHj0zTY9RHtf4P4/sjtEfkdkfkjv8AZLon/U6fot+n/9k=");

  --mobile-sidebar-min-width: 100vw
}
body {
  --background-primary: var(--tr);
  --background-secondary: var(--tr);
  --divider-width: 0px;
  --divider-color: var(--tr);
  --background-modifier-border: essaopie;
}

.theme-light .workspace {
  backdrop-filter: brightness(3) blur(var(--set-blur));
  --icon-color: #000000;
  --icon-color: var(--kolorboku);
}
.nav-file-title, .nav-folder-title {
  color: var(--kolorboku);
}

.theme-dark .workspace {
  backdrop-filter: brightness(0.54) blur(var(--set-blur));
  --icon-color: #ffffff;
}

.theme-light {
  --brightness: 3;
}
.theme-dark {
  --brightness: 0.50;
}

.nav-file-title,
.nav-folder-title {
  color: var(--kolorboku);
}


.workspace-split.mod-root {
  background-color: var(--background-primary);
}

/* there is background image coded in base64 in var. Change it if you wanna. */
.horizontal-main-container {
  background-image: var(--background-image-base64);
  background-size: cover;
  background-position: absolute;
}
body.is-focused .titlebar,
body.is-focused .workspace-ribbon.mod-left {
  --titlebar-background: var(--tr);
}


.workspace-tab-header-container {
  background-color: var(--tr);
}

.is-hidden-frameless:not(.is-fullscreen).is-focused .titlebar-button-container.mod-right {
  background-color: var(--tr);
}

.modal {
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
  border: 1px solid var(--tr);
  background-color: var(--tr);
  position: relative;
  z-index: 1000;
}
.modal-bg {
  width: 100%;
  background-color: var(--tr);
}
.menu {
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
  border: 1px solid var(--tr);
}

.prompt {
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
  border: 1px solid var(--tr);
  background-color: var(--tr);
}


/* dont add blur in small places because its useless */
textarea,
input.metadata-input-text,
input[type='date'],
input[type='datetime-local'],
input[type='text'],
input[type='search'],
input[type='email'],
input[type='password'],
input[type='number'] {

  background-color: #00000060;
}

.suggestion-container.mod-search-suggestion {
  background-color: var(--tr);
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
  border-color: var(--tr);
  max-height: 600px;
}

/* finnally buttons look good */
button:not(.clickable-icon) {
  background-color: var(--color-accent);
  box-shadow: none;
  color: #060606;
}
button {
  color: #060606;
}



/* sliders */
input[type='range'] {
  background-color: #00000060;
}

/* presentation background fix */
.reveal .slide-background-content {
  background-image: var(--background-image-base64);
  background-size: cover;
  filter: brightness(var(--brightness)) blur(var(--set-blur));
}


/* done task fix */
input[type=checkbox]:checked:after {
  background-color: #1C2E28;
}


select,
.dropdown {
  background-color: #00000060;
  backdrop-filter: blur(var(--set-blur));
  box-shadow: none;
}
.suggestion { 
    background-color: var(--tr);
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur)); /* tasks integration */
}
.status-bar {
  backdrop-filter: blur(2px);
}
.notice {
    background-color: var(--tr);
    backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));   
}

/* graph view */
.graph-controls {
  background-color: rgba(0, 0, 0, var(--brightness));
}
.graph-controls:not(.is-close) {
  box-shadow: none;
}







/* code blocks */
.cm-s-obsidian div.HyperMD-codeblock-bg {
  background-color: #00000060;
}
.markdown-source-view.mod-cm6 .cm-indent::before {
  border-inline-end: 0;
}
.cm-s-obsidian .cm-inline-code:not(.cm-formatting) {
  background-color: #00000060; /* smol */
}
.markdown-rendered pre {
  background-color: #00000060;
}
.markdown-rendered code {
  background-color: #00000060; /* both for read mode */
}
.cm-s-obsidian span.cm-inline-code {
  background-color: #00000060; /* source mode edit */
}
.checkbox-container:after {
  background-color: #00000099;
}
element.style {
  visibility: visible; /* idk if its matters but it icon look like refresh */
}
.slider {
  color: #000000;
  background-color: #000000;
}
input[type="range"]::-webkit-slider-thumb {
  background: var(--color-accent);
  border-color: var(--color-accent);

}
/* canvas */
.canvas-control-item {
  background-color: #00000000;
}

.canvas-card-menu {
  box-shadow: none;
}
.canvas-control-group {
  box-shadow: none;
}

/* hover on links */ 
.popover.hover-popover .markdown-preview-view {
  background-color: var(--tr);
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
}

.file-embed {
  background-color: #00000060;
}



/* make.md integration */

}
.mk-menu-container {
  width: auto;
  background-color: var(--tr);
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
}
/* templater code look better */ 
.cm-s-obsidian .templater-command-bg {
  background-color: #00000060;
}
/* ================================== TESTING ANIMATIONS ================================== */
/* Keyframes for the opening animation */
@keyframes modalOpen {
  0% {
    opacity: 0; /* Initially invisible */
    transform: translateY(-20px); /* Start slightly off-screen */
  }
  100% {
    opacity: 1; /* Fully visible */
    transform: translateY(0); /* End at the final position */
  }
}

/* When the modal is closed, apply this animation */
.modal-container.mod-dim .modal.closed {
  animation: modalClose 0.3s ease-in forwards; /* Animation for closing */
}

/* Keyframes for the closing animation */
@keyframes modalClose {
  0% {
    opacity: 1; /* Fully visible */
    transform: translateY(0); /* Current position */
  }
  100% {
    opacity: 0; /* Fully hidden */
    transform: translateY(-20px); /* Move off-screen */
  }
}
/* ================================== TESTING ANIMATIONS ================================== */
/* better settings */ 
.vertical-tab-header-group-title {
  color: var(--color-accent);
}

/* mobile (unended) */


.is-phone, .workspace-drawer {
  background-color: #000000;
  position: fixed;
  z-index: 10000000;
}

.mobile-toolbar-options-list {
  background-color: var(--tr);
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
  border-color: var(--tr);
}
.mobile-toolbar {
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
  background-color: var(--tr);
}
.mobile-navbar {
  background-color: #000000;
  backdrop-filter: brightness(var(--brightness)) blur(var(--set-blur));
  padding: var(--size-4-2) max(var(--safe-area-inset-right), var(--size-4-4)) max(var(--size-4-2), var(--safe-area-inset-bottom)) max(var(--safe-area-inset-left), var(--size-4-4));
  position: relative;
  z-index: 1;
}
.is-phone .workspace-split {
    padding-top: 0;
}
.is-phone .mobile-tab-switcher {
  backdrop-filter: blur(var(--set-blur));
}
