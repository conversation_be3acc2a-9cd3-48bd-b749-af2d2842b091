@charset "UTF-8";
body {
  /* ## Fonts */
  --font-text-theme: "<PERSON>", <PERSON><PERSON>, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Microsoft YaHei Light", sans-serif;
  --font-monospace-theme: "Roboto Mono", monospace, Menlo, SFMono-Regular, Consolas;
  --font-interface-theme: "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Inter", sans-serif ;
}

:root {
  /* ------------- */
  /* # TYPOGRAPHY */
  /* ------------- */
  /* ## Font Sizes */
  --smallest: 0.75em; /* Equal to px */
  --smaller: 0.875em; /* Equal to 14px */
  --h1: 2.25em;
  --h2: 2em;
  --h3: 1.75em;
  --h4: 1.5em;
  --h5: 1.375em;
  --h6: 1em;
  /* Roundness */
  --big-radius: 24px;
  --medium-radius: 16px;
  --small-radius: 8px;
  /* ## Font Weights */
  --light: 350;
  --regular: 400;
  --medium: 500;
  --bold: 650;
  /* ------------- */
  /* # COLOURS */
  /* ------------- */
  --elevation1: 0.05;
  --elevation2: 0.08;
  --elevation3: 0.11;
  --elevation4: 0.12;
  --elevation5: 0.14;
  --titlebar-text-color-focused: var(--text-normal);
  --link-color-hover: hsl(var(--accent-h), calc(var(--accent-s) * 0.8), 80%) !important;
  --link-external-color-hover: hsl(var(--accent-h), calc(var(--accent-s) * 0.8), 80%) !important;
}

.theme-light {
  --primary: #5F4BBD;
  --primary-r: 95;
  --primary-g: 75;
  --primary-b: 189;
  --on-primary: #FFFFFF;
  --primary-container: #E7DEFF;
  --on-primary-container: #1A0064;
  --secondary: #605C71;
  --on-secondary: #FFFFFF;
  --secondary-container: #E6DFF9;
  --on-secondary-container: #1C192C;
  --tertiary: #7C5264;
  --onTertiary: #FFFFFF;
  --tertiary-container: #FFD9E8;
  --on-tertiary-container: #301020;
  --error: #BA1B1B;
  --errorContainer: #FFDAD4;
  --onError: #FFFFFF;
  --onErrorContainer: #410001;
  --background: #FFFBFF;
  --on-background: #1C1B1E;
  --surface: #FFFBFF;
  --surface-r: 255;
  --surface-g: 251;
  --surface-b: 255;
  --surface: #FFFBFF;
  --on-surface: #1C1B1E;
  --surface-variant: #E5E0EB;
  --on-surface-variant: #48454F;
  --outline: #79767F;
  --inverse-on-surface: #F4EFF4;
  --inverse-surface: #313033;
  --primaryInverse: #CABEFF;
  --surface1: rgb(
    calc(var(--primary-r) * var(--elevation1) + var(--surface-r) * (1 - var(--elevation1))),
    calc(var(--primary-g) * var(--elevation1) + var(--surface-g) * (1 - var(--elevation1))),
    calc(var(--primary-b) * var(--elevation1) + var(--surface-b) * (1 - var(--elevation1)))
    );
  --surface2: rgb(
    calc(var(--primary-r) * var(--elevation2) + var(--surface-r) * (1 - var(--elevation2))),
    calc(var(--primary-g) * var(--elevation2) + var(--surface-g) * (1 - var(--elevation2))),
    calc(var(--primary-b) * var(--elevation2) + var(--surface-b) * (1 - var(--elevation2)))
    );
  --surface3: rgb(
    calc(var(--primary-r) * var(--elevation3) + var(--surface-r) * (1 - var(--elevation3))),
    calc(var(--primary-g) * var(--elevation3) + var(--surface-g) * (1 - var(--elevation3))),
    calc(var(--primary-b) * var(--elevation3) + var(--surface-b) * (1 - var(--elevation3)))
    );
  --surface4: rgb(
    calc(var(--primary-r) * var(--elevation4) + var(--surface-r) * (1 - var(--elevation4))),
    calc(var(--primary-g) * var(--elevation4) + var(--surface-g) * (1 - var(--elevation4))),
    calc(var(--primary-b) * var(--elevation4) + var(--surface-b) * (1 - var(--elevation4)))
    );
  --surface5: rgb(
    calc(var(--primary-r) * var(--elevation5) + var(--surface-r) * (1 - var(--elevation5))),
    calc(var(--primary-g) * var(--elevation5) + var(--surface-g) * (1 - var(--elevation5))),
    calc(var(--primary-b) * var(--elevation5) + var(--surface-b) * (1 - var(--elevation5)))
    );
  /* Standard colours */
  --background-primary: var(--background);
  --background-primary-alt: var(--surface1);
  --background-secondary: var(--surface-variant);
  --background-secondary-alt: var(--surface3);
  --background-modifier-border: #fff;
  --background-modifier-form-field: #fff;
  --background-modifier-form-field-highlighted: #fff;
  --background-modifier-box-shadow: rgba(0, 0, 0, 0.1);
  --background-modifier-success: #a4e7c3;
  --background-modifier-error: var(--error);
  --background-modifier-error-rgb: 230, 135, 135;
  --background-modifier-error-hover: #b00020;
  --background-modifier-cover: rgba(0, 0, 0, 0.8);
  --text-accent: var(--primary-container);
  --text-accent-hover: var(--primary-container);
  --text-normal: var(--on-background);
  --text-muted: var(--on-surface-variant);
  --text-muted-rgb: 136, 136, 136;
  --text-faint: var(--on-surface-variant);
  --text-error: #800000;
  --text-error-hover: #990000;
  --text-highlight-bg: var(--surface-variant);
  --text-highlight-bg-active: rgba(255, 128, 0, 0.4);
  --text-selection: rgba(204, 230, 255, 0.99);
  --interactive-normal: var(--surface-variant);
  --interactive-hover: var(--surface-variant);
  --interactive-accent: var(--primary);
  --interactive-accent-rgb: var(--primary);
  --interactive-accent-hover: var(--primary);
  --interactive-success: #197300;
  --scrollbar-active-thumb-bg: rgba(0, 0, 0, 0.2);
  --scrollbar-bg: rgba(0, 0, 0, 0.05);
  --scrollbar-thumb-bg: rgba(0, 0, 0, 0.1);
  --highlight-mix-blend-mode: darken;
  --focus: var(--surface4);
  --hover: var(--surface2);
  --text-on-accent: var(--text-muted);
  --on-accent: var(--text-muted);
  --hr-color: var(--surface-variant);
}

.theme-dark {
  --primary: #CABEFF;
  --primary-r: 202;
  --primary-g: 190;
  --primary-b: 255;
  --on-primary: #30128D;
  --primary-container: #4731A4;
  --on-primary-container: #E7DEFF;
  --secondary: #C9C3DC;
  --on-secondary: #312E41;
  --secondary-container: #484459;
  --on-secondary-container: #E6DFF9;
  --tertiary: #EDB8CD;
  --onTertiary: #482535;
  --tertiary-container: #623B4C;
  --on-tertiary-container: #FFD9E8;
  --error: #9c2b2b;
  --errorContainer: #930006;
  --onError: #680003;
  --onErrorContainer: #FFDAD4;
  --background: #1C1B1E;
  --on-background: #E5E1E5;
  --surface: #1C1B1E;
  --surface-r: 28;
  --surface-g: 27;
  --surface-b: 30;
  --on-surface: #E5E1E5;
  --surface-variant: #48454F;
  --on-surface-variant: #C9C4D0;
  --outline: #938F99;
  --inverse-on-surface: #1C1B1E;
  --inverse-surface: #E5E1E5;
  --primaryInverse: #5F4BBD;
  --on-primary: var(--on-primary);
  --primary-container: var(--primary-container);
  --on-primary-container: var(--on-primary-container);
  --on-secondary: var(--on-secondary);
  --secondary-container: var(--secondary-container);
  --on-secondary-container: var(--on-secondary-container);
  --on-tertiary: var(--onTertiary);
  --tertiary-container: var(--tertiary-container);
  --on-tertiary-container: var(--on-tertiary-container);
  --error-container: var(--errorContainer);
  --on-error: var(--onError);
  --on-error-container: var(--onErrorContainer);
  --on-background: var(--on-background);
  --on-surface: var(--on-surface);
  --surface-variant: var(--surface-variant);
  --on-surface-variant: var(--on-surface-variant);
  --inverse-on-surface: var(--inverse-on-surface);
  --inverse-surface: var(--inverse-surface);
  --primary-inverse: var(--primaryInverse);
  --surface1: rgb(
    calc(var(--primary-r) * var(--elevation1) + var(--surface-r) * (1 - var(--elevation1))),
    calc(var(--primary-g) * var(--elevation1) + var(--surface-g) * (1 - var(--elevation1))),
    calc(var(--primary-b) * var(--elevation1) + var(--surface-b) * (1 - var(--elevation1)))
    );
  --surface2: rgb(
    calc(var(--primary-r) * var(--elevation2) + var(--surface-r) * (1 - var(--elevation2))),
    calc(var(--primary-g) * var(--elevation2) + var(--surface-g) * (1 - var(--elevation2))),
    calc(var(--primary-b) * var(--elevation2) + var(--surface-b) * (1 - var(--elevation2)))
    );
  --surface3: rgb(
    calc(var(--primary-r) * var(--elevation3) + var(--surface-r) * (1 - var(--elevation3))),
    calc(var(--primary-g) * var(--elevation3) + var(--surface-g) * (1 - var(--elevation3))),
    calc(var(--primary-b) * var(--elevation3) + var(--surface-b) * (1 - var(--elevation3)))
    );
  --surface4: rgb(
    calc(var(--primary-r) * var(--elevation4) + var(--surface-r) * (1 - var(--elevation4))),
    calc(var(--primary-g) * var(--elevation4) + var(--surface-g) * (1 - var(--elevation4))),
    calc(var(--primary-b) * var(--elevation4) + var(--surface-b) * (1 - var(--elevation4)))
    );
  --surface5: rgb(
    calc(var(--primary-r) * var(--elevation5) + var(--surface-r) * (1 - var(--elevation5))),
    calc(var(--primary-g) * var(--elevation5) + var(--surface-g) * (1 - var(--elevation5))),
    calc(var(--primary-b) * var(--elevation5) + var(--surface-b) * (1 - var(--elevation5)))
    );
  --focus: var(--surface4);
  --hover: var(--surface2);
  /* Standard Colours */
  --background-primary: var(--background);
  --background-primary-alt: var(--surface1);
  --background-secondary: var(--surface-variant);
  --background-secondary-alt: var(--surface3);
  --background-modifier-border: var(--surface);
  --background-modifier-form-field: rgba(0, 0, 0, 0.3);
  --background-modifier-form-field-highlighted: rgba(0, 0, 0, 0.22);
  --background-modifier-box-shadow: rgba(0, 0, 0, 0.3);
  --background-modifier-success: #197300;
  --background-modifier-error: var(--error);
  --background-modifier-error-rgb: 61, 0, 0;
  --background-modifier-error-hover: #470000;
  --background-modifier-cover: rgba(0, 0, 0, 0.8);
  --text-accent: var(--primary-container);
  --text-accent-hover: var(--primary-container);
  --text-normal: var(--on-background);
  --text-muted: var(--on-surface-variant);
  --text-muted-rgb: 153, 153, 153;
  --text-error: #cf6679;
  --text-error-hover: #990000;
  --text-highlight-bg: var(--surface-variant);
  --text-highlight-bg-active: rgba(255, 128, 0, 0.4);
  --text-selection: rgba(23, 48, 77, 0.99);
  --interactive-normal: var(--surface-variant);
  --interactive-hover: var(--surface-variant);
  --interactive-accent: var(--primary);
  --interactive-accent-rgb: var(--primary);
  --interactive-accent-hover: var(--primary);
  --interactive-success: #197300;
  --scrollbar-active-thumb-bg: rgba(255, 255, 255, 0.2);
  --scrollbar-bg: rgba(255, 255, 255, 0.05);
  --scrollbar-thumb-bg: rgba(255, 255, 255, 0.1);
  --highlight-mix-blend-mode: lighten;
  --text-on-accent: var(--background);
  --on-accent: var(--background);
  --hr-color: var(--surface-variant);
}

.cm-formatting-header {
  color: var(--surface-variant) !important;
}

.markdown-reading-view h1,
.cm-s-obsidian .cm-header-1,
.mod-cm6 .cm-editor .HyperMD-header-1 {
  font-size: var(--h1);
  font-weight: var(--regular);
}

.markdown-reading-view h2,
.cm-s-obsidian .cm-header-2,
.mod-cm6 .cm-editor .HyperMD-header-2 {
  font-size: var(--h2);
  font-weight: var(--regular);
}

.markdown-reading-view h3,
.cm-s-obsidian .cm-header-3,
.mod-cm6 .cm-editor .HyperMD-header-3 {
  font-size: var(--h3);
  font-weight: var(--regular);
}

.markdown-reading-view h4,
.cm-s-obsidian .cm-header-4,
.mod-cm6 .cm-editor .HyperMD-header-4 {
  font-size: var(--h4);
  font-weight: var(--regular);
}

.markdown-reading-view h5,
.cm-s-obsidian .cm-header-5,
.mod-cm6 .cm-editor .HyperMD-header-5 {
  line-height: 1.75em;
  font-size: var(--h5);
  font-weight: var(--regular);
}

.markdown-reading-view h6,
.cm-s-obsidian .cm-header-6,
.mod-cm6 .cm-editor .HyperMD-header-6 {
  line-height: 1.5em;
  font-size: var(--h6);
  letter-spacing: 0.009375em;
  font-weight: var(--medium);
}

/* ------------- */
/* # Body */
/* ------------- */
Body Text .markdown-preview-view,
.markdown-source-view,
.cm-line {
  line-height: 1.5em;
  letter-spacing: 0.009375em;
  font-weight: var(--medium);
}

strong,
.cm-s-obsidian .cm-strong {
  font-weight: var(--bold);
}

/* Checkboxes */
/* Images */
img {
  border-radius: var(--small-radius);
}

span.cm-formatting.cm-formatting-list.cm-formatting-list-ul.cm-list-1,
span.cm-formatting.cm-formatting-list.cm-formatting-list-ol.cm-list-1 {
  color: var(--secondary);
}

/* callout boxes */
.cm-callout, div.callout {
  border-radius: var(--radius-l);
}

/* Blockquotes */
.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote {
  margin-top: 13px !important;
  color: var(--on-surface-variant);
  margin-bottom: -13px !important;
}
.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote::before {
  background-color: var(--surface-variant);
  display: block !important;
  content: "test" !important;
  border: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  position: absolute;
  left: 0px;
  right: 0;
  width: 100%;
  top: calc(-1 * var(--radius-l) + 2px);
  bottom: calc(-1 * var(--radius-l) + 2px);
  border-radius: var(--radius-l);
  z-index: -1;
}
.markdown-source-view.mod-cm6.is-live-preview .cm-transparent {
  color: transparent !important;
}

.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote + .HyperMD-quote {
  padding-top: inherit;
}
.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote + .HyperMD-quote span:not(.math) ::before {
  top: calc(-1 * var(--radius-l));
}

.markdown-preview-view blockquote {
  border-radius: var(--radius-l);
  border: none;
  background-color: var(--surface-variant);
  color: var(--on-surface-variant) !important;
  margin-top: 1px;
  margin-bottom: 6px;
  padding-left: 15px;
}

.markdown-preview-view blockquote br {
  display: block;
  -moz-column-break-before: always;
       break-before: always;
  -moz-column-break-after: always;
       break-after: always;
}

.markdown-preview-view blockquote::before,
.markdown-preview-view blockquote::after {
  display: block;
  content: "\a";
  height: 8px;
}

.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote:before, .markdown-source-view.mod-cm6 .cm-blockquote-border:before {
  display: none;
}

/* Lists */
ul > li::marker,
ol > li::marker {
  color: var(--text-muted);
}

/* Internal links */
.markdown-preview-view .internal-link,
.cm-s-obsidian .cm-formatting-link,
.cm-s-obsidian span.cm-link,
.cm-s-obsidian span.cm-hmd-internal-link,
.markdown-source-view.mod-cm6 .cm-underline {
  text-decoration: none;
  font-weight: var(--medium);
}

/* Frontmatter Edit Mode */
.markdown-preview-section .frontmatter code,
.frontmatter .token,
.cm-s-obsidian .cm-hmd-frontmatter,
pre.frontmatter[class*=language-yaml],
span.cm-hmd-frontmatter,
span.cm-def.cm-hmd-frontmatter {
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.015625em;
  font-weight: var(--medium);
  color: var(--secondary);
  font-family: var(--default-font);
}

/* Frontmatter / Metadata container */
.frontmatter-container {
  border: 1px solid var(--surface-variant);
  background-color: var(--surface);
  transition: border 250ms ease-in;
}
.frontmatter-container.is-collapsed {
  border: 0;
  color: var(--on-surface);
  background-color: var(--surface);
  transition: border 450ms ease-out;
}
.frontmatter-container .frontmatter-container-header {
  text-transform: none;
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.00625em;
  font-weight: var(--medium);
  color: var(--secondary);
  border: none;
}
.frontmatter-container .frontmatter-container-header:hover {
  color: var(--secondary);
}
.frontmatter-container .frontmatter-collapse-indicator {
  color: var(--secondary);
}
.frontmatter-container .frontmatter-collapse-indicator:hover {
  color: var(--secondary);
}
.frontmatter-container .tag {
  background-color: var(--secondary-container);
  color: var(--on-secondary-container);
}
.frontmatter-container .frontmatter-alias {
  background-color: var(--secondary-container);
  color: var(--on-secondary-container);
}
.frontmatter-container .frontmatter-alias-icon {
  color: var(--on-secondary-container);
}

.frontmatter-section {
  line-height: 1em;
  font-size: 0.75em;
  letter-spacing: 0.03125em;
  font-weight: var(--medium);
}

/* Code blocks */
/* Inline */
.markdown-preview-view code,
.cm-s-obsidian span.cm-inline-code,
.cm-s-obsidian span.cm-inline-code:not(.cm-formatting):not(.cm-hmd-indented-code):not(.obsidian-search-match-highlight) {
  background-color: var(--surface-variant);
  color: var(--on-surface-variant);
  font-family: var(--font-monospace);
  border-radius: var(--radius-s);
  margin: -2px;
  padding: 2px 4px;
  vertical-align: 2px;
}

.cm-highlight, .markdown-preview-view mark {
  background-color: var(--surface-variant);
  color: var(--on-surface-variant) !important;
  border-radius: var(--radius-s);
  margin: -2px 0;
  padding: 0 4px;
}

span.cm-highlight + img + span.math { /*Math in highlight*/
  background-color: var(--surface-variant);
  color: var(--on-surface-variant) !important;
  border-radius: var(--radius-s);
  margin: -2px -5px;
  padding: 0 5px;
}

.cm-s-obsidian .HyperMD-codeblock {
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.015625em;
  font-weight: var(--medium);
  color: var(--on-surface-variant);
  font-family: var(--font-monospace);
}

.markdown-preview-view pre {
  border-radius: var(--radius-l) !important;
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.015625em;
  font-weight: var(--medium);
  background-color: var(--surface-variant);
  color: var(--on-surface-variant);
  font-family: var(--font-monospace);
}

div.HyperMD-codeblock-begin-bg {
  border-top-left-radius: var(--radius-l) !important;
  border-top-right-radius: var(--radius-l) !important;
}

div.HyperMD-codeblock-end-bg {
  border-bottom-left-radius: var(--radius-l) !important;
  border-bottom-right-radius: var(--radius-l) !important;
}

.cm-s-obsidian div.HyperMD-codeblock-begin-bg,
.cm-s-obsidian div.HyperMD-codeblock-end-bg,
.cm-s-obsidian div.HyperMD-codeblock-bg {
  background-color: var(--surface-variant);
  border: none;
}

/* Empty State screen */
.empty-state-title::before {
  content: " ";
  background-size: 45vw 45vw;
  height: 45vw;
  width: 45vw;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' data-name='Layer 1' width='578.0013' height='621.92557' viewBox='0 0 578.0013 621.92557' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M677.64241,491.89724l-249.84894,64.339a33.03735,33.03735,0,0,1-40.18683-23.728L312.04574,239.081a33.03734,33.03734,0,0,1,23.728-40.18683l232.44363-59.85691L627.712,165.67105l73.65843,286.03936A33.03734,33.03734,0,0,1,677.64241,491.89724Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath d='M338.14273,208.094a23.52653,23.52653,0,0,0-16.89723,28.61789l75.5609,293.42725a23.52654,23.52654,0,0,0,28.6179,16.89723l249.84894-64.339a23.52654,23.52654,0,0,0,16.89723-28.61789l-72.51713-281.6073-52.285-23.40643Z' transform='translate(-310.99935 -139.03722)' fill='%23fff'/%3E%3Cpath d='M627.07719,167.18472l-38.66749,9.95733a10.99077,10.99077,0,0,1-13.38436-7.9027L567.671,140.68008a.68692.68692,0,0,1,.944-.7991l58.56966,26.01073A.68692.68692,0,0,1,627.07719,167.18472Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath d='M606.73438,417.76533,492.98165,447.058a5.49538,5.49538,0,0,1-2.74083-10.64353L603.99355,407.1218a5.49538,5.49538,0,1,1,2.74083,10.64353Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3Cpath d='M633.31182,430.07333l-135.705,34.9456A5.49538,5.49538,0,0,1,494.866,454.3754l135.705-34.94561a5.49539,5.49539,0,0,1,2.74084,10.64354Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3Ccircle id='a597741b-ffcf-4aba-98b0-6652ef5d57c9' data-name='Ellipse 44' cx='135.55495' cy='323.87767' r='19.42315' fill='%23e6e6e6'/%3E%3Cpath d='M594.63919,366.93361,443.56425,405.8227A17.01917,17.01917,0,0,1,422.863,393.59731l-31.6597-122.9905a17.01916,17.01916,0,0,1,12.22538-20.7012l151.075-38.88909a17.01916,17.01916,0,0,1,20.7012,12.22539l31.65971,122.9905A17.01917,17.01917,0,0,1,594.63919,366.93361Z' transform='translate(-310.99935 -139.03722)' fill='%23fff'/%3E%3Cpath d='M594.63919,366.93361,443.56425,405.8227A17.01917,17.01917,0,0,1,422.863,393.59731l-31.6597-122.9905a17.01916,17.01916,0,0,1,12.22538-20.7012l151.075-38.88909a17.01916,17.01916,0,0,1,20.7012,12.22539l31.65971,122.9905A17.01917,17.01917,0,0,1,594.63919,366.93361ZM403.9273,251.84246a15.017,15.017,0,0,0-10.7871,18.26578l31.6597,122.9905a15.017,15.017,0,0,0,18.26577,10.7871l151.075-38.88908a15.017,15.017,0,0,0,10.7871-18.26578L573.268,223.74048a15.017,15.017,0,0,0-18.26578-10.7871Z' transform='translate(-310.99935 -139.03722)' fill='%23e6e6e6'/%3E%3Cpath id='b056fd3f-f1a0-44f0-b006-deff0bee637d-685' data-name='Path 411' d='M546.83934,252.37075l-76.24555,19.62681a2.73087,2.73087,0,0,1-3.30848-1.71854,2.63064,2.63064,0,0,1,1.85283-3.33925l77.61329-19.97889c3.13521,1.58858,2.31023,4.83781.087,5.41011Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath id='f1ea6668-a825-428d-96fe-a2c4e1b5a672-686' data-name='Path 412' d='M550.282,265.74474l-76.24555,19.62681A2.73089,2.73089,0,0,1,470.728,283.653a2.63065,2.63065,0,0,1,1.85284-3.33925l77.61329-19.97889c3.13521,1.58858,2.31022,4.83781.087,5.41011Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath id='ee9aa382-a9c9-40d0-8ed3-22ec2dd616d6-687' data-name='Path 413' d='M459.51412,297.22917l-23.2694,5.98992a2.962,2.962,0,0,1-3.60325-2.12795l-7.06858-27.45979a2.962,2.962,0,0,1,2.12794-3.60325l23.2694-5.98991a2.963,2.963,0,0,1,3.60325,2.12795l7.06859,27.45982a2.962,2.962,0,0,1-2.12795,3.60324Z' transform='translate(-310.99935 -139.03722)' fill='%23e6e6e6'/%3E%3Cpath id='be954d2b-d8b8-4d26-80a0-a319e99a4b10-688' data-name='Path 414' d='M557.10914,293.18514,440.74446,323.13925a2.73087,2.73087,0,0,1-3.30847-1.71854,2.63062,2.63062,0,0,1,1.85284-3.33925L557.02218,287.775c3.13521,1.58859,2.31022,4.83781.087,5.41012Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath id='baaae9e4-1b4d-40c2-8a9d-f2abb078b489-689' data-name='Path 415' d='M560.55283,306.563,444.18814,336.51715a2.73086,2.73086,0,0,1-3.30846-1.71854,2.63061,2.63061,0,0,1,1.85283-3.33926l117.73335-30.30643c3.13521,1.58858,2.31022,4.83781.087,5.41011Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath id='a91bf4c9-37f6-4391-92ed-1882bd0ce21c-690' data-name='Path 416' d='M563.99426,319.93218,447.62957,349.8863a2.73086,2.73086,0,0,1-3.30846-1.71854,2.63061,2.63061,0,0,1,1.85283-3.33926l117.73335-30.30643c3.13521,1.58858,2.31023,4.83781.087,5.41011Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath id='efb98e07-468b-4c85-9a64-ee4cc5493d6f-691' data-name='Path 417' d='M567.43768,333.30908,451.073,363.2632a2.73087,2.73087,0,0,1-3.30847-1.71854,2.63063,2.63063,0,0,1,1.85284-3.33926L567.35072,327.899c3.13521,1.58858,2.31022,4.83781.087,5.41011Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath id='aeb1db98-32e5-40b8-ab89-fdad6a3263dc-692' data-name='Path 418' d='M570.87937,346.67924,454.51469,376.63336a2.73088,2.73088,0,0,1-3.30847-1.71855,2.63062,2.63062,0,0,1,1.85284-3.33925l117.73335-30.30643c3.13521,1.58858,2.31022,4.83781.087,5.41011Z' transform='translate(-310.99935 -139.03722)' fill='%23f2f2f2'/%3E%3Cpath id='be265de5-288f-49a7-867d-c42e7cdbf4db-693' data-name='Path 395' d='M447.98728,469.72335a2.01449,2.01449,0,0,1-1.27407-.08782l-.02505-.01034L441.3969,467.382a2.02852,2.02852,0,1,1,1.58747-3.73356l3.42865,1.45835,4.49293-10.56929a2.02766,2.02766,0,0,1,2.65942-1.07259l.00068.00028-.027.06912.02812-.06941a2.03011,2.03011,0,0,1,1.0723,2.66008l-5.28586,12.42716a2.02886,2.02886,0,0,1-1.36522,1.16845Z' transform='translate(-310.99935 -139.03722)' fill='%23fff'/%3E%3Cpath d='M773.47457,603.71475h-258a33.03734,33.03734,0,0,1-33-33v-303a33.03734,33.03734,0,0,1,33-33H755.50142l50.97315,40.62891V570.71475A33.03734,33.03734,0,0,1,773.47457,603.71475Z' transform='translate(-310.99935 -139.03722)' fill='%23e6e6e6'/%3E%3Cpath d='M515.47446,244.21475a23.52654,23.52654,0,0,0-23.5,23.5v303a23.52653,23.52653,0,0,0,23.5,23.5h258a23.52653,23.52653,0,0,0,23.5-23.5V279.92032l-44.79614-35.70557Z' transform='translate(-310.99935 -139.03722)' fill='%23fff'/%3E%3Cpath d='M723.29356,332.319H605.82977a5.49538,5.49538,0,0,1,0-10.99076H723.29356a5.49538,5.49538,0,1,1,0,10.99076Z' transform='translate(-310.99935 -139.03722)' fill='%23878787'/%3E%3Cpath d='M745.962,350.86594H605.82977a5.49539,5.49539,0,0,1,0-10.99077H745.962a5.49539,5.49539,0,1,1,0,10.99077Z' transform='translate(-310.99935 -139.03722)' fill='%23878787'/%3E%3Cpath d='M723.29425,404.44277h-117.46a5.495,5.495,0,1,0,0,10.99h117.46a5.495,5.495,0,0,0,0-10.99Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3Cpath d='M745.96417,422.99281H605.83429a5.495,5.495,0,1,0,0,10.99H745.96417a5.495,5.495,0,0,0,0-10.99Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3Cpath d='M723.29356,498.55433H605.82977a5.49538,5.49538,0,0,1,0-10.99076H723.29356a5.49538,5.49538,0,1,1,0,10.99076Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3Cpath d='M745.962,517.10125H605.82977a5.49539,5.49539,0,0,1,0-10.99077H745.962a5.49539,5.49539,0,1,1,0,10.99077Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3Ccircle id='abdb8e2f-a218-463c-85f4-c869fef49971' data-name='Ellipse 44' cx='245.91553' cy='197.05988' r='19.42315' fill='%23878787'/%3E%3Cpath id='ba7dbbd6-0052-44b1-a552-47a8298b8d3e-694' data-name='Path 395' d='M554.99015,343.50645a2.0144,2.0144,0,0,1-1.21191-.40277l-.02168-.01626-4.5647-3.49185a2.02852,2.02852,0,1,1,2.46838-3.21972l2.95665,2.26729,6.98671-9.11494a2.02767,2.02767,0,0,1,2.84288-.3755l.00058.00044-.04336.06021.04454-.06021a2.03011,2.03011,0,0,1,.37507,2.84345l-8.2179,10.71637a2.02892,2.02892,0,0,1-1.61348.79109Z' transform='translate(-310.99935 -139.03722)' fill='%23fff'/%3E%3Cpath d='M578.33429,419.21278a19.42256,19.42256,0,0,1-19.41992,19.43,4.17626,4.17626,0,0,1-.5-.02,19.422,19.422,0,1,1,19.91992-19.41Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3Ccircle id='e4a71040-498e-4958-ad41-c2d79154b8f7' data-name='Ellipse 44' cx='245.91553' cy='363.29519' r='19.42315' fill='%23ccc'/%3E%3Cpath d='M805.48234,276.65121h-39.929a10.99077,10.99077,0,0,1-10.99076-10.99077v-29.491a.68692.68692,0,0,1,1.11347-.53844l50.23281,39.79483A.68692.68692,0,0,1,805.48234,276.65121Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3Cpath d='M578.33429,419.21278a19.42256,19.42256,0,0,1-19.41992,19.43,4.17626,4.17626,0,0,1-.5-.02,42.05076,42.05076,0,0,1,3.77-38.56A19.43323,19.43323,0,0,1,578.33429,419.21278Z' transform='translate(-310.99935 -139.03722)' fill='%23878787'/%3E%3Cpath d='M600.33429,409.94277a5.50988,5.50988,0,0,1,5.5-5.5h29.27a41.57257,41.57257,0,0,1,3.60986,10.99H605.83429A5.50129,5.50129,0,0,1,600.33429,409.94277Z' transform='translate(-310.99935 -139.03722)' fill='%23878787'/%3E%3Cpath d='M639.39435,422.99281a41.92449,41.92449,0,0,1-1.46,10.99h-32.1001a5.495,5.495,0,1,1,0-10.99Z' transform='translate(-310.99935 -139.03722)' fill='%23878787'/%3E%3Cpath d='M711.816,490.77021a6.00013,6.00013,0,0,1-8.24672,1.9979l-70.0049-42.70029a6,6,0,0,1,6.24883-10.24462l70.00489,42.70029A6.00014,6.00014,0,0,1,711.816,490.77021Z' transform='translate(-310.99935 -139.03722)' fill='%233f3d56'/%3E%3Cpath d='M641.8111,448.06992a48,48,0,1,1-15.98318-65.97376A48.05436,48.05436,0,0,1,641.8111,448.06992Zm-71.71233-43.74176a36,36,0,1,0,49.48033-11.98738A36.04071,36.04071,0,0,0,570.09877,404.32816Z' transform='translate(-310.99935 -139.03722)' fill='%233f3d56'/%3E%3Ccircle cx='484.60301' cy='267.17256' r='24.56103' fill='%23a0616a'/%3E%3Cpath d='M794.015,543.90119a11.002,11.002,0,0,1,8.32251-14.15136,10.46752,10.46752,0,0,1,1.45923-.17969l25.87158-41.52344L806.036,465.57991a9.43208,9.43208,0,1,1,13.1206-13.55274L851.786,484.201l.06567.08008a8.54468,8.54468,0,0,1-.59448,10.18457l-36.25,42.873a10.301,10.301,0,0,1,.27,1.0459,11.0026,11.0026,0,0,1-9.875,13.11621q-.46839.041-.93213.041A11.0367,11.0367,0,0,1,794.015,543.90119Z' transform='translate(-310.99935 -139.03722)' fill='%23a0616a'/%3E%3Cpolygon points='444.151 609.001 431.891 609 426.059 561.712 444.153 561.713 444.151 609.001' fill='%23a0616a'/%3E%3Cpath d='M758.2767,759.92208l-39.53051-.00146v-.5a15.38605,15.38605,0,0,1,15.38647-15.38623h.001l24.1438.001Z' transform='translate(-310.99935 -139.03722)' fill='%232f2e41'/%3E%3Cpolygon points='566.007 592.05 555.473 598.322 526.268 560.676 541.815 551.419 566.007 592.05' fill='%23a0616a'/%3E%3Cpath d='M885.77238,739.69878l-33.96586,20.2233-.25581-.4296a15.386,15.386,0,0,1,5.34836-21.09206l.00084-.0005,20.74515-12.35158Z' transform='translate(-310.99935 -139.03722)' fill='%232f2e41'/%3E%3Cpath d='M735.512,727.21272c-8.21289-96.70606-13.09863-186.54492,16.92408-223.19336l.23217-.28418,50.52564,20.21094.08325.18066c.17041.37109,16.97388,37.333,13.0542,62.19434L828.794,643.64924l40.63574,68.041A4.50136,4.50136,0,0,1,867.379,718.118l-17.65918,7.76953a4.52142,4.52142,0,0,1-5.64844-1.76562l-44.2041-72.08008-24.96778-55.28613a1.50028,1.50028,0,0,0-2.85888.459L758.20052,727.31135a4.4918,4.4918,0,0,1-4.47461,4.02441H739.99764A4.53045,4.53045,0,0,1,735.512,727.21272Z' transform='translate(-310.99935 -139.03722)' fill='%232f2e41'/%3E%3Cpath d='M752.60628,504.786l-.24072-.11523-.0376-.26465c-1.88745-13.21.34668-27.8877,6.63989-43.625a34.63634,34.63634,0,0,1,40.20191-20.74317h0a34.59441,34.59441,0,0,1,22.06055,16.96387,34.2209,34.2209,0,0,1,2.3728,27.4248c-7.93384,23.2002-18.22583,44.90723-18.32886,45.124l-.21558.45312Z' transform='translate(-310.99935 -139.03722)' fill='%23878787'/%3E%3Cpath d='M697.48021,471.88251A11.002,11.002,0,0,1,713.65,474.72233a10.46856,10.46856,0,0,1,.67932,1.3039l47.95411,9.69217,12.7683-30.00357a9.43208,9.43208,0,1,1,17.28928,7.54372l-18.71,41.83025-.052.08956a8.54469,8.54469,0,0,1-9.74785,3.00972L710.97846,489.2473a10.30273,10.30273,0,0,1-.88511.61918,11.00261,11.00261,0,0,1-15.74382-4.6565q-.20244-.42436-.36484-.85874A11.0367,11.0367,0,0,1,697.48021,471.88251Z' transform='translate(-310.99935 -139.03722)' fill='%23a0616a'/%3E%3Cpath d='M884.33087,432.51247c-2.36572-4.19178-5.8125-8.03119-10.36914-9.58069-5.88476-2.001-12.25683.12964-18.30713,1.55218-4.66162,1.09595-9.53173,1.76679-14.23046.84192-4.69825-.92492-9.23047-3.65924-11.36817-7.94409-3.145-6.30359-.4956-13.82062-.687-20.86255a25.33438,25.33438,0,0,0-31.92334-23.81061c-5.79346-1.67193-11.03906-1.82659-14.62256,2.62714a17.0001,17.0001,0,0,0-17,17h16.25537a16.1496,16.1496,0,0,0,2.4541,11.93109c2.86963,4.21582,7.85938,7.2655,8.81983,12.274.93115,4.85351-2.36817,9.45868-5.8291,12.98669-3.46045,3.528-7.4751,7.02381-8.55567,11.84626a14.68871,14.68871,0,0,0,2.10352,10.56475,34.40329,34.40329,0,0,0,7.38623,8.13575,108.40184,108.40184,0,0,0,45.0376,23.04034c11.7041,2.81781,24.50586,3.54822,35.37109-1.6355a35.12563,35.12563,0,0,0,15.46484-48.96667Z' transform='translate(-310.99935 -139.03722)' fill='%232f2e41'/%3E%3Cpath d='M888.00065,760.96278h-190a1,1,0,0,1,0-2h190a1,1,0,0,1,0,2Z' transform='translate(-310.99935 -139.03722)' fill='%23ccc'/%3E%3C/svg%3E");
  display: block;
  margin: 10px;
}

.empty-state-title {
  transform: translateY(-25px);
  line-height: 1.5em;
  letter-spacing: 0.009375em;
  font-weight: var(--medium);
  text-align: center;
  color: var(--on-surface);
}

.is-mobile .empty-state-action:nth-child(2) {
  transform: translate(0px, -60px);
  background-color: var(--primary);
  color: var(--on-primary);
  text-transform: none;
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.00625em;
  font-weight: var(--medium);
  height: 40px;
  border-radius: 1.25em;
  padding-top: 10px;
  padding-left: 1.5em;
  padding-right: 1.5em;
  display: block;
  margin: auto;
  padding-left: 16px;
  padding-right: 24px;
  vertical-align: middle;
}
.is-mobile .empty-state-action:nth-child(2)::before {
  content: "";
  height: 1.125em;
  width: 1.125em;
  background-size: 1.125em 1.125em;
  background-position: center;
  background-color: var(--on-primary);
  display: inline-block;
  margin-top: -4px;
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z' /%3E%3C/svg%3E");
          mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z' /%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z' /%3E%3C/svg%3E");
  -webkit-mask-size: cover;
  mask-size: cover;
  transform: translateY(4px);
  margin-right: 8px;
}
.is-mobile .empty-state-action:first-of-type {
  transform: translate(0px, 30px);
  background-color: inherit;
  color: var(--primary);
  text-transform: none;
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.00625em;
  font-weight: var(--medium);
  height: 40px;
  border-radius: 1.25em;
  border-width: 0.0625em;
  border-color: var(--outline);
  padding-top: 9px;
  padding-left: 1.5em;
  padding-right: 1.5em;
  display: block;
  margin: auto;
  padding-left: 16px;
  padding-right: 24px;
  vertical-align: middle;
}
.is-mobile .empty-state-action:first-of-type::before {
  content: "";
  height: 1.125em;
  width: 1.125em;
  background-size: 1.125em 1.125em;
  background-position: center;
  background-color: var(--primary);
  display: inline-block;
  margin-top: -4px;
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z' /%3E%3C/svg%3E");
          mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z' /%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z' /%3E%3C/svg%3E");
  -webkit-mask-size: cover;
  mask-size: cover;
  transform: translateY(4px);
  margin-right: 8px;
}
.is-mobile .empty-state-action:nth-child(3) {
  background-color: inherit;
  color: var(--primary);
  text-transform: none;
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.00625em;
  font-weight: var(--medium);
  height: 40px;
  border-radius: 1.25em;
  border-width: 0.0625em;
  border-color: var(--outline);
  padding-top: 9px;
  padding-left: 1.5em;
  padding-right: 1.5em;
  display: block;
  margin: auto;
  padding-left: 16px;
  padding-right: 24px;
  vertical-align: middle;
}
.is-mobile .empty-state-action:nth-child(3)::before {
  content: "";
  height: 1.125em;
  width: 1.125em;
  background-size: 1.125em 1.125em;
  background-position: center;
  background-color: var(--primary);
  display: inline-block;
  margin-top: -4px;
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3' /%3E%3C/svg%3E");
          mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3' /%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3' /%3E%3C/svg%3E");
  -webkit-mask-size: cover;
  mask-size: cover;
  transform: translateY(4px);
  margin-right: 8px;
}

progress {
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
}

progress::-webkit-progress-bar {
  border-radius: var(--medium-radius);
  background: var(--primary-container);
  height: 0.6em;
}

progress::-webkit-progress-value {
  border-radius: var(--medium-radius);
  background: var(--on-primary-container);
  height: 0.6em;
}

/* Mobile Toolbar */
.is-mobile .mobile-toolbar {
  border-top: none;
  border-radius: 0;
}

/* Tags */
/* Adapted from https://github.com/kepano/obsidian-minimal/blob/master/obsidian.css */
/* Preview */
a.tag {
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.015625em;
  font-weight: var(--medium);
  background-color: var(--tertiary-container);
  color: var(--on-tertiary-container);
  padding: 2px 7px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  margin: 2px 0 0 0;
  border-radius: var(--medium-radius);
}

/* Editor */
.cm-s-obsidian span.cm-hashtag {
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.015625em;
  font-weight: var(--medium);
  font-size: 0.875em !important;
  background-color: var(--tertiary-container);
  color: var(--on-tertiary-container);
  text-align: center;
  text-decoration: none;
  display: inline-block;
  margin: 2px 0;
  padding-top: 1px;
  border-left: none;
  border-right: none;
  padding-bottom: 2px;
  cursor: text;
}
.cm-s-obsidian span.cm-hashtag.cm-hashtag-begin {
  border-top-left-radius: var(--medium-radius);
  border-bottom-left-radius: var(--medium-radius);
  padding-left: 8px;
  border-right: none;
}
.cm-s-obsidian span.cm-hashtag.cm-hashtag-end {
  border-top-right-radius: var(--medium-radius);
  border-bottom-right-radius: var(--medium-radius);
  border-left: none;
  padding-right: 8px;
}

/* Hide the # character in front of a tag in Preview */
/* Thanks to `@Klaas`: https://discord.com/channels/686053708261228577/702656734631821413/890122749459197993 */
/* Currently not working, review later */
/* a.tag {
  position: relative;
  padding-left: 3px;
}

a.tag:after {
  background: var(--background-primary);
  position: absolute;
  content: " ";
  top: 0;
  bottom: 0;
  width: 0.66em;
  left: 0px;
  border-radius: 4px;
} */
.markdown-preview-view table thead tr th, .markdown-preview-view table tbody tr th,
.markdown-source-view.mod-cm6.is-live-preview table thead tr th,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr th {
  background: var(--surface-variant) !important;
  color: var(--on-surface-variant);
}
.markdown-preview-view table thead tr td, .markdown-preview-view table thead tr td:nth-of-type(even), .markdown-preview-view table thead tr td:nth-of-type(odd), .markdown-preview-view table tbody tr td, .markdown-preview-view table tbody tr td:nth-of-type(even), .markdown-preview-view table tbody tr td:nth-of-type(odd),
.markdown-source-view.mod-cm6.is-live-preview table thead tr td,
.markdown-source-view.mod-cm6.is-live-preview table thead tr td:nth-of-type(even),
.markdown-source-view.mod-cm6.is-live-preview table thead tr td:nth-of-type(odd),
.markdown-source-view.mod-cm6.is-live-preview table tbody tr td,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr td:nth-of-type(even),
.markdown-source-view.mod-cm6.is-live-preview table tbody tr td:nth-of-type(odd) {
  background: var(--surface);
  color: var(--on-surface);
  padding-top: 1px;
  padding-bottom: 1px;
}
.markdown-preview-view table thead tr th, .markdown-preview-view table thead tr td, .markdown-preview-view table tbody tr th, .markdown-preview-view table tbody tr td,
.markdown-source-view.mod-cm6.is-live-preview table thead tr th,
.markdown-source-view.mod-cm6.is-live-preview table thead tr td,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr th,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr td {
  border: 3px solid transparent;
  height: var(--radius-l);
  width: var(--radius-l);
}
.markdown-preview-view table thead tr th:first-of-type, .markdown-preview-view table tbody tr th:first-of-type,
.markdown-source-view.mod-cm6.is-live-preview table thead tr th:first-of-type,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr th:first-of-type {
  border-top-left-radius: var(--radius-l);
}
.markdown-preview-view table thead tr th:last-of-type, .markdown-preview-view table tbody tr th:last-of-type,
.markdown-source-view.mod-cm6.is-live-preview table thead tr th:last-of-type,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr th:last-of-type {
  border-top-right-radius: var(--radius-l);
}
.markdown-preview-view table thead tr:last-of-type td:first-of-type, .markdown-preview-view table tbody tr:last-of-type td:first-of-type,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:last-of-type td:first-of-type,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:last-of-type td:first-of-type {
  border-bottom-left-radius: var(--radius-l);
}
.markdown-preview-view table thead tr:last-of-type td:last-of-type, .markdown-preview-view table tbody tr:last-of-type td:last-of-type,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:last-of-type td:last-of-type,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:last-of-type td:last-of-type {
  border-bottom-right-radius: var(--radius-l);
}
.markdown-preview-view table thead tr:nth-child(even) th, .markdown-preview-view table thead tr:nth-child(even) td, .markdown-preview-view table tbody tr:nth-child(even) th, .markdown-preview-view table tbody tr:nth-child(even) td,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(even) th,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(even) td,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(even) th,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(even) td {
  background-color: var(--surface-mid);
}
.markdown-preview-view table thead tr:nth-child(even) table, .markdown-preview-view table tbody tr:nth-child(even) table,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(even) table,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(even) table {
  margin-bottom: 10px;
}
.markdown-preview-view table thead tr:nth-child(even) table th, .markdown-preview-view table thead tr:nth-child(even) table td, .markdown-preview-view table tbody tr:nth-child(even) table th, .markdown-preview-view table tbody tr:nth-child(even) table td,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(even) table th,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(even) table td,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(even) table th,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(even) table td {
  background-color: var(--surface);
}
.markdown-preview-view table thead tr:nth-child(odd) table, .markdown-preview-view table tbody tr:nth-child(odd) table,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(odd) table,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(odd) table {
  margin-bottom: 10px;
}
.markdown-preview-view table thead tr:nth-child(odd) table th, .markdown-preview-view table thead tr:nth-child(odd) table td, .markdown-preview-view table tbody tr:nth-child(odd) table th, .markdown-preview-view table tbody tr:nth-child(odd) table td,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(odd) table th,
.markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(odd) table td,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(odd) table th,
.markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(odd) table td {
  background-color: var(--surface-mid);
}

@media print {
  .markdown-preview-view table thead tr:nth-child(even) th, .markdown-preview-view table thead tr:nth-child(even) td, .markdown-preview-view table tbody tr:nth-child(even) th, .markdown-preview-view table tbody tr:nth-child(even) td,
  .markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(even) th,
  .markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(even) td,
  .markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(even) th,
  .markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(even) td {
    background-color: var(--surface);
  }
  .markdown-preview-view table thead tr:nth-child(odd) th, .markdown-preview-view table thead tr:nth-child(odd) td, .markdown-preview-view table tbody tr:nth-child(odd) th, .markdown-preview-view table tbody tr:nth-child(odd) td,
  .markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(odd) th,
  .markdown-source-view.mod-cm6.is-live-preview table thead tr:nth-child(odd) td,
  .markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(odd) th,
  .markdown-source-view.mod-cm6.is-live-preview table tbody tr:nth-child(odd) td {
    background-color: transparent;
  }
}
.markdown-source-view.mod-cm6 .is-unresolved .cm-underline, .markdown-preview-view a {
  text-decoration-color: inherit !important;
}

/* ------------- */
/* # Title Bar */
/* ------------- */
.workspace-split.mod-root > .workspace-leaf:first-of-type:last-of-type .workspace-leaf.mod-active .view-header {
  border-bottom: none;
  background-color: var(--surface2);
  align-items: center;
  display: flex;
}

/* Style hamburger menu */
/* Replace icon */
.three-horizontal-bars {
  background-color: currentColor;
}

svg.three-horizontal-bars {
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z' /%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:24px;height:24px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z' /%3E%3C/svg%3E");
}

.view-header-title {
  line-height: 1.75em;
  font-size: var(--h5);
  font-weight: var(--regular);
  color: var(--on-surface);
}

.is-mobile .view-header-title {
  -webkit-mask-image: none;
  mask-image: none;
}

.is-mobile .view-header-icon {
  color: var(--on-surface);
  padding-left: 16px;
  padding-right: 16px;
}

.view-action:not(.page-header-button) {
  display: none;
}

.view-action.page-header-button {
  color: var(--on-surface-variant);
  margin-left: 12px;
  margin-right: 12px;
}

body.is-phone.hide-buttons-mobile .view-action:not(.page-header-button) {
  display: none;
}

/* Floating Action Button (FAB)*/
body.is-phone.fab-enabled-mobile:not(.custom-page-header),
body:not(.is-phone).fab-enabled-desktop:not(.custom-page-header) {
  /* Advanced Mobile Toolbar Mode */
  /* Shrink FAB on small displays */
}
body.is-phone.fab-enabled-mobile:not(.custom-page-header) .view-action:nth-last-of-type(4),
body:not(.is-phone).fab-enabled-desktop:not(.custom-page-header) .view-action:nth-last-of-type(4) {
  color: var(--on-tertiary-container);
  background-color: var(--tertiary-container);
  display: flex;
  padding: 0px;
  margin: 0;
  margin: 16px;
  position: absolute;
  transform: translate(-50%, -50%);
  left: calc(100% - 44px - 16px);
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  border-radius: var(--medium-radius);
  box-shadow: 0.4px 0.8px 4px rgba(0, 0, 0, 0.03), 1.2px 2.7px 13.4px rgba(0, 0, 0, 0.15), 8px 18px 80px rgba(0, 0, 0, 0.4);
  top: calc(100vh - 48px - 44px - 16px);
}
body.is-phone.fab-enabled-mobile:not(.custom-page-header) .view-action:nth-last-of-type(4) > *:first-child,
body:not(.is-phone).fab-enabled-desktop:not(.custom-page-header) .view-action:nth-last-of-type(4) > *:first-child {
  width: 24px;
  height: 24px;
}
body.is-phone.fab-enabled-mobile:not(.custom-page-header) body:not(:not(.advanced-toolbar-mode)) .view-action:nth-of-type(4),
body:not(.is-phone).fab-enabled-desktop:not(.custom-page-header) body:not(:not(.advanced-toolbar-mode)) .view-action:nth-of-type(4) {
  top: calc(90vh - var(--at-button-height) * var(--at-row-count));
}
@media screen and (max-height: 400px) {
  body.is-phone.fab-enabled-mobile:not(.custom-page-header) .view-action:nth-of-type(4),
  body:not(.is-phone).fab-enabled-desktop:not(.custom-page-header) .view-action:nth-of-type(4) {
    transform: scale(0.8) translate(-50%, -50%);
  }
}

body.is-phone.fab-enabled-mobile.custom-page-header,
body:not(.is-phone).fab-enabled-desktop.custom-page-header {
  /* Advanced Mobile Toolbar Mode */
  /* Shrink FAB on small displays */
}
body.is-phone.fab-enabled-mobile.custom-page-header .view-action:first-child,
body:not(.is-phone).fab-enabled-desktop.custom-page-header .view-action:first-child {
  color: var(--on-tertiary-container);
  background-color: var(--tertiary-container);
  display: flex;
  padding: 0px;
  margin: 0;
  margin: 16px;
  position: absolute;
  transform: translate(-50%, -50%);
  left: calc(100% - 44px - 16px);
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  border-radius: var(--medium-radius);
  box-shadow: 0.4px 0.8px 4px rgba(0, 0, 0, 0.03), 1.2px 2.7px 13.4px rgba(0, 0, 0, 0.15), 8px 18px 80px rgba(0, 0, 0, 0.4);
  top: calc(100vh - 48px - 44px - 16px);
}
body.is-phone.fab-enabled-mobile.custom-page-header .view-action:first-child > *:first-child,
body:not(.is-phone).fab-enabled-desktop.custom-page-header .view-action:first-child > *:first-child {
  width: 24px;
  height: 24px;
}
body.is-phone.fab-enabled-mobile.custom-page-header body:not(:not(.advanced-toolbar-mode)) .view-action:nth-last-of-type(5),
body:not(.is-phone).fab-enabled-desktop.custom-page-header body:not(:not(.advanced-toolbar-mode)) .view-action:nth-last-of-type(5) {
  top: calc(90vh - var(--at-button-height) * var(--at-row-count));
}
@media screen and (max-height: 400px) {
  body.is-phone.fab-enabled-mobile.custom-page-header .view-action:nth-last-of-type(5),
  body:not(.is-phone).fab-enabled-desktop.custom-page-header .view-action:nth-last-of-type(5) {
    transform: scale(0.8) translate(-50%, -50%);
  }
}

body:not(.is-phone).fab-enabled-desktop.custom-page-header:has(.titlebar) .view-action:first-child {
  top: calc(100vh - 48px - 44px - 16px - 40px);
}

/* Pencil SVG */
svg.pencil > path {
  display: none;
}

svg.pencil {
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:50px;height:50px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M14.06,9L15,9.94L5.92,19H5V18.08L14.06,9M17.66,3C17.41,3 17.15,3.1 16.96,3.29L15.13,5.12L18.88,8.87L20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18.17,3.09 17.92,3 17.66,3M14.06,6.19L3,17.25V21H6.75L17.81,9.94L14.06,6.19Z' /%3E%3C/svg%3E");
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' style='width:50px;height:50px' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M14.06,9L15,9.94L5.92,19H5V18.08L14.06,9M17.66,3C17.41,3 17.15,3.1 16.96,3.29L15.13,5.12L18.88,8.87L20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18.17,3.09 17.92,3 17.66,3M14.06,6.19L3,17.25V21H6.75L17.81,9.94L14.06,6.19Z' /%3E%3C/svg%3E");
}

/* -------------- */
/* Quick Action */
/* -------------- */
/* Text input */
.is-mobile input.prompt-input {
  background-color: var(--primary-container);
  border-radius: var(--big-radius);
  line-height: 1.5em;
  letter-spacing: 0.009375em;
  font-weight: var(--medium);
  color: var(--on-primary-container);
}
.is-mobile input.prompt-input:active, .is-mobile input.prompt-input:focus {
  border: 0;
}
.is-mobile input.prompt-input::-moz-placeholder {
  color: var(--secondary);
}
.is-mobile input.prompt-input::placeholder {
  color: var(--secondary);
}

/* Selection items */
.is-mobile .suggestion-item {
  border-bottom: 1px solid;
  padding-bottom: 10px;
  padding-top: 10px;
  border-color: var(--surface-variant);
  line-height: 1.5em;
  letter-spacing: 0.009375em;
  font-weight: var(--medium);
}
.is-mobile .suggestion-item .suggestion-prefix {
  position: absolute;
  top: 0px;
  margin-top: 2px;
  margin-bottom: 2px;
  color: var(--secondary);
  line-height: 1em;
  font-size: 0.75em;
  letter-spacing: 0.025em;
  font-weight: var(--medium);
}
.is-mobile .suggestion-item.is-selected {
  background-color: var(--surface);
}

.is-mobile .suggestion-hotkey {
  display: none;
}

.suggestion-highlight {
  color: var(--secondary);
}

/* Height */
.is-mobile .prompt,
.is-mobile .suggestion-container {
  height: calc(100% - 100px);
}

.is-mobile .prompt {
  min-width: 100%;
  background-color: var(--surface);
}

.is-mobile .suggestion-container:not(.modal-container) {
  height: 200px;
}

/* Settings */
/* Back modal */
.is-mobile .modal-setting-back-button {
  background-color: var(--primary-faint);
}

/* Reduce space between settings */
.vertical-tab-nav-item {
  padding: 0px 6px 10px 24px;
}

/* Style back */
.is-mobile .modal-setting-back-button {
  border-top-left-radius: var(--big-radius);
  border-top-right-radius: var(--big-radius);
}

/* Style setting headers */
.vertical-tab-header-group-title {
  padding: 6px 6px 6px 26px;
}

.nav-files-container {
  padding-right: 2.5px;
}

/* Add divider line */
.vertical-tab-header-group {
  border-bottom: 1px solid var(--background-secondary-alt);
}

.vertical-tab-header-group:last-of-type {
  border-bottom: none;
}

/* Buttons */
/* Filled button */
button.mod-cta {
  background-color: var(--primary);
  color: var(--background-primary);
}

.is-mobile button,
.is-mobile .empty-state-action {
  font-size: var(--smallest);
  border-radius: var(--big-radius);
}

/* Setting Headers */
.theme-light .vertical-tab-header-group-title {
  color: var(--primary-light);
}

.theme-dark .vertical-tab-header-group-title {
  color: var(--primary-dark);
}

/* Prompts (Quick switcher) */
.is-mobile .prompt,
.is-mobile .suggestion-container {
  border-radius: var(--medium-radius) var(--medium-radius) 0 0;
  padding: 15px 20px;
}

.is-mobile .modal {
  border-radius: var(--medium-radius);
  padding: 15px 20px;
}

/* Pulling down */
.pull-action.pull-down-action {
  border-radius: 0 0 var(--medium-radius) var(--medium-radius);
  background-color: var(--secondary-container);
  color: var(--on-secondary-container);
  text-transform: none;
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.00625em;
  font-weight: var(--medium);
}

.pull-action.mod-activated {
  background-color: var(--primary);
  color: var(--on-primary);
  text-transform: none;
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.00625em;
  font-weight: var(--medium);
}

/* Pulling from the left (navigating back
Trying to target but it's not these:
*/
/* PLUGINS */
/* Dataview Tables */
.table-view-table > thead > tr > th {
  border-bottom: none !important;
  font-weight: var(--medium);
  font-size: var(--editor-font-size);
  padding: var(--editor-font-size);
}

tr {
  border-top: 1px solid var(--background-secondary-alt);
}

/* ------------- */
/* # Sidebars */
/* ------------- */
/* Title of 'File explorer' and the like */
.workspace-drawer-active-tab-header {
  margin: 0;
}

.workspace-drawer-header-name-text {
  color: var(--text-muted);
  font-size: var(--smaller);
}

/* Icons */
.workspace-drawer-tab-option-item-icon, .workspace-drawer-active-tab-icon {
  color: var(--primary-dark);
}

/* Search */
.workspace-leaf-content[data-type=search] .nav-action-button {
  transform: scale(0.8);
  margin: none;
  padding: none;
}

/* Suggestions */
.is-mobile .suggestion-container.mod-search-suggestion {
  max-height: 40%;
  min-height: 30%;
  min-width: 100%;
}

/* Top buttons */
.is-mobile .workspace-drawer-actions,
.is-mobile .nav-buttons-container {
  background-color: var(--background-secondary);
  border-radius: var(--big-radius);
  margin: 0 11px 10px;
}

.is-mobile .workspace-leaf-content[data-type=search] .nav-action-button {
  margin: 0px 2px 2px 0;
  text-align: center;
  height: 36px;
  cursor: var(--cursor);
}

.nav-action-button {
  color: var(--text-muted);
  transform: scale(0.8);
  padding: 5px 8px 0px 8px;
  margin: 0px 6px 0px 6px;
}

.workspace-drawer-tab-container > * {
  border-top: none !important;
}

/* Significantly simplify left and right bars */
body.hide-sidebar-header .nav-folder.mod-root > .nav-file-title,
body.hide-sidebar-header .nav-folder.mod-root > .nav-folder-title,
body.hide-sidebar-header .workspace-drawer-header-name,
body.hide-sidebar-header .workspace-drawer-header-icon,
body.hide-sidebar-header .workspace-drawer-header-left {
  display: none;
}

/* Right sidebar */
.workspace-drawer.mod-right {
  border: none;
}

.backlink-pane,
.outgoing-link-pane {
  padding: 0 11px;
}

/* Backlink counts */
.tree-item-flair-outer {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Left sidebar */
.workspace-drawer.mod-left {
  border-right: 1px var(--background-primary) solid !important;
}

/* Ribbon */
.workspace-drawer-ribbon {
  background-color: var(--background-primary);
}

.workspace-drawer-tab-option-item {
  margin-left: 12px;
  margin-right: 12px;
}

.workspace-drawer-tab-option-item-title,
.workspace-drawer-active-tab-title {
  color: var(--text-normal);
}

.workspace-drawer-tab-container > * {
  border-top: 1px solid var(--background-secondary-alt);
}

/* ------------- */
/* # File explorer */
/* ------------- */
/* Folder */
.nav-folder-title-content {
  padding-left: 0px;
}

.nav-file-title-content,
.nav-folder-title-content {
  padding: 0px 0px;
  font-weight: var(--medium);
}

/* Files */
.is-mobile .nav-file-title,
.is-mobile .tag-container {
  padding-left: 11px;
  padding-right: 11px;
  color: var(--text-muted);
  font-weight: var(--light);
}

/* Highlighted file or backlink */
.nav-file-title.is-active,
.tree-item-self.is-clickable:hover,
.nav-folder-title.is-active,
body:not(.is-grabbing) .nav-file-title:hover,
body:not(.is-grabbing) .nav-folder-title:hover {
  background-color: var(--tertiary-container);
}
.nav-file-title.is-active .nav-file-title-content,
.tree-item-self.is-clickable:hover .nav-file-title-content,
.nav-folder-title.is-active .nav-file-title-content,
body:not(.is-grabbing) .nav-file-title:hover .nav-file-title-content,
body:not(.is-grabbing) .nav-folder-title:hover .nav-file-title-content {
  color: var(--on-tertiary-container);
}

.nav-file-title.is-active {
  background-color: var(--surface);
}
.nav-file-title.is-active .nav-folder-title-content, .nav-file-title.is-active .nav-file-title-content {
  color: var(--on-surface);
  font-weight: 600;
}

.nav-file-title.is-active:hover {
  background-color: var(--primary-container) !important;
}
.nav-file-title.is-active:hover .nav-folder-title-content, .nav-file-title.is-active:hover .nav-file-title-content {
  color: var(--surface) !important;
}

.nav-folder-title .nav-folder-title-content {
  color: var(--text-normal);
  font-weight: 550;
}

.markdown-source-view .markdown-embed, .markdown-source-view .file-embed {
  border: 2px solid var(--surface-variant);
  border-radius: var(--small-radius);
}

/* Embeds */
/* Naked Embeds (modified from death_au (https://forum.obsidian.md/t/naked-embeds-css-tweak/72) */
.markdown-embed-title {
  display: none;
}

.markdown-preview-view .markdown-embed-content > :first-child {
  margin-top: 0;
}

.markdown-preview-view .markdown-embed-content > :last-child {
  margin-bottom: 0;
}

.markdown-preview-view .markdown-embed {
  border: none;
  padding: 0;
  margin: 0;
}

/* the link on the top right corner*/
.markdown-embed-link {
  color: var(--secondary) !important;
  top: 15px;
}

.markdown-embed-link:hover {
  color: var(--text-accent) !important;
}

/* Scroll bars */
.is-mobile ::-webkit-scrollbar {
  display: none;
}

/* Inspired by Minimal by kepano: https://github.com/kepano/obsidian-minimal/blob/master/obsidian.css */
.BC-trail {
  margin-top: 5px !important;
  line-height: 1em;
  font-size: 0.75em;
  letter-spacing: 0.03125em;
  font-weight: var(--medium);
  color: var(--secondary);
}

.timer {
  font-style: italic;
}

.timer::before {
  content: "⏱ ";
  font-style: normal;
}

.cm-metadata,
.cm-metadata-key {
  line-height: 1.25em;
  font-size: 0.875em;
  letter-spacing: 0.015625em;
  font-weight: var(--medium);
  color: var(--secondary);
  font-family: var(--font-monospace);
}

.workspace-leaf-content[data-type=cook] .cm-formatting {
  color: var(--surface-variant) !important;
}

.excalidraw div, .excalidraw.theme--dark {
  --color-primary-light: var(--color-accent);
  --color-primary-darker: var(--icon-color-focused);
  --color-primary-darkest: var(--color-accent);
  --focus-highlight-color: var(--color-accent);
  --select-highlight-color: var(--color-accent);
  --color-selection: var(--color-accent);
  --icon-fill-color: var(--text-normal);
  --text-primary-color: var(--text-normal);
  --island-bg-color: var(--background-primary);
  --shadow-island: 0;
}
.excalidraw div *, .excalidraw.theme--dark * {
  border: none !important;
}

.view-content.excalidraw-view {
  --excalidraw-button-size: 32px;
}
.view-content.excalidraw-view div, .view-content.excalidraw-view .dropdown-menu .dropdown-menu-item-base {
  color: var(--text-normal) !important;
}
.view-content.excalidraw-view .HelpDialog__key {
  color: var(--on-accent);
}
.view-content.excalidraw-view .color-picker {
  border: 1px solid var(--window-border);
  background: var(--background-secondary) !important;
  border-radius: 8px;
}
.view-content.excalidraw-view .zoom-actions,
.view-content.excalidraw-view .undo-redo-buttons,
.view-content.excalidraw-view .layer-ui__wrapper__footer-right,
.view-content.excalidraw-view .layer-ui__wrapper__footer-right div,
.view-content.excalidraw-view .App-bottom-bar {
  background: none;
  border: none !important;
}
.view-content.excalidraw-view .App-toolbar,
.view-content.excalidraw-view .App-menu__left,
.view-content.excalidraw-view .sidebar-trigger,
.view-content.excalidraw-view .ToolIcon.ToolIcon_type_floating div,
.view-content.excalidraw-view .App-menu_top__left .menu-button,
.view-content.excalidraw-view .layer-ui__wrapper__footer-right .ToolIcon__icon,
.view-content.excalidraw-view .mobile-misc-tools-container {
  background: var(--surface) !important;
}
.view-content.excalidraw-view .App-toolbar button:hover,
.view-content.excalidraw-view .App-menu__left button:hover,
.view-content.excalidraw-view .sidebar-trigger button:hover,
.view-content.excalidraw-view .ToolIcon.ToolIcon_type_floating div button:hover,
.view-content.excalidraw-view .App-menu_top__left .menu-button button:hover,
.view-content.excalidraw-view .layer-ui__wrapper__footer-right .ToolIcon__icon button:hover,
.view-content.excalidraw-view .mobile-misc-tools-container button:hover {
  background: var(--surface-variant) !important;
}
.view-content.excalidraw-view .zoom-actions,
.view-content.excalidraw-view .undo-redo-buttons {
  background: var(--surface) !important;
}
.view-content.excalidraw-view .ToolIcon svg {
  mix-blend-mode: luminosity;
}
.view-content.excalidraw-view .ToolIcon.Shape, .view-content.excalidraw-view .dropdown-menu-button {
  height: var(--excalidraw-button-size) !important;
  width: var(--excalidraw-button-size) !important;
  background-color: var(--surface);
}
.view-content.excalidraw-view .ToolIcon.Shape :hover, .view-content.excalidraw-view .dropdown-menu-button :hover {
  background: var(--surface-variant) !important;
}
.view-content.excalidraw-view .ToolIcon__icon {
  height: var(--excalidraw-button-size) !important;
  width: var(--excalidraw-button-size) !important;
}
.view-content.excalidraw-view .App-bottom-bar .App-toolbar {
  margin-top: 0;
}
.view-content.excalidraw-view .view-header {
  z-index: 2;
}
.view-content.excalidraw-view .mobile-misc-tools-container div {
  border: none !important;
  border-radius: 6px;
}
.view-content.excalidraw-view .App-toolbar {
  border-radius: 10px;
}
.view-content.excalidraw-view .App-bottom-bar div.Island:last-child {
  margin-left: 8px !important;
  padding: 5.5px;
  background-color: var(--surface);
  border-radius: 9px;
}
.view-content.excalidraw-view .App-bottom-bar button:hover {
  background: var(--surface-variant) !important;
}
.view-content.excalidraw-view .ToolIcon__icon:hover {
  background: var(--surface-variant) !important;
}
.view-content.excalidraw-view .App-top-bar .mobile-misc-tools-container {
  right: -10px;
}
.view-content.excalidraw-view .App-top-bar .mobile-misc-tools-container .ToolIcon {
  margin-left: 4px;
  margin-right: 4px;
  margin-bottom: 3px;
}
.view-content.excalidraw-view .App-top-bar .mobile-misc-tools-container .ToolIcon:first-child {
  margin-top: 4px;
}
.view-content.excalidraw-view .App-top-bar .mobile-misc-tools-container .ToolIcon:first-child .ToolIcon__icon {
  border-radius: 6px;
}
.view-content.excalidraw-view .App-top-bar .mobile-misc-tools-container .ToolIcon:last-child {
  margin-bottom: 4px;
}
.view-content.excalidraw-view .layer-ui__wrapper__top-right .ToolIcon {
  margin-right: 8px;
}
.view-content.excalidraw-view .mobile-misc-tools-container .ToolIcon_type_floating:not(:first-child) .ToolIcon__icon svg {
  fill: var(--on-surface) !important;
}
.view-content.excalidraw-view .mobile-misc-tools-container label:not(.ToolIcon) {
  width: var(--excalidraw-button-size);
  padding: 0;
  margin: 3px 4px;
  margin-top: -1px;
}
.view-content.excalidraw-view .mobile-misc-tools-container label:not(.ToolIcon) :hover {
  background: var(--surface-variant) !important;
}
.view-content.excalidraw-view .sidebar-trigger.default-sidebar-trigger {
  height: var(--excalidraw-button-size);
  width: var(--excalidraw-button-size);
  margin: 0 !important;
  padding: calc(var(--excalidraw-button-size) / 2 - 8px) !important;
  display: block;
}
.view-content.excalidraw-view .sidebar-trigger.default-sidebar-trigger .library-button__label {
  display: none;
}
.view-content.excalidraw-view .popover {
  border-radius: 10px;
  box-shadow: none;
}
.view-content.excalidraw-view .popover ul, .view-content.excalidraw-view .popover ol {
  background-color: var(--surface) !important;
}
.view-content.excalidraw-view .popover li button:hover {
  background-color: var(--surface-variant) !important;
}

@media only screen and (max-height: 700px) {
  body.pen-mode .view-content.excalidraw-view {
    --excalidraw-button-size: 25px;
  }
}
@media only screen and (max-height: 580px) {
  body.pen-mode .view-content.excalidraw-view {
    --excalidraw-button-size: 20px;
  }
}
@media only screen and (max-height: 500px) {
  body.pen-mode .view-content.excalidraw-view {
    --excalidraw-button-size: 15px;
  }
}
body.pen-mode .view-content.excalidraw-view .App-top-bar {
  flex-direction: row !important;
}
body.pen-mode .view-content.excalidraw-view .App-top-bar .Stack_horizontal {
  grid-auto-flow: row;
}
body.pen-mode .view-content.excalidraw-view .App-top-bar .Stack_vertical {
  margin-bottom: 170px;
}
body.pen-mode .view-content.excalidraw-view .App-top-bar .mobile-misc-tools-container {
  right: inherit;
  top: inherit;
  bottom: 0;
  border-radius: 10px;
}
body.pen-mode .view-content.excalidraw-view .App-top-bar .App-toolbar__divider {
  height: 1px;
  width: auto;
}
body.pen-mode .view-content.excalidraw-view .App-bottom-bar {
  padding-left: calc(var(--excalidraw-button-size) + 12px);
}
body.pen-mode .view-content.excalidraw-view .excalidraw--mobile .HintViewer span {
  position: absolute !important;
  top: auto !important;
  bottom: 10px !important;
  right: 10px !important;
  opacity: 0.2;
}
body.pen-mode .view-content.excalidraw-view .excalidraw--mobile .welcome-screen-center {
  display: none !important;
}
body.pen-mode .view-content.excalidraw-view .App-toolbar__extra-tools-dropdown {
  left: 0 !important;
}

/* @plugins
core:
- backlink
- command-palette
- file-explorer
- global-search
- graph
- outgoing-link
- outline
- page-preview
- starred
- switcher
- tag-pane

community:
- obsidian-system-dark-mode
- obsidian-style-settings
- cooklang-obsidian
- breadcrumbs
- obsidian-excalidraw-plugin
*/
/* flat dark bg */
body {
  --divider-color: transparent;
}

.workspace-split.mod-horizontal.mod-left-split {
  overflow: hidden;
}

.mod-left-split {
  margin-right: 0.5px;
}

.menu {
  background: var(--background-secondary) !important;
}

/* Tabs */
.view-header-title-container:not(.mod-at-end):after {
  display: none;
}

.workspace .mod-root .workspace-tab-header-inner::after {
  right: -1px;
}

.view-header {
  background: var(--tab-background-active) !important;
}

.workspace-tab-header-container, .mod-sidedock, .workspace-sidedock-vault-profile {
  background: none;
  background-color: transparent !important;
}

.workspace-tab-header-container-inner {
  margin-left: -15px;
}

body:not(.is-popout-window) .workspace-tab-header-container, .workspace-tabs, .mod-root, .app-container,
.horizontal-main-container, .workspace, .mod-left, .mod-right, .workspace-tab-container, .workspace-leaf:not(.nav-folder-title) {
  background: none !important;
}

.workspace-tab-header {
  border: none;
}

.workspace-ribbon.side-dock-ribbon.mod-left::before {
  background: none;
}

.tooltip {
  background: var(--tooltip-color) !important;
  color: var(--on-surface-variant) !important;
}

.tooltip .tooltip-arrow {
  border-bottom-color: var(--tooltip-color) !important;
}

.tooltip.mod-right .tooltip-arrow {
  border-bottom-color: transparent !important;
  border-right-color: var(--tooltip-color) !important;
}

.tooltip.mod-left .tooltip-arrow {
  border-bottom-color: transparent !important;
  border-left-color: var(--tooltip-color) !important;
}

.tooltip.mod-top .tooltip-arrow {
  border-bottom-color: transparent !important;
  border-top-color: var(--tooltip-color) !important;
}

body.disable-tooltip-animation .tooltip {
  animation: none !important;
}

body.theme-light {
  background-color: var(--surface-variant);
  --tooltip-color: white;
}

body.theme-dark {
  background-color: black;
  --tooltip-color: var(--surface-variant);
}

body.mod-windows.enable-mica, body.mod-mac.enable-mica, body.mod-linux.enable-mica {
  background-color: transparent !important;
}
body.mod-windows.enable-mica .cm-line:not(.HyperMD-quote, .HyperMD-codeblock), body.mod-mac.enable-mica .cm-line:not(.HyperMD-quote, .HyperMD-codeblock), body.mod-linux.enable-mica .cm-line:not(.HyperMD-quote, .HyperMD-codeblock) {
  background-color: var(--background);
}
body.mod-windows.enable-mica .cm-line.HyperMD-quote + .cm-line:not(.HyperMD-quote, .HyperMD-codeblock), body.mod-mac.enable-mica .cm-line.HyperMD-quote + .cm-line:not(.HyperMD-quote, .HyperMD-codeblock), body.mod-linux.enable-mica .cm-line.HyperMD-quote + .cm-line:not(.HyperMD-quote, .HyperMD-codeblock) {
  background-color: transparent;
}

body.mod-windows.enable-mica.is-popout-window.theme-dark {
  background: #202020 !important;
}

body.mod-windows.enable-mica.is-popout-window.theme-light {
  background: var(--surface-variant) !important;
}

body:not(.is-phone) {
  --pane-separation: 6px;
}
body:not(.is-phone) .mod-root div.mod-top, body:not(.is-phone) .mod-root div.mod-horizontal .workspace-tabs {
  padding-right: var(--pane-separation);
}
body:not(.is-phone) .mod-root .mod-horizontal .workspace-leaf {
  border-bottom-left-radius: var(--radius-m);
  border-bottom-right-radius: var(--radius-m);
  margin-bottom: -5px;
}
body:not(.is-phone) .mod-root div:last-of-type.mod-top, body:not(.is-phone) .mod-root div:last-of-type.mod-horizontal .workspace-tabs {
  padding-right: 1px;
}
body:not(.is-phone) hr.workspace-leaf-resize-handle {
  min-width: var(--pane-separation);
  min-height: var(--pane-separation);
}

.mod-stacked .workspace-tab-header {
  background: var(--surface-variant) !important;
  border-top-left-radius: var(--radius-m) !important;
  border-bottom-left-radius: var(--radius-m) !important;
  margin-bottom: 0 !important;
  padding-bottom: 5px !important;
}

.mod-stacked .workspace-leaf, .mod-stacked .workspace-leaf-content {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: var(--radius-m) !important;
  margin-right: 5px !important;
}
.mod-stacked .view-header {
  border-top-left-radius: 0 !important;
  border-top-right-radius: var(--radius-m) !important;
}

.internal-embed {
  border: none !important;
}

.markdown-embed-content {
  background-color: var(--background-secondary);
  border-radius: var(--radius-l);
}

.mod-root .markdown-embed-content {
  padding: 0 20px;
}

.canvas-node-container .markdown-embed-content {
  border-radius: inherit;
  padding: inherit;
  background-color: var(--background-secondary);
}

.canvas-node.is-themed .markdown-embed-content {
  background-color: transparent;
}

body.theme-dark .canvas-wrapper .canvas-node {
  box-shadow: rgba(0, 0, 0, 0.3764705882) 0px 3px 5px;
  border-radius: var(--radius-l);
}
body.theme-dark .canvas-wrapper .canvas-node.is-themed .markdown-embed-content {
  background-color: rgba(255, 255, 255, 0.062745098);
}

body.theme-light .canvas-wrapper {
  --shadow-stationary: none;
  --canvas-dot-pattern: #BBB;
}
body.theme-light .canvas-wrapper .canvas-node {
  box-shadow: rgba(0, 0, 0, 0.1882352941) 0px 3px 10px;
  border: none;
  border-radius: var(--radius-l);
}
body.theme-light .canvas-wrapper .canvas-node-container {
  border: none;
}

.mod-inside-iframe {
  background-color: var(--background-secondary);
}

.notice-container .notice {
  background-color: var(--surface-variant) !important;
  color: var(--on-surface-variant) !important;
}

body .workspace-leaf-content {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

body:not(.hide-toolbar-desktop) .workspace-leaf-content, .workspace-tabs:not(.mod-top-left-space) .workspace-leaf-content {
  border-top-left-radius: var(--radius-m);
}

.workspace-tabs:not(.mod-top-right-space) .workspace-leaf-content {
  border-top-right-radius: var(--radius-m);
}

body.show-view-header body:not(.hide-toolbar-desktop) .view-header, body.show-view-header .workspace-tabs:not(.mod-top-left-space) .view-header {
  border-top-left-radius: var(--radius-m);
}
body.show-view-header .workspace-tabs:not(.mod-top-right-space) .view-header {
  border-top-right-radius: var(--radius-m);
}
body.show-view-header .workspace-leaf-content {
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
}

body.mod-windows.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed, body.mod-linux.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed {
  max-width: 0px;
  margin: 0;
  padding: 0;
  margin-top: calc(var(--header-height) * 2 + 3px);
  padding-top: 4px;
  padding-bottom: 12px;
  overflow: hidden;
  border: none;
}
body.mod-windows.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed::before, body.mod-linux.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed::before {
  display: none;
}
body.mod-windows.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left, body.mod-linux.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left {
  transition-duration: 0s;
}
body.mod-windows.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed:hover::after, body.mod-linux.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed:hover::after {
  opacity: 0.4;
}
body.mod-windows.hide-toolbar-desktop .workspace-tabs.mod-top-left-space .workspace-tab-header-container, body.mod-linux.hide-toolbar-desktop .workspace-tabs.mod-top-left-space .workspace-tab-header-container {
  margin-left: var(--ribbon-width);
}
body.mod-windows.hide-toolbar-desktop .mod-left-split .workspace-tabs.mod-top-left-space .workspace-tab-header-container, body.mod-linux.hide-toolbar-desktop .mod-left-split .workspace-tabs.mod-top-left-space .workspace-tab-header-container {
  padding-left: 0;
  margin-left: 0;
}
body.mod-windows.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed .side-dock-actions,
body.mod-windows.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed .side-dock-settings, body.mod-linux.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed .side-dock-actions,
body.mod-linux.hide-toolbar-desktop .workspace-ribbon.side-dock-ribbon.mod-left.is-collapsed .side-dock-settings {
  padding-left: 7px;
  opacity: 0;
}

.is-phone .prompt {
  position: absolute !important;
  height: calc(100% - 40px) !important;
}

.is-phone .suggestion-prefix {
  position: inherit !important;
}

.horizontal-tab-nav-item, .vertical-tab-nav-item {
  padding-top: var(--size-4-2) !important;
  padding-bottom: var(--size-4-2) !important;
}

.horizontal-tab-nav-item.is-active, .vertical-tab-nav-item.is-active {
  color: #111;
}

.view-header {
  height: 35px;
  padding: 6px;
  padding-top: 7.5px;
}

.view-header-title {
  font-size: medium;
}

.is-phone .view-header {
  height: 50px;
}

.is-phone .view-header-title {
  font-size: larger;
}

body:not(.show-view-header) {
  --tab-background-active: var(--background-primary) !important;
}

body:not(.hide-toolbar-desktop) .mod-root .workspace-tab-header, .workspace-tabs:not(.mod-top-left-space) .mod-root .workspace-tab-header {
  margin-bottom: -9px;
  padding-bottom: 14px;
  overflow: visible !important;
  z-index: -1000 !important;
}

body:not(.hide-toolbar-desktop) .mod-root .workspace-tab-header.is-active:first-of-type,
body.hide-toolbar-desktop .mod-root .workspace-tabs:not(.mod-top-left-space) .workspace-tab-header.is-active:first-of-type {
  margin-bottom: -9px;
  padding-bottom: 13px;
}

body:not(.hide-toolbar-desktop) .mod-root .workspace-tab-header.is-active:first-of-type::after,
body.hide-toolbar-desktop .mod-root .workspace-tabs:not(.mod-top-left-space) .workspace-tab-header.is-active:first-of-type::after {
  bottom: 9px;
}

.workspace-tab-header-container-inner {
  margin-bottom: -10px;
  padding-bottom: 9px !important;
}

body:not(.is-phone) .view-action:not(.page-header-button) {
  display: block;
  padding: 4px 6px;
  padding-bottom: 0;
}

.empty-state-title {
  max-height: 100%;
}

.empty-state-title::before {
  max-height: 100% !important;
  width: 100%;
  height: 20vh;
  background-size: 15vh 15vh;
  background-repeat: no-repeat;
  background-position: center;
}

body.more-drag-desktop .view-header, body.more-drag-desktop .titlebar-inner, body.more-drag-desktop .workspace-ribbon.side-dock-ribbon {
  -webkit-app-region: drag;
}
body.more-drag-desktop .mod-root .workspace-tab-header {
  -webkit-app-region: drag;
}
body.more-drag-desktop .mod-root .workspace-tab-header .workspace-tab-header-inner-title {
  -webkit-app-region: no-drag;
  margin-bottom: -5px;
  padding-bottom: 5px;
}

body.tab-drag-desktop .mod-root .workspace-tab-header.is-active {
  -webkit-app-region: drag;
}
body.tab-drag-desktop .mod-root .workspace-tab-header.is-active .workspace-tab-header-inner-title {
  -webkit-app-region: drag;
}

.sidebar-toggle-button.mod-left {
  -webkit-app-region: drag;
}

.workspace-tab-header-inner-close-button, .view-header-title-parent, .view-header-title, .clickable-icon {
  -webkit-app-region: no-drag;
}

.status-bar {
  opacity: 0.1;
  transition: opacity 0.25s;
}

.status-bar:not(:hover) {
  background: none;
  border-color: transparent;
}

.status-bar:hover {
  opacity: 1;
}

.cm-editor .HyperMD-header + .HyperMD-header::before {
  content: "";
}

.HyperMD-header {
  padding-top: 15px !important;
  padding-bottom: 0px !important;
  margin: 0 !important;
}

.markdown-reading-view h1, .markdown-reading-view h2, .markdown-reading-view h3, .markdown-reading-view h4, .markdown-reading-view h5, .markdown-reading-view h6 {
  padding-top: 17px !important;
  padding-bottom: 8px !important;
  margin: 0 !important;
}
.markdown-reading-view hr {
  display: block;
  unicode-bidi: isolate;
  margin-block-start: 0.5em;
  margin-block-end: 0.5em;
  margin-inline-start: auto;
  margin-inline-end: auto;
  overflow: hidden;
  border-style: inset;
  border-width: 1px;
  border: none;
  border-top: var(--hr-thickness) solid;
  border-color: var(--hr-color);
  margin: 11px 0;
}
.markdown-reading-view table {
  margin-bottom: 0;
}
.markdown-reading-view .markdown-rendered table {
  margin-block-start: 1em;
}

.markdown-rendered table {
  margin-block-start: 0;
}

body.space-above-document .cm-editor .cm-scroller .cm-sizer,
body.space-above-document .markdown-reading-view .markdown-preview-view .markdown-preview-sizer {
  padding-top: 25vh;
}
body.space-above-document .markdown-embed-content .markdown-preview-sizer, body.space-above-document .markdown-embed-content .cm-sizer, body.space-above-document .canvas-node-container .markdown-preview-sizer, body.space-above-document .canvas-node-container .cm-sizer {
  padding-top: inherit !important;
}

.view-content .cm-header-1, .view-content h1 {
  font-weight: 700;
}
.view-content .cm-header-2, .view-content h2 {
  font-weight: 600;
}
.view-content .cm-header-3, .view-content h3 {
  font-weight: 600;
}
.view-content .cm-header-5, .view-content h5 {
  color: var(--color-accent-2);
}
.view-content .cm-header-6, .view-content h6 {
  color: var(--color-accent-2);
}
.view-content .cm-header-1.cm-strong,
.view-content .cm-header-2.cm-strong,
.view-content .cm-header-3.cm-strong {
  font-weight: 900;
}
.view-content h1, .view-content h2, .view-content h3, .view-content h5, .view-content h6 {
  letter-spacing: 0.009375em;
}
.view-content h1 b, .view-content h2 b, .view-content h3 b, .view-content h5 b, .view-content h6 b {
  font-weight: 900;
}

body {
  --h2: 20pt;
  --h3: 16pt;
  --h4: 15pt;
  --h5: 14pt;
  --h6: 12pt;
}

p {
  margin-block-start: 0;
  margin-block-end: 0;
}

.markdown-embed {
  padding-left: 0;
}

.clickable-icon.nav-action-button {
  margin: 0;
  padding: 5px 10px;
}

.view-header-title-container:not(.mod-at-start):before {
  display: none !important;
}

.mobile-navbar {
  color: var(--secondary-container);
}
.mobile-navbar .clickable-icon {
  color: var(--on-secondary-container);
}

.view-header-breadcrumb {
  text-overflow: ellipsis;
  max-width: 10vw;
  overflow: hidden;
  justify-content: center;
}

.mobile-navbar, .mobile-toolbar {
  background: var(--surface);
}
.mobile-navbar .mobile-toolbar-option, .mobile-navbar .clickable-icon, .mobile-toolbar .mobile-toolbar-option, .mobile-toolbar .clickable-icon {
  color: var(--on-surface);
}
.mobile-navbar .mobile-toolbar-option div, .mobile-navbar .clickable-icon div, .mobile-toolbar .mobile-toolbar-option div, .mobile-toolbar .clickable-icon div {
  border-color: var(--on-surface);
}

body.button-shapes-mobile.is-phone .mobile-navbar .clickable-icon {
  background-color: var(--surface-variant);
  height: 30px;
  width: 60px;
  max-width: 15vw;
  border-radius: 15px;
}
body.button-shapes-mobile.is-phone .mobile-toolbar {
  height: 45px;
}
body.button-shapes-mobile.is-phone .mobile-toolbar-option {
  background-color: var(--surface-variant);
  height: 30px;
  width: 50px;
  margin: 7px 5px;
  border-radius: 15px;
}
body.button-shapes-mobile.is-phone .nav-action-button {
  background-color: var(--surface);
  height: 40px;
  min-width: 65px;
  border-radius: 20px;
  color: var(--on-surface);
}

.is-phone .nav-buttons-container {
  flex-wrap: wrap !important;
}

.status-bar-item {
  mix-blend-mode: luminosity;
}

.workspace-tab-header-container-inner {
  padding-top: 2px !important;
  padding-bottom: 7px !important;
}

.mod-root .workspace-tab-header-container-inner {
  padding-top: 1px !important;
  padding-bottom: 9px !important;
}

div.titlebar {
  background-color: transparent !important;
}
div.titlebar .titlebar-text {
  opacity: 1;
  font-weight: normal;
}

body.mod-windows {
  --frame-right-space: 142px !important;
}
body.mod-windows .titlebar-text {
  justify-content: start;
  padding-left: 50px;
  padding-top: 4px;
}
body.mod-windows .titlebar-button-container .titlebar-button {
  width: 46px;
}
body.mod-windows .titlebar-button-container .titlebar-button svg {
  width: 11px;
  height: 11px;
}

body {
  --input-shadow: inherit !important;
  --input-shadow-hover: inset 0 1000px 0 0 #FFFFFF20 !important;
  --input-height: 32px;
}

.setting-hotkey {
  padding-top: 5px;
}

input[type=range]::-webkit-slider-thumb {
  background: var(--color-accent);
  border: none;
}

input[type=range]::-webkit-slider-thumb:hover {
  background: var(--icon-color-focused);
  border: none;
}

body {
  --slider-track-background: var(--surface-variant);
}

.vertical-tab-content-container .cm-scroller,
.vertical-tab-content-container .cm-scroller * {
  background-color: var(--background-secondary) !important;
  color: var(--text-color);
}

body.no-top-space .app-container {
  margin-top: -7px;
  height: calc(100% + 7px);
}

body.mod-windows.win-menu div.menu {
  background-color: rgba(248, 248, 248, 0.6274509804) !important;
  border: 1px solid rgba(54, 54, 54, 0.0705882353);
  padding: 1px 5px;
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  --menu-text-color: black;
  --win-blue: #0078d4;
  box-shadow: 0px 8px 8px 3px rgba(0, 0, 0, 0.05), 0px 7px 9px -7px rgba(0, 0, 0, 0.13);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}
body.mod-windows.win-menu div.menu .menu-item {
  border-radius: 4px !important;
  margin: 4px 0;
  padding: 5px 8px;
  font-size: 14px;
  transition: 0.09s;
  font-weight: 400;
  border-radius: 4px;
}
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon {
  color: var(--menu-text-color);
}
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg {
  fill: white;
  stroke-width: 1px;
  transform: scale(1.2);
}
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="m9 14 2 2 4-4"],
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="M9 12v-1h6v1"], body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="M11 17h2"], body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="M12 11v6"],
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="M18 9a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"], body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="M6 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"],
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="M21 14H11"], body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="m15 10-4 4 4 4"],
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"] {
  color: var(--win-blue);
}
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg rect, body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg circle {
  color: var(--win-blue);
}
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg rect[y="2"], body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg rect[y="3"] {
  color: var(--menu-text-color);
}
body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="M15 6a9 9 0 0 0-9 9"], body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg path[d="m15 10-4 4 4 4"], body.mod-windows.win-menu div.menu .menu-item .menu-item-icon svg polyline {
  fill: none;
}
body.mod-windows.win-menu div.menu .menu-item .menu-item-title {
  color: var(--menu-text-color);
}
body.mod-windows.win-menu div.menu .menu-item .cmdr-menu-more-options {
  color: var(--menu-text-color);
  opacity: 0.5;
}
body.mod-windows.win-menu div.menu .menu-item .cmdr-menu-more-options:hover {
  opacity: 1;
}
body.mod-windows.win-menu div.menu .menu-item.is-disabled {
  opacity: 0.5;
}
body.mod-windows.win-menu div.menu .menu-item.selected:not(.is-disabled):not(.is-label) {
  background-color: rgba(54, 54, 54, 0.0431372549);
}
body.mod-windows.win-menu div.menu .menu-separator {
  border-color: rgba(54, 54, 54, 0.0705882353);
  margin: 6px 0 !important;
}

body.mod-windows.win-menu.theme-dark div.menu {
  background-color: rgba(32, 31, 33, 0.8352941176) !important;
  border-color: rgba(255, 255, 255, 0.062745098);
}
body.mod-windows.win-menu.theme-dark div.menu .menu-item.selected:not(.is-disabled):not(.is-label) {
  background-color: rgba(255, 255, 255, 0.062745098);
}
body.mod-windows.win-menu.theme-dark div.menu .menu-separator {
  border-color: rgba(255, 255, 255, 0.062745098);
}
body.mod-windows.win-menu.theme-dark div.menu .menu-item {
  --menu-text-color: white;
}
body.mod-windows.win-menu.theme-dark div.menu .menu-item .menu-item-icon svg {
  fill: none;
}

body.mod-windows.win-menu.enable-mica div.menu {
  background-color: rgba(250, 250, 250, 0.9411764706) !important;
  -webkit-backdrop-filter: blur(8px) !important;
          backdrop-filter: blur(8px) !important;
}

body.mod-windows.win-menu.enable-mica.theme-dark div.menu {
  background-color: rgba(39, 38, 39, 0.9411764706) !important;
}

body:not(.vibrant).theme-dark {
  --color-accent: hsl(var(--accent-h), calc(var(--accent-s) * 0.6), 70%) !important;
  --color-accent-1: hsl(var(--accent-h), calc(var(--accent-s) * 0.7), 80%) !important;
  --color-accent-2: hsl(var(--accent-h), calc(var(--accent-s) * 0.8), 90%) !important;
  --primary: hsl(var(--accent-h), calc(var(--accent-s) * 0.75), 80%) !important;
  --secondary: hsl(var(--accent-h), calc(var(--accent-s) * 0.4), 70%) !important;
  --text-accent: hsl(var(--accent-h), calc(var(--accent-s) * 0.4), 90%) !important;
  --text-accent: hsl(var(--accent-h), calc(var(--accent-s) * 0.4), 90%) !important;
}

body:not(.vibrant).theme-light {
  --color-accent: hsl(var(--accent-h), calc(var(--accent-s) * 0.8), 75%) !important;
  --color-accent-1: hsl(var(--accent-h), calc(var(--accent-s) * 0.7), 80%) !important;
  --color-accent-2: hsl(var(--accent-h), calc(var(--accent-s) * 0.6), 85%) !important;
  --primary: hsl(var(--accent-h), calc(var(--accent-s) * 0.86), 73%) !important;
  --secondary: hsl(var(--accent-h), calc(var(--accent-s) * 0.6), 70%) !important;
  --text-accent: hsl(var(--accent-h), calc(var(--accent-s) * 0.6), 50%) !important;
}

body.theme-dark {
  --on-primary: var(--text-on-accent);
  --primary: var(--color-accent);
  --primary-container: var(--color-accent-1);
  --on-primary-container: var(--color-accent-2);
  --secondary: var(--color-accent-2);
  --background: hsl(var(--accent-h), calc(var(--accent-s) * 0.06), 11.5%);
  --on-background: #E5E1E5;
  --surface: hsl(var(--accent-h), calc(var(--accent-s) * 0.1), 15%);
  --surface-variant: hsl(var(--accent-h), calc(var(--accent-s) * 0.08), 28%);
  --surface-variant: hsl(var(--accent-h), calc(var(--accent-s) * 0.08), 28%);
  --surface-mid: hsl(var(--accent-h), calc(var(--accent-s) * 0.09), 19%);
  --on-surface-variant: hsl(var(--accent-h), calc(var(--accent-s) * 0.31), 80%);
  --outline: hsl(var(--accent-h), calc(var(--accent-s) * 0.5), 25%);
  --inverse-on-surface: #1C1B1E;
  --inverse-surface: #E5E1E5;
  --tab-background-active: var(--surface);
  --on-secondary: var(--text-on-accent);
  --secondary-container: var(--text-on-accent);
  --on-secondary-container: var(--color-accent-1);
  --tertiary-container: hsl(var(--accent-h), calc(var(--accent-s) * 0.5), 18%);
  --on-tertiary-container: hsl(var(--accent-h), calc(var(--accent-s) * 0.5), 78%);
  --text-selection: hsl(var(--accent-h), calc(var(--accent-s) * 0.3), 20%);
}

body.theme-light.dim-light-mode {
  --background: hsl(var(--accent-h), calc(var(--accent-s) * 0.15), 95%);
  --surface: hsl(var(--accent-h), calc(var(--accent-s) * 0.10), 98%);
}

body.theme-light {
  --primary: var(--color-accent-2);
  --primary-container: var(--color-accent-1);
  --on-primary-container: var(--color-accent);
  --secondary: var(--color-accent-2);
  --background: #fff;
  --on-background: #111;
  --surface: #FFF;
  --surface-variant: hsl(var(--accent-h), calc(var(--accent-s) * 0.75), 93%);
  --surface-mid: hsl(var(--accent-h), calc(var(--accent-s) * 0.35), 93%);
  --on-surface-variant: hsl(var(--accent-h), calc(var(--accent-s) * 0.30), 20%);
  --outline: hsl(var(--accent-h), calc(var(--accent-s) * 0.5), 85%);
  --inverse-on-surface: #EEE;
  --inverse-surface: #111;
  --on-secondary: var(--text-on-accent);
  --secondary-container: var(--text-on-accent);
  --on-secondary-container: var(--color-accent-1);
  --tertiary-container: hsl(var(--accent-h), calc(var(--accent-s) * 0.6), 66%);
  --on-tertiary-container: hsl(var(--accent-h), calc(var(--accent-s) * 0.2), 12%);
  --interactive-hover: hsl(var(--accent-h), 15%, 85%);
  --text-selection: hsl(var(--accent-h), calc(var(--accent-s) * 0.5), 80%);
}

/* Style Settings */
/* @settings

name: Material Flat
id: title
settings:
    -
        id: patch-header
        title: Behaviour
        type: heading
        level: 2
        collapsed: true
    - 
        id: hide-toolbar-desktop
        title: Hide left toolbar with left panel (Desktop)
        description: Hides the left toolbar ("ribbon") when the left panel is closed. (Windows & Linux Only)
        type: class-toggle
        default: true
    -  
        id: more-drag-desktop
        title: Drag window more easily (Desktop)
        description: Allows you to drag the window by the tab title bar (if enabled) as well as the left sidebar (ribbon). Recommended to leave on unless you have issues.
        type: class-toggle
        default: true
    -
        id: tab-drag-desktop
        title: Drag window with active tab (Desktop)
        description: When on, dragging an active tab will drag the entire window, instead of the tab itself.
        type: class-toggle
        default: false
    -
        id: pen-mode
        title: Pen Mode (for Excalidraw)
        description: Currently only applies to Excalidraw plugin in tray mode. Moves the toolbar to the left for easier access, and hides the welcome splash.
        type: class-toggle
        default: true
    -
        id: appearance-header
        title: Appearance
        type: heading
        level: 2
        collapsed: true
    - 
        id: vibrant
        title: Increase Accent Color Vibrancy
        description: Lets the fully saturated accent through on certain elements. May cause readability problems, depending on chosen accent color.
        type: class-toggle
        default: false
    -
        id: dim-light-mode
        title: Dim Light Mode
        description: Uses a slightly tinted grey in light mode, instead of pure white, for backgrounds.
        type: class-toggle
        default: false
    -
        id: space-above-document
        title: Add scroll space above document
        description: Adds some space above documents, so you can scroll them a little further down. Similar to Dropbox Paper.
        type: class-toggle
        default: true
    -
        id: disable-tooltip-animation
        title: Disable tooltip animation
        description: Disables the "pop-out" animation on tooltips.
        type: class-toggle
        default: false
    -
        id: enable-mica
        title: Enable transparency support (Desktop)
        description: Make parts of the main window translucent. Use instead of built-in transparency option. Both mac and windows require native frame to be set to system for this to work. Windows additionally requires Mica for Everyone, with the following settings for obsidian - Titlebar Color System, Backdrop Type Mica, Extend Frame into Client Area On
        type: class-toggle
        default: false
    -
        id: win-menu
        title: Native Windows Right-Click Menus (Desktop)
        description: Makes right-click menus on Windows look like Windows 11 native menus. 
        type: class-toggle
        default: true
    -
        id: no-top-space
        title: Remove top padding (Desktop)
        description: Removes the bit of space above the tabs.
        type: class-toggle
        default: true
    - 
        id: hide-sidebar-header
        title: Hide sidebar header (Mobile)
        description: Hides the header that appears at the top of the left sidebar on mobile. This will remove the buttons for opening settings and switching vaults, so you'll have to do those using the command palette.
        type: class-toggle
        default: false
    -   
        id: button-shapes-mobile
        title: Button shapes (Mobile)
        description: Displays many buttons on mobile as 'chips' with a distinct background. Turn off for a more minimal style and tighter spacing.
        type: class-toggle
        default: true
    - 
        id: font-header
        title: Fonts
        type: heading
        level: 2
        collapsed: true
    - 
        id: font-preview
        title: Preview Font
        description: Used in preview mode
        type: variable-text
        default:  Roboto, "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Microsoft YaHei Light", sans-serif;
    - 
        id: font-editor
        title: Editor Font
        description: Used in editor mode
        type: variable-text
        default: Roboto,"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Microsoft YaHei Light",sans-serif;
    - 
        id: font-monospace
        title: Monospace Font
        description: Used in code
        type: variable-text
        default: Menlo,"Roboto Mono",monospace,SFMono-Regular,Consolas;
    - 
        id: fab-header
        title: Floating Action Button settings
        type: heading
        level: 2
        collapsed: true
    -
        id: fab-enabled-mobile
        title: Enable FAB (Mobile)
        description: Displays a floating button in the bottom right, on phones. Tab title bar must be on!
        type: class-toggle
        default: true
    -
        id: fab-enabled-desktop
        title: Enable FAB (Desktop)
        description: Displays a floating button in the bottom right, on computers. Tab title bar must be on! (May not be visible in horizontally split views)
        type: class-toggle
        default: true
    -
        id: hide-buttons-mobile
        title: Hide tab-status-bar buttons on mobile
        description: Hides buttons in the optional tab status bar, excluding the one used for the FAB. (Useful with Commander plugin)
        type: class-toggle
        default: true
    - 
        id: custom-page-header
        title: Customizable FAB action
        description: Uses the first button in the optional tab status bar for the FAB. Set an item of your choice to appear first using the Commander plugin to customize the FAB action.
        type: class-toggle
    -
*//*# sourceMappingURL=theme.css.map */