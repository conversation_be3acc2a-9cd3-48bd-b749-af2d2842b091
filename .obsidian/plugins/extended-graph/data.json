{"interactiveSettings": {"tag": {"colormap": "hsv", "colors": [], "unselected": [], "noneType": "none", "showOnGraph": true, "enableByDefault": true}, "link": {"colormap": "rainbow", "colors": [], "unselected": [], "noneType": "none", "showOnGraph": true, "enableByDefault": true}, "folder": {"colormap": "winter", "colors": [], "unselected": [], "noneType": ".", "showOnGraph": true, "enableByDefault": false}}, "additionalProperties": {}, "backupGraphOptions": {"colorGroups": [], "hideUnresolved": false, "showAttachments": false, "showOrphans": true, "showTags": false, "localBacklinks": true, "localForelinks": true, "localInterlinks": false, "localJumps": 1, "lineSizeMultiplier": 1, "nodeSizeMultiplier": 1, "showArrow": false, "textFadeMultiplier": 0, "centerStrength": 0.5187132489703118, "linkDistance": 250, "linkStrength": 1, "repelStrength": 10}, "states": [{"id": "default-vault", "name": "Vault (default)", "engineOptions": {"colorGroups": [], "hideUnresolved": false, "showAttachments": false, "showOrphans": true, "showTags": false, "localBacklinks": true, "localForelinks": true, "localInterlinks": false, "localJumps": 1, "lineSizeMultiplier": 1, "nodeSizeMultiplier": 1, "showArrow": false, "textFadeMultiplier": 0, "centerStrength": 0.5187132489703118, "linkDistance": 250, "linkStrength": 1, "repelStrength": 10}, "toggleTypes": {}}], "startingStateID": "default-vault", "imageProperty": "image", "borderFactor": 0.06, "allowExternalImages": false, "allowExternalLocalImages": false, "nodesSizeProperty": "", "nodesSizeFunction": "default", "nodesColorColormap": "YlOrRd", "nodesColorFunction": "default", "linksSizeFunction": "default", "linksColorColormap": "YlOrRd", "linksColorFunction": "default", "zoomFactor": 2, "maxNodes": 20, "delay": 500, "enableFeatures": {"graph": {"auto-enabled": false, "tags": false, "properties": false, "property-key": true, "links": false, "curvedLinks": false, "folders": false, "imagesFromProperty": false, "imagesFromEmbeds": false, "imagesForAttachments": false, "focus": true, "shapes": false, "source": false, "target": false, "elements-stats": true, "names": false, "icons": true}, "localgraph": {"auto-enabled": true, "tags": true, "properties": false, "property-key": true, "links": true, "curvedLinks": false, "folders": false, "imagesFromProperty": true, "imagesFromEmbeds": false, "imagesForAttachments": false, "focus": false, "shapes": true, "source": false, "target": false, "elements-stats": true, "names": false, "icons": false}}, "shapeQueries": {"circle": {"combinationLogic": "AND", "index": 0, "rules": []}, "square": {"combinationLogic": "AND", "index": 1, "rules": []}, "triangle": {"combinationLogic": "AND", "index": 2, "rules": []}, "diamond": {"combinationLogic": "AND", "index": 3, "rules": []}, "pentagon": {"combinationLogic": "AND", "index": 4, "rules": []}, "hexagon": {"combinationLogic": "AND", "index": 5, "rules": []}, "octagon": {"combinationLogic": "AND", "index": 6, "rules": []}, "decagon": {"combinationLogic": "AND", "index": 7, "rules": []}, "star4": {"combinationLogic": "AND", "index": 8, "rules": []}, "star5": {"combinationLogic": "AND", "index": 9, "rules": []}, "star6": {"combinationLogic": "AND", "index": 10, "rules": []}, "star8": {"combinationLogic": "AND", "index": 11, "rules": []}, "star10": {"combinationLogic": "AND", "index": 12, "rules": []}}, "multipleNodesData": {}, "exportSVGOptions": {"asImage": true, "onlyVisibleArea": false, "showNodeNames": true, "useCurvedLinks": false, "useNodesShapes": false, "showArcs": false, "showFolders": true}, "fadeOnDisable": false, "focusScaleFactor": 1.8, "borderUnresolved": "", "invertArrows": false, "numberOfCharacters": null, "showOnlyFileName": false, "noExtension": false, "usePropertyForName": null, "iconProperty": "", "usePluginForIcon": true, "usePluginForIconColor": true, "useParentIcon": false, "collapseState": true, "collapseLegend": true}