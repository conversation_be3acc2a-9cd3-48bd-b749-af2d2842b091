/* For a full overview of styling options, see https://js.cytoscape.org/#style */
.tag-paper {
  shape: rectangle;
  width: 50px;
  height: 45px;
  font-size: 5;
  text-valign: center;
  text-max-width: 45px;
  text-opacity: 1;
}
edge[edgeCount] {
  width: mapData(edgeCount, 1, 15, 0.5, 3);
  line-opacity: mapData(edgeCount, 1, 15, 0.5, 0.9);
}
edge.inline {
  label: data(context);
  text-opacity: 0.2;
  font-size: 2;
  font-weight: 200;
  text-wrap: wrap;
  text-valign: center;
  text-max-width: 35px;
  text-overflow-wrap: anywhere;
}

edge.inline.hover {
  text-opacity: 1;
}
