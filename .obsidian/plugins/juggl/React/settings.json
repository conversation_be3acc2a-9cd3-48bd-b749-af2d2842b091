{"animateLayout": true, "autoAddNodes": true, "autoExpand": false, "autoZoom": true, "coreStore": "Obsidian", "expandInitial": true, "fdgdLayout": "d3-force", "filter": "", "height": "100%", "hoverEdges": true, "layout": {"name": "dagre", "animate": true, "animationDuration": 500, "spacingFactor": 0.5, "fit": false, "padding": 30, "nodeDimensionsIncludeLabels": true, "avoidOverlap": true}, "limit": 10000, "mergeEdges": true, "mode": "workspace", "navigator": false, "openWithShift": true, "readContent": true, "styleGroups": [], "toolbar": true, "width": "100%", "zoomSpeed": 0.99}