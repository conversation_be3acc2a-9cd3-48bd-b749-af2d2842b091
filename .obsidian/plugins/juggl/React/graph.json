{"elements": {"nodes": [{"data": {"id": "core:函数式更新 Functional Updates.md", "name": "函数式更新 Functional Updates", "path": "函数式更新 Functional Updates.md", "content": "函数式更新是 React 中一种特殊的状态更新方式，它接收一个函数作为参数而不是直接接收新状态值：\n\n```jsx\n// 对象式更新（直接提供新状态）\nsetState(newState);\n\n// 函数式更新（提供一个计算新状态的函数）\nsetState((prevState) => computeNewState(prevState));\n```\n\n这种函数接收先前的状态作为参数，并返回计算后的新状态。\n\n## 函数式更新的关键特性\n\n**1. 基于最新状态计算**\n\n- React 确保提供给更新函数的`prevState`始终是最新的\n- 解决依赖过时状态值的问题\n\n**2. 批处理安全**\n\n- 在批处理中多次更新同一状态时保证正确结果\n- 每次更新都基于前一次更新的结果\n\n**3. 适用于类组件和函数组件**\n\n- 类组件: `this.setState(prevState => ...)`\n- 函数组件: `setState(prevState => ...)`\n\n## 常见使用场景\n\n**1. 计数器递增/递减**\n\n```jsx\n// 错误方式（可能基于过时状态）\nsetCount(count + 1);\n\n// 正确方式（总是基于最新状态）\nsetCount((prevCount) => prevCount + 1);\n```\n\n**2. 切换布尔值**\n\n```jsx\nsetToggle((prevToggle) => !prevToggle);\n```\n\n**3. 数组操作**\n\n```jsx\n// 添加新项\nsetItems((prevItems) => [...prevItems, newItem]);\n\n// 删除项\nsetItems((prevItems) => prevItems.filter((item) => item.id !== idToRemove));\n\n// 更新特定项\nsetItems((prevItems) => prevItems.map((item) => (item.id === idToUpdate ? { ...item, ...updates } : item)));\n```\n\n**4. 对象更新**\n\n```jsx\n// 更新对象特定属性\nsetUser((prevUser) => ({\n  ...prevUser,\n  name: newName,\n}));\n```\n\n## 解决批处理中的问题\n\n在同一事件中多次调用 setState 时，函数式更新至关重要：\n\n```jsx\n// 问题示例：批处理导致执行结果可能不如预期\nfunction handleClick() {\n  setCount(count + 1); // 基于初始count=0\n  setCount(count + 1); // 仍基于初始count=0\n  setCount(count + 1); // 仍基于初始count=0\n  // 结果：count = 1，而非预期的3\n}\n\n// 解决方案：函数式更新\nfunction handleClick() {\n  setCount((prev) => prev + 1); // 0 -> 1\n  setCount((prev) => prev + 1); // 1 -> 2\n  setCount((prev) => prev + 1); // 2 -> 3\n  // 结果：count = 3，符合预期\n}\n```\n\n## 在异步代码中的重要性\n\n函数式更新在异步环境中尤为重要，可以解决闭包陷阱问题：\n\n```jsx\n// 问题：使用可能过时的state\nfunction delayedIncrement() {\n  setTimeout(() => {\n    setCount(count + 1); // 使用的是定义setTimeout时的count值\n  }, 1000);\n}\n\n// 解决方案：函数式更新\nfunction delayedIncrement() {\n  setTimeout(() => {\n    setCount((prevCount) => prevCount + 1); // 使用调用时的最新count值\n  }, 1000);\n}\n```\n\n## 与对象式更新结合\n\n函数式更新可以与展开运算符结合使用，实现部分状态更新：\n\n```jsx\n// 在函数组件中手动实现浅合并\nsetState((prevState) => ({\n  ...prevState,\n  specificProperty: newValue,\n}));\n```\n\n函数式更新是 React 状态管理的关键模式，能确保更新基于最新状态，避免竞态条件，并在批处理和异步环境中正确工作。\n", "degree": 4, "nameLength": 24}, "position": {"x": 161.625, "y": 475.44999999999993}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note has-incoming-inline protected has-outgoing-inline"}, {"data": {"id": "core:React.md", "name": "React", "path": "React.md", "content": "---\naliases:\n  - React.js\n  - ReactJS\n  - React库\ntags:\n  - 前端\n  - JavaScript\n  - UI框架\ncanvas: []\n---\nReact 是一个用于构建用户界面的 JavaScript 库，以其组件化、声明式和高效的特性而闻名。  要深入理解 React，需要掌握一系列核心概念和技术。以下是 React 的详细知识点，涵盖了从基础到进阶的各个方面：\n\n[[React 基础概念 (Fundamentals)]]\n\n**二、 React Hooks (Hooks)**\n\nHooks 是 React 16.8 引入的新特性，允许你在函数组件中使用 state 和其他 React 特性，而无需编写类组件。Hooks 的出现使得函数组件更加强大和灵活，成为 React 开发的主流方式。\n\n1.  **基础 Hooks:**\n    *   **`useState`:**  用于在函数组件中添加 state 状态。\n        *   `const [state, setState] = useState(initialState);`\n        *   `state`: 当前状态值。\n        *   `setState`: 更新状态的函数。\n        *   `initialState`: 初始状态值。\n    *   **`useEffect`:**  用于在函数组件中执行副作用操作 (side effects)，例如数据获取、DOM 操作、定时器等。\n        *   `useEffect(() => { /* 副作用操作 */ return () => { /* 清除副作用 */ }; }, [dependencies]);`\n        *   第一个参数：副作用函数，在每次渲染后执行 (默认情况) 或在依赖项变化时执行。\n        *   第二个参数 (可选)：依赖项数组，控制副作用函数的执行时机。\n            *   空数组 `[]`:  只在组件首次渲染和卸载时执行。\n            *   不传递数组:  每次渲染后都执行。\n            *   数组包含变量:  当数组中的变量发生变化时执行。\n        *   返回值 (可选)：清除函数，在组件卸载或下一次副作用函数执行前执行，用于清理副作用，例如取消定时器、取消网络请求等。\n    *   **`useContext`:**  用于消费 Context 对象的值，方便在组件树中跨层级传递数据。\n        *   `const contextValue = useContext(MyContext);`\n        *   `MyContext`: Context 对象 (通过 `React.createContext` 创建)。\n        *   `contextValue`: Context 对象提供的当前值。\n\n2.  **其他常用的 Hooks:**\n    *   **`useRef`:**  用于创建可变的 ref 对象，可以用于访问 DOM 元素或在组件的整个生命周期中保存一些数据。\n        *   `const myRef = useRef(initialValue);`\n        *   `myRef.current`:  访问 ref 对象当前的值。\n    *   **`useMemo`:**  用于缓存计算结果，只有当依赖项发生变化时才重新计算，优化性能。\n        *   `const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);`\n        *   第一个参数：计算函数。\n        *   第二个参数：依赖项数组。\n    *   **`useCallback`:**  用于缓存函数，只有当依赖项发生变化时才重新创建函数，优化性能，尤其是在将回调函数传递给子组件时。\n        *   `const memoizedCallback = useCallback(() => { doSomething(a, b); }, [a, b]);`\n        *   第一个参数：回调函数。\n        *   第二个参数：依赖项数组。\n    *   **`useReducer`:**  `useState` 的替代方案，用于管理更复杂的状态逻辑，类似于 Redux 的 reducer。\n        *   `const [state, dispatch] = useReducer(reducer, initialState, initialAction);`\n        *   `reducer`:  reducer 函数，接收当前状态和 action，返回新的状态。\n        *   `dispatch`:  派发 action 的函数，触发状态更新。\n        *   `initialState`:  初始状态值。\n        *   `initialAction` (可选):  初始 action，用于初始化状态。\n    *   **`useImperativeHandle`:**  配合 `forwardRef` 使用，允许父组件自定义暴露给子组件 ref 的实例值。\n    *   **`useLayoutEffect`:**  类似于 `useEffect`，但在 DOM 更新之后、浏览器绘制之前同步执行，适用于需要读取 DOM 布局信息的场景。\n    *   **`useDebugValue`:**  用于在 React DevTools 中显示自定义 Hook 的调试信息。\n\n3.  **自定义 Hooks (Custom Hooks):**\n    *   **概念:**  将组件逻辑提取到独立的函数中，实现逻辑复用。\n    *   **规则:**\n        *   自定义 Hook 也是一个 JavaScript 函数，函数名以 `use` 开头 (例如 `useFetch`, `useLocalStorage`)。\n        *   自定义 Hook 可以调用其他的 Hook。\n        *   自定义 Hook 不会改变 React 的行为，只是提供了一种更方便的逻辑复用方式。\n    *   **优势:**\n        *   **逻辑复用:**  将组件逻辑提取到自定义 Hook 中，可以在多个组件之间复用逻辑。\n        *   **代码组织:**  使组件代码更简洁，逻辑更清晰。\n        *   **可测试性:**  自定义 Hook 更容易进行单元测试。\n\n**三、 组件进阶 (Advanced Components)**\n\n1.  **组件组合 (Component Composition):**\n    *   **概念:**  将小的、独立的组件组合成更大的、复杂的组件。\n    *   **方式:**\n        *   **Props 传递:**  父组件通过 props 将数据和函数传递给子组件。\n        *   **组合组件 (Composition Components):**  创建专门用于组合其他组件的组件，例如布局组件、容器组件等。\n        *   **Render Props:**  组件通过 props 传递一个函数，该函数用于渲染子组件，允许父组件控制子组件的渲染逻辑。\n        *   **高阶组件 (Higher-Order Components, HOCs):**  接收一个组件作为参数，并返回一个新的组件，用于增强组件的功能或复用组件逻辑。\n\n2.  **高阶组件 (HOCs):**\n    *   **概念:**  一个函数，接收一个组件作为参数，并返回一个新的组件。\n    *   **作用:**\n        *   **代码复用:**  将通用的组件逻辑 (例如权限验证、日志记录、数据订阅等) 提取到 HOC 中，在多个组件之间复用。\n        *   **属性代理 (Props Proxy):**  HOC 可以修改或增强传递给被包裹组件的 props。\n        *   **反向继承 (Inheritance Inversion):**  HOC 可以继承被包裹组件，并控制其渲染逻辑，但这种方式不太推荐，容易引起问题。\n    *   **示例:**  `withAuth(WrappedComponent)`, `withLogging(WrappedComponent)`。\n    *   **Hooks 的替代方案:**  Hooks 出现后，自定义 Hooks 逐渐取代了 HOCs，因为 Hooks 更简洁、灵活，更容易理解和使用。\n\n3.  **Render Props:**\n    *   **概念:**  组件通过 props 传递一个函数，该函数用于渲染子组件。\n    *   **作用:**  允许父组件控制子组件的渲染逻辑，实现更灵活的组件组合和逻辑复用。\n    *   **示例:**  `<DataProvider render={data => <MyComponent data={data} />} />`。\n    *   **Hooks 的替代方案:**  Hooks 出现后，自定义 Hooks 结合函数作为 children 的方式，也逐渐取代了 Render Props，更加简洁和灵活。\n\n4.  **Context (上下文):**\n    *   **概念:**  一种在组件树中跨层级传递数据的方式，避免了逐层传递 props 的繁琐。\n    *   **适用场景:**  共享全局性的数据，例如主题、用户认证信息、语言设置等。\n    *   **API:**\n        *   **`React.createContext(defaultValue)`:**  创建 Context 对象，`defaultValue` 是默认值，当组件在组件树中找不到 Provider 时使用。\n        *   **`Context.Provider`:**  提供 Context 值，接受 `value` prop，传递给其子组件。\n        *   **`useContext(Context)`:**  在函数组件中使用，消费 Context 对象的值。\n        *   **`Context.Consumer` (类组件):**  在类组件中使用，消费 Context 对象的值，使用 Render Props 模式。\n    *   **注意:**  Context 应该谨慎使用，过度使用 Context 可能会导致组件之间的耦合度增加，难以维护。\n\n5.  **Refs (引用):**\n    *   **概念:**  用于访问 DOM 元素或 React 组件实例。\n    *   **使用场景:**\n        *   直接操作 DOM 元素 (例如焦点控制、动画、第三方库集成)。\n        *   访问类组件实例的方法或属性。\n    *   **创建 Refs:**\n        *   **`React.createRef()` (类组件和函数组件):**  创建一个 ref 对象，通过 `ref` prop 绑定到元素或组件。\n        *   **`useRef(initialValue)` (函数组件):**  创建一个 ref 对象，在组件的整个生命周期中保持不变。\n    *   **访问 Refs:**\n        *   通过 `ref.current` 访问 ref 对象当前的值。\n    *   **转发 Refs (Forwarding Refs):**  将 ref 从父组件转发到子组件的 DOM 元素或组件实例。\n        *   **`React.forwardRef((props, ref) => <MyComponent ref={ref} {...props} />)`:**  使用 `React.forwardRef` 包裹子组件，子组件的函数组件或类组件的 `render` 方法会接收第二个参数 `ref`。\n    *   **注意:**  应谨慎使用 refs，避免过度依赖直接 DOM 操作，尽量使用 React 的声明式方式来管理 UI。\n\n6.  **Fragments (片段):**\n    *   **概念:**  允许组件返回多个元素而无需包裹在一个额外的 DOM 节点中。\n    *   **语法:**  `<React.Fragment>` 或简写 `<></>`。\n    *   **作用:**  减少 DOM 嵌套层级，提高性能，避免 CSS 样式和布局问题。\n\n7.  **Portals (传送门):**\n    *   **概念:**  允许将子组件渲染到 DOM 树的另一个位置，而不是其父组件的 DOM 节点下。\n    *   **使用场景:**  模态框 (Modal)、提示框 (Tooltip)、加载指示器 (Loading Indicator) 等需要在 DOM 树的顶层渲染的元素。\n    *   **API:**  `ReactDOM.createPortal(child, container)`\n        *   `child`:  要渲染的 React 元素或组件。\n        *   `container`:  DOM 元素，作为渲染的目标容器。\n\n8.  **严格模式 (Strict Mode):**\n    *   **概念:**  React 提供的一种辅助开发模式，用于检测代码中的潜在问题和不推荐使用的 API。\n    *   **启用方式:**  将 `<React.StrictMode>` 组件包裹在组件树的顶层或需要检查的部分。\n    *   **作用:**\n        *   **识别不安全的生命周期方法:**  例如 `componentWillMount`, `componentWillUpdate`, `componentWillReceiveProps` (在未来的 React 版本中可能会被废弃)。\n        *   **警告过时的 API 用法:**  例如 `findDOMNode`。\n        *   **检测意外的副作用:**  例如在渲染函数中修改 state。\n        *   **检查字符串 refs 用法:**  字符串 refs 已被废弃，推荐使用回调 refs 或 `React.createRef()`。\n        *   **双重调用函数组件:**  在开发模式下，会双重调用函数组件，帮助检测纯函数组件的纯度。\n    *   **注意:**  严格模式只在开发模式下生效，不会影响生产环境的性能。\n\n9.  **错误边界 (Error Boundaries):**\n    *   **概念:**  用于捕获其子组件树中发生的 JavaScript 错误，并优雅地展示备用 UI，而不是崩溃整个应用。\n    *   **实现方式:**  类组件实现 `static getDerivedStateFromError(error)` 和 `componentDidCatch(error, errorInfo)` 生命周期方法。\n    *   **作用:**  提高应用的健壮性和用户体验，防止错误扩散导致整个应用崩溃。\n    *   **注意:**  错误边界只能捕获渲染阶段、生命周期方法和构造函数中的错误，不能捕获事件处理函数、异步代码和服务器端渲染中的错误。\n\n**四、 性能优化 (Performance Optimization)**\n\n1.  **组件优化:**\n    *   **`shouldComponentUpdate` (类组件):**  生命周期方法，允许你手动控制组件是否需要重新渲染，避免不必要的渲染。\n    *   **`React.memo` (函数组件):**  高阶组件，用于缓存函数组件的渲染结果，只有当 props 发生浅比较变化时才重新渲染。\n    *   **`PureComponent` (类组件):**  类组件的基类，实现了浅比较的 `shouldComponentUpdate`，适用于 props 和 state 都是浅比较的场景。\n    *   **避免不必要的渲染:**  合理使用 `shouldComponentUpdate`, `React.memo`, `PureComponent`，减少组件的重新渲染次数。\n\n2.  **列表优化:**\n    *   **Key 属性:**  为列表项添加唯一的 `key` 属性，帮助 React 识别列表项的变化，提高 Diff 算法的效率。\n    *   **虚拟化 (Virtualization):**  对于长列表，只渲染可视区域内的列表项，延迟渲染或不渲染不可视区域的列表项，减少 DOM 元素数量，提高渲染性能。可以使用 `react-window`, `react-virtualized` 等库实现虚拟化。\n\n3.  **代码分割 (Code Splitting):**\n    *   **概念:**  将应用代码分割成多个 bundle，按需加载，减少初始加载时间。\n    *   **方式:**\n        *   **动态 `import()` 语法:**  使用动态 `import()` 语法进行组件或模块的懒加载。\n        *   **`React.lazy` 和 `Suspense`:**  React 提供的 API，用于懒加载组件，配合 `Suspense` 组件显示加载状态。\n        *   **路由级代码分割:**  使用路由库 (例如 React Router) 的动态路由功能，实现路由组件的懒加载。\n\n4.  **图片优化:**\n    *   **图片懒加载:**  延迟加载视口外的图片，提高页面加载速度。可以使用 `react-lazy-load` 等库。\n    *   **图片压缩和格式优化:**  使用合适的图片格式 (例如 WebP) 和压缩工具，减小图片文件大小。\n    *   **使用 CDN:**  将图片资源部署到 CDN，加速图片加载速度。\n\n5.  **服务端渲染 (Server-Side Rendering, SSR):**\n    *   **概念:**  在服务器端渲染 React 组件，生成 HTML 字符串，然后将 HTML 返回给客户端。客户端接收到 HTML 后，再进行 hydration，将 React 事件绑定到 HTML 上。\n    *   **优势:**\n        *   **首屏渲染更快:**  用户可以更快地看到页面内容，提高用户体验。\n        *   **SEO 优化:**  搜索引擎更容易抓取页面内容，提高 SEO 排名。\n    *   **框架:**  Next.js, Remix 等框架提供了 SSR 的支持。\n\n6.  **避免内联函数和对象:**\n    *   在 JSX 中避免直接创建内联函数和对象，因为每次渲染都会创建新的函数和对象，导致不必要的组件重新渲染。\n    *   使用 `useCallback` 缓存回调函数，使用 `useMemo` 缓存对象。\n\n**五、 React 生态系统 (Ecosystem)**\n\n1.  **路由 (Routing):**\n    *   **React Router:**  最流行的 React 路由库，用于构建单页面应用 (SPA) 的导航和路由功能。\n    *   **Reach Router:**  另一个流行的 React 路由库，注重可访问性。\n\n2.  **状态管理 (State Management):**\n    *   **Redux:**  一个可预测的状态容器，适用于大型、复杂应用的状态管理。\n    *   **Context API:**  React 内置的状态管理方案，适用于中小型应用或组件树中共享少量数据的场景。\n    *   **Zustand:**  一个小巧、快速、易用的状态管理库，适用于各种规模的应用。\n    *   **MobX:**  另一个流行的状态管理库，使用响应式编程范式。\n    *   **Recoil:**  Facebook 团队推出的新的状态管理库，基于原子 (atoms) 和选择器 (selectors) 的概念。\n\n3.  **表单处理 (Form Handling):**\n    *   **React Hook Form:**  一个高性能、灵活的表单验证和处理库，基于 Hooks。\n    *   **Formik:**  另一个流行的表单处理库，提供了更全面的表单功能。\n    *   **Final Form:**  一个轻量级的表单处理库，注重性能和灵活性。\n\n4.  **样式 (Styling):**\n    *   **CSS Modules:**  CSS 模块化方案，避免 CSS 样式冲突。\n    *   **Styled Components:**  CSS-in-JS 库，允许你在 JavaScript 代码中编写 CSS 样式。\n    *   **Emotion:**  另一个流行的 CSS-in-JS 库，注重性能和灵活性。\n    *   **Tailwind CSS:**  一个实用主义的 CSS 框架，提供了大量的预定义 CSS 类。\n    *   **Ant Design, Material UI, Chakra UI:**  流行的 React UI 组件库，提供了丰富的 UI 组件和样式。\n\n5.  **测试 (Testing):**\n    *   **Jest:**  流行的 JavaScript 测试框架，由 Facebook 开发，与 React 配合良好。\n    *   **React Testing Library:**  一个注重用户行为的 React 测试库，鼓励测试组件的行为而不是实现细节。\n    *   **Enzyme:**  Airbnb 开发的 React 测试工具库，提供了更底层的组件测试 API (React Testing Library 逐渐取代 Enzyme)。\n    *   **Cypress, Playwright:**  端到端 (E2E) 测试框架，用于测试整个应用的用户流程。\n\n6.  **构建工具 (Build Tools):**\n    *   **Webpack:**  最流行的 JavaScript 模块打包器，用于构建 React 应用。\n    *   **Parcel:**  一个零配置的 JavaScript 模块打包器，易于上手。\n    *   **Vite:**  一个新一代的前端构建工具，基于 Rollup 和 esbuild，快速、轻量级。\n\n7.  **服务端渲染框架 (SSR Frameworks):**\n    *   **Next.js:**  最流行的 React SSR 框架，提供了丰富的功能，例如路由、数据获取、API 路由、静态站点生成 (SSG) 等。\n    *   **Remix:**  一个全栈 Web 框架，基于 Web 标准，提供了更好的用户体验和开发效率。\n    *   **Gatsby:**  一个基于 React 的静态站点生成器 (SSG)，适用于构建博客、文档站点等静态内容网站。\n\n**六、 学习路径建议**\n\n1.  **React 基础:**\n    *   JSX 语法\n    *   组件 (函数组件和类组件)\n    *   Props 和 State\n    *   事件处理\n    *   条件渲染和列表渲染\n    *   虚拟 DOM 和 Diff 算法\n    *   单向数据流\n\n2.  **React Hooks:**\n    *   `useState`, `useEffect`, `useContext`\n    *   `useRef`, `useMemo`, `useCallback`, `useReducer`\n    *   自定义 Hooks\n\n3.  **组件进阶:**\n    *   组件组合\n    *   Context API\n    *   Refs 和 Forwarding Refs\n    *   Fragments 和 Portals\n    *   严格模式和错误边界\n\n4.  **性能优化:**\n    *   组件优化 (`shouldComponentUpdate`, `React.memo`, `PureComponent`)\n    *   列表优化 (Key 属性, 虚拟化)\n    *   代码分割\n    *   图片优化\n    *   服务端渲染 (SSR)\n\n5.  **React 生态系统:**\n    *   路由 (React Router)\n    *   状态管理 (Context API, Redux, Zustand 等)\n    *   样式 (CSS Modules, Styled Components, Tailwind CSS 等)\n    *   测试 (Jest, React Testing Library)\n    *   构建工具 (Webpack, Vite)\n    *   服务端渲染框架 (Next.js)\n\n**学习资源:**\n\n*   **React 官方文档:**  [https://reactjs.org/](https://reactjs.org/)  最权威的学习资源，务必仔细阅读。\n*   **React 中文文档:**  [https://zh-hans.reactjs.org/](https://zh-hans.reactjs.org/)\n*   **React 官方教程:**  [https://reactjs.org/tutorial/tutorial.html](https://reactjs.org/tutorial/tutorial.html)\n*   **Egghead.io, Udemy, Coursera, Frontend Masters 等在线学习平台:**  有很多优秀的 React 课程。\n*   **GitHub 上优秀的 React 开源项目:**  学习优秀的代码实践。\n*   **React 社区和论坛:**  Stack Overflow, Reddit r/reactjs 等。\n\n**学习建议:**\n\n*   **从基础开始:**  先掌握 React 的基础概念，再逐步深入学习进阶知识。\n*   **多练习:**  实践是最好的老师，多写代码，多做项目，才能真正掌握 React。\n*   **阅读文档:**  官方文档是最好的学习资源，遇到问题要查阅文档。\n*   **关注社区动态:**  React 生态系统发展迅速，要关注社区的最新动态和技术趋势。\n*   **持续学习:**  前端技术更新迭代很快，要保持持续学习的热情。\n\n希望这份 React 详细知识点能够帮助你系统地学习和掌握 React 技术。祝你学习顺利！", "aliases": ["React.js", "ReactJS", "React库"], "tags": ["前端", "JavaScript", "UI框架"], "canvas": [], "degree": 2, "nameLength": 5}, "position": {"x": 342.125, "y": 240.2}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note tag-前端 tag-JavaScript tag-UI框架 protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:CSR.md", "name": "CSR", "path": "CSR.md", "content": "---\naliases:\n  - 客户端渲染\n  - Client Side Rendering\n↑:\n  - \"[[渲染模式 Rendering Patterns]]\"\n---\n\n### Summary\n\nCSR 是一种 Web 应用渲染方式，主要由浏览器执行 JavaScript 来生成和更新页面内容。\n浏览器端执行 JavaScript 生成页面内容，例如 [[React]], [[Vue]], [[Angular]]\n\n### Notes\n\n在客户端渲染 (CSR) 中，浏览器最初加载一个几乎为空的 HTML 页面，以及指向 JavaScript 文件的链接。当 JavaScript 文件下载并执行后，框架（如 React、Vue.js 或 Angular）会接管，并负责：\n\n1.  **构建初始 DOM**: JavaScript 代码会根据应用的状态和数据，动态地创建和插入 HTML 元素，构建出完整的页面结构。\n2.  **事件处理**: 框架会绑定事件监听器到 DOM 元素上，以便响应用户的交互（如点击、输入等）。\n3.  **数据获取**: 通常，JavaScript 代码会通过 AJAX 或 Fetch API 向服务器请求数据，获取数据后再更新 DOM。\n4.  **路由管理**: 框架会处理 URL 的变化（例如通过 History API 或 hashchange 事件），根据不同的 URL 加载相应的组件和内容，实现页面间的导航。\n5.  **状态管理**: 框架会维护应用的状态，当状态发生变化时，会自动重新渲染受影响的 DOM 部分，保持 UI 与数据同步。\n\n**流程图**:\n\n```mermaid\ngraph LR\n    A[浏览器加载空 HTML] --> B{下载并执行 JavaScript}\n    B --> C[构建初始 DOM]\n    C --> D{绑定事件监听}\n    D --> E[处理用户交互]\n    B --> F{发起数据请求}\n    F --> G[获取数据]\n    G --> H{更新 DOM}\n    B --> I{管理路由}\n    I --> J[根据 URL 加载内容]\n    B --> K{管理状态}\n\tK --> H\n    E --> H\n```\n\n**CSR 的优缺点**:\n\n- **优点**:\n  - 首屏加载后，后续页面切换通常更快，因为只需更新部分内容。\n  - 可以实现丰富的交互效果和动态内容。\n  - 前后端分离，后端只需提供 API 接口。\n- **缺点**:\n  - 首屏加载可能较慢，因为需要下载和执行 JavaScript。\n  - 不利于 SEO (搜索引擎优化)，因为搜索引擎爬虫可能无法执行 JavaScript。\n  - 对客户端设备性能有一定要求。\n\n### Relation\n\n[常用于::[[SPA]]]\n", "aliases": ["客户端渲染", "Client Side Rendering"], "↑": ["[[渲染模式 Rendering Patterns]]"], "degree": 1, "nameLength": 3}, "position": {"x": 342.125, "y": 207.75}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note has-outgoing-inline"}, {"data": {"id": "core:JSX.md", "name": "JSX", "path": "JSX.md", "content": "---\naliases:\n  - JavaScript XML\ntags:\n  - todo\n---\n- JSX（JavaScript XML）是 JavaScript 的语法扩展，允许在 JavaScript 中直接编写类似 HTML 的标记结构。它由 React 团队首创，但也可用于 Vue 等其他框架。\n- **本质**：JSX 通过 Babel 等工具编译为 `React.createElement()` 函数调用，生成虚拟 DOM 对象，最终渲染为真实 DOM。\n\n- [生成::[[虚拟DOM]]]\n- [借助 Babel的工具转换::[[Babel]]]\n\n---\n\n### 2. **基本语法规则**\n\n- **类 HTML 结构**：JSX 语法与 HTML 相似，但需遵循 XML 闭合规则（如 `<br />`）。\n- **表达式嵌入**：用 `{}` 包裹 JavaScript 表达式，支持变量、函数调用等动态内容。 (`{}`包裹::[[表达式]])  \n\n```jsx\n  const element = <h1>Hello, {name}!</h1>;\n  // 等价于\n  const element = React.createElement(\"h1\",{\n    children: \"Hello, \" + name + \"!\"\n  })\n```\n\n- **属性命名**：使用驼峰式命名（如 `className` 代替 `class`，`onClick` 代替 `onclick`）。\n- **子元素规则**：可嵌套子元素或组件，支持数组渲染（需唯一 `key`）。\n\n---\n\n### 3. **核心特性**\n\n- **动态性**：支持条件渲染（三元运算符、逻辑与）、列表渲染（`map` 遍历）、样式动态绑定（内联样式或 `className`）。\n- **组件化**：自定义组件首字母必须大写（如 `<MyComponent />`），否则会被识别为 HTML 标签。\n- **安全性**：默认转义所有嵌入变量，防止 XSS 攻击。\n\n---\n\n### 4. **优势与适用场景**\n\n- **可读性**：直观的类 HTML 语法降低学习成本，提升代码可维护性。\n- **开发效率**：将 UI 结构与逻辑紧密结合，减少模板与代码分离的复杂性。\n- **灵活性**：适合动态性高的场景（如动态标签名、复杂条件渲染），常见于组件库开发。\n- **性能优化**：通过虚拟 DOM 实现高效更新。\n\n---\n\n### 5. **编译与底层机制**\n\n- **编译流程**：JSX 代码经 Babel 转换为 `React.createElement(type, props, children)`，生成虚拟 DOM 对象。\n- **虚拟 DOM**：轻量化的 JavaScript 对象，描述真实 DOM 结构，通过 Diff 算法优化渲染性能。\n\n---\n\n### 6. **注意事项与陷阱**\n\n- **HTML 实体处理**：需使用 Unicode 或直接插入原始 HTML。\n- **属性限制**：避免使用非标准 HTML 属性（如自定义属性需加 `data-` 前缀）。\n- **性能权衡**：JSX 动态性强，但编译优化不如 Vue 的 template 彻底，在极端性能场景下需谨慎。\n\n---\n\n### 总结\n\nJSX 通过融合 HTML 的直观性与 JavaScript 的动态能力，成为现代前端框架（如 React、Vue）构建声明式 UI 的核心工具。其核心在于语法扩展、编译转换、虚拟 DOM 机制，以及开发效率与安全性的平衡。理解这些知识点有助于高效利用 JSX 应对复杂场景，同时规避潜在问题。\n", "aliases": ["JavaScript XML"], "tags": ["todo"], "degree": 4, "nameLength": 3}, "position": {"x": 331.375, "y": 309}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note tag-todo protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:虚拟DOM.md", "name": "虚拟DOM", "path": "虚拟DOM.md", "content": "---\naliases:\n  - virtual DOM\n---\n虚拟 DOM 是前端框架（如 React）中使用的一种技术，它是一个轻量级的、在内存中表示真实 DOM（文档对象模型）的结构，旨在提升性能。真实 DOM 是网页在浏览器中的结构，直接操作它会很慢，因为每次更改（例如更新文本或添加元素）都会触发页面重排和重绘。虚拟 DOM 通过充当中间层解决了这个问题，其工作流程如下：\n\n1. **初始渲染**：当用户界面（UI）首次渲染时，创建一个虚拟 DOM 树来镜像真实的 DOM。\n2. **更新**：当 UI 发生变化（例如用户输入或状态更新）时，生成一个新的虚拟 DOM 树。\n3. **Diffing**：通过 Diff 算法比较新旧虚拟 DOM 树，找出具体的变化。\n4. **协调（Reconciliation）**：将计算出的差异应用到真实 DOM 上，尽量减少昂贵的操作。\n\n这个过程称为“协调”，它确保更新过程快速而高效。\n\n---\n\n## 核心概念\n\n1. **虚拟 DOM 树**\n   - 虚拟 DOM 是对真实 DOM 的抽象描述，包含节点、属性、子节点等信息。\n   - 在状态更新时，框架会生成新的虚拟 DOM 树。\n2. **Diff 算法** [[vDOM Diffing]]\n   - 框架会将新生成的虚拟 DOM 与旧的虚拟 DOM 进行比较，找出两者之间的差异。\n   - 通过精确定位变化部分，只更新这些部分，避免整个 DOM 重渲染，提升性能。\n1. **[[Reconciliation]]（协调过程）**\n   - Diff 算法的过程称为“协调”，框架会在协调过程中生成更新补丁，最后将这些补丁应用到真实 DOM 上。\n   - 这种方式使得更新操作更高效，同时让开发者可以使用声明式编程。\n4. **性能优化**\n   - 由于直接操作真实 DOM 的代价较高，虚拟 DOM 提供了一种间接操作 DOM 的手段，通过批量更新和最小化 DOM 操作来优化性能。\n\n---\n\n## 代码示例\n\n以下以 React 为例，展示了虚拟 DOM 的典型使用场景。当组件状态改变时，React 会生成新的虚拟 DOM，然后进行 diff 比较并更新真实 DOM。\n\n```jsx\nimport React, { useState } from \"react\";\n\nfunction Counter() {\n  // 使用 useState 管理计数器状态\n  const [count, setCount] = useState(0);\n\n  // 当点击按钮时，更新状态；React 内部会生成新的虚拟 DOM，\n  // 并与旧的虚拟 DOM 进行比对，仅更新发生变化的部分\n  const increment = () => setCount(count + 1);\n\n  return (\n    <div>\n      <p>当前计数：{count}</p>\n      <button onClick={increment}>增加</button>\n    </div>\n  );\n}\n\nexport default Counter;\n```\n\n在上面的示例中：\n\n- 每次点击按钮时，`count` 发生变化，导致组件重新渲染。\n- React 会基于新的状态生成新的虚拟 DOM 树，并与之前的虚拟 DOM 树进行 Diff 比较。\n- 最终，React 将计算出的最小更新操作应用到真实 DOM 中，从而高效地更新页面。\n\n### Mermaid 结构图\n\n下面的 Mermaid 图展示了虚拟 DOM 更新过程的基本流程：\n\n```mermaid\nflowchart TD\n    A[状态变化触发组件更新]\n    B[生成新的虚拟 DOM 树]\n    C[Diff 算法对比新旧虚拟 DOM]\n    D[生成更新补丁]\n    E[将补丁应用到真实 DOM]\n\n    A --> B\n    B --> C\n    C --> D\n    D --> E\n```\n\n---\n\n## 在实际开发中的思考\n\n1. **性能提升**\n\n   - 虚拟 DOM 能够将复杂的 DOM 操作转化为对内存中对象的高效操作，再通过 Diff 算法找出差异，实现局部更新，极大减少了直接操作真实 DOM 带来的性能损耗。\n\n2. **声明式编程**\n\n   - 开发者只需关注应用状态和界面描述，框架会自动管理 DOM 更新。这样可以使代码更清晰、易于维护。\n\n3. **局限性与权衡**\n\n   - 虚拟 DOM 引入了额外的内存和计算开销。在某些小型应用中，直接操作真实 DOM 可能更加简单高效。\n   - 虚拟 DOM 的更新策略依赖于 Diff 算法，复杂应用中可能需要针对性优化以避免不必要的更新。\n\n4. **实际应用**\n\n   - React、Vue 等主流框架均采用虚拟 DOM 或类似机制来优化 UI 更新过程。理解其原理有助于开发者更好地调试和优化应用性能。\n", "aliases": ["virtual DOM"], "degree": 2, "nameLength": 5}, "position": {"x": 313.25, "y": 342.04999999999995}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline"}, {"data": {"id": "core:Context API.md", "name": "Context API", "path": "React/Context API.md", "content": "React Context API 是 React 内置的一种机制，用于在组件树中共享全局数据，而无需通过每层组件手动传递 props。它常用于全局主题、用户认证、语言设置等场景，有效减少“prop drilling”，使组件之间的数据共享更直观和集中管理。\n\n---\n\n## 核心概念\n\n- **Context 对象：**  \n   由 `React.createContext()` 创建，用于存储和传递共享数据。\n- **Provider 组件：**  \n   作为 Context 的上层包装器，通过 `value` 属性将数据传递给所有后代组件。\n- **Consumer 组件 / useContext Hook：**  \n   用于在组件中读取 Context 中的数据，传统方式为 `<Context.Consumer>`，现代方式为 `useContext(Context)` Hook。 [[useContext]]\n\n下面通过一个 Mermaid 类图展示 React Context API 的核心概念以及它们之间的关系：\n\n```mermaid\nflowchart TD\n    A[React.createContext_defaultValue]\n    B[Context_Object]\n    C[Provider_组件]\n    D[Consumer_组件 Context.Consumer]\n    E[useContext_Hook]\n    F[共享数据_value]\n    G[子组件]\n\n    A -->|创建| B\n    B -->|包含| C\n    B -->|包含| D\n    C -->|提供| F\n    C -->|包裹| G\n    G -->|使用| D\n    G -->|使用| E\n    C -->|Note: Provider 在 value 更新时会通知所有后代组件重新渲染| G\n\n```\n\n## 核心逻辑\n\nReact Context API 的实现逻辑主要包括以下步骤：\n\n1. **创建 Context：**\n   使用 `React.createContext()` 定义一个上下文对象，设定默认值（可选）。\n2. **数据提供：**\n   在组件树中较高层级使用 Provider 组件，通过 `value` 属性将共享数据传递下去。\n3. **数据消费：**\n   在需要数据的子组件中使用 Consumer 组件或 `useContext` Hook 来访问共享数据。\n4. **注意事项：**\n   - 避免频繁更新 Provider 内的 `value`，以防止不必要的重新渲染。\n   - 根据业务复杂性考虑是否将 Context 与其他状态管理方案（如 Redux 或 useReducer）结合使用。\n\n下面通过一个 Mermaid 时序图展示 React Context API 的核心逻辑步骤：\n\n```mermaid\nsequenceDiagram\n    participant A as App\n    participant P as Provider\n    participant C as Child Component\n    A->>P: 创建 Context 并传入初始 value\n    P->>C: 通过 Provider 向下传递 value\n    C->>P: 使用 useContext 或 Consumer 请求数据\n    P-->>C: 返回当前 context value\n```\n\n---\n\n## 代码示例\n\n下面使用 JavaScript (React) 给出一个简单示例，展示如何使用 React Context API 来共享主题数据：\n\n```javascript\nimport React, { createContext, useContext, useState } from \"react\";\n\n// 创建一个 ThemeContext，并设置默认值为 'light'\nconst ThemeContext = createContext(\"light\");\n\n// 定义 Provider 组件，用于提供主题数据\nfunction ThemeProvider({ children }) {\n  // 使用 useState 管理主题状态\n  const [theme, setTheme] = useState(\"light\");\n\n  // 切换主题函数\n  const toggleTheme = () => {\n    setTheme((prevTheme) => (prevTheme === \"light\" ? \"dark\" : \"light\"));\n  };\n\n  // Provider 通过 value 属性传递当前主题和切换函数\n  return <ThemeContext.Provider value={{ theme, toggleTheme }}>{children}</ThemeContext.Provider>;\n}\n\n// 定义一个消费者组件，使用 useContext Hook 来访问主题数据\nfunction ThemedComponent() {\n  // 从 ThemeContext 中获取当前主题及切换主题函数\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div\n      style={{\n        background: theme === \"light\" ? \"#fff\" : \"#333\",\n        color: theme === \"light\" ? \"#000\" : \"#fff\",\n        padding: \"20px\",\n      }}\n    >\n      <p>当前主题: {theme}</p>\n      <button onClick={toggleTheme}>切换主题</button>\n    </div>\n  );\n}\n\n// 应用组件：使用 ThemeProvider 包裹组件树\nfunction App() {\n  return (\n    <ThemeProvider>\n      <ThemedComponent />\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n```\n\n_代码说明：_\n\n- **ThemeProvider：** 负责创建和管理主题状态，并通过 `value` 属性传递给子组件。\n- **ThemedComponent：** 通过 `useContext(ThemeContext)` 获取当前主题，并提供一个按钮实现主题切换。\n\n---\n\n## 在实际开发中的思考\n\n- **全局状态管理：**\n  React Context API 非常适用于管理全局状态，如用户信息、主题设置或语言环境，但对于大型复杂应用，建议结合 `useReducer` 或 Redux 以优化性能和状态更新控制。\n- **避免频繁更新：**\n  Provider 内部的 `value` 更新会导致所有消费组件重新渲染，开发时需注意性能问题，可以将 context 分割成多个小的 context 或使用 memoization 优化。\n- **与组件化开发结合：**\n  利用 Context API 可以使组件之间的耦合度降低，方便组件的独立开发与测试，特别适用于中大型应用的状态管理需求。\n\n---\n\n## 学习资源\n\n- **官方文档与教程：**\n  - [React Context API 官方文档](https://reactjs.org/docs/context.html)\n- **推荐书籍：**\n  - 《React 进阶之路》\n  - 《Pure React》\n- **在线资源：**\n  - 各大技术博客中关于 Context API 的实践案例与优化方案\n  - GitHub 上的开源项目源码，有助于了解实际应用场景下的设计和实现\n\n---\n\n通过以上讲解，我们详细了解了 React Context API 的核心概念、实现逻辑以及实际应用场景，希望这些内容能帮助你在 React 项目中更好地实现全局数据共享和状态管理。\n\n```\n\n```\n", "degree": 1, "nameLength": 11}, "position": {"x": 422.875, "y": 207.75}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-outgoing-inline"}, {"data": {"id": "core:React Hooks.md", "name": "React Hooks", "path": "React Hooks.md", "content": "---\naliases:\n  - 钩子函数\n---\n\n## 一、 核心概念与定义 (Foundation & Definition)\n\n---\n\n### 本质定义 (Essential Definition):\n\nReact Hooks 是一组特殊的函数，它们让函数组件能够 “hook into” React 的 state 和生命周期等特性。本质上，Hooks 旨在 **在不使用 class 组件的情况下，复用状态逻辑和副作用逻辑**，并使函数组件具备更强大的功能和更好的代码组织性。  它们是 React 16.8 版本引入的核心特性，代表着 React 编程范式向函数式组件的进一步演进。\n\n### 关键要素/组成部分 (Key Elements/Components):\n\nReact Hooks 的核心组成部分是一系列内置 Hook 函数，以及开发者自定义 Hook 的能力。 主要内置 Hooks 可以大致分为以下几类：\n\n* **基础 Hooks (Basic Hooks):**\n    * `useState`:  在函数组件中添加 state 状态。[[useState]]\n    * `useEffect`: 在函数组件中执行副作用操作（如数据获取、订阅、手动修改 DOM 等）。\n    * `useContext`:  消费 React Context 的值。[[useContext]]\n\n* **额外的 Hooks (Additional Hooks):**\n    * `useReducer`:  `useState` 的替代方案，用于管理更复杂的状态逻辑，类似于 Redux 的 reducer。\n    * `useCallback`:  缓存函数定义，避免不必要的组件重新渲染。\n    * `useMemo`:  缓存计算结果，避免昂贵的计算重复执行。\n    * `useRef`:  创建一个可变的 ref 对象，用于在组件的整个生命周期中保存值，常用于访问 DOM 元素或保存定时器 ID 等。\n    * `useImperativeHandle`:  自定义在使用 `ref` 时暴露给父组件的实例值。\n    * `useLayoutEffect`:  类似于 `useEffect`，但在所有 DOM 变更后同步执行，适用于需要同步 DOM 测量的副作用。\n    * `useDebugValue`:  用于在 React DevTools 中显示自定义 Hook 的调试信息。\n    * `useTransition`, `useDeferred`: 用于优化更新性能，处理优先级较低的更新。\n    * `useId`: 在客户端和服务端生成稳定的唯一 ID，避免 hydration 不匹配问题。\n\n### 相关术语与概念 (Related Terms & Concepts):\n\n* **函数组件 (Function Components):**  React Hooks 只能在函数组件内部使用。函数组件是接收 props 作为参数并返回 React 元素的 JavaScript 函数。 Hooks 的引入使得函数组件能够拥有原本只有 class 组件才具备的状态和生命周期管理能力。\n* **Class 组件 (Class Components):**  React Hooks 出现之前的组件形式。Class 组件使用 ES6 class 语法定义，通过 `this.state` 和生命周期方法（如 `componentDidMount`, `componentDidUpdate`）管理状态和副作用。Hooks 旨在取代 class 组件在状态逻辑和副作用逻辑方面的作用，并提供更简洁、更易复用的解决方案。\n    * **区别与联系:** Class 组件和函数组件都可以构建 React 应用。 Hooks 是为了增强函数组件的功能而设计的，使得函数组件在功能上可以完全替代 class 组件，并且在代码组织和复用性上更具优势。  Class 组件仍然可以继续使用，但官方推荐在新项目中优先使用函数组件和 Hooks。\n\n* **状态 (State):**  组件内部用于存储和管理动态数据的机制。在 class 组件中，状态通过 `this.state` 和 `this.setState` 管理。在函数组件中，通过 `useState` Hook 管理。\n* **副作用 (Side Effects):**  在 React 组件渲染之外发生的操作，例如数据获取、DOM 操作、定时器、日志记录等。在 class 组件中，副作用通常在生命周期方法中处理。在函数组件中，通过 `useEffect` Hook 处理。\n* **Context API:**  React 提供的用于在组件树中共享数据的机制。`useContext` Hook 使得在函数组件中消费 Context 数据变得更加简洁方便。\n* **高阶组件 (Higher-Order Components - HOCs) 和 Render Props:**  在 Hooks 出现之前，HOCs 和 Render Props 是 React 中常用的代码复用模式。Hooks 提供了一种更简洁、更灵活的代码复用方式，在很多场景下可以替代 HOCs 和 Render Props。\n    * **区别与联系:** HOCs 和 Render Props 都是为了解决代码复用问题，但它们有一定的局限性，例如 HOCs 容易导致 “props 命名冲突” 和 “wrapper hell”，Render Props 代码结构可能比较冗余。Hooks 通过自定义 Hook 提供了一种更优雅、更易组合的代码复用方案。\n\n### 发展历史与演变 (History & Evolution):\n\n* **React 早期 (v0.x - v15.x):**  主要使用 class 组件构建应用。函数组件主要用于展示型组件 (presentational components)，不包含状态和副作用逻辑。状态逻辑和副作用逻辑在 class 组件的生命周期方法中管理，随着组件逻辑复杂度的增加，class 组件容易变得臃肿和难以维护。\n* **React 16.x 之前的痛点:**\n    * **组件状态逻辑复用困难:**  在不同的组件之间复用状态逻辑比较困难，常用的模式如 HOCs 和 Render Props 有其局限性。\n    * **Class 组件的复杂性:**  生命周期方法分散了相关逻辑，`this` 关键字的指向问题容易混淆，组件逻辑难以拆分和测试。\n    * **大型组件难以理解和维护:**  随着组件功能增多，class 组件的代码量不断增加，使得组件难以理解和维护。\n\n* **React 16.8 (Hooks 诞生):**  React 团队推出了 Hooks，旨在解决上述痛点。 Hooks 的设计目标包括：\n    * **状态逻辑复用:**  通过自定义 Hook，可以将状态逻辑提取到可复用的函数中，并在多个组件之间共享。\n    * **简化组件逻辑:**  Hooks 将组件中相关的逻辑组织在一起，例如将设置 state 和处理副作用的代码放在一起，提高代码的可读性和可维护性。\n    * **拥抱函数式编程:**  Hooks 鼓励使用函数组件，并使得函数组件具备与 class 组件相同甚至更强大的能力，推动 React 社区向函数式编程范式转变。\n\n* **React 16.8+ (Hooks 的普及和发展):**  Hooks 推出后迅速被 React 社区接受和广泛应用。  React 团队持续改进和扩展 Hooks API，例如新增 `useLayoutEffect`, `useDebugValue`, `useTransition`, `useDeferred`, `useId` 等 Hooks，并不断优化 Hooks 的性能和开发体验。  Hooks 已经成为构建现代 React 应用的主流方式。\n\n### 多维视角理解 (Multi-dimensional Perspectives):\n\n* **开发者视角:**  Hooks 降低了学习 React 的门槛，使得函数组件能够处理复杂的状态和副作用逻辑，代码更加简洁易懂，开发效率更高。Hooks 也使得代码复用和测试更加容易。\n* **团队视角:**  Hooks 促进了团队协作和代码共享。自定义 Hook 可以封装通用的状态逻辑和业务逻辑，方便团队成员在不同组件中复用。统一的代码风格和更简洁的组件结构也降低了团队维护代码的成本。\n* **性能优化视角:**  Hooks 本身并不直接解决性能问题，但通过 `useCallback`, `useMemo` 等 Hooks 可以进行性能优化，避免不必要的函数创建和计算，提升组件渲染性能。 同时，函数组件相比 class 组件在某些情况下可能具有轻微的性能优势。\n* **React 库维护者视角:**  Hooks 的引入使得 React 内部架构更加清晰和可维护。Hooks 允许在不破坏现有 API 的情况下，为函数组件添加新的功能。Hooks 也为未来的 React 特性（如 Concurrent Mode, Server Components）奠定了基础。\n* **社区生态视角:**  Hooks 推动了 React 社区生态的繁荣。大量的第三方 Hook 库和工具涌现，扩展了 Hooks 的应用场景，加速了 React 生态的发展。\n\n---\n## 二、 功能、原理与机制 (Functionality, Principles & Mechanisms)\n\n---\n\n### 核心功能/作用 (Core Functions/Roles):\n\nReact Hooks 的核心功能和作用可以归纳为以下几点：\n\n1. **在函数组件中添加状态 (State Management in Function Components):**  `useState` Hook 使得函数组件能够拥有自己的 state，并提供更新 state 的方法，解决了函数组件无法管理状态的问题。\n2. **在函数组件中处理副作用 (Side Effect Handling in Function Components):**  `useEffect` Hook 使得函数组件能够执行副作用操作，例如数据获取、订阅、DOM 操作等，解决了函数组件无法处理副作用的问题。\n3. **代码复用 (Code Reusability):**  自定义 Hook 允许开发者将组件逻辑（包含状态逻辑和副作用逻辑）提取到独立的函数中，并在多个组件之间复用，提高了代码的复用性和可维护性。\n4. **逻辑组织 (Logic Organization):**  Hooks 使得组件内部的逻辑更加清晰和有条理。可以将相关的逻辑（例如，与特定数据获取相关的 state 和 effect）放在一起，而不是像 class 组件那样分散在不同的生命周期方法中。\n5. **简化复杂组件 (Simplifying Complex Components):**  对于包含复杂状态逻辑和副作用逻辑的组件，使用 Hooks 可以将其拆分成更小的、更易于理解和维护的函数，降低了组件的复杂度。\n\n### 工作原理/机制 (Working Principles/Mechanisms):\n\nReact Hooks 的工作原理主要基于以下几个关键机制：\n\n1. **闭包 (Closures):**  Hooks 依赖 JavaScript 的闭包特性。当你在函数组件中使用 Hook 时，Hook 函数会捕获组件渲染时的状态值。即使组件重新渲染，Hook 仍然可以访问到上次渲染时的状态值，从而实现状态的持久化和更新。\n    * **类比:**  可以把 Hook 理解成一个“魔法盒子”，每次组件渲染时，React 会为每个 Hook 创建一个新的“盒子”，并将当前渲染周期的状态值放入盒子中。下次渲染时，即使函数组件重新执行，Hook 仍然可以找到对应的“盒子”并取出之前的状态值。\n\n2. **调用顺序 (Order of Calls):**  **Hooks 必须按照相同的顺序在每次渲染中被调用。**  这意味着你不能在循环、条件语句或嵌套函数中调用 Hooks。React 依赖 Hooks 的调用顺序来正确地关联 Hook 和组件的状态。\n    * **原因:** React 内部使用一个数组来存储 Hooks 的状态。每次组件渲染时，React 会按照 Hooks 的调用顺序遍历这个数组，找到对应的状态值。如果 Hooks 的调用顺序发生变化，React 就无法正确地找到状态值，导致程序出错。\n\n3. **组件内部状态管理 (Internal State Management):**  React 内部维护着每个函数组件的 Hook 状态列表。当组件首次渲染时，React 会初始化 Hook 的状态，并将状态值存储在列表中。当组件重新渲染时，React 会根据 Hooks 的调用顺序，从列表中取出对应的状态值，并更新组件。\n\n4. **依赖数组 (Dependency Array in `useEffect` and `useCallback` 等):**  `useEffect`, `useCallback`, `useMemo` 等 Hooks 接受一个可选的依赖数组作为参数。React 使用这个依赖数组来判断 Hook 是否需要在当前渲染周期重新执行。\n    * **作用:**  依赖数组帮助 React 优化性能，避免不必要的副作用执行或函数/值重新计算。只有当依赖数组中的值发生变化时，Hook 才会重新执行。\n    * **空数组 `[]`:**  表示 Hook 只在组件首次渲染时执行一次，类似于 class 组件的 `componentDidMount`。\n    * **不传依赖数组:**  表示 Hook 在每次渲染后都会执行，类似于 class 组件的 `componentDidUpdate` 和 `componentDidMount` 的组合。\n    * **传入依赖项:**  表示 Hook 在依赖项发生变化时才执行，更精细地控制副作用的执行时机。\n\n### 关键流程/步骤 (Key Processes/Steps):\n\n以 `useState` Hook 为例，其关键流程和步骤如下：\n\n1. **调用 `useState` Hook:**  在函数组件内部调用 `useState(initialValue)`，并传入初始值 `initialValue`。\n2. **React 初始化 State:**  React 内部为该组件创建一个 state 变量，并将初始值 `initialValue` 存储起来。\n3. **返回 State 和 Update 函数:**  `useState` Hook 返回一个包含两个元素的数组：\n    * 第一个元素是当前的 state 值 (例如 `state`)。\n    * 第二个元素是更新 state 的函数 (例如 `setState`)。\n4. **组件渲染:**  函数组件使用返回的 `state` 值进行渲染。\n5. **调用 `setState` 函数:**  在事件处理函数或其他逻辑中调用 `setState(newValue)`，并传入新的 state 值 `newValue`。\n6. **React 更新 State 和触发重新渲染:**  React 接收到新的 state 值 `newValue`，更新组件内部的 state 变量，并触发组件的重新渲染。\n7. **组件重新渲染:**  React 再次调用函数组件，组件内部的 `useState` Hook 返回更新后的 state 值，组件使用新的 state 值进行渲染，完成 UI 的更新。\n\n**流程图：**\n\n```mermaid\ngraph LR\n    A[函数组件调用 useState(initialValue)] --> B{React 初始化 State}\n    B --> C[useState 返回 [state, setState]]\n    C --> D[组件使用 state 渲染]\n    D --> E{用户交互或程序逻辑}\n    E --> F[调用 setState(newValue)]\n    F --> G{React 更新 State & 触发重新渲染}\n    G --> H[组件重新渲染 (useState 返回新 state)]\n    H --> I[UI 更新]\n```\n\n### 输入与输出 (Inputs & Outputs):\n\n**以 `useState` Hook 为例:**\n\n* **输入 (Input):**\n    * `initialValue`:  `useState` Hook 的初始值，可以是任何 JavaScript 值（number, string, boolean, object, array, function 等）。初始值只在组件的首次渲染时生效，后续渲染会被忽略。\n\n* **输出 (Output):**\n    * `state`:  当前的 state 值。\n    * `setState`:  更新 state 值的函数。调用 `setState` 会触发组件的重新渲染。\n\n**以 `useEffect` Hook 为例:**\n\n* **输入 (Input):**\n    * `effect 函数`:  包含副作用逻辑的函数。\n    * `dependencies (可选)`:  依赖项数组。用于控制 effect 函数的执行时机。\n\n* **输出 (Output):**\n    * `cleanup 函数 (可选)`:  `effect 函数` 可以返回一个 cleanup 函数。这个函数会在组件卸载或 effect 函数下次执行之前执行，用于清理副作用，例如取消订阅、清除定时器等。\n\n### 优势与劣势 (Advantages & Disadvantages):\n\n**优势 (Advantages):**\n\n* **代码复用性更高:**  自定义 Hook 使得状态逻辑和副作用逻辑可以轻松地在不同的组件之间复用，提高了代码的复用性和可维护性。\n* **逻辑组织更清晰:**  Hooks 将组件内部相关的逻辑组织在一起，提高了代码的可读性和可维护性，避免了 class 组件中逻辑分散在不同生命周期方法的问题。\n* **函数组件功能更强大:**  Hooks 使得函数组件能够拥有与 class 组件相同甚至更强大的功能，并且代码更简洁、更易理解。\n* **更容易测试:**  Hook 函数本身是普通的 JavaScript 函数，更容易进行单元测试。组件的逻辑也更容易拆分和测试。\n* **更好的性能 (Potential):**  在某些情况下，函数组件相比 class 组件可能具有轻微的性能优势。通过 `useCallback` 和 `useMemo` 等 Hooks 可以进行性能优化，避免不必要的函数创建和计算。\n* **更简洁的语法:**  相比 class 组件，使用 Hooks 的函数组件代码通常更简洁，减少了模板代码 (boilerplate code)。\n\n**劣势 (Disadvantages):**\n\n* **学习曲线 (Initial Learning Curve):**  对于初学者来说，理解 Hooks 的概念和规则可能需要一定的学习成本。特别是 Hooks 的调用规则 (Rules of Hooks) 需要严格遵守。\n* **心智模型转变 (Shift in Mental Model):**  从 class 组件的面向对象编程范式到函数组件和 Hooks 的函数式编程范式，需要一定的思维模式转变。\n* **过度使用 Hooks (Potential for Overuse):**  虽然 Hooks 提供了强大的功能，但过度使用 Hooks 也可能导致组件逻辑过于复杂，需要权衡使用 Hooks 的场景和程度。\n* **闭包陷阱 (Closure Traps):**  不正确地使用闭包和依赖数组可能导致一些意想不到的问题，例如 stale closure 问题。需要对闭包和依赖数组有深入的理解。\n* **调试复杂性 (Debugging Complexity in Certain Cases):**  对于一些复杂的 Hook 组合和副作用逻辑，调试起来可能比 class 组件更具挑战性。\n\n---\n## 三、 与其他概念的关系 (Relationships with Other Concepts)\n\n---\n\n### 上下游关系 (Upstream & Downstream Relationships):\n\n在更宏观的 React 技术体系和前端开发领域中，React Hooks 处于一个重要的中间层位置，它连接了底层的 JavaScript 语言特性和上层的 UI 组件构建与应用逻辑。\n\n* **上游概念 (Upstream Concepts):**\n    * **JavaScript 语言特性:**  Hooks 的实现和工作原理 heavily relies on JavaScript 的闭包 (closures)、函数 (functions)、作用域 (scope) 等核心语言特性。对 JavaScript 语言的深入理解是理解 Hooks 的基础。\n    * **函数式编程 (Functional Programming):**  Hooks 的设计理念深受函数式编程思想的影响。例如，强调纯函数 (pure functions)、避免副作用 (side effects)、数据不可变性 (immutability) 等。理解函数式编程的概念有助于更好地理解和使用 Hooks。\n    * **React 组件模型 (React Component Model):**  Hooks 是 React 组件模型的一部分，特别是针对函数组件的增强。理解 React 组件的生命周期、渲染机制、Props 和 State 等概念是使用 Hooks 的前提。\n\n* **下游概念 (Downstream Concepts):**\n    * **UI 组件库 (UI Component Libraries):**  许多 React UI 组件库 (例如 Material UI, Ant Design, Chakra UI) 都广泛使用 Hooks 来构建和管理组件的状态和逻辑。Hooks 使得 UI 组件库的开发更加灵活和高效。\n    * **状态管理库 (State Management Libraries):**  Hooks 可以与各种状态管理库 (例如 Redux, Zustand, Recoil) 结合使用。例如，可以使用 `useContext` Hook 消费 Context 中的状态，可以使用自定义 Hook 封装状态管理库的 API。Hooks 并没有取代状态管理库，而是提供了更灵活的状态管理方案。\n    * **前端应用开发 (Frontend Application Development):**  Hooks 已经成为构建现代 React 前端应用的主流方式。无论是单页应用 (SPA)、服务端渲染 (SSR) 应用、还是移动应用 (React Native)，Hooks 都被广泛应用于组件开发、状态管理、副作用处理、性能优化等方面。\n    * **React 生态系统 (React Ecosystem):**  Hooks 的出现极大地丰富了 React 生态系统。大量的第三方 Hook 库、工具、教程和社区资源涌现，推动了 React 生态的快速发展。\n\n**关系图：**\n\n```mermaid\ngraph LR\n    A[JavaScript 语言特性] --> B[React Hooks]\n    C[函数式编程] --> B\n    D[React 组件模型] --> B\n    B --> E[UI 组件库]\n    B --> F[状态管理库]\n    B --> G[前端应用开发]\n    B --> H[React 生态系统]\n```\n\n### 竞争与替代 (Competition & Alternatives):\n\n虽然 React Hooks 已经成为构建 React 应用的主流方式，但在某些场景下，仍然存在一些竞争或替代方案：\n\n* **Class 组件 (Class Components):**  在 Hooks 出现之前，Class 组件是 React 中管理状态和副作用的主要方式。对于老的 React 项目，仍然可能存在大量的 Class 组件代码。Class 组件在某些场景下仍然可以使用，但对于新的项目和组件，Hooks 通常是更推荐的选择。\n    * **竞争优势:** Class 组件是 React 早期的组件模型，社区积累了大量的 Class 组件的实践经验和教程。对于熟悉 Class 组件的开发者来说，上手成本可能更低。\n    * **劣势:** Class 组件的代码相对冗余，逻辑分散在不同的生命周期方法中，代码复用性较差，测试相对复杂。\n\n* **Render Props 和 高阶组件 (HOCs):**  在 Hooks 出现之前，Render Props 和 HOCs 是 React 中常用的代码复用模式。Hooks 在代码复用方面提供了更简洁、更灵活的方案，在很多场景下可以替代 Render Props 和 HOCs。\n    * **竞争优势:** Render Props 和 HOCs 在 Hooks 出现之前是解决代码复用问题的主要手段，社区积累了大量的相关实践和库。\n    * **劣势:** Render Props 代码结构可能比较冗余，HOCs 容易导致 props 命名冲突和 wrapper hell，代码可读性和维护性相对较差。\n\n* **状态管理库 (State Management Libraries):**  例如 Redux, Zustand, Recoil 等。Hooks 并没有完全取代状态管理库，而是提供了更灵活的状态管理方案。对于简单的组件状态管理，`useState` 和 `useReducer` Hook 已经足够。对于复杂的应用状态管理，仍然可以结合状态管理库使用。\n    * **竞争优势:** 状态管理库通常提供更强大的状态管理功能，例如时间旅行调试、中间件、持久化等。对于大型应用的状态管理，状态管理库可能更适合。\n    * **劣势:** 引入状态管理库会增加项目的复杂性，学习成本较高。对于简单的应用，可能过度设计。\n\n* **其他前端框架 (Other Frontend Frameworks):**  例如 Vue.js, Angular, Svelte 等。这些框架也有各自的状态管理和组件模型。选择哪个框架取决于具体的项目需求、团队技能和个人偏好。\n    * **竞争优势:**  Vue.js 以其易学易用著称，Angular 提供了更完善的框架结构，Svelte 以其编译时优化和高性能著称。\n    * **劣势:**  迁移到其他框架需要学习新的技术栈，重写代码，成本较高。\n\n**何时选择 React Hooks 更合适？**\n\n* **新的 React 项目:**  Hooks 是构建现代 React 应用的主流方式，对于新的项目，优先选择 Hooks 是最佳实践。\n* **需要复用状态逻辑和副作用逻辑的组件:**  Hooks 提供的自定义 Hook 功能使得代码复用更加容易，适合需要复用逻辑的场景。\n* **需要简化组件逻辑，提高代码可读性和可维护性的组件:**  Hooks 可以将组件逻辑组织得更清晰，提高代码的可读性和可维护性。\n* **需要进行性能优化的组件:**  `useCallback` 和 `useMemo` 等 Hooks 可以帮助优化组件性能。\n\n**何时选择替代方案更优？**\n\n* **维护老的 React 项目，并且 Class 组件代码已经比较稳定:**  迁移到 Hooks 可能需要较大的改造成本，如果 Class 组件代码已经比较稳定，可以继续使用 Class 组件。\n* **需要使用状态管理库提供的更高级的功能:**  例如时间旅行调试、中间件、持久化等，可以结合状态管理库使用。\n* **团队已经非常熟悉 Class 组件，并且迁移到 Hooks 需要较大的学习成本:**  可以根据团队的具体情况选择是否迁移到 Hooks。\n* **项目需求更适合其他前端框架的特性:**  例如，如果项目对性能要求极高，可以考虑使用 Svelte 等框架。\n\n### 交叉与融合 (Intersection & Integration):\n\nReact Hooks 与许多其他概念、技术、领域和学科存在交叉与融合，这种交叉融合产生了新的发展、新的应用和新的趋势。\n\n* **与 Context API 的融合:**  `useContext` Hook 使得在函数组件中消费 Context 数据变得非常简洁。Hooks 和 Context API 的结合，使得 React 的状态管理能力更加强大和灵活。\n    * **融合成果:**  更简洁、更易用的全局状态管理方案，例如使用 `useContext` 和 `useReducer` 构建轻量级的全局状态管理。\n\n* **与测试库的融合:**  Hooks 的可测试性使得 React 组件更容易进行单元测试和集成测试。许多测试库 (例如 React Testing Library, Jest) 都提供了对 Hooks 的良好支持。\n    * **融合成果:**  更完善的 React 组件测试方案，例如使用 React Testing Library 提供的 Hooks 测试工具函数，可以方便地测试自定义 Hook 的逻辑。\n\n* **与类型检查工具的融合:**  TypeScript 和 Flow 等类型检查工具可以与 Hooks 很好地配合使用，提供更好的类型安全性和开发体验。\n    * **融合成果:**  更健壮、更可靠的 React 应用，类型检查工具可以帮助开发者在开发阶段发现 Hooks 使用中的类型错误和潜在问题。\n\n* **与服务端渲染 (SSR) 的融合:**  Hooks 可以在服务端渲染环境中使用，使得 React SSR 应用的开发更加方便。\n    * **融合成果:**  更高效、更易维护的 React SSR 应用，Hooks 使得服务端和客户端的代码可以更好地共享和复用。\n\n* **与动画库的融合:**  Hooks 可以与各种动画库 (例如 Framer Motion, React Spring) 结合使用，实现更丰富的 UI 动画效果。\n    * **融合成果:**  更生动、更流畅的用户界面，Hooks 使得在 React 组件中集成动画效果更加方便。\n\n* **与 WebAssembly (Wasm) 的融合:**  Hooks 可以与 WebAssembly 模块进行交互，构建高性能的 Web 应用。\n    * **融合成果:**  更高性能的 Web 应用，WebAssembly 可以用于处理计算密集型任务，Hooks 可以用于管理 UI 组件和状态，两者结合可以构建更强大的 Web 应用。\n\n**未来可能出现的交叉融合趋势：**\n\n* **Hooks 与 Server Components 的融合:**  React Server Components 是 React 未来的重要发展方向，旨在提升 SSR 性能和开发体验。Hooks 可能会在 Server Components 中扮演更重要的角色，例如用于管理客户端状态和副作用。\n* **Hooks 与 Concurrent Mode 的深入融合:**  React Concurrent Mode 旨在提升应用的响应性和用户体验。Hooks 可能会与 Concurrent Mode 更加深入地融合，例如用于处理优先级更新、可中断渲染等。\n* **Hooks 与 AI 和机器学习的结合:**  Hooks 可能会与 AI 和机器学习技术结合，例如用于构建智能 UI 组件、自动化 UI 测试、用户行为分析等。\n* **Hooks 在更多领域的应用:**  Hooks 可能会在更多领域得到应用，例如 VR/AR 应用、游戏开发、数据可视化等。\n\n---\n## 四、 应用与影响 (Applications & Impacts)\n\n---\n\n### 主要应用领域 (Main Application Areas):\n\nReact Hooks 几乎可以应用于所有使用 React 构建的应用程序中。其主要应用领域包括：\n\n1. **Web 应用程序 (Web Applications):**  这是 React 最主要的应用领域。Hooks 在 Web 应用开发中被广泛用于组件状态管理、副作用处理、表单处理、路由管理、数据获取、用户认证、权限控制等方面。\n    * **应用特点:**  Hooks 可以帮助构建更复杂、更交互式的 Web 应用程序，例如电商网站、社交媒体平台、在线教育平台、企业管理系统等。\n\n2. **移动应用程序 (Mobile Applications - React Native):**  React Native 是使用 React 构建原生移动应用的技术。Hooks 在 React Native 应用开发中同样被广泛使用，用于构建跨平台移动应用。\n    * **应用特点:**  Hooks 可以提高 React Native 应用的代码复用性和开发效率，构建高性能的跨平台移动应用。\n\n3. **桌面应用程序 (Desktop Applications - Electron/React Desktop):**  Electron 和 React Desktop 等技术可以使用 React 构建跨平台桌面应用程序。Hooks 在桌面应用开发中也可以发挥重要作用。\n    * **应用特点:**  Hooks 可以帮助构建具有良好用户体验的跨平台桌面应用程序，例如代码编辑器、音乐播放器、效率工具等。\n\n4. **单页应用程序 (Single-Page Applications - SPAs):**  Hooks 非常适合构建 SPA 应用。SPA 应用通常有复杂的状态管理和路由逻辑，Hooks 可以很好地应对这些挑战。\n    * **应用特点:**  Hooks 可以帮助构建高性能、高交互性的 SPA 应用，例如 Gmail, Google Maps, Netflix 等。\n\n5. **服务端渲染应用程序 (Server-Side Rendered Applications - SSR):**  Hooks 可以在服务端渲染环境中使用，使得 React SSR 应用的开发更加方便。\n    * **应用特点:**  Hooks 可以提高 React SSR 应用的性能和 SEO，例如电商网站、博客、新闻网站等。\n\n6. **组件库 (Component Libraries):**  React Hooks 非常适合构建可复用的组件库。Hooks 可以将组件的逻辑封装在自定义 Hook 中，方便组件库的使用者复用和定制。\n    * **应用特点:**  Hooks 可以提高组件库的开发效率和质量，构建更易用、更灵活的组件库。\n\n7. **数据可视化 (Data Visualization):**  React Hooks 可以用于构建交互式数据可视化组件和应用。例如，使用 Hooks 管理图表的状态、处理用户交互、更新数据等。\n    * **应用特点:**  Hooks 可以帮助构建动态、可交互的数据可视化应用，例如仪表盘、报表、数据分析工具等。\n\n8. **游戏开发 (Game Development - React Three Fiber):**  React Three Fiber 是一个使用 React 和 Three.js 构建 3D 动画和游戏的库。Hooks 可以用于管理游戏状态、处理用户输入、控制动画等。\n    * **应用特点:**  Hooks 可以简化 Web 游戏开发流程，构建具有良好性能和用户体验的 Web 游戏。\n\n### 实际应用案例 (Real-world Use Cases):\n\n1. **表单处理 (Form Handling):**  `useState` Hook 被广泛用于表单字段的状态管理。每个表单字段可以使用一个 `useState` Hook 来管理其值，并使用 `setState` 函数更新字段值。\n    * **案例:**  电商网站的注册表单、登录表单、订单填写表单等，几乎都离不开 `useState` Hook 来管理表单字段的状态。\n    * **数据支撑:**  React 官方文档和各种 React 教程中，表单处理都是 `useState` Hook 的经典应用场景。\n\n2. **数据获取 (Data Fetching):**  `useEffect` Hook 被广泛用于在组件加载后或更新时进行数据获取。可以使用 `useEffect` Hook 发起 API 请求，并在请求完成后更新组件的状态。\n    * **案例:**  社交媒体平台的用户信息展示、新闻网站的文章列表展示、电商网站的商品列表展示等，都需要使用 `useEffect` Hook 进行数据获取。\n    * **数据支撑:**  React 社区中有很多关于使用 `useEffect` Hook 进行数据获取的最佳实践和示例代码。\n\n3. **主题切换 (Theme Switching):**  `useContext` Hook 可以与 Context API 结合使用，实现主题切换功能。可以将主题数据放在 Context 中，然后在组件中使用 `useContext` Hook 消费主题数据，实现主题的动态切换。\n    * **案例:**  很多网站和应用程序都支持主题切换功能，例如 GitHub, VS Code, Material UI 组件库等。这些应用很可能使用了 `useContext` Hook 来实现主题切换。\n    * **数据支撑:**  很多 React UI 组件库和主题库都使用了 `useContext` Hook 来实现主题切换功能。\n\n4. **无限滚动 (Infinite Scrolling):**  `useEffect` Hook 可以用于实现无限滚动功能。可以使用 `useEffect` Hook 监听滚动事件，并在用户滚动到页面底部时加载更多数据。\n    * **案例:**  社交媒体平台的信息流、电商网站的商品列表、图片网站的图片瀑布流等，很多都使用了无限滚动功能。\n    * **数据支撑:**  React 社区中有很多关于使用 `useEffect` Hook 实现无限滚动的教程和示例代码。\n\n5. **动画效果 (Animation Effects):**  `useEffect` Hook 可以与 `requestAnimationFrame` 等 API 结合使用，实现各种动画效果。可以使用 `useEffect` Hook 在组件渲染后或更新时执行动画逻辑。\n    * **案例:**  网站的加载动画、过渡动画、交互动画等，都可以使用 `useEffect` Hook 来实现。\n    * **数据支撑:**  React 动画库 (例如 Framer Motion, React Spring) 通常会使用 Hooks 来实现动画效果。\n\n**案例成功或失败之处及关键影响因素分析:**\n\n* **成功之处:**  Hooks 使得上述应用场景的代码更加简洁、易读、易维护。Hooks 提高了代码的复用性和开发效率。\n* **失败之处 (潜在):**  如果 Hooks 使用不当，例如过度使用 Hooks、滥用依赖数组、不理解闭包等，可能会导致性能问题、bug 或代码难以维护。\n* **关键影响因素:**\n    * **Hooks 的正确使用:**  理解 Hooks 的规则和工作原理，正确使用 Hooks 是保证应用成功运行的关键。\n    * **Hooks 的性能优化:**  合理使用 `useCallback`, `useMemo` 等 Hooks 进行性能优化，避免不必要的渲染和计算。\n    * **团队的 Hooks 知识水平:**  团队成员需要具备一定的 Hooks 知识和经验，才能充分发挥 Hooks 的优势。\n\n### 积极影响与价值 (Positive Impacts & Value):\n\nReact Hooks 带来了以下积极影响和价值：\n\n1. **提升开发效率:**  Hooks 使得代码更简洁、更易复用，降低了代码量，提高了开发效率。\n2. **改善代码可读性:**  Hooks 将组件逻辑组织得更清晰，提高了代码的可读性和可维护性。\n3. **增强组件功能:**  Hooks 使得函数组件能够拥有与 class 组件相同甚至更强大的功能，拓展了函数组件的应用场景。\n4. **促进代码复用:**  自定义 Hook 使得状态逻辑和副作用逻辑可以轻松地在组件之间复用，降低了代码冗余。\n5. **简化组件测试:**  Hook 函数本身是普通的 JavaScript 函数，更容易进行单元测试，提高了组件的测试覆盖率。\n6. **提高应用性能 (Potential):**  通过 `useCallback` 和 `useMemo` 等 Hooks 可以进行性能优化，提升组件渲染性能。\n7. **推动 React 生态发展:**  Hooks 的出现激发了 React 社区的创新活力，大量的第三方 Hook 库和工具涌现，加速了 React 生态的发展。\n\n**量化或具体化积极影响和价值:**\n\n* **开发效率提升:**  根据一些开发者反馈和案例分析，使用 Hooks 构建组件，代码量可以减少 20%-50%，开发时间可以缩短 10%-30%。 (需要更严谨的数据支撑，例如行业报告或benchmark)\n* **代码可读性提升:**  代码行数减少，逻辑组织更清晰，使得代码更容易理解和维护，降低了维护成本。 (可读性提升难以量化，但可以通过代码审查、开发者调查等方式进行评估)\n* **性能提升:**  使用 `useCallback` 和 `useMemo` 可以避免不必要的函数创建和计算，在某些场景下可以显著提升组件渲染性能。 (可以通过性能测试工具，例如 React Profiler, Lighthouse 等，量化性能提升幅度)\n\n### 潜在风险与挑战 (Potential Risks & Challenges):\n\n应用或发展 React Hooks 也存在一些潜在的风险、挑战和局限性：\n\n1. **学习曲线和理解成本:**  Hooks 的概念和规则对于初学者来说可能需要一定的学习成本。特别是 Hooks 的调用规则 (Rules of Hooks) 需要严格遵守，否则容易出错。\n    * **挑战:**  需要提供更完善的 Hooks 学习资源和教程，帮助开发者快速掌握 Hooks。\n\n2. **过度使用 Hooks 导致组件逻辑复杂:**  虽然 Hooks 提供了强大的功能，但过度使用 Hooks 也可能导致组件逻辑过于复杂，代码难以维护。\n    * **挑战:**  需要引导开发者合理使用 Hooks，避免过度设计，保持组件的简洁性。\n\n3. **闭包陷阱和依赖数组问题:**  不正确地使用闭包和依赖数组可能导致一些意想不到的问题，例如 stale closure 问题、无限循环渲染等。\n    * **挑战:**  需要加强对 Hooks 闭包和依赖数组的理解和最佳实践的推广，提供更好的开发工具和调试技巧。\n\n4. **性能问题 (误用导致):**  如果 Hooks 使用不当，例如在 `useEffect` 中进行耗时操作且没有合理控制依赖项，可能会导致性能问题。\n    * **挑战:**  需要提供更好的性能优化指南和工具，帮助开发者避免 Hooks 使用中的性能陷阱。\n\n5. **向后兼容性 (与 Class 组件共存):**  Hooks 是在 React 16.8 版本引入的，对于老的 React 项目，仍然存在大量的 Class 组件代码。Hooks 需要与 Class 组件良好地共存和兼容。\n    * **挑战:**  需要提供平滑的迁移方案，帮助开发者逐步将 Class 组件迁移到 Hooks，并保证新旧代码的兼容性。\n\n6. **社区生态成熟度 (相对于 Class 组件):**  Hooks 相对 Class 组件来说是较新的技术，社区生态的成熟度还需要进一步发展。例如，一些第三方库和工具对 Hooks 的支持可能还不够完善。\n    * **挑战:**  需要社区共同努力，完善 Hooks 生态，开发更多高质量的 Hook 库和工具。\n\n### 伦理与社会考量 (Ethical & Social Considerations):\n\n与 React Hooks 相关的伦理和社会问题相对较少，但仍然需要关注以下方面：\n\n1. **可访问性 (Accessibility):**  使用 Hooks 构建的 UI 组件需要保证可访问性，例如符合 WCAG 标准，方便残障人士使用。\n    * **伦理考量:**  所有用户都应该平等地访问和使用 Web 应用，包括残障人士。\n\n2. **性能优化 (Performance Optimization):**  Hooks 的性能优化对于用户体验至关重要，特别是对于低端设备和网络环境较差的用户。\n    * **伦理考量:**  应该尽可能提高应用的性能，减少资源消耗，为所有用户提供流畅的用户体验，避免造成数字鸿沟。\n\n3. **开发者福祉 (Developer Well-being):**  Hooks 旨在提高开发效率和代码质量，但如果过度追求新技术，可能会给开发者带来学习压力和焦虑。\n    * **伦理考量:**  应该关注开发者的福祉，提供良好的学习资源和开发环境，避免技术焦虑。\n\n4. **技术垄断 (Technology Monopoly):**  React 是 Facebook 开发和维护的开源库。过度依赖 React 技术可能会导致技术垄断，限制技术多样性。\n    * **伦理考量:**  鼓励技术多样性，支持其他前端框架和技术的发展，避免过度依赖单一技术。\n\n**如何从伦理和社会角度审视和规范 Hooks 的应用和发展？**\n\n* **推广可访问性最佳实践:**  在 Hooks 相关的文档、教程和社区活动中，强调可访问性最佳实践，提高开发者对可访问性的意识。\n* **重视性能优化:**  在 Hooks 相关的性能优化指南和工具中，强调性能优化对用户体验的重要性，特别是对于低端设备和弱网环境的用户。\n* **关注开发者福祉:**  在 Hooks 相关的社区活动和技术交流中，关注开发者的福祉，鼓励健康的工作生活平衡，避免技术焦虑。\n* **支持技术多样性:**  鼓励开发者学习和使用不同的前端框架和技术，促进前端技术生态的健康发展。\n* **开源和社区驱动:**  React Hooks 本身是开源的，应该继续保持开源和社区驱动的模式，鼓励社区参与 Hooks 的发展和改进。\n\n---\n## 五、 发展趋势与未来展望 (Development Trends & Future Outlook)\n\n---\n\n### 当前研究热点 (Current Research Hotspots):\n\n目前关于 React Hooks 的研究热点和前沿方向主要集中在以下几个方面：\n\n1. **性能优化 (Performance Optimization):**  如何进一步优化 Hooks 的性能，例如减少不必要的渲染、提高 Hook 的执行效率、优化内存占用等，仍然是研究的热点。\n    * **关注点:**  React Forget (自动 memoization 编译器) 的进展，以及如何更好地利用 Hooks API 进行手动性能优化。\n\n2. **并发模式 (Concurrent Mode) 与 Hooks 的结合:**  React Concurrent Mode 是 React 未来的重要发展方向，旨在提升应用的响应性和用户体验。如何将 Hooks 与 Concurrent Mode 更好地结合，例如处理优先级更新、可中断渲染、Suspense 数据获取等，是研究的热点。\n    * **关注点:**  `useTransition`, `useDeferred` 等 Hooks 的应用和优化，以及如何利用 Concurrent Mode 提升 Hooks 应用的用户体验。\n\n3. **Server Components 与 Hooks 的关系:**  React Server Components 旨在提升 SSR 性能和开发体验。Hooks 在 Server Components 中将扮演什么角色，如何利用 Hooks 构建 Server Components，是研究的热点。\n    * **关注点:**  Hooks 在 Server Components 中的限制和最佳实践，以及如何利用 Server Components 优化 Hooks 应用的 SSR 性能。\n\n4. **自定义 Hook 的最佳实践和模式:**  如何设计和开发高质量、可复用的自定义 Hook，仍然是研究的热点。例如，如何处理异步逻辑、错误处理、状态管理、测试等方面，都有待进一步探索和总结最佳实践。\n    * **关注点:**  各种自定义 Hook 库和社区实践，以及如何构建更健壮、更易维护的自定义 Hook。\n\n5. **Hooks 的类型系统和工具支持:**  如何提升 Hooks 的类型安全性和开发体验，例如通过 TypeScript 或 Flow 等类型检查工具，以及开发更好的 Hooks 开发工具和调试工具，是研究的热点。\n    * **关注点:**  TypeScript 对 Hooks 的类型推断和类型检查的改进，以及各种 Hooks 开发工具和调试工具的创新。\n\n6. **Hooks 在新兴领域的应用:**  探索 Hooks 在新兴领域的应用，例如 VR/AR 应用、游戏开发、WebAssembly 应用、AI 和机器学习应用等，是研究的热点。\n    * **关注点:**  Hooks 在这些新兴领域的应用场景和挑战，以及如何利用 Hooks 解决这些领域的技术难题。\n\n### 未来发展趋势预测 (Future Development Trends Prediction):\n\n未来几年，React Hooks 的发展趋势可能包括：\n\n1. **更深入的性能优化:**  React 团队和社区将持续投入 Hooks 的性能优化，例如通过 React Forget 自动 memoization 编译器，以及更智能的 Hooks 调度和执行机制，进一步提升 Hooks 应用的性能。\n2. **与 Concurrent Mode 更紧密的集成:**  Hooks 将与 Concurrent Mode 更加紧密地集成，例如提供更丰富的 API 和工具，支持更复杂的并发场景，提升用户体验。\n3. **Server Components 的普及和应用:**  React Server Components 将逐渐普及和应用，Hooks 将在 Server Components 中扮演更重要的角色，成为构建全栈 React 应用的关键技术。\n4. **更强大的类型系统和工具支持:**  TypeScript 等类型检查工具对 Hooks 的支持将更加完善，Hooks 开发工具和调试工具将更加智能和易用，提升开发者体验。\n5. **更多高质量的自定义 Hook 和库:**  React 社区将涌现更多高质量的自定义 Hook 和库，覆盖更广泛的应用场景，降低开发门槛，提高开发效率。\n6. **Hooks 在新兴领域的广泛应用:**  Hooks 将在更多新兴领域得到应用，例如 VR/AR 应用、游戏开发、WebAssembly 应用、AI 和机器学习应用，拓展 React 的应用边界。\n7. **Hooks 成为 React 组件开发的标准范式:**  Hooks 将彻底取代 Class 组件，成为 React 组件开发的标准范式，所有新的 React 项目和组件都将基于 Hooks 构建。\n\n### 关键技术突破点 (Key Technological Breakthrough Points):\n\n为了更好地发展和应用 React Hooks，未来可能需要在以下关键技术上取得突破：\n\n1. **React Forget (自动 Memoization 编译器):**  React Forget 旨在实现自动 memoization，解决 React 开发者手动 memoization 的痛点，提升 Hooks 应用的性能和开发效率。React Forget 的成熟和普及将是 Hooks 发展的重要突破点。\n2. **更智能的 Hooks 调度和执行机制:**  React 团队可能会研究更智能的 Hooks 调度和执行机制，例如根据组件的更新优先级、依赖项的变化频率等，动态调整 Hooks 的执行时机和频率，进一步提升性能。\n3. **更强大的 Hooks 类型系统:**  TypeScript 等类型检查工具需要进一步完善对 Hooks 的类型推断和类型检查，例如支持更复杂的 Hook 组合、自定义 Hook 的类型定义等，提升 Hooks 的类型安全性和开发体验。\n4. **更智能的 Hooks 开发工具和调试工具:**  需要开发更智能的 Hooks 开发工具和调试工具，例如自动检测 Hooks 的错误用法、可视化 Hooks 的状态变化、性能分析工具等，帮助开发者更高效地开发和调试 Hooks 应用。\n5. **Server Components 与 Hooks 的无缝集成:**  需要实现 Server Components 与 Hooks 的无缝集成，例如允许在 Server Components 中使用部分 Hooks API，提供统一的开发体验，降低 Server Components 的学习门槛。\n\n---\n## 六、 深入学习与实践 (In-depth Learning & Practice)\n\n---\n\n### 学习资源推荐 (Learning Resources Recommendation):\n\n为了更深入地了解 React Hooks，以下是一些高质量的学习资源推荐：\n\n1. **React 官方文档 - Hooks 介绍:**  [https://reactjs.org/docs/hooks-intro.html](https://reactjs.org/docs/hooks-intro.html)\n    * **推荐理由:**  最权威、最全面的 Hooks 学习资源，由 React 团队编写，内容准确、详细、示例丰富，是学习 Hooks 的首选资源。\n    * **适用人群:**  所有 React 开发者，特别是初学者和需要系统学习 Hooks 的开发者。\n\n2. **React 官方博客 - Introducing Hooks:**  [https://reactjs.org/blog/2018/10/25/introducing-hooks.html](https://reactjs.org/blog/2018/10/25/introducing-hooks.html)\n    * **推荐理由:**  React 团队发布 Hooks 的官方博客文章，介绍了 Hooks 的设计理念、解决的问题、核心特性等，有助于理解 Hooks 的背景和动机。\n    * **适用人群:**  所有 React 开发者，特别是希望深入理解 Hooks 设计思想的开发者。\n\n3. **《深入浅出React Hooks》:**  (中文书籍，作者：程墨)\n    * **推荐理由:**  国内优秀的 React Hooks 专著，系统、深入地讲解了 Hooks 的各个方面，包括核心概念、API 详解、最佳实践、高级技巧等，适合中文读者深入学习 Hooks。\n    * **适用人群:**  希望系统、深入学习 Hooks 的中文读者，有一定 React 基础的开发者。\n\n4. **Udemy/Coursera 等在线课程平台上的 React Hooks 课程:**  (例如 Stephen Grider 的 \"Modern React with Redux [2023 Update]\")\n    * **推荐理由:**  在线课程通常以视频形式讲解，结合代码示例和练习，更直观、更生动，适合通过视频学习的开发者。可以选择评分高、内容全面的 Hooks 课程。\n    * **适用人群:**  喜欢通过视频学习的开发者，希望快速上手 Hooks 的开发者。\n\n5. **React 社区博客和文章:**  (例如 Dan Abramov 的个人博客 overreacted.io, React 官方博客, Kent C. Dodds 的博客 kentcdodds.com 等)\n    * **推荐理由:**  React 社区有很多优秀的博客和文章，分享 Hooks 的最佳实践、高级技巧、性能优化、案例分析等，可以持续关注社区动态，学习最新的 Hooks 技术和经验。\n    * **适用人群:**  希望持续学习和深入了解 Hooks 的开发者，关注 React 最新技术的开发者。\n\n6. **GitHub 上的 React Hooks 开源项目和示例代码:**  (例如 react-hook-form, usehooks-ts 等)\n    * **推荐理由:**  通过阅读开源项目的源码和示例代码，可以学习 Hooks 在实际项目中的应用，了解 Hooks 的最佳实践和高级技巧。\n    * **适用人群:**  有一定 React 基础，希望通过实践学习 Hooks 的开发者，希望学习优秀 Hooks 代码的开发者。\n\n### 实践方法建议 (Practical Methods Suggestions):\n\n以下是一些实践方法，可以帮助更好地理解和掌握 React Hooks：\n\n1. **构建小型项目:**  从零开始构建一些小型 React 项目，例如 TODO 应用、计数器应用、简单的表单应用等，在项目中大量使用 Hooks，例如 `useState`, `useEffect`, `useContext` 等，通过实践加深对 Hooks 的理解。\n\n2. **重构 Class 组件为 Hooks 组件:**  将老的 Class 组件项目或代码片段，逐步重构为 Hooks 组件，例如将 `componentDidMount`, `componentDidUpdate`, `componentWillUnmount` 等生命周期方法替换为 `useEffect` Hook，将 `this.state` 和 `this.setState` 替换为 `useState` Hook，通过重构加深对 Hooks 的理解和应用。\n\n3. **编写自定义 Hook:**  尝试编写一些自定义 Hook，封装通用的状态逻辑或副作用逻辑，例如数据获取 Hook, 表单处理 Hook, 节流防抖 Hook 等，通过自定义 Hook 提高代码复用性和模块化水平。\n\n4. **参与开源项目:**  参与 React Hooks 相关的开源项目，例如贡献代码、提交 issue、参与讨论等，通过参与开源项目学习 Hooks 的最佳实践和高级技巧，与其他开发者交流学习经验。\n\n5. **阅读 Hooks 源码:**  如果对 Hooks 的实现原理感兴趣，可以尝试阅读 React Hooks 的源码 (React 源码在 GitHub 上开源)，了解 Hooks 的内部实现机制，加深对 Hooks 工作原理的理解。\n\n6. **进行代码练习和挑战:**  参加一些在线代码练习平台或挑战，例如 LeetCode, Codewars 等，选择 React Hooks 相关的题目进行练习，通过代码练习提高 Hooks 的应用能力和解决问题的能力。\n\n7. **进行性能测试和优化:**  构建一些性能敏感的 React Hooks 应用，使用 React Profiler 等工具进行性能测试和分析，找出性能瓶颈，并使用 `useCallback`, `useMemo` 等 Hooks 进行性能优化，通过性能测试和优化提高 Hooks 的性能意识和优化技巧。\n\n### 持续学习路径 (Continuous Learning Path):\n\n如果想持续深入学习和精通 React Hooks，建议遵循以下学习路径和进阶方向：\n\n1. **扎实掌握基础 Hooks:**  首先要扎实掌握 `useState`, `useEffect`, `useContext` 等基础 Hooks 的用法和原理，理解 Hooks 的规则 (Rules of Hooks)，这是深入学习 Hooks 的基础。\n\n2. **深入学习高级 Hooks:**  在掌握基础 Hooks 的基础上，进一步学习 `useReducer`, `useCallback`, `useMemo`, `useRef`, `useImperativeHandle`, `useLayoutEffect`, `useDebugValue`, `useTransition`, `useDeferred`, `useId` 等高级 Hooks 的用法和应用场景，拓展 Hooks 的技能树。\n\n3. **学习自定义 Hook 的设计和最佳实践:**  深入学习自定义 Hook 的设计原则、模式和最佳实践，掌握如何编写高质量、可复用的自定义 Hook，提高代码复用性和模块化水平。\n\n4. **关注 React 最新动态和 Hooks 新特性:**  持续关注 React 官方博客、社区动态和技术会议，了解 React 最新版本发布的新特性和 Hooks 的发展趋势，及时学习和掌握新的 Hooks 技术。\n\n5. **学习 Hooks 性能优化技巧:**  深入学习 Hooks 的性能优化技巧，例如合理使用 `useCallback`, `useMemo`, 避免不必要的渲染、优化依赖数组、使用 React.memo 等，提高 Hooks 应用的性能。\n\n6. **学习 Hooks 与其他技术的结合:**  学习 Hooks 与 Context API, Redux, MobX, TypeScript, Server Components, Concurrent Mode 等技术的结合应用，拓展 Hooks 的应用场景和技术视野。\n\n7. **参与 React 社区和交流:**  积极参与 React 社区，例如参与开源项目、参与技术讨论、参加技术会议、与其他开发者交流学习经验，保持学习热情和技术敏感度。\n\n8. **持续实践和项目经验积累:**  持续进行 React Hooks 项目实践，积累项目经验，不断总结和反思，提高 Hooks 的应用能力和解决问题的能力。", "degree": 1, "nameLength": 11, "aliases": ["钩子函数"]}, "position": {"x": 380.375, "y": 207.75}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-outgoing-inline"}, {"data": {"id": "core:useContext.md", "name": "useContext", "path": "useContext.md", "content": "", "degree": 2, "nameLength": 10}, "position": {"x": 401.625, "y": 240.2}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline"}, {"data": {"id": "core:React 基础概念 (Fundamentals).md", "name": "React 基础概念 (Fundamentals)", "path": "React 基础概念 (Fundamentals).md", "content": "\n1.  **[[JSX]] (JavaScript XML):**\n    *   **定义:**  JSX 是一种 JavaScript 的语法扩展，允许你在 JavaScript 代码中编写类似 HTML 的结构。\n    *   **作用:**  简化了 UI 结构的描述，使代码更易读和维护。\n    *   **编译:**  JSX 代码在构建过程中会被 Babel 编译成标准的 JavaScript 函数调用 (例如 `React.createElement`)。\n    *   **注意:**  JSX 不是 HTML，它有自己的语法规则，例如使用 `className` 代替 `class`，`htmlFor` 代替 `for` 等。\n\n2.  **组件 (Components):**[[React.组件]]\n    *   **定义:**  React 应用是由一个个独立、可复用的组件构成的。组件是构建 UI 的基本单元。\n    *   **类型:**\n        *   **函数组件 (Functional Components):**  使用 JavaScript 函数定义，更简洁，推荐用于大多数场景，可以通过 Hooks 添加状态和生命周期功能。\n        *   **类组件 (Class Components):**  使用 ES6 类定义，拥有生命周期方法，但在 Hooks 出现后，函数组件逐渐取代了类组件。\n    *   **组件的特点:**\n        *   **可复用性 (Reusability):**  组件可以被多次使用在不同的地方。\n        *   **组合性 (Composition):**  组件可以嵌套组合成更复杂的 UI。\n        *   **独立性 (Isolation):**  组件拥有自己的状态和逻辑，彼此独立。\n\n3.  **虚拟 DOM (Virtual DOM):** [[虚拟DOM|virtual DOM]]\n    *   **定义:**  虚拟 DOM 是一个轻量级的 JavaScript 对象，代表了真实的 DOM 结构。\n    *   **工作原理:**\n        1.  **首次渲染:** React 创建一个虚拟 DOM 树，并将其与真实的 DOM 树进行比较，找出差异，然后只更新需要更新的部分真实 DOM。\n        2.  **状态更新:** 当组件的状态发生变化时，React 会重新渲染虚拟 DOM 树。\n        3.  **Diff 算法:** React 使用高效的 Diff 算法 (Reconciliation) 比较新旧虚拟 DOM 树，找出差异 (patches)。\n        4.  **批量更新:** React 将差异批量应用到真实的 DOM 上，减少直接操作 DOM 的次数，提高性能。\n    *   **优势:**\n        *   **性能优化:**  减少了直接操作 DOM 的次数，提升了渲染性能。\n        *   **跨平台:**  虚拟 DOM 可以用于不同的渲染目标，例如 Web、Native (React Native)。\n\n4.  **单向数据流 (Unidirectional Data Flow):**\n    *   **概念:**  数据从父组件流向子组件，子组件不能直接修改父组件的数据。数据的改变只能通过事件回调函数传递给父组件，由父组件更新状态，再将新的数据传递给子组件。\n    *   **优势:**\n        *   **可预测性:**  数据流向清晰，易于追踪和调试。\n        *   **易维护性:**  组件之间的依赖关系明确，修改一个组件不易影响其他组件。\n    *   **实现方式:**  通过 `props` 将数据从父组件传递给子组件，通过回调函数 (例如 `onChange`，`onClick`) 将事件传递给父组件。\n\n5.  **Props (属性):**\n    *   **定义:**  Props 是组件的输入，用于从父组件向子组件传递数据。\n    *   **特点:**\n        *   **只读 (Read-only):**  子组件不能直接修改 props 的值，props 的值由父组件控制。\n        *   **传递数据:**  可以传递任何类型的数据，包括字符串、数字、对象、函数等。\n        *   **组件配置:**  Props 用于配置组件的行为和外观。\n    *   **使用方式:**  通过组件标签的属性传递，例如 `<MyComponent name=\"John\" age={30} />`，子组件通过 `props` 对象访问这些属性。\n\n6.  **State (状态):**\n    *   **定义:**  State 是组件内部的数据，用于控制组件的渲染和行为。\n    *   **特点:**\n        *   **可变 (Mutable):**  State 的值可以被组件自身修改。\n        *   **触发渲染:**  State 的改变会触发组件的重新渲染。\n        *   **组件私有:**  State 是组件私有的，只能在组件内部访问和修改。\n    *   **管理方式:**\n        *   **函数组件:** 使用 `useState` Hook 管理状态。\n        *   **类组件:** 使用 `this.state` 初始化状态，使用 `this.setState` 更新状态。\n    *   **状态提升 (Lifting State Up):**  当多个组件需要共享状态时，将状态提升到它们共同的父组件中管理。\n\n7.  **事件处理 (Event Handling):**\n    *   **概念:**  React 使用合成事件 (Synthetic Events) 系统，是对浏览器原生事件的封装。\n    *   **优势:**\n        *   **跨浏览器兼容性:**  合成事件系统确保了事件处理在不同浏览器中的行为一致。\n        *   **性能优化:**  事件委托 (Event Delegation) 机制，减少了事件监听器的数量。\n    *   **处理方式:**\n        *   在 JSX 元素上绑定事件处理函数，例如 `<button onClick={handleClick}>Click me</button>`。\n        *   事件处理函数会接收一个合成事件对象 (SyntheticEvent)，包含了事件的信息。\n        *   常用的事件类型：`onClick`, `onChange`, `onSubmit`, `onKeyDown`, `onMouseOver` 等。\n        *   **注意 `this` 绑定:**  在类组件中，事件处理函数需要手动绑定 `this`，或者使用箭头函数。函数组件中无需手动绑定。\n\n8.  **条件渲染 (Conditional Rendering):**\n    *   **概念:**  根据不同的条件渲染不同的 UI 内容。\n    *   **方式:**\n        *   **if/else 语句:**  在 JSX 外部使用 if/else 语句进行条件判断。\n        *   **三元运算符 (Ternary Operator):**  在 JSX 中使用三元运算符 `condition ? expression1 : expression2`。\n        *   **逻辑与运算符 (&&):**  在 JSX 中使用逻辑与运算符 `condition && expression`，当 `condition` 为真时渲染 `expression`。\n        *   **组件提取:**  将不同条件的 UI 逻辑封装成不同的组件，根据条件渲染不同的组件。\n\n9.  **列表渲染 (List Rendering):**\n    *   **概念:**  渲染一组数据列表。\n    *   **方式:**  使用 JavaScript 的 `map()` 方法遍历数据数组，为每个数据项创建一个 React 元素或组件。\n    *   **Key 属性 (Key Prop):**  在渲染列表时，必须为每个列表项添加 `key` 属性。\n        *   **作用:**  帮助 React 识别列表项的变化，提高 Diff 算法的效率，优化性能。\n        *   **规则:**  `key` 属性必须是唯一且稳定的，通常使用数据项的唯一 ID。\n\n10. **表单 (Forms):**\n    *   **受控组件 (Controlled Components):**\n        *   **概念:**  表单元素的值由 React 组件的状态 (state) 控制。\n        *   **特点:**  React 组件的状态是 \"唯一数据源\"，表单元素的值始终与状态同步。\n        *   **优势:**  可以方便地进行表单验证、数据处理等操作。\n        *   **实现:**  使用 `value` 属性绑定状态值，使用 `onChange` 事件处理函数更新状态。\n    *   **非受控组件 (Uncontrolled Components):**\n        *   **概念:**  表单元素的值由 DOM 自身管理，React 组件不直接控制。\n        *   **特点:**  类似于传统的 HTML 表单，可以通过 ref 访问表单元素的值。\n        *   **适用场景:**  简单的表单，或者需要与第三方库集成时。\n        *   **实现:**  使用 `ref` 获取表单元素，通过 `ref.current.value` 获取值。\n", "degree": 4, "nameLength": 25}, "position": {"x": 342.125, "y": 274.45}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:Babel.md", "name": "<PERSON>l", "path": "Babel.md", "content": "---\naliases: \ntags:\n  - todo\n---\n### **一、核心功能与定位**\n\n1. **作用**：将 ES6+ 语法（如箭头函数、类、模块）和实验性特性转换为向后兼容的 JavaScript 代码，确保在不同浏览器或环境中运行 。\n2. **扩展性**：支持语法扩展（如 JSX）和静态类型检查（如 TypeScript）。\n3. **工具链**：提供\n\t- 编译器核心（`@babel/core`）\n\t- 命令行工具（`@babel/cli`）、插件系统等模块化工具 。\n\n---\n\n### **二、核心模块与包**\n\n1. **`@babel/core`**：Babel 的编译核心，负责解析、转换和生成代码 。\n2. **`@babel/preset-env`**：智能预设，根据目标环境（如浏览器版本）自动选择需转换的语法，减少冗余代码 。\n3. **`@babel/plugin-transform-runtime`**：  \n   - 解决辅助函数重复引入的问题（如 `_classCallCheck`）。\n   - 配合 `@babel/runtime` 提供模块化的 Polyfill，避免全局污染（替代已废弃的 `@babel/polyfill`）。\n4. **`core-js` 与 `regenerator-runtime`**：自 Babel 7.4 起推荐使用，为 ES2015+ 特性提供完整的 Polyfill 支持 。\n\n---\n\n### **三、工作流程**\n\n1. **解析（Parsing）** ：将源代码转换为 **抽象语法树（AST）**  。(解析为::[[AST]])\n2. **转换（Transforming）** ：遍历并操作 AST，通过插件或预设实现<mark style=\"background: #FFB86CA6;\">语法转换</mark> 。\n3. **生成（Generating）** ：将修改后的 AST 转换为目标代码，并生成<mark style=\"background: #FFB86CA6;\">源码映射</mark>（Source Map）。\n\n---\n\n### **四、配置实践**\n\n1. **配置文件**：  \n   - 支持 `babel.config.js`（项目级配置）或 `.babelrc`（文件级配置）。\n   - 示例配置：\n\n```javascript\n     // babel.config.js\n     module.exports = {\n       presets: [\"@babel/preset-env\"],\n       plugins: [\"@babel/plugin-transform-runtime\"]\n     };\n```\n\n1. **与 Webpack 集成**：  \n   - 安装 `babel-loader`，在 `webpack.config.js` 中添加规则：\n\n```javascript\n     module: {\n       rules: [{ test: /\\.js$/, use: \"babel-loader\" }]\n     }\n```\n\n- 结合 `@babel/preset-env` 的 `targets` 字段指定目标环境 。\n\n---\n\n### **五、关键注意事项**\n\n1. **Polyfill 的演进**：  \n   - `@babel/polyfill` 因全局污染问题已废弃，推荐使用 `core-js/stable` 和 `regenerator-runtime/runtime` 按需引入 。\n2. **开发与生产依赖**：  \n   - `@babel/core`、`@babel/preset-env` 等属于开发依赖（DevDependencies）。\n   - `@babel/runtime` 和 `core-js` 属于生产依赖（Dependencies）。\n3. **第三方库开发**：避免污染全局，应使用 `@babel/plugin-transform-runtime` 而非全局 Polyfill 。\n\n---\n\n### **六、进阶应用**\n\n1. **自定义插件与预设**：通过编写插件实现特定语法转换，或组合插件为预设（Preset）复用配置 。\n2. **源码映射（Source Map）** ：调试时定位原始代码位置，需在 Babel 配置中启用 。\n3. **性能优化**：通过 `@babel/preset-env` 的 `useBuiltIns` 参数控制 Polyfill 的引入方式（如 `usage` 按需加载）。\n\n---\n\n### **总结**\n\nBabel 是现代 JavaScript 开发的基石，通过灵活的插件系统和预设配置，解决了跨环境兼容性问题。开发者需关注版本差异（如 Babel 7.4+ 的 Polyfill 变更），并结合构建工具（Webpack）实现高效编译与打包 。\n", "aliases": null, "tags": ["todo"], "degree": 3, "nameLength": 5}, "position": {"x": 350.5, "y": 342.04999999999995}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note tag-todo protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:AST.md", "name": "AST", "path": "AST.md", "content": "---\naliases:\n  - 抽象语法树\n  - Abstract Syntax Tree\n---\n# **AST（抽象语法树，Abstract Syntax Tree）**\n\n**AST** 是源代码的 **树状结构化表示**，用于描述代码的语法结构，忽略无关细节（如空格、分号等）。它是编译器、解释器、代码分析工具的核心数据结构，广泛应用于代码转换、静态分析、优化等场景。\n\n---\n\n### **核心概念**\n\n1. **作用**  \n   - **代码解析**：将文本代码转换为结构化数据。  \n   - **静态分析**：检查代码错误、风格问题（如 ESLint）。  \n   - **代码转换**：修改代码结构（如 Babel 转译、代码压缩）。  \n   - **执行优化**：编译器通过 AST 生成更高效的机器码。\n\n2. **生成流程**  \n   1. **词法分析**：将代码拆分为 **词法单元（Token）** （如标识符、运算符）。  \n   2. **语法分析**：根据语法规则将 Token 组合为树形结构（AST）。\n\n---\n\n### **AST 结构示例**\n\n#### 源代码\n\n```javascript\nfunction add(a, b) {\n    return a + b;\n}\n```\n\n#### 对应的 AST（简化版）\n\n```json\n{\n  \"type\": \"Program\",\n  \"body\": [\n    {\n      \"type\": \"FunctionDeclaration\",\n      \"id\": { \"type\": \"Identifier\", \"name\": \"add\" },\n      \"params\": [\n        { \"type\": \"Identifier\", \"name\": \"a\" },\n        { \"type\": \"Identifier\", \"name\": \"b\" }\n      ],\n      \"body\": {\n        \"type\": \"BlockStatement\",\n        \"body\": [\n          {\n            \"type\": \"ReturnStatement\",\n            \"argument\": {\n              \"type\": \"BinaryExpression\",\n              \"operator\": \"+\",\n              \"left\": { \"type\": \"Identifier\", \"name\": \"a\" },\n              \"right\": { \"type\": \"Identifier\", \"name\": \"b\" }\n            }\n          }\n        ]\n      }\n    }\n  ]\n}\n```\n\n#### 关键节点类型\n\n| 节点类型             | 示例                          |\n|----------------------|-------------------------------|\n| `Identifier`         | 变量名、函数名（如 `add`, `a`）|\n| `FunctionDeclaration` | 函数声明                      |\n| `BinaryExpression`   | 二元运算（如 `a + b`）        |\n| `ReturnStatement`    | `return` 语句                 |\n\n---\n\n### **AST 的应用场景**\n\n1. **代码检查工具**  \n   - **[[ESLint]]**：通过 AST 检测代码是否符合规则（如未使用的变量）。  \n   - **[[Prettier]]**：基于 AST 重新格式化代码。\n\n2. **代码转换工具**  \n   - **[[Babel]]**：将 ES6+ 代码转换为 ES5，通过修改 AST 实现语法降级。  \n   - **代码压缩工具**（如 Terser）：删除注释、重命名变量。\n\n3. **IDE 功能**  \n   - **自动补全**：分析 AST 提供上下文建议。  \n   - **代码高亮**：根据节点类型渲染不同颜色。\n\n4. **自定义代码生成**  \n   - 根据 AST 生成文档、测试用例或可视化流程图。\n\n---\n\n### **操作 AST 的工具与方法**\n\n#### 1. **生成 AST**\n\n- **JavaScript**：使用 `Espree`（ESLint 默认解析器）、`@babel/parser`（Babel 解析器）。  \n- **Python**：标准库 `ast` 模块。  \n- **Java**：工具如 `JavaParser`。\n\n**示例：用 Espree 生成 AST**\n\n```javascript\nconst espree = require(\"espree\");\nconst code = \"const x = 1 + 2;\";\nconst ast = espree.parse(code, { ecmaVersion: 2023 });\nconsole.log(ast);\n```\n\n#### 2. **遍历与修改 AST**\n\n- **访问者模式（Visitor Pattern）** ：遍历 AST 并修改节点。  \n- **工具库**：  \n    - JavaScript：`estraverse`、`@babel/traverse`。  \n    - Python：`ast.NodeTransformer`。\n\n**示例：用 `estraverse` 修改 AST**\n\n```javascript\nconst estraverse = require(\"estraverse\");\nconst code = \"a + b * c\";\nconst ast = espree.parse(code);\n\n// 将所有 \"+\" 替换为 \"-\"\nestraverse.replace(ast, {\n  enter: function (node) {\n    if (node.type === \"BinaryExpression\" && node.operator === \"+\") {\n      return { ...node, operator: \"-\" };\n    }\n  },\n});\n\n// 修改后的代码：a - b * c\n```\n\n#### 3. **生成代码**\n\n- **JavaScript**：`escodegen`、`@babel/generator`。  \n- **Python**：`ast.unparse()`（Python 3.9+）。\n\n**示例：用 `escodegen` 生成代码**\n\n```javascript\nconst escodegen = require(\"escodegen\");\nconst modifiedCode = escodegen.generate(modifiedAST);\nconsole.log(modifiedCode); // 输出修改后的代码\n```\n\n---\n\n### **AST 与 CST 的区别**\n\n| **AST**                          | **CST（具体语法树）**          |\n|----------------------------------|--------------------------------|\n| 抽象表示，忽略语法细节（如括号位置） | 保留所有语法细节（包括空格、符号） |\n| 用于语义分析、优化               | 用于精确还原原始代码（如代码格式化） |\n| 结构简洁                         | 结构复杂                      |\n\n---\n\n### **常见问题**\n\n1. **如何调试 AST？**  \n   - 使用 AST 可视化工具（如 [AST Explorer](https://astexplorer.net/)）。  \n   - 打印节点类型和位置信息：\n\n     ```javascript\n     console.log(JSON.stringify(ast, null, 2));\n     ```\n\n2. **如何处理大型代码的 AST？**  \n   - 按需解析（如仅处理特定函数）。  \n   - 使用流式解析库（如 `acorn-loose`）。\n\n3. **不同语言的 AST 是否通用？**  \n   - 不同语言的 AST 结构差异较大，但核心逻辑相似（如节点类型、遍历方式）。\n\n---\n\n### **代码示例：提取函数调用链**\n\n```javascript\nconst espree = require(\"espree\");\n\nconst code = `\n    fetchData()\n        .then(res => res.json())\n        .catch(console.error);\n`;\n\nconst ast = espree.parse(code, { ecmaVersion: 2023, sourceType: \"module\" });\n\n// 提取链式调用的方法名（\"then\", \"catch\"）\nconst calls = [];\nestraverse.traverse(ast, {\n    enter: (node) => {\n        if (node.type === \"CallExpression\" && node.callee.type === \"MemberExpression\") {\n            calls.push(node.callee.property.name);\n        }\n    },\n});\n\nconsole.log(calls); // 输出 [\"then\", \"catch\"]\n```\n\n---\n\n**AST 是理解代码底层逻辑的关键工具**，掌握其原理与应用，可大幅提升开发效率，尤其在构建代码分析、自动化重构等高级功能时。\n", "aliases": ["抽象语法树", "Abstract Syntax Tree"], "degree": 2, "nameLength": 3}, "position": {"x": 350.5, "y": 375.25}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:表达式.md", "name": "表达式", "path": "表达式.md", "content": "---\naliases:\n  - JS 表达式\n  - JavaScript 表达式\n---\n### **1. 表达式的定义**  \n\n表达式（Expression）是 JavaScript 中的一个**短语**，由运算元和运算符（可选）构成，解释器会将其**计算为一个结果值**。例如：  \n\n- `1 + 2` 计算结果为 `3`（算术表达式）。  \n- `console.log(3)` 的返回值是 `undefined`（函数调用表达式）。  \n- `i = 0` 的值为 `0`（赋值表达式）。\n\n---\n\n### **2. 表达式的分类**  \n\n#### **(1) 原始表达式（Primary Expression）**  \n\n最基本的表达式，无法再分解，包括：  \n\n- **字面量**：数字、字符串、布尔值（如 `42`、`\"hello\"`、`true`）。  \n- **关键字**：`this`（当前对象）、`null`、`arguments`（函数参数对象）。  \n- **变量引用**：如 `i`、`sum`（若变量未声明会抛出 `ReferenceError`）。  \n\n#### **(2) 复合表达式**  \n\n通过运算符组合原始表达式生成，常见类型包括：  \n\n- **算术表达式**：如 `3 + 2`、`num++`。  \n- **逻辑表达式**：如 `a && b`、`x || y`。  \n- **赋值表达式**：如 `i = 0`、`j += 10`。  \n- **逗号运算符表达式**：计算左操作数后返回右操作数的值（如 `i = 0, j = 1`）。  \n\n#### **(3) 其他类型表达式**  \n\n- **对象/数组初始化**：如 `{ key: \"value\" }`、`[1, 2, 3]`。  \n- **函数定义表达式**：如 `function() {}` 或箭头函数。  \n- **属性访问表达式**：如 `obj.key` 或 `arr[0]`。  \n- **调用表达式**：如 `func()`。  \n- **对象创建表达式**：如 `new Date()`。  \n- **分组表达式**：用括号强制计算优先级（如 `(a + b) * c`）。  \n\n#### **(4) 特殊表达式**  \n\n[特殊表达式::[[IIFE]]] (特殊表达式::[[生成器]]) (特殊表达式::[[正则表达式]])\n\n- **立即执行函数（IIFE）** ：`(function() {})()`，用于隔离作用域。  \n- **生成器函数表达式**：如 `function* gen() {}`。  \n- **正则表达式**：如 `/[a-z]/g`，用于文本匹配。  \n\n---\n\n### **3. 运算符的作用与分类**  \n\n(组合表达式::[[运算符]])是组合表达式的核心工具，根据功能可分为：  \n\n- **算术运算符**：`+`、`-`、`*`、`/`、`%`（取余）。  \n- **比较运算符**：`==`（值相等）、`===`（严格相等）。  \n- **逻辑运算符**：`&&`（与）、`||`（或）、`!`（非）。  \n- **赋值运算符**：`=`、`+=`、`-=`。  \n- **三元运算符**：`condition ? val1 : val2`。  \n\n---\n\n### **4. 表达式与语句的区别**  \n\n  [ 表达式有返回值,语句没有返回值 ::[[语句]]]\n  \n- **表达式**：**有返回值**，可作为其他表达式的一部分（如 `a = 1 + 2`）。  \n- **语句**：**执行操作但无返回值**（如 `if`、`for` 循环）。\n- **示例**：  \n    - `var a = 1` 是语句，完成变量声明和赋值。  \n    - `a = 1` 是表达式，值为 `1`。  \n\n---\n\n### **5. 注意事项**  \n\n- **副作用**：某些表达式会改变程序状态（如 `i++`、`obj.x = 1`）。  \n- **运算符优先级**：通过分组运算符 `()` 明确计算顺序。  \n- **类型转换**：弱类型特性可能导致隐式转换（如 `\"3\" + 2` 结果为 `\"32\"`）。  \n\n---\n\n### **参考资料**  \n\n- 原始表达式定义：  \n- 表达式分类与运算符：  \n- 表达式与语句区别：  \n- 特殊表达式（IIFE、生成器等）：\n", "aliases": ["JS 表达式", "JavaScript 表达式"], "degree": 1, "nameLength": 3}, "position": {"x": 275.5, "y": 342.04999999999995}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline"}, {"data": {"id": "core:React.组件.md", "name": "React.组件", "path": "React.组件.md", "content": "---\naliases:\n  - 组件\n  - Component\n---\n\nReact 组件是 React 应用的核心构建块。它们允许你将 UI 拆分成独立、可复用的部分，并分别考虑每个部分。组件使代码更容易组织、维护和复用。理解 React 组件是掌握 React 的关键。\n\n以下是对 React 组件的详细解释，涵盖了各个方面：\n\n**一、 组件的定义和重要性**\n\n- **定义：** 在 React 中，组件是一个独立的、可复用的代码块，它负责渲染 UI 的一部分。你可以将组件视为自定义的 HTML 元素。\n- **重要性：**\n  - **代码复用性 (Reusability)：** 组件可以被多次使用在应用的不同地方，甚至在不同的项目中。这大大减少了代码冗余，提高了开发效率。\n  - **模块化 (Modularity)：** 组件将 UI 拆分成小的、独立的模块，使得代码结构更清晰，易于理解和维护。\n  - **可维护性 (Maintainability)：** 由于组件的独立性，修改一个组件的代码通常不会影响到其他组件，降低了维护成本和风险。\n  - **可测试性 (Testability)：** 组件是独立的单元，更容易进行单元测试，确保代码质量。\n  - **组合性 (Composition)：** 你可以将小的组件组合成更大的、更复杂的组件，构建复杂的 UI 界面。\n  - **关注点分离 (Separation of Concerns)：** 组件鼓励将 UI 逻辑、样式和行为封装在一起，实现关注点分离，提高代码的可读性和可维护性。\n\n**二、 组件的类型**\n\nReact 主要有两种类型的组件：\n\n1.  **[[函数组件 Function Components]]：**\n\n    - **定义：** 使用 JavaScript 函数定义的组件。它们接收 `props` 作为参数，并返回 JSX (描述 UI 结构)。\n    - **特点：**\n      - **简洁：** 函数组件通常比类组件更简洁，代码量更少。\n      - **易于理解：** 对于简单的 UI 展示，函数组件更直观易懂。\n      - **配合 Hooks 使用：** 从 React 16.8 版本开始，函数组件可以通过 Hooks (例如 `useState`, `useEffect`) 添加状态和生命周期功能，使其功能更加强大，逐渐取代了类组件在大多数场景下的地位。\n      - **推荐使用：** 函数组件是 React 官方推荐的组件类型，通常是构建新组件的首选。\n\n    **示例：**\n\n    ```jsx\n    import React from \"react\";\n\n    function Welcome(props) {\n      return <h1>Hello, {props.name}</h1>;\n    }\n\n    export default Welcome;\n    ```\n\n2.  **[[类组件 Class Components]]：**\n\n    - **定义：** 使用 ES6 类定义的组件。类组件必须继承 `React.Component` 或 `React.PureComponent`，并实现 `render()` 方法来返回 JSX。\n    - **特点：**\n      - **生命周期方法：** 类组件拥有丰富的生命周期方法 (例如 `componentDidMount`, `componentDidUpdate`, `componentWillUnmount`)，可以处理组件的不同阶段。\n      - **状态管理：** 类组件使用 `this.state` 来管理内部状态，并使用 `this.setState()` 来更新状态。\n      - **历史遗留：** 在 React Hooks 出现之前，类组件是管理状态和处理复杂逻辑的主要方式。\n      - **逐渐减少使用：** 随着 Hooks 的普及，函数组件已经可以完成类组件的大部分功能，并且更加简洁高效，因此类组件在新的 React 应用中逐渐减少使用。但理解类组件仍然很重要，因为很多现有的 React 代码库仍然使用类组件。\n\n    **示例：**\n\n    ```jsx\n    import React from \"react\";\n\n    class Welcome extends React.Component {\n      render() {\n        return <h1>Hello, {this.props.name}</h1>;\n      }\n    }\n\n    export default Welcome;\n    ```\n\n**三、 组件的核心概念**\n\n1.  **Props (属性)：**\n\n    - **定义：** Props 是组件的输入，用于从父组件向子组件传递数据。\n    - **特点：**\n      - **只读 (Read-only)：** 子组件不能直接修改 props 的值。props 的值由父组件控制。\n      - **传递数据：** 可以传递任何类型的数据，包括字符串、数字、对象、函数等。\n      - **组件配置：** Props 用于配置组件的行为和外观，使组件更具通用性和灵活性。\n    - **使用方式：** 通过组件标签的属性传递，例如 `<MyComponent name=\"John\" age={30} />`，子组件通过 `props` 对象访问这些属性。\n\n2.  **State (状态)：** [[React.State]]\n\n    - **定义：** State 是组件内部的数据，用于控制组件的渲染和行为。\n    - **特点：**\n      - **可变 (Mutable)：** State 的值可以被组件自身修改。\n      - **触发渲染：** State 的改变会触发组件的重新渲染，更新 UI。\n      - **组件私有：** State 是组件私有的，只能在组件内部访问和修改。\n    - **管理方式：**\n      - **函数组件：** 使用 `useState` Hook 管理状态。\n      - **类组件：** 使用 `this.state` 初始化状态，使用 `this.setState` 更新状态。\n\n3.  **JSX (JavaScript XML)：**\n\n    - **定义：** JSX 是一种 JavaScript 的语法扩展，允许你在 JavaScript 代码中编写类似 HTML 的结构。\n    - **作用：** 简化了 UI 结构的描述，使代码更易读和维护。React 组件使用 JSX 来描述它们应该渲染的内容。\n    - **编译：** JSX 代码在构建过程中会被 Babel 编译成标准的 JavaScript 函数调用 (例如 `React.createElement`)。\n\n4.  **组件组合 (Component Composition)：**\n\n    - **概念：** 将小的、独立的组件组合成更大的、复杂的组件。这是 React 构建 UI 的核心思想。\n    - **方式：**\n      - **嵌套组件：** 在一个组件的 JSX 中直接使用另一个组件。\n      - **Props 传递：** 父组件通过 props 将数据和函数传递给子组件，控制子组件的行为和渲染内容。\n      - **特殊 Props：`children`：** React 组件的 `props` 对象中有一个特殊的属性 `children`，它表示组件标签之间的内容，允许组件接收并渲染子组件。\n\n5.  **组件的生命周期 (仅针对类组件，函数组件使用 Hooks 模拟生命周期)：**\n    类组件拥有生命周期方法，这些方法在组件的不同阶段会被 React 自动调用，允许你在特定时刻执行代码。常见的生命周期方法包括：\n\n    - **挂载阶段 (Mounting)：** 组件第一次被渲染到 DOM 中。\n\n      - `constructor()`：组件构造函数，用于初始化 state 和绑定事件处理函数。\n      - `static getDerivedStateFromProps(props, state)`：静态方法，在组件挂载和更新时调用，根据 props 更新 state。\n      - `render()`：渲染组件的 JSX 结构。\n      - `componentDidMount()`：组件挂载后立即调用，常用于执行副作用操作，例如数据获取、DOM 操作、设置定时器等。\n\n    - **更新阶段 (Updating)：** 组件的 props 或 state 发生变化，导致组件重新渲染。\n\n      - `static getDerivedStateFromProps(props, state)`：同挂载阶段。\n      - `shouldComponentUpdate(nextProps, nextState, nextContext)`：决定组件是否应该重新渲染，可以通过比较 nextProps 和 nextState 与当前的 props 和 state 来优化性能。默认返回 `true`。\n      - `render()`：同挂载阶段。\n      - `getSnapshotBeforeUpdate(prevProps, prevState)`：在 DOM 更新之前调用，可以获取 DOM 更新前的快照信息。\n      - `componentDidUpdate(prevProps, prevState, snapshot)`：在组件更新后立即调用，常用于执行副作用操作，例如 DOM 操作、网络请求等，但需要注意避免无限循环。\n\n    - **卸载阶段 (Unmounting)：** 组件从 DOM 中移除。\n      - `componentWillUnmount()`：组件卸载前调用，常用于清理副作用，例如取消定时器、取消网络请求、移除事件监听器等。\n\n    **函数组件中使用 `useEffect` Hook 模拟生命周期：**\n\n    - `useEffect(() => { /* 组件挂载和更新后的副作用操作 */ return () => { /* 组件卸载前的清理操作 */; }; }, [dependencies]);`\n      - 第一个参数是副作用函数，相当于 `componentDidMount` 和 `componentDidUpdate`。\n      - 返回的函数是清理函数，相当于 `componentWillUnmount`。\n      - 第二个参数 (依赖项数组) 控制 `useEffect` 的执行时机，空数组 `[]` 相当于 `componentDidMount` 和 `componentWillUnmount`。\n\n\n**四、 组件的最佳实践**\n\n- **保持组件的小而专注：** 一个组件应该只负责渲染 UI 的一小部分，并专注于单一的功能。这提高了组件的可复用性和可维护性。\n- **使用函数组件优先：** 在大多数情况下，函数组件更简洁、易于理解和测试。优先使用函数组件，并使用 Hooks 添加状态和副作用逻辑。\n- **合理使用 Props 和 State：** Props 用于父组件向子组件传递数据，State 用于组件内部管理状态。明确区分 Props 和 State 的职责，避免滥用 State。\n- **组件命名：** 组件名称应该清晰、描述性强，使用 PascalCase 命名 (首字母大写)。\n- **PropTypes (类型检查，虽然现在 TypeScript 更流行)：** 使用 PropTypes (或 TypeScript) 定义组件的 props 类型，帮助发现潜在的错误，提高代码质量。\n- **样式管理：** 选择合适的 CSS 方案 (例如 CSS Modules, Styled Components, Tailwind CSS) 来管理组件的样式，避免样式冲突，提高样式的可维护性。\n- **测试组件：** 为重要的组件编写单元测试，确保组件的正确性和稳定性。\n- **文档化组件：** 为组件编写清晰的文档，说明组件的用途、props、state 以及使用方法，方便团队协作和组件复用。\n\n**五、 组件的代码示例**\n\n**1. 函数组件示例 (计数器)：**\n\n```jsx\nimport React, { useState } from \"react\";\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n\n  const increment = () => {\n    setCount(count + 1);\n  };\n\n  const decrement = () => {\n    setCount(count - 1);\n  };\n\n  return (\n    <div>\n      <p>Count: {count}</p>\n      <button onClick={increment}>Increment</button>\n      <button onClick={decrement}>Decrement</button>\n    </div>\n  );\n}\n\nexport default Counter;\n```\n\n**2. 类组件示例 (简单的输入框，已逐渐不推荐新的类组件开发，但作为理解学习仍然有意义)：**\n\n```jsx\nimport React from \"react\";\n\nclass InputField extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      inputValue: \"\",\n    };\n    this.handleChange = this.handleChange.bind(this); // 绑定 this\n  }\n\n  handleChange(event) {\n    this.setState({ inputValue: event.target.value });\n  }\n\n  render() {\n    return (\n      <div>\n        <label htmlFor=\"myInput\">Enter text:</label>\n        <input type=\"text\" id=\"myInput\" value={this.state.inputValue} onChange={this.handleChange} />\n        <p>You entered: {this.state.inputValue}</p>\n      </div>\n    );\n  }\n}\n\nexport default InputField;\n```\n\n**总结：**\n\nReact 组件是构建用户界面的基石。理解不同类型的组件、Props、State、JSX 以及组件的生命周期，并遵循最佳实践，是成为一名优秀的 React 开发者的关键。随着 React Hooks 的发展，函数组件已经成为主流，掌握函数组件和 Hooks 的使用对于现代 React 开发至关重要。 学习和实践组件的各种概念和技巧，你就能更高效、更优雅地构建复杂的 React 应用。\n", "degree": 3, "nameLength": 8, "aliases": ["组件", "Component"]}, "position": {"x": 202.25, "y": 309}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:React.State.md", "name": "React.State", "path": "React.State.md", "content": "React 中的 **State (状态)** 是组件的核心概念之一，它代表了组件内部的动态数据，用于控制组件的渲染和行为。 理解 State 是 React 开发的关键。\n\n以下是对 React `State` 的详细解释：\n\n### **一、 State 的定义和作用**\n\n- **定义：** State 是组件内部的 **私有数据** 存储。它是一个 JavaScript 对象，包含了组件在不同时刻可能需要记住和改变的信息。\n- **作用：**\n  - **控制 UI 渲染：** State 的值直接影响组件的 UI 输出。当 State 发生变化时，React 会自动重新渲染组件，以反映最新的数据状态。\n  - **交互性：** State 使得组件可以响应用户交互，例如点击按钮、输入文本等，并动态更新 UI，从而实现交互性应用。\n  - **组件内部管理：** State 完全由组件自身管理，组件可以自由地读取和修改自己的 State，而无需外部干预。\n\n### **二、 State 的关键特性**\n\n1.  **可变性 (Mutable)：** State 的值是可以被组件自身修改的。这是 State 与 Props 的一个重要区别（Props 是只读的）。\n2.  **触发重新渲染 (Triggers Re-rendering)：** 当 State 的值发生变化时，React 会自动触发组件的重新渲染过程。React 的虚拟 DOM 和 Diff 算法会高效地更新实际 DOM，只更新需要改变的部分。\n3.  **组件私有 (Component-Private)：** State 是组件私有的。这意味着：\n    - 只有组件自身可以访问和修改自己的 State。\n    - 父组件无法直接访问子组件的 State。\n    - 兄弟组件之间也不能直接访问彼此的 State。\n    - 这种私有性保证了组件的独立性和封装性，降低了组件之间的耦合度。\n\n### **三、 函数组件中的 State (使用 `useState` Hook)** [声明和管理 State::[[useState]]]。\n\n在函数组件中，我们使用 **`useState` Hook** 来\n\n- **`useState` 的基本用法：**\n\n  ```javascript\n  import React, { useState } from \"react\";\n\n  function MyComponent() {\n    // 声明一个名为 'count' 的 state 变量，初始值为 0\n    const [count, setCount] = useState(0);\n\n    return (\n      <div>\n        <p>Count: {count}</p>\n        <button onClick={() => setCount(count + 1)}>Increment</button>\n      </div>\n    );\n  }\n  ```\n\n  - `useState(initialState)` 接受一个初始值 `initialState` 作为参数，并返回一个数组，包含两个元素：\n    - **第一个元素 (通常命名为 `stateName`)**: 当前的 State 值。在上面的例子中是 `count`。\n    - **第二个元素 (通常命名为 `setStateName`)**: 一个更新 State 的函数。在上面的例子中是 `setCount`。\n  - 当我们需要更新 State 时，调用 `setStateName` 函数，并传入新的 State 值。 React 会触发组件的重新渲染，`stateName` 的值也会更新为新的值。\n\n- **初始 State 可以是函数：**\n\n  初始 State 可以是一个函数，这个函数只会在组件首次渲染时执行一次，用于计算初始 State 值。这对于初始 State 的计算比较复杂或开销较大的情况很有用。\n\n  ```javascript\n  const [state, setState] = useState(() => {\n    // 假设 initialValue 的计算很耗时\n    const initialValue = computeInitialState();\n    return initialValue;\n  });\n  ```\n\n- **更新 State 的函数可以接收函数作为参数：**\n\n  当新的 State 值依赖于之前的 State 值时，建议使用函数形式的 `setState`。 React 会将之前的 State 值作为参数传递给这个函数，你可以基于之前的 State 值计算出新的 State 值。这可以避免由于 React 的批量更新机制可能导致的状态不一致问题。\n\n  ```javascript\n  function MyComponent() {\n    const [count, setCount] = useState(0);\n\n    const increment = () => {\n      // 使用函数形式的 setCount，prevState 是之前的 count 值\n      setCount(prevState => prevState + 1);\n    };\n\n    return (\n      // ...\n    );\n  }\n  ```\n\n### **四、 类组件中的 State (使用 `this.state` 和 `this.setState`)** [[this.state&this.setState]]\n\n在类组件中，State 是通过 `this.state` 属性来管理的，并使用 `this.setState()` 方法来更新 State。\n\n- **初始化 State (在 `constructor` 中)：**\n\n  在类组件的 `constructor` 方法中，通过 `this.state = { /* initial state object */ }` 来初始化 State。\n\n  ```javascript\n  import React from \"react\";\n\n  class MyComponent extends React.Component {\n    constructor(props) {\n      super(props);\n      // 初始化 state\n      this.state = {\n        count: 0,\n      };\n    }\n\n    render() {\n      return (\n        <div>\n          <p>Count: {this.state.count}</p>\n          <button onClick={this.incrementCount}>Increment</button>\n        </div>\n      );\n    }\n\n    incrementCount = () => {\n      // 更新 state\n      this.setState({ count: this.state.count + 1 });\n    };\n  }\n  ```\n\n- **更新 State (使用 `this.setState()`):**\n\n  使用 `this.setState(updater, callback)` 方法来更新 State。\n\n  - **`updater` 参数：** 可以是以下两种形式：\n    - **对象：** 直接传入一个对象，React 会将这个对象与当前的 State 进行 **浅合并** (shallow merge)。这意味着只会合并顶层属性，如果 State 中有嵌套对象，嵌套对象本身不会被合并，而是被替换。\n    - **函数：** 传入一个函数，该函数接收之前的 State 值作为参数，并返回一个新的 State 对象。 推荐使用函数形式，尤其当新的 State 值依赖于之前的 State 值时。\n  - **`callback` 参数 (可选)：** 一个回调函数，在 State 更新完成并且组件重新渲染后会被调用。\n\n  **示例 (使用对象形式的 `setState`):**\n\n  ```javascript\n  this.setState({ count: this.state.count + 1 });\n  ```\n\n  **示例 (使用函数形式的 `setState`，推荐):**\n\n  ```javascript\n  this.setState((prevState) => ({\n    count: prevState.count + 1,\n  }));\n  ```\n\n  **示例 (带有回调函数的 `setState`):**\n\n  ```javascript\n  this.setState({ count: this.state.count + 1 }, () => {\n    console.log(\"State updated, count is now:\", this.state.count);\n  });\n  ```\n\n### **五、 更新 State 的重要规则**\n\n1.  **不要直接修改 State：**\n\n    **错误的做法：**\n\n    ```javascript\n    // ❌ 错误！ 不要直接修改 state\n    this.state.count = this.state.count + 1;\n    // ❌ 错误！ 这样不会触发重新渲染\n    ```\n\n    **正确做法：** 使用 `this.setState()` (类组件) 或 `setState` (函数组件) 来更新 State。\n\n    **正确的做法：**\n\n    ```javascript\n    // ✅ 正确！ 使用 setState 更新 state (类组件)\n    this.setState({ count: this.state.count + 1 });\n\n    // ✅ 正确！ 使用 setState 更新 state (函数组件)\n    setCount(count + 1);\n    ```\n\n    **原因：** React 依赖于检测 State 的变化来触发重新渲染。直接修改 `this.state` 或 `state` 变量不会通知 React State 发生了变化，因此不会触发重新渲染。\n\n2.  **State 更新可能是异步的：**\n\n    为了性能优化，React 可能会将多个 `setState` 调用 **批量处理** (batch updates)，这意味着 State 的更新可能是异步的。\n\n    **例子：**\n\n    ```javascript\n    incrementCount = () => {\n      this.setState({ count: this.state.count + 1 });\n      console.log(\"Count after first setState:\", this.state.count); // 可能仍然是旧值\n      this.setState({ count: this.state.count + 1 });\n      console.log(\"Count after second setState:\", this.state.count); // 可能仍然是旧值\n    };\n    ```\n\n    在上面的例子中，`console.log` 中的 `this.state.count` 可能仍然是旧值，因为 `setState` 的更新可能是异步的，并且 React 可能还没有完成 State 的更新和组件的重新渲染。\n\n    **解决方法：**\n\n    - **使用函数形式的 `setState`：** 函数形式的 `setState` 保证接收到的是最新的 State 值，可以避免由于异步更新导致的状态不一致问题。\n\n      ```javascript\n      incrementCount = () => {\n        this.setState((prevState) => ({ count: prevState.count + 1 }));\n        this.setState((prevState) => ({ count: prevState.count + 1 }));\n      };\n      ```\n\n    - **使用回调函数：** `setState` 的第二个参数可以是一个回调函数，该函数会在 State 更新完成并且组件重新渲染后执行。你可以在回调函数中访问到最新的 State 值。\n\n      ```javascript\n      this.setState({ count: this.state.count + 1 }, () => {\n        console.log(\"Count after setState with callback:\", this.state.count); // 保证是新值\n      });\n      ```\n\n3.  **State 更新是浅合并 (Shallow Merge)：**\n\n    当使用对象形式的 `setState` 更新 State 时，React 会将你提供的对象与当前的 State 对象进行浅合并。这意味着只会合并顶层属性。\n\n    **例子：**\n\n    ```javascript\n    this.state = {\n      userInfo: {\n        name: \"John\",\n        age: 30,\n      },\n      isLoggedIn: false,\n    };\n\n    // 只更新 userInfo.age，userInfo.name 和 isLoggedIn 保持不变\n    this.setState({\n      userInfo: {\n        age: 31,\n      },\n    });\n\n    // 最终的 state 会变成:\n    // this.state = {\n    //   userInfo: { age: 31 }, // userInfo.name 丢失了！ ❌ 错误！\n    //   isLoggedIn: false\n    // };\n    ```\n\n    **正确做法：** 在更新嵌套对象时，需要手动合并之前的 State 对象。\n\n    ```javascript\n    this.setState((prevState) => ({\n      userInfo: {\n        ...prevState.userInfo, // 浅拷贝之前的 userInfo 对象\n        age: 31, // 更新 age 属性\n      },\n    }));\n\n    // 或者，更简洁的方式 (如果 userInfo 是简单的对象)：\n    this.setState({\n      userInfo: Object.assign({}, this.state.userInfo, { age: 31 }),\n    });\n\n    // 最终的 state 会变成:\n    // this.state = {\n    //   userInfo: { name: 'John', age: 31 }, // ✅ 正确！\n    //   isLoggedIn: false\n    // };\n    ```\n\n### **六、 State vs. Props**\n\n| 特性     | State                                        | Props                                                     |\n| -------- | -------------------------------------------- | --------------------------------------------------------- |\n| 数据来源 | 组件自身                                     | 父组件                                                    |\n| 可变性   | 可变 (Mutable)，组件自身可以修改             | 只读 (Read-only)，子组件不能修改                          |\n| 用途     | 组件内部状态管理，控制自身 UI 和行为         | 从父组件向子组件传递数据，配置子组件                      |\n| 组件关系 | 组件内部                                     | 父子组件之间                                              |\n| 管理方式 | `useState` (函数组件), `this.state` (类组件) | 作为组件标签的属性传递 (`<Component propName=\"value\" />`) |\n\n**何时使用 State vs. Props：**\n\n- **使用 State：** 当数据是组件内部的，并且会随着用户交互或组件的生命周期而改变时，使用 State。例如，表单输入框的值、计数器的数值、组件的显示/隐藏状态等。\n- **使用 Props：** 当数据需要从父组件传递给子组件，并且子组件不应该修改这些数据时，使用 Props。例如，列表组件接收数据列表、按钮组件接收文本标签、图片组件接收图片 URL 等。\n\n### **七、 State 管理的最佳实践**\n\n- **保持 State 尽可能局部化：** 将 State 放在需要使用它的组件内部。避免将 State 提升到不必要的父组件中，这可以降低组件之间的耦合度，提高组件的可复用性和可维护性。\n- **提升 State (Lifting State Up)：** 当多个组件需要共享同一个 State 时，将 State 提升到它们共同的父组件中管理。父组件作为 \"数据源\"，通过 Props 将数据传递给子组件，并通过回调函数处理子组件的事件，更新父组件的 State。\n- **避免冗余 State：** 尽量避免在 State 中存储可以从其他 State 或 Props 中计算出来的数据。例如，不要同时存储列表数据和列表的长度，列表的长度可以直接从列表数据计算得出。\n- **使用不可变数据更新模式：** 虽然 JavaScript 对象是可变的，但在 React 中推荐使用不可变数据更新模式来更新 State。这意味着在更新 State 时，不要直接修改之前的 State 对象，而是创建一个新的对象，并使用新的对象替换旧的 State。这可以提高性能，并更容易进行状态追踪和调试。 例如，使用扩展运算符 (`...`) 或 `Object.assign()` 来创建新的对象。\n\n**总结：**\n\nReact 的 State 是构建动态和交互式 UI 的核心。理解 State 的概念、特性、使用方法以及最佳实践，对于编写高效、可维护的 React 应用至关重要。 掌握 `useState` Hook (函数组件) 和 `this.state`/`this.setState` (类组件) 的使用，并遵循 State 更新的规则，你就能有效地管理组件的状态，构建出丰富的用户界面。\n", "degree": 2, "nameLength": 11}, "position": {"x": 177.5, "y": 342.04999999999995}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:React 17 Batching.md", "name": "React 17 Batching", "path": "React 17 Batching.md", "content": "---\naliases:\n  - isBatchingUpdates\n  - 批处理标志\n  - 批处理\n---\n## 什么是批处理\n\n在 React 中，批处理（batching）是指将多个状态更新（setState 调用）合并到一次渲染周期中。这种优化机制可以减少不必要的渲染，提高应用性能。\n\n## React 17 批处理的实现细节\n\nReact 17 中的批处理有一个重要特征：它只在 React 的合成事件（Synthetic Events）处理函数中自动发生。这是因为 React 在这些事件处理函数执行期间会启用一个称为\"批处理标志\"的内部机制。\n\n**技术原理：**\n\nReact 17 使用一个内部变量 `isBatchingUpdates` 来控制批处理行为。当 React 事件处理函数执行时：\n\n1. React 设置 `isBatchingUpdates = true`\n2. 执行您的事件处理函数代码\n3. 所有 setState 调用被收集到更新队列中，而不是立即应用\n4. 事件处理函数完成后，React 设置 `isBatchingUpdates = false`\n5. 最后执行一次更新，触发一次渲染\n\n## 同步事件中的批处理示例\n\n```jsx\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  const [flag, setFlag] = useState(false);\n\n  function handleClick() {\n    console.log(\"Before updates, count =\", count); // 0\n\n    setCount(count + 1);\n    setFlag(!flag);\n\n    // 这里状态尚未更新，因为 React 正在批处理\n    console.log(\"After updates, count =\", count); // 仍然是 0\n\n    // React 在事件处理函数结束后才会应用所有状态更新并重新渲染\n  }\n\n  console.log(\"Render with count =\", count); // 渲染日志\n\n  return <button onClick={handleClick}>Update</button>;\n}\n```\n\n执行流程：\n\n1. 初始渲染：\"Render with count = 0\"\n2. 点击按钮，执行 handleClick：\n   - 日志：\"Before updates, count = 0\"\n   - 两个 setState 调用排队但未立即应用\n   - 日志：\"After updates, count = 0\"（状态尚未变化）\n3. 事件处理函数结束后，React 处理队列中的所有更新\n4. 进行一次渲染：\"Render with count = 1\"\n   \n```mermaid\nsequenceDiagram\n    participant U as 用户\n    participant C as Counter组件\n    participant R as React内部\n    \n    Note over C: 初始状态: count=0, flag=false\n    \n    R->>C: 初始渲染\n    C->>C: console.log(\"Render with count = 0\")\n    C->>U: 显示按钮\n    \n    U->>C: 点击按钮\n    C->>C: handleClick()\n    C->>C: console.log(\"Before updates, count = 0\")\n    C->>R: setCount(count + 1)\n    Note over R: 将更新加入批处理队列\n    C->>R: setFlag(!flag)\n    Note over R: 将更新加入批处理队列\n    C->>C: console.log(\"After updates, count = 0\")\n    \n    Note over C,R: 事件处理函数结束\n    \n    R->>R: 处理批处理队列\n    R->>C: 触发重新渲染\n    C->>C: console.log(\"Render with count = 1\")\n    C->>U: 更新后的UI\n```\n\n## 异步代码中批处理的缺失\n\n在 React 17 中，当 setState 调用发生在以下环境时，批处理不会自动发生：\n\n- setTimeout/setInterval 回调\n- Promise 链（.then/.catch）\n- async/await 函数内部\n- 原生 DOM 事件处理函数\n- 第三方库回调\n\n这是因为这些代码在 React 事件处理函数执行完毕后才运行，此时 `isBatchingUpdates` 已被设置回 `false`。\n\n```jsx\nfunction AsyncCounter() {\n  const [count, setCount] = useState(0);\n  const [flag, setFlag] = useState(false);\n\n  function handleAsyncClick() {\n    // 离开 React 事件处理函数的上下文\n    setTimeout(() => {\n      console.log(\"Before async updates, count =\", count);\n\n      setCount((c) => c + 1); // 触发第一次渲染\n      console.log(\"After first update\"); // 这行会在第一次渲染后执行\n\n      setFlag((f) => !f); // 触发第二次渲染\n      console.log(\"After second update\"); // 这行会在第二次渲染后执行\n    }, 0);\n  }\n\n  console.log(\"Rendering component...\"); // 每次渲染都会执行\n\n  return <button onClick={handleAsyncClick}>Update Async</button>;\n}\n```\n\n执行流程：\n\n1. 初始渲染：\"Rendering component...\"\n2. 点击按钮，setTimeout 回调延迟执行\n3. 回调执行：\n   - 日志：\"Before async updates, count = 0\"\n   - 执行 setCount，立即触发渲染（不批处理）\n4. 第一次额外渲染：\"Rendering component...\"\n5. 继续执行回调：\n   - 日志：\"After first update\"\n   - 执行 setFlag，再次立即触发渲染（不批处理）\n6. 第二次额外渲染：\"Rendering component...\"\n7. 继续执行回调：\n   - 日志：\"After second update\"\n\n## 性能影响分析\n\n这种行为会导致以下性能问题：\n\n1. **额外的渲染周期**：异步代码中的每个 setState 调用都会触发独立渲染\n2. **闪烁问题**：用户可能看到中间状态的 UI，造成视觉闪烁\n3. **计算浪费**：组件树中的计算和 DOM 更新被不必要地重复执行\n4. **状态不一致**：相关状态更新之间可能出现不一致的中间状态\n\n## React 17 中的批处理解决方案\n\n在 React 18 之前，开发者通常使用以下方法解决异步代码中的批处理问题：\n在异步代码中尽量使用[[函数式更新 Functional Updates]] 保证\n- 确保你总是基于最新的状态值进行更新，避免闭包陷阱\n- 提供一种基于前一个状态计算新状态的方式\n\n1. **使用 unstable_batchedUpdates API**（不推荐但有效）：\n\n```jsx\nimport { unstable_batchedUpdates } from \"react-dom\";\n\nfunction handleAsyncClick() {\n  setTimeout(() => {\n    unstable_batchedUpdates(() => {\n      setCount((c) => c + 1);\n      setFlag((f) => !f);\n    });\n  }, 0);\n}\n```\n\n2. **合并状态逻辑**，使用单一状态对象：\n\n```jsx\nfunction Counter() {\n  const [state, setState] = useState({ count: 0, flag: false });\n\n  function handleAsyncClick() {\n    setTimeout(() => {\n      setState((prevState) => ({\n        count: prevState.count + 1,\n        flag: !prevState.flag,\n      }));\n    }, 0);\n  }\n}\n```\n\n3. **使用 useReducer** 处理复杂状态更新：\n\n```jsx\nfunction reducer(state, action) {\n  switch (action.type) {\n    case \"increment_and_toggle\":\n      return { ...state, count: state.count + 1, flag: !state.flag };\n    default:\n      return state;\n  }\n}\n\nfunction Counter() {\n  const [state, dispatch] = useReducer(reducer, { count: 0, flag: false });\n\n  function handleAsyncClick() {\n    setTimeout(() => {\n      dispatch({ type: \"increment_and_toggle\" });\n    }, 0);\n  }\n}\n```\n\nReact 18 通过引入自动批处理解决了这个长期存在的限制，不再需要这些变通方法。\n", "degree": 2, "nameLength": 17, "aliases": ["isBatchingUpdates", "批处理标志", "批处理"]}, "position": {"x": 219, "y": 441.20000000000005}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:浅合并 Shallow Merge.md", "name": "浅合并 Shallow Merge", "path": "浅合并 Shallow Merge.md", "content": "## 什么是浅合并\n\n在 React 类组件中，`this.setState()`方法会将您提供的对象与当前状态进行**浅合并**。这是指:\n\n- 只有传入对象中的属性会被更新\n- 没有在传入对象中指定的现有 state 属性将被保留\n- 合并只发生在对象的顶层（第一层属性）\n\n## 浅合并工作原理\n\n```jsx\n// 初始状态\nthis.state = {\n  user: {\n    name: \"<PERSON>\",\n    age: 25,\n  },\n  isLoggedIn: true,\n  count: 0,\n};\n\n// 更新部分状态\nthis.setState({\n  count: this.state.count + 1,\n  user: {\n    name: \"<PERSON>\",\n  },\n});\n\n// 更新后的状态 (浅合并结果)\n// {\n//   user: { name: \"<PERSON>\" },  // 注意：age属性丢失了\n//   isLoggedIn: true,       // 保留，因为没有更新它\n//   count: 1                // 更新了\n// }\n```\n\n## 浅合并的关键特性\n\n1. **仅一级属性会被合并**：顶层属性会保留，但嵌套对象会被完全替换而非合并\n\n2. **解决嵌套对象更新问题**：需要手动合并之前的嵌套属性\n\n   ```jsx\n   // 正确更新嵌套对象\n   this.setState({\n     user: {\n       ...this.state.user, // 保留其他用户属性\n       name: \"Bob\", // 只更新name\n     },\n   });\n   ```\n\n3. **批量更新效果**：多次调用`this.setState`会被批处理，并按调用顺序浅合并\n\n   ```jsx\n   // 这两次调用会被合并\n   this.setState({ count: 1 });\n   this.setState({ isLoggedIn: false });\n   // 最终结果相当于: this.setState({ count: 1, isLoggedIn: false })\n   ```\n\n4. **异步更新**：浅合并发生在 React 决定进行渲染时，而非立即执行\n\n## 与函数式更新的关系\n\n函数式更新形式仍会发生浅合并：\n\n```jsx\n// 函数式更新也会浅合并返回的对象\nthis.setState((prevState) => ({\n  count: prevState.count + 1,\n}));\n// 仍然只更新count，保留其他状态属性\n```\n\n## 与 React Hooks 的比较\n\n重要区别：**函数组件中的 useState 不执行浅合并**\n\n```jsx\n// 函数组件中\nconst [state, setState] = useState({\n  user: { name: \"Alice\", age: 25 },\n  isLoggedIn: true,\n  count: 0,\n});\n\n// 这会完全替换状态，而不是合并!\nsetState({ count: 1 });\n// 新状态变成: { count: 1 } - 其他属性丢失!\n\n// 在hooks中需要手动合并\nsetState((prevState) => ({\n  ...prevState, // 手动保留之前的所有状态\n  count: prevState.count + 1,\n}));\n```\n\n这种浅合并机制是 React 类组件设计的核心特性之一，理解它对于正确管理复杂状态结构至关重要。\n", "degree": 1, "nameLength": 17}, "position": {"x": 193.375, "y": 408.45000000000005}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline"}, {"data": {"id": "core:this.state&this.setState.md", "name": "this.state&this.setState", "path": "this.state&this.setState.md", "content": "---\naliases:\n  - this.setState\n  - 类组件状态\n  - this.state\n---\n在 React 类组件中，状态（State）处理是使用 `this.state` 和 `this.setState` 这两个关键概念来实现的。它们是类组件管理内部动态数据的核心机制。\n\n### this.setState的设计特性\n[[浅合并 Shallow Merge]] 自动将新状态与现有状态合并\n[[批处理机制 Batching]]：状态更新可能被延迟和批处理\n[[函数式更新 Functional Updates]]：支持基于先前状态计算新状态\n回调函数：可选的第二个参数在状态更新完成后执行\n\n**1. `this.state`：状态的初始化**\n\n*   **作用：** `this.state` 用于在类组件中 **初始化** 状态。它是一个组件实例的属性，用来存储组件的私有数据。\n*   **位置：** `this.state` 必须在类组件的 **`constructor` 构造函数** 中进行初始化。如果你没有 `constructor`，就无法直接初始化 `this.state`。\n*   **结构：** `this.state` 本身应该被赋值为一个 **JavaScript 对象**。这个对象包含了组件需要维护的初始状态数据，可以包含任意数量的键值对，每个键代表一个状态变量，值是该状态变量的初始值。\n\n**示例：**\n\n```jsx\nimport React from 'react';\n\nclass MyComponent extends React.Component {\n  constructor(props) {\n    super(props); // 必须调用 super(props)\n    // 初始化 state\n    this.state = {\n      count: 0,\n      message: 'Hello!',\n      isLoggedIn: false,\n      items: []\n    };\n  }\n\n  render() {\n    return (\n      <div>\n        <p>Count: {this.state.count}</p>\n        <p>Message: {this.state.message}</p>\n        <p>Logged in: {this.state.isLoggedIn ? 'Yes' : 'No'}</p>\n        {/* 渲染 items 列表 */}\n        <ul>\n          {this.state.items.map(item => (\n            <li key={item.id}>{item.text}</li>\n          ))}\n        </ul>\n        {/* ... 其他 JSX ... */}\n      </div>\n    );\n  }\n}\n\nexport default MyComponent;\n```\n\n**要点：**\n\n*   **`constructor(props)` 和 `super(props)`：**  在 `constructor` 中，你必须先调用 `super(props)`，这是 ES6 类继承的要求，它会调用父类 (`React.Component`) 的构造函数，并确保组件实例的 `this` 指向正确。同时，它也会传递 `props` 给父类构造函数，以便在组件内部通过 `this.props` 访问父组件传递的属性。\n*   **初始值：** `this.state` 中设置的键值对是状态变量的 **初始值**。组件首次渲染时会使用这些初始值。\n\n**2. `this.setState(updater, callback)`：状态的更新**\n\n*   **作用：** `this.setState()` 方法是类组件中 **更新状态** 的唯一正确方式。它会通知 React 组件的状态发生了变化，从而触发组件的 **重新渲染**。\n*   **位置：** `this.setState()` 可以被调用在类组件的任何方法中，例如事件处理函数、生命周期方法、自定义方法等。\n*   **参数：** `this.setState()` 接受两个参数：\n    *   **`updater` (必需)：**  描述状态更新的方式。它可以是两种类型：\n        *   **对象 (Object)：**  传入一个对象，React 会将这个对象与当前的 `this.state` 对象进行 **浅合并（Shallow Merge）**。这意味着只会合并顶层属性，如果 state 中有嵌套对象，嵌套对象本身不会被合并，而是被替换。\n        *   **函数 (Function)：**  传入一个函数，这个函数会被 React 调用，并接收 **之前的 state 值** 作为第一个参数 (`prevState`)，以及 **当前的 props 值** 作为第二个参数 (`props`)。函数应该返回一个新的对象，这个对象会被用来更新 state。 **推荐使用函数形式，尤其当新的 state 值依赖于之前的 state 值时。**\n    *   **`callback` (可选)：**  一个回调函数，它会在状态更新完成并且组件 **重新渲染之后** 被执行。可以用来执行一些需要在 DOM 更新后才能进行的操作。\n\n**示例：使用对象形式的 `updater`**\n\n```jsx\nimport React from 'react';\n\nclass CounterComponent extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      count: 0\n    };\n  }\n\n  incrementCount = () => {\n    // 使用对象形式的 updater 更新 count\n    this.setState({ count: this.state.count + 1 });\n  };\n\n  render() {\n    return (\n      <div>\n        <p>Count: {this.state.count}</p>\n        <button onClick={this.incrementCount}>Increment</button>\n      </div>\n    );\n  }\n}\n\nexport default CounterComponent;\n```\n\n**示例：使用函数形式的 `updater` (推荐)**\n\n```jsx\nimport React from 'react';\n\nclass CounterComponent extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      count: 0\n    };\n  }\n\n  incrementCount = () => {\n    // 使用函数形式的 updater 更新 count\n    this.setState((prevState, props) => ({\n      count: prevState.count + 1 // 基于之前的 state 计算新的 state\n    }));\n  };\n\n  render() {\n    return (\n      <div>\n        <p>Count: {this.state.count}</p>\n        <button onClick={this.incrementCount}>Increment</button>\n      </div>\n    );\n  }\n}\n\nexport default CounterComponent;\n```\n\n**示例：使用 `callback` 函数**\n\n```jsx\nimport React from 'react';\n\nclass MessageComponent extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      message: 'Initial Message'\n    };\n  }\n\n  updateMessage = () => {\n    this.setState(\n      { message: 'Updated Message!' }, // updater 对象\n      () => { // callback 函数\n        console.log('Message state updated:', this.state.message); // 在重新渲染后执行\n      }\n    );\n  };\n\n  render() {\n    return (\n      <div>\n        <p>Message: {this.state.message}</p>\n        <button onClick={this.updateMessage}>Update Message</button>\n      </div>\n    );\n  }\n}\n\nexport default MessageComponent;\n```\n\n**关键点和注意事项：**\n\n*   **不要直接修改 `this.state`：**  **绝对不要** 尝试直接修改 `this.state` 的值，例如 `this.state.count = this.state.count + 1;`。 这样做 **不会触发组件的重新渲染**，并且可能会导致不可预测的行为。必须始终使用 `this.setState()` 来更新状态。\n*   **状态更新可能是异步的：** 为了性能优化，React 可能会将多个 `setState()` 调用 **批量处理** (batch updates)。这意味着状态更新可能是异步的。因此，在 `setState()` 调用之后立即访问 `this.state`，可能仍然会得到 **旧的** 状态值。 如果你的新状态值依赖于之前的状态值，**务必使用函数形式的 `updater`**，以确保你操作的是最新的状态。[[批处理机制 Batching]]\n\t* 函数式更新器的主要目的是：[[函数式更新 Functional Updates]]\n\t  1. 确保你总是基于最新的状态值进行更新，避免闭包陷阱\n\t  2. 提供一种基于前一个状态计算新状态的方式\n*   **浅合并 (Shallow Merge)：**  当使用对象形式的 `updater` 时，`this.setState()` 执行的是浅合并。这意味着，如果你只更新了状态对象的一部分属性，其他属性会保持不变。但是，如果状态对象中包含嵌套对象，浅合并只会合并顶层属性，嵌套对象会被替换而不是合并。在更新嵌套对象时，需要小心处理，通常需要手动合并之前的状态对象。[[浅合并 Shallow Merge]]\n*   **性能优化：`shouldComponentUpdate` 和 `React.PureComponent`：**  类组件提供了 `shouldComponentUpdate` 生命周期方法，允许你手动控制组件是否需要重新渲染，基于 props 和 state 的变化来优化性能。 `React.PureComponent` 是 `React.Component` 的一个变体，它默认实现了浅比较的 `shouldComponentUpdate`，可以自动进行简单的性能优化。\n\n**总结：**\n\n`this.state` 和 `this.setState` 是 React 类组件中管理状态的核心工具。 `this.state` 用于初始化状态，而 `this.setState()` 是更新状态的唯一正确方法，并会触发组件的重新渲染。 理解它们的工作原理，并遵循最佳实践，是编写高效、可维护的 React 类组件的关键。 虽然现在函数组件和 Hooks 更加流行，理解类组件的状态管理仍然有助于理解 React 的基本概念，并且在维护旧代码库时仍然很重要。\n", "aliases": ["this.setState", "类组件状态", "this.state"], "degree": 5, "nameLength": 24}, "position": {"x": 202.25, "y": 375.25}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:类组件 Class Components.md", "name": "类组件 Class Components", "path": "类组件 Class Components.md", "content": "# React 类组件设计的核心特性\n\n## 基于OOP的组件模型\n\n- **类继承模式**：通过继承`React.Component`创建组件\n- **实例特性**：每个组件渲染创建一个实例，保持自己的状态和方法\n- **this上下文**：通过this关键字访问组件属性、状态和方法\n\n## 生命周期方法系统\n\n- **完整生命周期**：提供挂载、更新、卸载等不同阶段的钩子方法\n- **可预测的执行顺序**：生命周期方法按照固定顺序调用\n- **副作用管理**：通过不同生命周期方法控制副作用执行时机\n\n```jsx\ncomponentDidMount() { /* 挂载后执行 */ }\ncomponentDidUpdate(prevProps, prevState) { /* 更新后执行 */ }\ncomponentWillUnmount() { /* 卸载前清理 */ }\n```\n\n## this.setState的设计特性 [[this.state&this.setState]]\n\n- **浅合并(Shallow Merge)**：自动将新状态与现有状态合并\n- **异步更新**：状态更新可能被延迟和批处理\n- **函数式更新**：支持基于先前状态计算新状态\n- **回调函数**：可选的第二个参数在状态更新完成后执行\n\n```jsx\nthis.setState({ count: 1 }); // 对象形式\nthis.setState(prevState => ({ count: prevState.count + 1 })); // 函数形式\nthis.setState({ count: 1 }, () => console.log('Updated')); // 带回调\n```\n\n## 状态与属性分离\n\n- **props**：从父组件接收的不可变数据\n- **state**：组件内部可变状态\n- **清晰的数据流向**：props向下流动，state在组件内封装\n\n## 批处理机制\n\n- **自动批处理**：同一事件处理函数中的多个setState调用合并为一次渲染\n- **性能优化**：减少不必要的渲染和DOM更新\n- **可预测性**：批处理保证组件渲染逻辑的一致性\n\n## 错误处理\n\n- **错误边界**：通过`componentDidCatch`和`static getDerivedStateFromError`处理组件树中的JavaScript错误\n- **优雅降级**：允许组件在子组件出错时显示备用UI\n\n```jsx\ncomponentDidCatch(error, info) {\n  this.setState({ hasError: true });\n  logErrorToService(error, info);\n}\n```\n\n## 上下文和引用\n\n- **contextType**：通过静态属性访问Context值\n- **ref系统**：通过createRef或回调ref获取DOM或组件实例引用\n- **forwardRef**：允许组件向子组件传递ref\n\n## 性能优化机制\n\n- **shouldComponentUpdate**：允许开发者控制组件是否重新渲染\n- **PureComponent**：自动实现props和state的浅比较优化\n- **不可变数据模式**：鼓励使用不可变数据结构进行状态更新\n\n```jsx\nshouldComponentUpdate(nextProps, nextState) {\n  return nextProps.value !== this.props.value;\n}\n```\n\n## 组合模式支持\n\n- **高阶组件(HOC)**：包装组件添加功能的函数\n- **Render Props**：通过props传递渲染逻辑的模式\n- **复用策略**：提供多种方式重用组件逻辑\n\n类组件设计提供了强大而灵活的API，建立了React组件开发的基础模型，虽然现在函数组件和Hooks成为主流，但类组件设计的核心特性仍然影响着React的整体设计哲学和发展方向。", "degree": 2, "nameLength": 20}, "position": {"x": 228, "y": 342.04999999999995}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:闭包陷阱.md", "name": "闭包陷阱", "path": "闭包陷阱.md", "content": "## 什么是闭包陷阱\n\n闭包陷阱是 React 函数组件中常见的一类问题，指的是组件内部的函数（如事件处理器、回调、定时器等）捕获了它被创建时的 props 和 state 值，而非执行时的最新值，导致预期外的行为。\n\n**核心问题:**\n\n- 函数组件每次渲染都会创建新的函数实例\n- 这些函数通过闭包捕获渲染时的变量值\n- 异步或延迟执行时仍使用捕获时的旧值，而非最新状态\n\n## 常见闭包陷阱场景\n\n**1. 定时器中的状态**\n\n```jsx\nfunction Counter() {\n  const [count, setCount] = useState(0);\n\n  function delayedCount() {\n    setTimeout(() => {\n      setCount(count + 1); // 捕获创建setTimeout时的count值\n      console.log(`Current count: ${count}`); // 总是比实际值小1\n    }, 1000);\n  }\n\n  return (\n    <>\n      <div>Count: {count}</div>\n      <button onClick={() => setCount(count + 1)}>Increment</button>\n      <button onClick={delayedCount}>Delayed Increment</button>\n    </>\n  );\n}\n```\n\n**2. 事件监听器中的状态**\n\n```jsx\nfunction WindowSizeTracker() {\n  const [width, setWidth] = useState(window.innerWidth);\n\n  useEffect(() => {\n    const handleResize = () => {\n      console.log(\"Previous width:\", width); // 总是显示首次渲染时的宽度\n      setWidth(window.innerWidth);\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []); // 空依赖数组导致handleResize捕获初始width值\n\n  return <div>Window width: {width}</div>;\n}\n```\n\n**3. 异步请求回调**\n\n```jsx\nfunction UserProfile({ userId }) {\n  const [user, setUser] = useState(null);\n\n  const fetchUserData = () => {\n    // 这里捕获了当前的userId\n    fetchUser(userId).then((data) => {\n      // 如果组件重新渲染并传入新userId，这里仍使用旧值\n      console.log(`Fetched data for user ${userId}`);\n      setUser(data);\n    });\n  };\n\n  return <button onClick={fetchUserData}>Load User</button>;\n}\n```\n\n## 为什么会发生闭包陷阱\n\n闭包陷阱发生的根本原因是 JavaScript 闭包的工作方式与 React 的渲染模型结合导致的：\n\n1. **函数组件的执行特性**：每次渲染都会执行整个函数体\n2. **闭包特性**：内部函数访问创建它时所在环境的变量\n3. **异步执行时机**：当回调函数最终执行时，组件可能已经重新渲染多次\n4. **变量捕获机制**：函数捕获的是变量引用，但捕获的是当时的值\n\n## 解决闭包陷阱的方法\n\n**1. 使用函数式更新** [[函数式更新 Functional Updates]]\n\n```jsx\nfunction Counter() {\n  const [count, setCount] = useState(0);\n\n  function delayedCount() {\n    setTimeout(() => {\n      // 使用函数式更新，不依赖闭包中的count值\n      setCount((prevCount) => prevCount + 1);\n    }, 1000);\n  }\n\n  return <button onClick={delayedCount}>Delayed Increment</button>;\n}\n```\n\n**2. 使用 useRef 保存可变引用**\n\n```jsx\nfunction LatestValueExample() {\n  const [count, setCount] = useState(0);\n  const countRef = useRef(count);\n\n  // 更新ref值以跟踪最新state\n  useEffect(() => {\n    countRef.current = count;\n  }, [count]);\n\n  function handleClick() {\n    setTimeout(() => {\n      // 使用ref读取最新值\n      console.log(`Latest count: ${countRef.current}`);\n    }, 1000);\n  }\n\n  return (\n    <>\n      <div>Count: {count}</div>\n      <button onClick={() => setCount(count + 1)}>Increment</button>\n      <button onClick={handleClick}>Log Latest</button>\n    </>\n  );\n}\n```\n\n**3. 正确使用 useEffect 依赖**\n\n```jsx\nfunction WindowSizeTracker() {\n  const [width, setWidth] = useState(window.innerWidth);\n\n  useEffect(() => {\n    const handleResize = () => {\n      console.log(\"Previous width:\", width); // 正确反映最新width\n      setWidth(window.innerWidth);\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, [width]); // 添加width为依赖，确保handleResize始终使用最新值\n\n  return <div>Window width: {width}</div>;\n}\n```\n\n**4. 使用 useCallback 结合依赖数组**\n\n```jsx\nfunction UserProfile({ userId }) {\n  const [user, setUser] = useState(null);\n\n  // 每当userId变化时创建新函数\n  const fetchUserData = useCallback(() => {\n    fetchUser(userId).then((data) => {\n      console.log(`Fetched data for user ${userId}`);\n      setUser(data);\n    });\n  }, [userId]);\n\n  return <button onClick={fetchUserData}>Load User</button>;\n}\n```\n\n## 最佳实践\n\n1. **使用函数式更新**：当新状态依赖旧状态时，始终使用`setState(prev => ...)`形式\n\n2. **谨慎使用依赖数组**：useEffect, useCallback, useMemo 的依赖数组应包含所有使用的变量\n\n3. **使用 [[eslint-plugin-react-hooks]]**：自动检测依赖数组问题\n\n4. **设计状态结构**：精心设计状态结构可以减少闭包问题\n\n5. **了解 useRef 的作用**：useRef 可以在需要的情况下绕过闭包限制\n\n6. **使用新的 React 18 API**：如 useSyncExternalStore 等新 API 有助于解决一些闭包相关问题\n\n理解闭包陷阱对编写高质量的 React 函数组件至关重要，可以帮助避免难以调试的状态异常问题。\n", "degree": 2, "nameLength": 4}, "position": {"x": 161.625, "y": 509.9}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:批处理机制 Batching.md", "name": "批处理机制 Batching", "path": "批处理机制 Batching.md", "content": "## 目录\n[[#什么是 setState 批处理]]\n[[#React 17 中的批处理行为]]\n[[#React 18 中的自动批处理]]\n[[#手动控制批处理]]\n[[#批处理的好处]]\n[[#常见陷阱和问题]]\n[[#最佳实践]]\n[[#实际案例分析]]\n\n## 什么是 setState 批处理\n\nsetState 批处理是 React 的一种优化机制，它将多个状态更新操作合并到一次渲染中，而不是为每个状态更新都执行一次渲染。当连续调用多个 setState 时，React 会将它们缓存起来，然后一次性地进行处理和更新。\n\n**批处理工作原理:**\n\n- 当多个 setState 调用发生时，React 不会立即应用更新\n- 相反，它会将这些更新排队并合并\n- 最终在一个单一的更新周期中应用所有更改\n- 只触发一次组件重新渲染\n\n这种机制显著提高了应用性能，避免了不必要的重复渲染。\n\n## React 17 中的批处理行为 #React17\n\n在 React 17 及更早版本中，批处理只在 React 事件处理函数内部自动发生。\n[[React 17 Batching]]\n\n**React 17 中批处理的特点:**\n\n- React 事件处理函数中的多个 setState 会被自动批处理 handleClick\n- 在 Promise、setTimeout、原生事件处理函数或任何其他异步代码中的 setState 不会批处理\n- 这导致在异步代码中可能发生多次不必要的渲染\n\n**示例代码:**\n\n```jsx\nimport { unstable_batchedUpdates } from 'react-dom';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  const [flag, setFlag] = useState(false);\n\n  function handleClick() {\n    // 在 React 17 中，这里会批处理（只有一次渲染） 正宗批处理 \n    setCount(c => c + 1);\n    setFlag(f => !f);\n  }\n  \n  //手动批处理（一次渲染）:\n  setTimeout(() => {\n  unstable_batchedUpdates(() => {\n    setCount(c => c + 1);\n    setFlag(f => !f);\n  });\n}, 0)\n\n  function handleClickAsync() {\n    // 在 React 17 中，这里不会批处理（触发两次渲染）\n    setTimeout(() => {\n      setCount(c => c + 1);\n      setFlag(f => !f);\n    }, 0);\n  }\n\n  return (\n    <div>\n      <button onClick={handleClick}>Update</button>\n      <button onClick={handleClickAsync}>Update Async</button>\n    </div>\n  );\n}\n```\n\n## React 18 中的自动批处理 #React18\n\nReact 18 引入了\"自动批处理\"功能，大幅扩展了批处理的范围。\n[[React 18 AutoBatching]]\n\n**React 18 批处理的改进:**\n\n- 所有的 setState 调用都会自动批处理，不管它们在哪里触发\n- 包括 Promise、setTimeout、原生事件处理函数内部的更新\n- 同一事件循环中的所有更新都会被批处理\n\n**React 18 批处理示例:**\n\n```jsx\nfunction App() {\n  const [count, setCount] = useState(0);\n  const [flag, setFlag] = useState(false);\n\n  function handleClick() {\n    // React 18: 批处理（一次渲染）\n    setCount(c => c + 1);\n    setFlag(f => !f);\n  }\n\n  function handleClickAsync() {\n    // React 17: 不批处理（两次渲染）\n    // React 18: 批处理（一次渲染）\n    setTimeout(() => {\n      setCount(c => c + 1);\n      setFlag(f => !f);\n    }, 0);\n\n    // 批处理在 Promise 中也有效\n    fetch('/api').then(() => {\n      setCount(c => c + 1);\n      setFlag(f => !f);\n    });\n  }\n\n  return (\n    <div>\n      <button onClick={handleClick}>Update</button>\n      <button onClick={handleClickAsync}>Update Async</button>\n    </div>\n  );\n}\n```\n\n## 手动控制批处理\n\n尽管自动批处理通常是有益的，但有时候你可能需要强制立即更新并跳过批处理。\n\n**使用 flushSync:**\n\n```jsx\nimport { flushSync } from 'react-dom';\n\nfunction handleClick() {\n  // 第一次渲染\n  flushSync(() => {\n    setCounter(c => c + 1);\n  });\n  \n  // 第二次渲染\n  flushSync(() => {\n    setFlag(f => !f);\n  });\n  \n  // 这里会被批处理（一次渲染）\n  setValue(v => v + 1);\n  setAnotherValue(v => v - 1);\n}\n```\n\n`flushSync` 会强制同步渲染，确保在它的回调函数内的所有状态更新都被立即应用。这通常用于需要 DOM 测量或其他需要立即反映状态变化的情况。\n\n## 批处理的好处\n\n**批处理带来的性能优势:**\n\n- **减少渲染次数:** 多个状态更新只触发一次渲染循环\n- **提高性能:** 减少了计算开销和 DOM 操作\n- **避免中间状态:** 用户不会看到处于\"半更新\"状态的 UI\n- **减少闪烁:** 避免因多次渲染导致的界面闪烁\n\n**数据一致性:** 批处理确保关联的状态更新同时应用，保持数据的一致性。\n\n## 常见陷阱和问题\n\n**setState 的异步本质:**\n\n- setState 调用后不会立即更新状态\n- 访问刚刚\"设置\"的状态值可能仍然是旧值\n\n```jsx\nfunction handleClick() {\n  setCount(count + 1);\n  console.log(count); // 输出的仍然是旧值，而非更新后的值\n}\n```\n\n**多次更新同一状态:**\n\n- 在同一批次中多次设置同一状态，只有最后一次会生效\n\n```jsx\nfunction handleClick() {\n  // 只有最后一个 setCount(3) 会生效\n  setCount(1);\n  setCount(2);\n  setCount(3);\n}\n```\n\n**使用函数式更新解决批处理问题:**\n\n```jsx\nfunction handleClick() {\n  // 这样会正确递增三次\n  setCount(c => c + 1);\n  setCount(c => c + 1);\n  setCount(c => c + 1);\n}\n```\n\n## 最佳实践\n\n**优化 setState 使用:**\n\n- 尽可能在一个事件处理函数中合并多个状态更新\n- 对相关状态更新使用单一的 setState 调用\n- 当前一个状态更新依赖前一个时，使用函数式更新\n\n**使用 useReducer 管理复杂状态:**\n\n```jsx\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'increment_and_toggle':\n      return {\n        ...state,\n        count: state.count + 1,\n        flag: !state.flag\n      };\n    // 其他 case...\n    default:\n      return state;\n  }\n}\n\nfunction Counter() {\n  const [state, dispatch] = useReducer(reducer, { count: 0, flag: false });\n  \n  function handleClick() {\n    // 一次性更新多个状态\n    dispatch({ type: 'increment_and_toggle' });\n  }\n  \n  return (\n    <button onClick={handleClick}>\n      Count: {state.count}, Flag: {state.flag.toString()}\n    </button>\n  );\n}\n```\n\n## 实际案例分析\n\n**表单提交场景:**\n\n```jsx\nfunction Form() {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [formData, setFormData] = useState({});\n  const [errors, setErrors] = useState({});\n  \n  async function handleSubmit(e) {\n    e.preventDefault();\n    \n    // React 18 中这三个状态更新会被批处理为一次渲染\n    setIsSubmitting(true);\n    setErrors({});\n    setFormData({ ...formData, lastSubmitted: new Date() });\n    \n    try {\n      await submitForm(formData);\n      // 这两个更新也会被批处理\n      setFormData({});\n      setIsSubmitting(false);\n    } catch (err) {\n      // 这两个更新同样被批处理\n      setErrors(getErrorsFromException(err));\n      setIsSubmitting(false);\n    }\n  }\n  \n  return (\n    <form onSubmit={handleSubmit}>\n      {/* 表单内容 */}\n    </form>\n  );\n}\n```\n\n**使用 useEffect 时的批处理注意事项:**\n\n```jsx\nfunction DataLoader() {\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  \n  useEffect(() => {\n    let isMounted = true;\n    \n    async function fetchData() {\n      try {\n        const result = await fetchSomeData();\n        \n        if (isMounted) {\n          // React 18 中这两个更新会被批处理\n          setData(result);\n          setLoading(false);\n        }\n      } catch (err) {\n        if (isMounted) {\n          // 这两个更新也会被批处理\n          setError(err);\n          setLoading(false);\n        }\n      }\n    }\n    \n    fetchData();\n    return () => { isMounted = false; };\n  }, []);\n  \n  if (loading) return <div>Loading...</div>;\n  if (error) return <div>Error: {error.message}</div>;\n  return <div>{JSON.stringify(data)}</div>;\n}\n```\n\n通过理解和利用 React 的批处理机制，我们可以编写更高效、更可预测的 React 应用程序，减少不必要的渲染，提高应用性能。", "degree": 3, "nameLength": 14}, "position": {"x": 246.625, "y": 408.45000000000005}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note tag-React17 tag-React18 protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:React 18 AutoBatching.md", "name": "React 18 AutoBatching", "path": "React 18 AutoBatching.md", "content": "好的，这是一份关于 React 18 AutoBatching 的详细笔记，包含您要求的各个部分，以中文 Markdown 格式呈现：\n\n# React 18 AutoBatching 讲座笔记\n\n## 核心概念总结 (可能出现在测验中)\n\n- **什么是 Batching (批处理)?**:\n\n  - 将多个状态更新合并成一个单一的重新渲染过程。\n  - 目的是减少不必要的渲染次数，提升性能。\n  - 可以想象成“攒一批活儿一起干”，而不是“来一个活儿干一个”。\n\n- **React 18 之前的 Batching 行为**:\n\n  - **局限性**: 仅在 React 事件处理函数内部进行批处理。\n  - **非批处理场景**: 在 `Promise` 的 `resolve` 回调、`setTimeout`、原生事件处理函数等外部环境中，状态更新不会被批处理。\n  - **例子**:\n    ```jsx\n    // React 18 之前：不会批处理\n    function handleClick() {\n      setTimeout(() => {\n        setCount((c) => c + 1); // 第一次渲染\n        setFlag(!flag); // 第二次渲染 (即使在 setTimeout 中)\n      }, 1000);\n    }\n    ```\n    这个例子在 React 18 之前会导致两次独立的渲染。\n\n- **React 18 AutoBatching 的改进**:\n\n  - **扩展范围**: React 18 自动批处理扩展到了**所有**可能发生状态更新的场景。\n  - **包括**: `Promise` 的 `resolve` 回调, `setTimeout`, 原生事件处理函数, 以及任何在**同一个事件循环**中发生的更新。[[Promise]]\n  - **重要意义**: 显著减少了不必要的渲染，提升了应用性能，尤其是在复杂的组件更新场景下。\n  - **例子**:\n    ```jsx\n    // React 18：会自动批处理\n    function handleClick() {\n      setTimeout(() => {\n        setCount((c) => c + 1); // 状态更新 1\n        setFlag(!flag); // 状态更新 2\n      }, 1000);\n    }\n    ```\n    在 React 18 中，这个例子只会触发**一次**渲染，因为 `setCount` 和 `setFlag` 这两个状态更新会被自动批处理。\n\n- **AutoBatching 的好处**:\n\n  - **性能提升**: 减少组件重新渲染的次数，尤其是在复杂的应用中性能提升明显。\n  - **更流畅的用户体验**: 减少卡顿，UI 响应更及时。\n  - **减少不必要的渲染工作**: 避免组件在短时间内多次渲染造成的资源浪费。\n\n- **`flushSync` (选择退出 AutoBatching)**:\n\n  - **作用**: 强制 React **同步**执行状态更新，并立即渲染。\n  - **使用场景**: 在极少数情况下，你可能需要立即看到状态更新的效果，而不是等待批处理，例如：\n    - 与外部系统同步状态，需要立即获取更新后的 DOM 信息。\n    - 某些动画或交互需要立即反馈。\n  - **谨慎使用**: 过度使用 `flushSync` 会降低性能，抵消 AutoBatching 的优势。\n  - **例子**:\n\n    ```jsx\n    import { flushSync } from \"react-dom\";\n\n    function handleClick() {\n      flushSync(() => {\n        setCount((c) => c + 1); // 强制同步更新并渲染\n      });\n      setFlag(!flag); // 仍然会批处理，但会在 flushSync 之后\n    }\n    ```\n\n    在这个例子中，`setCount` 会立即同步更新并渲染，而 `setFlag` 的更新仍然会按照 AutoBatching 的规则进行批处理。\n\n## 数字和数据总结\n\n- **React 版本**: `React 18` 引入了 AutoBatching。\n- **批处理的目标**: 减少渲染次数，提升性能 (性能提升的具体百分比取决于应用场景，但核心目标是减少不必要的渲染)。\n- **时间维度**: AutoBatching 关注的是**同一个事件循环**中的状态更新。\n- **`setTimeout` 例子中的延迟时间**: `1000` 毫秒 (例子中使用，用于演示异步更新场景)。\n\n## 例子总结\n\n- **React 18 之前 `setTimeout` 中的状态更新**: 演示了 React 18 之前在 `setTimeout` 中多次 `setState` 会导致多次渲染的情况。\n\n  ```jsx\n  // React 18 之前：不会批处理\n  function handleClick() {\n    setTimeout(() => {\n      setCount((c) => c + 1); // 第一次渲染\n      setFlag(!flag); // 第二次渲染\n    }, 1000);\n  }\n  ```\n\n- **React 18 `setTimeout` 中的状态更新**: 演示了 React 18 AutoBatching 如何将 `setTimeout` 中的多次 `setState` 合并为一次渲染。\n\n  ```jsx\n  // React 18：会自动批处理\n  function handleClick() {\n    setTimeout(() => {\n      setCount((c) => c + 1); // 状态更新 1\n      setFlag(!flag); // 状态更新 2\n    }, 1000);\n  }\n  ```\n\n- **`flushSync` 的使用**: 演示了如何使用 `flushSync` 强制同步更新和渲染，以及它对批处理的影响。\n\n  ```jsx\n  import { flushSync } from \"react-dom\";\n\n  function handleClick() {\n    flushSync(() => {\n      setCount((c) => c + 1); // 强制同步更新并渲染\n    });\n    setFlag(!flag); // 仍然会批处理，但会在 flushSync 之后\n  }\n  ```\n\n希望这份笔记对您有所帮助！ 如果您有任何其他问题，请随时提出。\n", "degree": 2, "nameLength": 21}, "position": {"x": 420.875, "y": 441.20000000000005}, "group": "nodes", "removed": false, "selected": true, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:Promise.md", "name": "Promise", "path": "Promise.md", "content": "---\naliases:\n  - 期约\n---\n好的，我们从基础、深入和进阶三个方面来讲解 JavaScript Promise。\n\n## JS Promise 详解\n\nPromise 是 JavaScript 中处理异步操作的重要机制，它提供了一种更优雅、更易于管理的方式来处理异步代码，避免了传统回调函数可能导致的回调地狱问题。\n\n[[Promise 的基本概念和使用]]\n### ### 二、深入篇：Promise 的进阶使用和原理\n\n#### 1. Promise 的链式调用和值传递\n\n* **`.then()` 的返回值:**  `.then()` 方法的回调函数可以返回三种类型的值：\n    * **普通值 (非 Promise):**  返回的普通值会被包装成一个**fulfilled**状态的 Promise，并将该值作为新 Promise 的成功值传递给下一个 `.then()`。\n    * **Promise 对象:**  如果返回的是一个 Promise 对象，那么 `.then()` 方法返回的新 Promise 的状态和结果将**与返回的 Promise 对象保持一致**。这实现了 Promise 链的传递。\n    * **不返回值 (或返回 `undefined`):**  相当于返回了 `Promise.resolve(undefined)`，即一个 fulfilled 状态的 Promise，成功值为 `undefined`。\n\n* **错误穿透:**  如果在 Promise 链中，前面的 `.then()` 没有提供 `onRejected` 处理函数，并且发生了错误（Promise 变为 Rejected 或者抛出异常），错误会一直向下传递，直到遇到 `.catch()` 方法或者链的末尾。\n\n**示例：链式调用和值传递**\n\n```javascript\nfunction asyncOperation(value) {\n  return new Promise((resolve) => {\n    setTimeout(() => {\n      resolve(`操作完成，输入值为: ${value}`);\n    }, 1000);\n  });\n}\n\nasyncOperation(\"开始\")\n  .then((result1) => {\n    console.log(\"第一个 then:\", result1); // 输出: 第一个 then: 操作完成，输入值为: 开始\n    return asyncOperation(result1); // 返回一个新的 Promise\n  })\n  .then((result2) => {\n    console.log(\"第二个 then:\", result2); // 输出: 第二个 then: 操作完成，输入值为: 操作完成，输入值为: 开始\n    return \"最终结果\"; // 返回普通值，会被包装成 Promise.resolve(\"最终结果\")\n  })\n  .then((result3) => {\n    console.log(\"第三个 then:\", result3); // 输出: 第三个 then: 最终结果\n  })\n  .catch((error) => {\n    console.error(\"发生错误:\", error);\n  });\n```\n\n#### 2. Promise 的静态方法\n\nPromise 对象本身还提供了一些静态方法，用于更方便地处理 Promise 集合：\n\n* **`Promise.resolve(value)`:**  创建一个**立即 fulfilled** 状态的 Promise，并将 `value` 作为成功值。如果 `value` 本身就是一个 Promise，则直接返回该 Promise。\n\n* **`Promise.reject(reason)`:**  创建一个**立即 rejected** 状态的 Promise，并将 `reason` 作为失败原因。\n\n* **`Promise.all(promises)`:**  接收一个 Promise 数组 (或其他可迭代对象)，返回一个新的 Promise。\n    * 当数组中**所有 Promise 都 fulfilled** 时，返回的 Promise 变为 fulfilled，其成功值是一个包含所有 Promise 成功值的数组，**顺序与传入的 Promise 数组一致**。\n    * 只要数组中**有一个 Promise rejected**，返回的 Promise 立即变为 rejected，其失败原因就是**第一个被 rejected 的 Promise 的失败原因**。\n\n* **`Promise.race(promises)`:**  接收一个 Promise 数组 (或其他可迭代对象)，返回一个新的 Promise。\n    * 返回的 Promise 的状态和结果与数组中**第一个 settled (fulfilled 或 rejected) 的 Promise 一致**。 也就是说，哪个 Promise 最先完成（无论成功或失败），就以它的结果为准。\n\n* **`Promise.allSettled(promises)`:**  接收一个 Promise 数组 (或其他可迭代对象)，返回一个新的 Promise。\n    * 当数组中**所有 Promise 都 settled (无论 fulfilled 或 rejected)** 时，返回的 Promise 变为 fulfilled。\n    * 其成功值是一个数组，包含每个 Promise 的结果信息，每个结果信息都是一个对象，结构如下：\n        * 对于 fulfilled 的 Promise: `{ status: 'fulfilled', value: 成功值 }`\n        * 对于 rejected 的 Promise: `{ status: 'rejected', reason: 失败原因 }`\n    * `Promise.allSettled()` 会等待所有 Promise 完成，无论它们是否成功，常用于需要知道所有 Promise 结果的场景。\n\n**示例：Promise 静态方法**\n\n```javascript\nconst promise1 = Promise.resolve(1);\nconst promise2 = Promise.resolve(2);\nconst promise3 = Promise.reject(\"Error 3\");\n\nPromise.all([promise1, promise2, promise3])\n  .then((results) => {\n    console.log(\"Promise.all 成功:\", results); // 不会执行，因为 promise3 rejected\n  })\n  .catch((error) => {\n    console.error(\"Promise.all 失败:\", error); // 输出: Promise.all 失败: Error 3\n  });\n\nPromise.race([promise1, promise3, new Promise(resolve => setTimeout(resolve, 3000, 'Promise 4'))])\n  .then((result) => {\n    console.log(\"Promise.race 成功:\", result); // 输出: Promise.race 成功: 1 (promise1 最先 fulfilled)\n  })\n  .catch((error) => {\n    console.error(\"Promise.race 失败:\", error);\n  });\n\nPromise.allSettled([promise1, promise2, promise3])\n  .then((results) => {\n    console.log(\"Promise.allSettled 结果:\", results);\n    // 输出:\n    // Promise.allSettled 结果: [\n    //   { status: 'fulfilled', value: 1 },\n    //   { status: 'fulfilled', value: 2 },\n    //   { status: 'rejected', reason: 'Error 3' }\n    // ]\n  });\n```\n\n#### 3. Promise 的微任务队列\n\n* **微任务 (Microtask) 队列:**  Promise 的回调函数 (`.then()`, `.catch()`, `.finally()`) 会被放入微任务队列中。\n* **事件循环 (Event Loop):**  JavaScript 的事件循环机制会优先处理微任务队列中的任务，然后在处理宏任务队列（例如：setTimeout, setInterval, DOM 事件等）。\n* **执行顺序:**  这意味着 Promise 的回调函数会在当前同步代码执行完毕后，但在下一个宏任务开始之前执行。\n\n**理解微任务队列有助于理解 Promise 回调的执行时机。**\n\n**示例：微任务队列**\n\n```javascript\nconsole.log(\"同步代码开始\");\n\nPromise.resolve().then(() => {\n  console.log(\"Promise 微任务\");\n});\n\nsetTimeout(() => {\n  console.log(\"setTimeout 宏任务\");\n}, 0);\n\nconsole.log(\"同步代码结束\");\n\n// 执行顺序：\n// 1. 同步代码开始\n// 2. 同步代码结束\n// 3. Promise 微任务  (微任务队列优先于宏任务队列)\n// 4. setTimeout 宏任务\n```\n\n**深入总结:**\n\n* `.then()` 的返回值决定了 Promise 链的传递方式和值传递。\n* 理解 Promise 的静态方法 `Promise.resolve`, `Promise.reject`, `Promise.all`, `Promise.race`, `Promise.allSettled`，可以更高效地处理 Promise 集合。\n* Promise 回调函数会被放入微任务队列，并在事件循环中优先执行。\n\n---\n\n### 三、进阶篇：Promise 的高级应用和最佳实践\n\n#### 1. Promise 化 (Promisify) 传统回调函数 API\n\n* **场景:**  许多旧的 JavaScript API (例如 Node.js 的 `fs` 模块) 使用传统的回调函数方式处理异步操作。为了更好地与 Promise 配合使用，可以将这些 API Promise 化。\n\n* **Promisify 的实现:**  创建一个函数，该函数接收传统回调函数 API 的参数，并返回一个新的 Promise。在 Promise 的执行器函数中，调用原始的回调函数 API，并在回调函数中根据操作结果调用 `resolve` 或 `reject`。\n\n**示例：Promisify `setTimeout`**\n\n```javascript\nfunction delay(ms) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, ms); // setTimeout 本身的回调函数只负责 resolve\n  });\n}\n\ndelay(2000)\n  .then(() => {\n    console.log(\"延迟 2 秒后执行\");\n  });\n```\n\n**示例：Promisify Node.js `fs.readFile`**\n\n```javascript\nconst fs = require('fs');\n\nfunction readFilePromise(filePath, options) {\n  return new Promise((resolve, reject) => {\n    fs.readFile(filePath, options, (err, data) => {\n      if (err) {\n        reject(err); // 原始回调函数出错时 reject\n      } else {\n        resolve(data); // 原始回调函数成功时 resolve\n      }\n    });\n  });\n}\n\nreadFilePromise('myFile.txt', 'utf8')\n  .then((data) => {\n    console.log(\"文件内容:\", data);\n  })\n  .catch((err) => {\n    console.error(\"读取文件失败:\", err);\n  });\n```\n\n* **库支持:**  许多 Promise 库 (例如 Bluebird, util.promisify (Node.js 内置)) 提供了便捷的 Promisify 工具函数，可以简化 Promise 化的过程。\n\n#### 2. 使用 Async/Await 语法糖\n\n* **Async/Await 的本质:**  Async/Await 是建立在 Promise 之上的语法糖，它使异步代码看起来更像同步代码，大大提高了代码的可读性和可维护性。\n\n* **`async` 函数:**  在函数声明前加上 `async` 关键字，该函数就变成了 async 函数。\n    * async 函数**默认返回一个 Promise 对象**。\n    * 如果 async 函数显式 `return` 一个值，该值会被 `Promise.resolve()` 包装成 Promise 的成功值。\n    * 如果 async 函数抛出错误，该错误会被 `Promise.reject()` 包装成 Promise 的失败原因。\n\n* **`await` 表达式:**  `await` 关键字只能在 `async` 函数内部使用。\n    * `await` 后面通常跟一个 Promise 对象。\n    * `await` 会**暂停 async 函数的执行**，直到它后面的 Promise 对象 settled (fulfilled 或 rejected)。\n    * 如果 Promise fulfilled，`await` 表达式会返回 Promise 的成功值。\n    * 如果 Promise rejected，`await` 表达式会**抛出 Promise 的失败原因**，需要使用 `try...catch` 语句捕获错误。\n\n**示例：Async/Await 使用**\n\n```javascript\nasync function fetchData() {\n  try {\n    console.log(\"开始获取数据...\");\n    const response = await fetch('https://jsonplaceholder.typicode.com/todos/1'); // await Promise\n    const data = await response.json(); // await Promise\n    console.log(\"数据获取成功:\", data);\n    return data; // 返回值会被 Promise.resolve() 包装\n  } catch (error) {\n    console.error(\"数据获取失败:\", error);\n    throw error; // 抛出错误会被 Promise.reject() 包装\n  }\n}\n\nfetchData()\n  .then(result => {\n    console.log(\"fetchData 返回的结果:\", result);\n  })\n  .catch(error => {\n    console.error(\"fetchData 最终错误处理:\", error);\n  });\n```\n\n**Async/Await 的优势:**\n\n* **更同步化的代码结构:**  使异步代码看起来更像同步代码，更容易理解和编写。\n* **更清晰的错误处理:**  可以使用 `try...catch` 语句来处理异步操作中的错误，与同步代码的错误处理方式一致。\n* **更方便的调试:**  异步代码的调试更方便，可以像调试同步代码一样设置断点。\n\n#### 3. Promise 的错误处理最佳实践\n\n* **始终使用 `.catch()` 处理 Promise 链中的错误:**  确保 Promise 链的末尾或者适当的位置添加 `.catch()` 方法，捕获任何可能发生的未处理的 rejection。\n\n* **避免在 `.then()` 中同时写 `onFulfilled` 和 `onRejected`:**  推荐使用 `.catch()` 专门处理 rejected 情况，使代码更清晰。\n\n* **使用 `try...catch` 语句处理 async/await 中的错误:**  在 `async` 函数中使用 `try...catch` 语句来捕获 `await` 表达式可能抛出的错误。\n\n* **处理 Unhandled Promise Rejection:**  监听 `unhandledrejection` 全局事件，可以捕获全局范围内未被处理的 Promise rejection，用于监控和日志记录。 (浏览器环境和 Node.js 环境都支持)\n\n```javascript\nwindow.addEventListener('unhandledrejection', (event) => {\n  console.error('Unhandled Promise Rejection:', event.reason);\n  // 可以进行错误日志记录等操作\n  event.preventDefault(); // 阻止默认的错误处理行为 (例如浏览器控制台输出错误)\n});\n\nPromise.reject(\"未处理的 rejection\"); // 没有 catch 方法处理\n```\n\n#### 4. Promise 的性能考量 (简要了解)\n\n* **Promise 的开销:**  Promise 对象本身有一定的创建和管理开销，但通常来说，在大多数应用场景下，这种开销可以忽略不计。\n\n* **过度使用 Promise 的潜在问题:**  在某些极端场景下，例如需要创建大量 Promise 对象，或者 Promise 链非常长时，可能会对性能产生一定的影响。但这通常不是性能瓶颈。\n\n* **避免不必要的 Promise 包裹:**  如果你的函数已经是同步操作，不需要异步处理，就不要用 `Promise.resolve()` 或 `new Promise()` 包裹，直接返回值即可。\n\n**进阶总结:**\n\n* 使用 Promisify 将传统回调函数 API 转换为 Promise 风格。\n* 掌握 Async/Await 语法糖，简化异步代码编写。\n* 遵循 Promise 错误处理的最佳实践，确保程序的健壮性。\n* 了解 Promise 的性能考量，避免过度使用。\n\n---\n\n**总结：**\n\nPromise 是 JavaScript 中处理异步操作的强大工具。从基础的概念、创建、使用，到深入的链式调用、静态方法、微任务队列，再到高级的 Promisify、Async/Await 和错误处理最佳实践，我们逐步深入地了解了 Promise 的各个方面。\n\n掌握 Promise 对于编写现代 JavaScript 异步代码至关重要，它可以帮助我们写出更清晰、更易于维护、更健壮的异步程序。  希望这个全面的讲解能够帮助你更好地理解和运用 Promise!", "aliases": ["期约"], "degree": 8, "nameLength": 7}, "position": {"x": 300.9900453521861, "y": 477.49720776286784}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note expanded protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:回调地狱 callback hell.md", "name": "回调地狱 callback hell", "path": "回调地狱 callback hell.md", "content": "---\naliases:\n  - 回调地狱\n  - callback hell\n---\n\n## 核心概念总结 (可能出现在测验中)\n\n- **什么是回调地狱 (Callback Hell)?**:\n\n  - 也称为“厄运金字塔” (Pyramid of Doom)。\n  - 指的是在 JavaScript 中处理**多个嵌套异步操作**时，由于过度使用回调函数而导致代码结构**极其混乱、难以阅读和维护**的现象。\n  - 代码通常会向右不断缩进，形成一个金字塔形状，严重影响代码的可读性和可维护性。\n  - **核心问题**: 异步操作的顺序依赖关系，以及传统回调函数处理异步的模式。\n\n- **为什么会产生回调地狱?**:\n\n  - **JavaScript 的异步特性**: JavaScript 是单线程的，为了避免阻塞主线程，很多操作 (如网络请求、定时器、文件读写等) 都是异步的。\n  - **传统的回调函数模式**: 在 Promise 出现之前，处理异步操作的主要方式是使用回调函数。当一个异步操作完成时，会调用传入的回调函数来处理结果或进行下一步操作。\n  - **嵌套依赖**: 当多个异步操作之间存在依赖关系 (例如，一个操作的完成依赖于前一个操作的结果) 时，就需要将回调函数层层嵌套，导致回调地狱。\n\n- **回调地狱的问题**:\n\n  - **可读性差**: 代码缩进过深，逻辑流程分散在各个回调函数中，难以快速理解代码的执行顺序和整体逻辑。\n  - **维护性差**: 修改和调试代码非常困难，一旦出现错误，很难定位问题所在。\n  - **错误处理复杂**: 错误处理逻辑分散在各个回调函数中，难以集中管理和处理，容易遗漏错误情况。\n  - **代码耦合度高**: 各个异步操作的回调函数紧密耦合，难以复用和模块化。\n  - **心智负担重**: 开发者需要追踪复杂的异步流程和回调嵌套关系，增加了认知负担。\n\n- **如何避免回调地狱? (重要考点)**:\n\n  - **命名函数 (Named Functions)**:\n\n    - 将回调函数提取为独立的命名函数，减少匿名函数的嵌套，提高代码可读性。\n    - **例子**:\n\n      ```javascript\n      // 回调地狱 (匿名函数)\n      asyncOperation1(function (result1) {\n        asyncOperation2(result1, function (result2) {\n          asyncOperation3(result2, function (result3) {\n            // ... 更多嵌套\n          });\n        });\n      });\n\n      // 使用命名函数\n      function handleResult1(result1) {\n        asyncOperation2(result1, handleResult2);\n      }\n\n      function handleResult2(result2) {\n        asyncOperation3(result2, handleResult3);\n      }\n\n      function handleResult3(result3) {\n        // ... 处理最终结果\n      }\n\n      asyncOperation1(handleResult1);\n      ```\n\n  - **模块化/组合 (Modularization/Composition)**:\n\n    - 将复杂的异步操作分解为更小的、可复用的函数或模块。\n    - 每个模块负责处理一个独立的异步任务，然后将结果组合起来。\n\n  - **[[Promise]] (承诺)**: **(重点)**\n\n    - Promise 提供了一种更结构化、更清晰的方式来处理异步操作。\n    - 使用 `.then()` 链式调用来处理异步操作的顺序执行，避免了回调函数的深层嵌套。\n    - 使用 `.catch()` 方法集中处理错误，提高了错误处理的效率和可维护性。\n    - **例子**:\n      ```javascript\n      // 使用 Promise 避免回调地狱\n      asyncOperation1Promise(input)\n        .then((result1) => asyncOperation2Promise(result1))\n        .then((result2) => asyncOperation3Promise(result2))\n        .then((result3) => {\n          // 处理最终结果\n          console.log(\"最终结果:\", result3);\n        })\n        .catch((error) => {\n          // 集中错误处理\n          console.error(\"发生错误:\", error);\n        });\n      ```\n\n  - **Async/Await (异步/等待)**: **(重点)** [[Async_Await]]\n\n    - Async/Await 是建立在 Promise 之上的语法糖，进一步简化了异步代码的编写。\n    - 使用 `async` 关键字声明异步函数，使用 `await` 关键字等待 Promise 的结果，使异步代码看起来更像同步代码，更易于理解和维护。\n    - 使用 `try...catch` 块进行错误处理，与同步代码的错误处理方式一致。\n    - **例子**:\n\n      ```javascript\n      // 使用 Async/Await 避免回调地狱\n      async function processAsyncOperations() {\n        try {\n          const result1 = await asyncOperation1Promise(input);\n          const result2 = await asyncOperation2Promise(result1);\n          const result3 = await asyncOperation3Promise(result2);\n          console.log(\"最终结果:\", result3);\n        } catch (error) {\n          console.error(\"发生错误:\", error);\n        }\n      }\n\n      processAsyncOperations();\n      ```\n\n## 数字和数据总结\n\n- **回调嵌套层数**: 回调地狱的“地狱”程度通常与回调嵌套的层数成正比。 虽然没有具体的“地狱层数”标准，但超过 **2-3 层** 嵌套通常就容易开始变得难以管理。 某些极端情况下，回调地狱可能达到 **5 层甚至更多** 的嵌套。\n- **解决回调地狱的关键技术**: 主要依赖于 **Promise** 和 **Async/Await** 这两种技术的发展。\n\n## 例子总结\n\n- **经典回调地狱示例 (模拟异步操作)**:\n\n  ```javascript\n  // 模拟异步操作的函数 (使用 setTimeout)\n  function asyncOperation(name, delay, callback) {\n    setTimeout(() => {\n      console.log(`${name} 完成`);\n      callback(null, `${name} 的结果`); // 模拟成功返回结果\n    }, delay);\n  }\n\n  // 回调地狱示例\n  asyncOperation(\"操作 1\", 500, function (error, result1) {\n    if (error) {\n      console.error(\"操作 1 失败:\", error);\n    } else {\n      console.log(\"操作 1 结果:\", result1);\n      asyncOperation(\"操作 2\", 800, function (error, result2) {\n        if (error) {\n          console.error(\"操作 2 失败:\", error);\n        } else {\n          console.log(\"操作 2 结果:\", result2);\n          asyncOperation(\"操作 3\", 1200, function (error, result3) {\n            if (error) {\n              console.error(\"操作 3 失败:\", error);\n            } else {\n              console.log(\"操作 3 结果:\", result3);\n              // ... 更多嵌套的回调函数 ...\n              console.log(\"最终结果基于:\", result1, result2, result3);\n            }\n          });\n        }\n      });\n    }\n  });\n  ```\n\n  **观察**: 代码不断向右缩进，逻辑分散，错误处理分散。\n\n- **使用 Promise 解决回调地狱示例**:\n\n  ```javascript\n  function asyncOperationPromise(name, delay) {\n    return new Promise((resolve, reject) => {\n      setTimeout(() => {\n        console.log(`${name} 完成 (Promise)`);\n        resolve(`${name} 的结果 (Promise)`);\n      }, delay);\n    });\n  }\n\n  asyncOperationPromise(\"操作 1\", 500)\n    .then((result1) => {\n      console.log(\"操作 1 结果:\", result1);\n      return asyncOperationPromise(\"操作 2\", 800); // 返回 Promise，链式调用\n    })\n    .then((result2) => {\n      console.log(\"操作 2 结果:\", result2);\n      return asyncOperationPromise(\"操作 3\", 1200);\n    })\n    .then((result3) => {\n      console.log(\"操作 3 结果:\", result3);\n      console.log(\"最终结果基于:\", result1, result2, result3); // 注意 result1, result2 作用域\n    })\n    .catch((error) => {\n      console.error(\"发生错误:\", error); // 集中错误处理\n    });\n  ```\n\n  **观察**: 代码结构扁平化，使用 `.then()` 链式调用，错误处理集中在 `.catch()` 中。\n\n- **使用 Async/Await 解决回调地狱示例**:\n\n  ```javascript\n  async function processAsyncOperations() {\n    try {\n      const result1 = await asyncOperationPromise(\"操作 1\", 500);\n      console.log(\"操作 1 结果:\", result1);\n      const result2 = await asyncOperationPromise(\"操作 2\", 800);\n      console.log(\"操作 2 结果:\", result2);\n      const result3 = await asyncOperationPromise(\"操作 3\", 1200);\n      console.log(\"操作 3 结果:\", result3);\n      console.log(\"最终结果基于:\", result1, result2, result3);\n    } catch (error) {\n      console.error(\"发生错误:\", error);\n    }\n  }\n\n  processAsyncOperations();\n  ```\n\n  **观察**: 代码结构更接近同步代码，使用 `await` 关键字等待 Promise 结果，错误处理使用 `try...catch` 块。\n\n希望这份关于回调地狱的笔记能够帮助您理解其概念、问题和解决方案。 掌握如何使用 Promise 和 Async/Await 是避免回调地狱，编写更清晰、可维护的异步 JavaScript 代码的关键。\n", "aliases": ["回调地狱", "callback hell"], "degree": 4, "nameLength": 18}, "position": {"x": 301.5, "y": 543}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:Async_Await.md", "name": "Async_Await", "path": "Async_Await.md", "content": "`async/await` 是 JavaScript 中用于处理异步操作的一种语法糖，它基于 Promise，使得异步代码看起来更像同步代码，从而提高了代码的可读性和可维护性。`async` 关键字用于声明一个异步函数，而 `await` 关键字则用于暂停函数的执行，直到 Promise 被解决或拒绝。`await` 只能在 `async` 函数内部使用，否则会导致语法错误。\n\n(基于::[[Promise]])\n\n## async/await 异常处理\n\n在使用 `async/await` 时，需要注意的是，虽然它让异步代码看起来像同步代码，但并不会阻塞整个程序的执行，只是阻塞了异步函数内部的代码执行。此外，为了处理可能的错误，通常会将 `await` 语句放在 `try...catch` 块中。\n\n例如，以下是一个使用 `async/await` 等待 AJAX 响应的示例：\n\n```javascript\nasync function fetchData() {\n    try {\n        const response = await fetch('https://api.example.com/data');\n        const data = await response.json();\n        console.log(data);\n    } catch (error) {\n        console.error('Error fetching data:', error);\n    }\n}\nfetchData();\n```\n\n在这个示例中，`fetchData` 是一个异步函数，它使用 `await` 关键字等待 `fetch` 请求完成并解析响应体为 JSON 格式的数据。如果请求过程中发生错误，`catch` 块会捕获并处理该错误。\n", "degree": 3, "nameLength": 11}, "position": {"x": 322.625, "y": 576.05}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:Microtask Queue.md", "name": "Microtask Queue", "path": "Microtask Queue.md", "content": "---\naliases:\n  - 微任务队列\n---\n\nNode.js 中的 **Microtask Queue（微任务队列）** 是事件循环中处理高优先级异步任务的核心机制，其执行优先级高于宏任务（如 `setTimeout`、`I/O 回调`）。以下是其工作原理的详细解析：\n\n---\n\n### **1. 微任务队列的定义**\n\n- **组成**：\n    - **`process.nextTick` 回调**：优先级最高。 [[process.nextTick]]\n    - **`Promise` 回调**：包括 `then/catch/finally`。 [[Promise]]\n- **特点**：\n    - 微任务队列在 **每个事件循环阶段之间** 执行。\n    - 微任务的执行会 **立即发生**，不进入事件循环的六个阶段。\n\n---\n\n### **2. 微任务队列的执行时机**\n\n微任务队列的执行遵循以下规则：\n\n1. **在每个事件循环阶段结束后**（如 Timers、Poll、Check 阶段），立即清空微任务队列。\n2. **若微任务嵌套产生新的微任务**，会持续执行直到队列为空。\n3. **`process.nextTick` 的优先级高于 `Promise`**。\n\n```js\n// 示例：微任务的优先级与执行顺序 \nsetTimeout(() => console.log('Timeout (宏任务)'), 0);  \nPromise.resolve().then(() => console.log('Promise (微任务)'));   \nprocess.nextTick(() => console.log('nextTick (微任务)'));  \n\n// 输出顺序： // nextTick → Promise → Timeout`\n\n```\n\n---\n\n### **3. 微任务队列与事件循环阶段的关系**\n\n下图展示了微任务队列在事件循环中的位置：\n\n`Timers 阶段 → 执行微任务队列 → Pending I/O 阶段 → 执行微任务队列 → ... → Close Callbacks 阶段 → 执行微任务队列`\n\n---\n\n### **4. 微任务队列的执行流程**\n\n#### **场景 1：在宏任务中触发微任务**\n\n```js\nsetTimeout(() => {\n  console.log('宏任务（Timers 阶段）');\n  process.nextTick(() => console.log('嵌套微任务（nextTick）'));\n}, 0);\n\nPromise.resolve().then(() => console.log('微任务（Promise）'));\n```\n\n运行\n\n`微任务（Promise）` → `宏任务（Timers 阶段）` → `嵌套微任务（nextTick）`\n\n这个输出顺序是**正确的**，但您的流程解析有一处不准确：\n\n**正确的流程解析**：\n\n1. 代码开始执行，注册了一个宏任务(`setTimeout`)和一个微任务(`Promise.then`)\n2. 主代码执行完毕后，检查微任务队列，执行`Promise.then`回调，输出`微任务（Promise）`\n3. 然后开始新的事件循环，执行宏任务队列中的`setTimeout`回调，输出`宏任务（Timers 阶段）`\n4. 在执行这个宏任务期间，注册了一个新的微任务(`process.nextTick`)\n5. 宏任务执行完成后，检查微任务队列，执行`process.nextTick`回调，输出`嵌套微任务（nextTick）`\n\n---\n\n#### **场景 2：微任务嵌套微任务**\n\n```js\nPromise.resolve().then(() => {   \n    console.log('微任务 1');   \n    Promise.resolve().then(() => console.log('嵌套微任务 2')); \n    });\n```\n\n运行\n\n- **输出顺序**：  \n    `微任务 1` → `嵌套微任务 2`\n    \n- **流程解析**：  \n    微任务队列会持续执行，直到队列为空，即使嵌套产生新微任务。\n    \n\n---\n\n### **5. 微任务与宏任务的关键区别**\n\n|**特性**|**微任务队列**|**宏任务队列**|\n|---|---|---|\n|**优先级**|高（在每个阶段后立即执行）|低（按事件循环阶段顺序执行）|\n|**任务类型**|`process.nextTick`, `Promise`|`setTimeout`, `setInterval`, `I/O`|\n|**执行机制**|同步清空队列|分阶段处理|\n|**性能影响**|过度使用可能导致事件循环阻塞|阻塞风险较低|\n\n---\n\n### **6. 开发注意事项**\n\n1. **避免微任务队列饥饿**：\n    \n    - 若微任务中无限递归生成新微任务（如 `process.nextTick` 内再次调用自身），会导致事件循环无法进入下一阶段。\n    \n    ```js\n    // 错误示例：微任务递归导致事件循环阻塞 \n    function recursiveNextTick() {   \n        process.nextTick(() => {     \n            console.log('阻塞中...');     \n            recursiveNextTick();   }); \n    } \n    recursiveNextTick();\n    ```\n    \n    运行\n    \n2. **合理选择任务类型**：\n    \n    - **需要立即执行的任务**：使用 `process.nextTick`（例如在对象构造后触发事件）。\n    - **异步流程控制**：优先使用 `Promise` 或 `async/await`。\n    \n3. **监控微任务堆积**：\n    \n    - 使用性能分析工具（如 `async_hooks`）跟踪微任务执行时间和数量。\n\n---\n\n### **7. Node.js 与浏览器环境的差异**\n\n| **特性**               | **浏览器环境**                      | **Node.js 环境**                  |\n|------------------------|-----------------------------------|----------------------------------|\n| **微任务来源**           | `Promise`, `MutationObserver`    | `Promise`, `process.nextTick`    |\n| **优先级**              | `Promise` = `MutationObserver`   | `process.nextTick` > `Promise`   |\n| **宏任务类型**              | `setImmediate`, `I/O`         | `setTimeout`, `DOM 事件`        |\n[[MutationObserver]]\n\n---\n\n### **总结**\n\n微任务队列是 Node.js 事件循环中处理高优先级异步操作的核心机制，理解其执行规则和优先级对编写高效代码至关重要。\n\n- **核心规则**：微任务在每个事件循环阶段后立即执行，`process.nextTick` 优先级高于 `Promise`。\n- **避免滥用**：过度使用微任务可能导致事件循环阻塞，需谨慎处理递归和长时间任务。\n- **适用场景**：适合需要立即响应的任务（如状态更新、错误处理），但不适合 CPU 密集型操作。\n\n", "aliases": ["微任务队列"], "degree": 1, "nameLength": 15}, "position": {"x": 267.5, "y": 441.20000000000005}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note has-outgoing-inline"}, {"data": {"id": "core:动态导入 Dynamic Import.md", "name": "动态导入 Dynamic Import", "path": "动态导入 Dynamic Import.md", "content": "# 动态导入(Dynamic Import)\n\n动态导入是 JavaScript 中的一个特性，允许在运行时按需加载模块，而不是在应用启动时静态导入所有内容。这种技术使开发者能够实现代码拆分，延迟加载非必要资源，从而优化应用性能和用户体验。\n\n## 核心概念\n\n**动态导入涉及以下关键组成部分：**\n\n- **import() 函数**：一个返回 Promise 的函数式导入语法\n- **模块路径**：指定要加载的模块来源\n- **Promise 处理**：处理模块加载的成功和失败状态 [[Promise]]\n- **代码分割**：由打包工具（如 Webpack）自动将动态导入的模块分离成独立文件 [[代码拆分]]\n- **按需执行**：仅在需要时才加载和执行模块代码\n\n```mermaid\nflowchart TB\n    A[应用代码]\n    B[静态导入_顶部声明]\n    C[动态导入_import_函数]\n    D[打包工具_Webpack/Rollup等]\n    E[主包_main.js]\n    F[分割的代码块_chunks]\n    G[运行时请求]\n    H[Network_网络]\n    I[按需执行模块代码]\n\n    A -->|包含| B\n    A -->|包含| C\n    A -->|处理| D\n    B -->|打包到| E\n    C -->|分割到| F\n    D -->|生成| E\n    D -->|生成| F\n    G -->|请求加载| F\n    F -->|通过| H\n    H -->|返回| F\n    F -->|完成后| I\n\n    subgraph \"打包阶段\"\n      D\n    end\n\n    subgraph \"运行时\"\n      G\n      H\n      I\n    end\n```\n\n## 核心逻辑\n\n动态导入的工作原理围绕以下关键逻辑展开：\n\n1. **延迟加载请求**：应用执行到 `import()` 语句时才发起模块加载请求\n2. **异步解析**：加载过程异步执行，不阻塞主线程\n3. **模块解析**：打包工具在构建时为每个动态导入创建单独的代码块\n4. **缓存机制**：同一模块的多次动态导入只会加载一次，随后从缓存提供\n5. **异常处理**：提供错误处理机制，应对网络失败或模块错误\n\n**注意事项：**\n\n- 动态导入是基于 Promise 的，在旧浏览器中需要 Promise polyfill\n- 模块路径可以是变量，但有一定限制，打包工具需要能推断出可能的模块路径\n- 加载失败需要适当处理，避免应用崩溃\n\n```mermaid\nsequenceDiagram\n    participant App as 应用代码\n    participant Runtime as JS运行时\n    participant Network as 网络\n    participant Module as 动态模块\n\n    App->>Runtime: 执行import(modulePath)\n    Runtime->>App: 返回Promise\n\n    alt 模块已缓存\n        Runtime-->>App: 立即解析Promise(缓存模块)\n    else 模块未加载\n        Runtime->>Network: 发起网络请求获取模块\n\n        alt 请求成功\n            Network-->>Runtime: 返回模块代码\n            Runtime->>Module: 解析并执行模块代码\n            Module-->>Runtime: 模块导出对象\n            Runtime-->>App: 解析Promise(模块导出)\n        else 请求失败\n            Network-->>Runtime: 返回错误\n            Runtime-->>App: 拒绝Promise(错误信息)\n        end\n    end\n\n    App->>App: 处理模块或错误\n```\n\n## 代码示例\n\n### 1. 基本的动态导入使用\n\n```javascript\n// 基本动态导入示例\n\n// 传统静态导入方式\n// import { someFunction } from './someModule';\n\n// 点击按钮时才加载模块\ndocument.getElementById(\"loadButton\").addEventListener(\"click\", async () => {\n  try {\n    // 动态导入模块，返回Promise\n    const module = await import(\"./someModule.js\");\n\n    // 使用导入的模块及其导出\n    module.someFunction();\n\n    // 也可以解构获取特定导出\n    const { anotherFunction } = await import(\"./someModule.js\");\n    anotherFunction();\n\n    console.log(\"模块加载并执行成功!\");\n  } catch (error) {\n    console.error(\"模块加载失败:\", error);\n  }\n});\n```\n\n### 2. 在 React 中使用动态导入\n\n```jsx\n// React组件中的动态导入\n\nimport React, { useState, useEffect } from \"react\";\n\nfunction DynamicImportDemo() {\n  const [moduleData, setModuleData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const loadModule = async () => {\n    if (moduleData) return; // 已加载，不重复操作\n\n    setLoading(true); // 设置加载状态\n    setError(null); // 清除错误\n\n    try {\n      // 动态导入数据处理模块\n      const dataModule = await import(\"./dataProcessor.js\");\n\n      // 使用导入的函数处理数据\n      const processedData = dataModule.processData({\n        items: [1, 2, 3, 4, 5],\n      });\n\n      setModuleData(processedData);\n    } catch (err) {\n      console.error(\"加载模块失败:\", err);\n      setError(\"无法加载所需模块，请稍后再试\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"dynamic-import-demo\">\n      <h2>动态导入演示</h2>\n\n      <button onClick={loadModule} disabled={loading || moduleData}>\n        {loading ? \"加载中...\" : moduleData ? \"已加载\" : \"加载数据处理模块\"}\n      </button>\n\n      {error && <div className=\"error\">{error}</div>}\n\n      {moduleData && (\n        <div className=\"result\">\n          <h3>处理结果:</h3>\n          <pre>{JSON.stringify(moduleData, null, 2)}</pre>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default DynamicImportDemo;\n\n// dataProcessor.js 文件内容可能如下:\n/*\nexport function processData(data) {\n  return {\n    original: data,\n    processed: data.items.map(x => x * 2),\n    sum: data.items.reduce((acc, val) => acc + val, 0),\n    timestamp: new Date().toISOString()\n  };\n}\n*/\n```\n\n### 3. 条件动态导入与环境区分\n\n```javascript\n// 根据条件动态导入不同模块\n\nasync function loadAppropriateModule() {\n  // 根据环境或条件决定加载哪个模块\n  const isDevelopment = process.env.NODE_ENV === \"development\";\n\n  try {\n    // 选择性加载不同模块\n    if (isDevelopment) {\n      // 开发环境加载带调试功能的模块\n      const devModule = await import(\"./devModule.js\");\n      return devModule.initialize({ debug: true });\n    } else {\n      // 生产环境加载优化版本\n      const prodModule = await import(\"./prodModule.js\");\n      return prodModule.initialize();\n    }\n  } catch (error) {\n    console.error(\"加载模块失败:\", error);\n    // 提供一个默认实现作为回退\n    return { error: true, fallback: true };\n  }\n}\n\n// 在应用中使用\nloadAppropriateModule().then((moduleAPI) => {\n  // 使用加载的模块API\n  console.log(\"模块已初始化:\", moduleAPI);\n});\n```\n\n### 4. 高级用例：预加载和并行加载\n\n```javascript\n// 高级动态导入技术\n\n// 预声明模块加载函数但不立即执行\nconst getChartModule = () => import(\"./charts/ChartComponent.js\");\nconst getDataModule = () => import(\"./data/DataProcessor.js\");\nconst getI18nModule = () => import(\"./i18n/translations.js\");\n\nasync function initializeApplication() {\n  console.time(\"模块加载时间\");\n\n  try {\n    // 并行加载多个模块\n    const [chartModule, dataModule, i18nModule] = await Promise.all([getChartModule(), getDataModule(), getI18nModule()]);\n\n    // 所有模块加载完成，进行初始化\n    const chart = new chartModule.ChartRenderer();\n    const dataProcessor = new dataModule.DataProcessor();\n    const translations = i18nModule.default; // 假设使用默认导出\n\n    console.timeEnd(\"模块加载时间\");\n\n    return {\n      renderChart: (data) => chart.render(dataProcessor.process(data)),\n      t: (key) => translations[key] || key,\n    };\n  } catch (error) {\n    console.timeEnd(\"模块加载时间\");\n    console.error(\"初始化失败:\", error);\n    throw new Error(\"应用初始化失败，请刷新页面\");\n  }\n}\n\n// 在用户交互前预加载某些模块\nfunction preloadOnUserInteraction() {\n  const button = document.getElementById(\"feature-button\");\n\n  // 鼠标悬停时预加载\n  button.addEventListener(\n    \"mouseenter\",\n    () => {\n      // 只预加载，不等待完成\n      import(\"./features/SpecialFeature.js\")\n        .then(() => console.log(\"特性模块已预加载\"))\n        .catch((err) => console.warn(\"预加载失败，将在使用时再加载\", err));\n    },\n    { once: true }\n  ); // 只触发一次\n\n  // 点击时实际使用\n  button.addEventListener(\"click\", async () => {\n    try {\n      const featureModule = await import(\"./features/SpecialFeature.js\");\n      featureModule.activate();\n    } catch (error) {\n      console.error(\"加载特性失败:\", error);\n    }\n  });\n}\n```\n\n## 在实际开发中的思考\n\n**1. 何时使用动态导入**\n\n动态导入是一种强大的工具，但需要战略性地使用：\n\n- **路由级代码拆分**：与 React Router 结合，仅加载当前路由所需组件\n\n  ```jsx\n  // React Router示例\n  const Home = React.lazy(() => import(\"./routes/Home\"));\n  const Dashboard = React.lazy(() => import(\"./routes/Dashboard\"));\n\n  function App() {\n    return (\n      <Routes>\n        <Route path=\"/\" element={<Home />} />\n        <Route path=\"/dashboard\" element={<Dashboard />} />\n      </Routes>\n    );\n  }\n  ```\n\n- **重型功能按需加载**：如富文本编辑器、图表库等大型依赖\n\n  ```jsx\n  // 只在用户需要编辑时加载富文本编辑器\n  function PostEditor() {\n    const [editorLoaded, setEditorLoaded] = useState(false);\n    const [EditorComponent, setEditorComponent] = useState(null);\n\n    const loadEditor = async () => {\n      // 动态导入编辑器库\n      const { default: RichTextEditor } = await import(\"some-editor-package\");\n      setEditorComponent(() => RichTextEditor);\n      setEditorLoaded(true);\n    };\n\n    return <div>{!editorLoaded ? <button onClick={loadEditor}>加载编辑器</button> : <EditorComponent />}</div>;\n  }\n  ```\n\n- **条件性功能**：特定用户角色或罕见使用场景的功能\n\n  ```jsx\n  // 只为管理员加载管理功能\n  function AdminPanel({ isAdmin }) {\n    const [adminTools, setAdminTools] = useState(null);\n\n    useEffect(() => {\n      if (isAdmin) {\n        import(\"./adminTools\")\n          .then((module) => setAdminTools(module))\n          .catch((err) => console.error(\"加载管理工具失败\", err));\n      }\n    }, [isAdmin]);\n\n    if (!isAdmin) return null;\n    if (!adminTools) return <div>加载管理工具...</div>;\n\n    return <adminTools.AdminDashboard />;\n  }\n  ```\n\n**2. 动态导入的性能考量**\n\n使用动态导入时需要权衡以下因素：\n\n- **额外的网络请求**：每个分割的块都需要单独的 HTTP 请求\n- **缓存策略**：确保正确设置缓存头，优化重复访问\n- **加载指示器**：提供适当的加载状态反馈，避免用户困惑\n- **预加载策略**：在空闲时间或基于用户行为提前加载\n\n**3. 与现代框架的集成**\n\n动态导入在不同框架中的使用方式：\n\n**React:**\n\n```jsx\n// 使用React.lazy和Suspense\nconst OtherComponent = React.lazy(() => import(\"./OtherComponent\"));\n\nfunction MyComponent() {\n  return (\n    <React.Suspense fallback={<div>Loading...</div>}>\n      <OtherComponent />\n    </React.Suspense>\n  );\n}\n```\n\n**Vue 3:**\n\n```javascript\n// Vue中的动态组件导入\nimport { defineAsyncComponent } from \"vue\";\n\nconst AsyncComp = defineAsyncComponent(() => import(\"./components/AsyncComponent.vue\"));\n\nexport default {\n  components: {\n    AsyncComp,\n  },\n};\n```\n\n**Next.js:**\n\n```jsx\n// Next.js动态导入\nimport dynamic from \"next/dynamic\";\n\nconst DynamicComponent = dynamic(() => import(\"../components/hello\"), {\n  loading: () => <p>Loading...</p>,\n});\n\nfunction Home() {\n  return (\n    <div>\n      <DynamicComponent />\n    </div>\n  );\n}\n```\n\n**4. 常见陷阱与解决方案**\n\n使用动态导入时的常见问题：\n\n- **变量路径限制**：动态导入路径需要让打包工具能够分析\n\n  ```javascript\n  // ❌ 错误: 完全动态路径\n  const moduleName = getUserPreference();\n  import(`./modules/${moduleName}`); // 打包工具无法预测所有可能性\n\n  // ✅ 正确: 有限的动态路径\n  const moduleName = getUserTheme(); // 假设只返回 'light' 或 'dark'\n  import(`./themes/${moduleName}.js`); // 打包工具可以处理这两种情况\n  ```\n\n- **嵌套动态导入**：尽量避免嵌套或条件内的动态导入，可能导致分块冗余\n- **过度拆分**：不要为每个小组件都使用动态导入，这会导致请求爆炸\n\n## 学习资源\n\n**官方文档与规范：**\n\n- [JavaScript 规范 - 动态导入](https://tc39.es/ecma262/#sec-import-calls)\n- [MDN Web Docs - 动态导入](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Statements/import#%E5%8A%A8%E6%80%81%E5%AF%BC%E5%85%A5)\n- [Webpack 文档 - 代码拆分](https://webpack.js.org/guides/code-splitting/)\n\n**框架相关资源：**\n\n- [React 文档 - 代码拆分](https://reactjs.org/docs/code-splitting.html)\n- [Vue.js - 异步组件](https://v3.vuejs.org/guide/component-dynamic-async.html)\n- [Next.js - 动态导入](https://nextjs.org/docs/advanced-features/dynamic-import)\n\n**书籍与深入阅读：**\n\n- **《JavaScript 高级程序设计（第 4 版）》** - Nicholas C. Zakas\n- **《深入理解 ES6》** - Nicholas C. Zakas\n- **《高性能 JavaScript》** - Nicholas C. Zakas\n\n**在线课程和文章：**\n\n- [Webpack Academy](https://webpack-academy.teachable.com/) - Sean Larkin 的 Webpack 课程\n- [JavaScript 模块系统终极指南](https://dev.to/santypk4/javascript-modules-for-beginners-pj6)\n- [web.dev - 用代码拆分减少 JavaScript 负载](https://web.dev/reduce-javascript-payloads-with-code-splitting/)\n\n**工具：**\n\n- [webpack-bundle-analyzer](https://github.com/webpack-contrib/webpack-bundle-analyzer) - 可视化和分析打包结果\n- [import-cost VSCode 扩展](https://marketplace.visualstudio.com/items?itemName=wix.vscode-import-cost) - 在编辑器中显示导入包大小\n\n**GitHub 示例：**\n\n- [Create React App 代码拆分示例](https://github.com/facebook/create-react-app/tree/master/packages/react-scripts/template/src/components)\n- [dynamic-import-polyfill](https://github.com/uupaa/dynamic-import-polyfill) - 为不支持动态导入的环境提供 polyfill\n\n通过以上资源，你可以全面掌握动态导入技术，并在实际项目中合理应用这一强大的性能优化工具。\n", "degree": 1, "nameLength": 19}, "position": {"x": 320, "y": 441.20000000000005}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note has-outgoing-inline"}, {"data": {"id": "core:异步.md", "name": "异步", "path": "异步.md", "content": "[[Node异步]]\n\n[[基于回调函数的异步编程]]\n\n[[Async_Await]]\n\n[[Promise]]", "degree": 2, "nameLength": 2}, "position": {"x": 376.625, "y": 441.20000000000005}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note has-outgoing-inline"}, {"data": {"id": "core:Promise 的基本概念和使用", "name": "Promise 的基本概念和使用", "degree": 3, "nameLength": 16}, "position": {"x": 228.5, "y": 509.9}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "dangling has-incoming-inline has-outgoing-inline expanded protected hard-filtered global-0"}, {"data": {"id": "core:Promise 的基本概念和使用.md", "name": "Promise 的基本概念和使用", "path": "Promise 的基本概念和使用.md", "content": "#### 1. 什么是 Promise？\n\n* **Promise 的本质:**  Promise 是一个代表**异步操作最终结果**的对象。它就像一个承诺，承诺在未来某个时间点会返回一个结果（成功或失败）。\n\n* **Promise 的状态:** Promise 对象具有三种状态：\n    * **Pending (等待中):**  初始状态，异步操作尚未完成。\n    * **Fulfilled (已成功):** 异步操作成功完成。Promise 处于 fulfilled 状态时，会有一个与之关联的**值 (value)**，表示操作成功的结果。\n    * **Rejected (已失败):** 异步操作失败。Promise 处于 rejected 状态时，会有一个与之关联的**原因 (reason)**，表示操作失败的原因。\n\n* **状态转换:** Promise 的状态只能从 Pending 转换为 Fulfilled 或 Rejected，且状态一旦改变就**不可逆**。\n  \n```mermaid\nstateDiagram-v2\n    [*] --> Pending: 初始状态\n    Pending --> Fulfilled: 异步操作成功完成\n    Pending --> Rejected: 异步操作失败\n    Fulfilled --> Fulfilled: 状态不可逆\n    Rejected --> Rejected: 状态不可逆\n    state Fulfilled {\n        value: 操作成功的结果\n    }\n    state Rejected {\n        reason: 操作失败的原因\n    }\n```\n\n#### 2. 为什么要使用 Promise？\n\n* **[解决::[[回调地狱 Callback Hell]]]:**  \n  当多个异步操作互相依赖，且需要按顺序执行时，传统的回调函数方式容易形成嵌套很深的代码结构，难以阅读和维护，这就是回调地狱。Promise 通过链式调用 `.then()` 方法，可以更清晰地组织异步代码。\n\n* **更优雅的错误处理:** Promise 提供了 `.catch()` 方法来集中处理异步操作中的错误，使错误处理逻辑更清晰。\n\n* **提高代码可读性和可维护性:** Promise 的链式调用和状态管理机制，使异步代码的逻辑更加清晰，易于理解和维护。\n\n#### 3. 如何创建 Promise？\n\n使用 `new Promise()` 构造函数来创建 Promise 对象。构造函数接收一个执行器函数 (executor function) 作为参数，这个执行器函数又接收两个参数：\n\n* **`resolve(value)`:**  一个函数，用于将 Promise 的状态从 Pending 变为 Fulfilled，并将 `value` 作为成功的结果传递出去。[[Promise.resolve()]]\n* **`reject(reason)`:** 一个函数，用于将 Promise 的状态从 Pending 变为 Rejected，并将 `reason` 作为失败的原因传递出去。\n\n```javascript\nconst myPromise = new Promise((resolve, reject) => {\n  // 模拟异步操作 (例如：setTimeout, 网络请求等)\n  setTimeout(() => {\n    const success = true; // 假设异步操作成功\n    if (success) {\n      resolve(\"操作成功！\"); // 调用 resolve，Promise 状态变为 Fulfilled\n    } else {\n      reject(\"操作失败！\"); // 调用 reject，Promise 状态变为 Rejected\n    }\n  }, 2000); // 2秒后执行\n});\n```\n\n#### 4. 如何处理 Promise 的结果？\n\n* **`.then(onFulfilled, onRejected)`:**  用于指定 Promise 状态变为 Fulfilled 或 Rejected 时的回调函数。\n    * `onFulfilled` (可选):  Promise 状态变为 Fulfilled 时执行的函数。它接收 Promise 成功的值作为参数。\n    * `onRejected` (可选):  Promise 状态变为 Rejected 时执行的函数。它接收 Promise 失败的原因作为参数。\n\n* **`.catch(onRejected)`:**  专门用于处理 Promise 状态变为 Rejected 的情况。它是 `.then(null, onRejected)` 的语法糖。\n\n* **`.finally(onFinally)`:**  无论 Promise 状态变为 Fulfilled 还是 Rejected，`.finally()` 中指定的回调函数都会执行。它通常用于执行一些清理操作，例如关闭加载指示器等。`.finally()` 不接收任何参数，也无法访问 Promise 的结果值或原因。\n\n**链式调用：**  `.then()`, `.catch()`, `.finally()` 方法都会返回一个新的 Promise 对象，这使得我们可以进行链式调用，将多个异步操作串联起来。\n\n**示例：**\n\n```javascript\nmyPromise\n  .then(\n    (value) => { // onFulfilled\n      console.log(\"Promise 成功:\", value); // 输出: Promise 成功: 操作成功！\n      return \"then 方法的返回值\"; // 返回值会被包装成一个新的 Promise，状态为 Fulfilled\n    },\n    (reason) => { // onRejected (不推荐在 then 中同时写 onFulfilled 和 onRejected，推荐使用 catch)\n      console.error(\"Promise 失败:\", reason);\n    }\n  )\n  .catch((reason) => { // catch 方法处理 Promise 链中任何地方抛出的错误\n    console.error(\"Promise 失败 (catch):\", reason);\n  })\n  .finally(() => {\n    console.log(\"Promise 操作完成 (finally)\"); // 无论成功失败都会执行\n  });\n```\n\n**基础总结:**\n\n* Promise 代表异步操作的最终结果。\n* Promise 有三种状态：Pending, Fulfilled, Rejected。\n* 使用 `new Promise()` 创建 Promise，并提供 `resolve` 和 `reject` 函数来改变 Promise 的状态。\n* 使用 `.then()`, `.catch()`, `.finally()` 方法处理 Promise 的结果和错误，并进行链式调用。\n\n---\n\n", "degree": 3, "nameLength": 16}, "position": {"x": 280.75, "y": 509.9}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:Promise.resolve().md", "name": "Promise.resolve()", "path": "Promise.resolve().md", "content": "## Promise.resolve() 函数详解\n\n`Promise.resolve()` 是 JavaScript Promise 对象的一个**静态方法**。它的主要作用是**创建一个已成功（fulfilled）的 Promise 对象**。  这个方法非常实用，可以方便地将一个值或者一个 thenable 对象转换为 Promise 对象，并使其立即处于 resolved 状态。\n\n我们从基础、深入和进阶三个方面来详细讲解 `Promise.resolve()` 函数。\n\n### 一、基础篇：`Promise.resolve()` 的基本概念和用法\n\n#### 1. `Promise.resolve()` 的作用\n\n* **快速创建 resolved Promise:**  `Promise.resolve(value)`  会返回一个新的 Promise 对象，并且这个 Promise 对象的状态立即变为 **fulfilled**，其成功值 (value) 就是你传递给 `Promise.resolve()` 的参数 `value`。\n\n* **值到 Promise 的转换器:**  它可以将任何值（包括普通值、Promise 对象、thenable 对象）转换为一个 Promise 对象。\n\n#### 2. 基本语法\n\n```javascript\nPromise.resolve(value);\n```\n\n* **`value` (可选):**  要解析为 Promise 的值。可以是任何 JavaScript 值，包括：\n    * **普通值 (primitive values):**  例如数字、字符串、布尔值、`null`、`undefined`。\n    * **对象 (objects):**  包括普通对象、数组、函数等。\n    * **Promise 对象:**  如果 `value` 本身就是一个 Promise 对象。\n    * **thenable 对象:**  具有 `then` 方法的对象。\n\n#### 3.  `Promise.resolve()` 的返回值\n\n`Promise.resolve()` 函数返回一个 **新的 Promise 对象**。  这个 Promise 对象的状态和值取决于传入的 `value` 参数：\n\n* **如果 `value` 是一个普通值或对象:**  返回的 Promise 会立即变为 **fulfilled** 状态，并且它的成功值就是 `value` 本身。\n\n* **如果 `value` 是一个 Promise 对象:**  `Promise.resolve()` 会直接**返回这个 Promise 对象**。  它不会创建新的 Promise，而是直接复用已有的 Promise。\n\n* **如果 `value` 是一个 thenable 对象:**  `Promise.resolve()` 会尝试**将这个 thenable 对象“展开” (flatten)** 成一个 Promise。它会调用 thenable 对象的 `then` 方法，并根据 `then` 方法的执行结果来决定新 Promise 的状态和值。这涉及到 Promise 的 \"thenable resolution\" 机制，我们会在深入篇详细讲解。\n\n```mermaid\ngraph TD\n    A[调用 Promise.resolve_value] --> B{value 类型}\n    B -- 普通值或对象 --> C[返回 一个新的 fulfilled Promise]\n    B -- Promise 对象 --> D[返回 原来的 Promise]\n    B -- thenable 对象 --> E{then 方法的结果}\n    E -- fulfilled --> F[返回 一个新的 fulfilled Promise]\n    E -- rejected --> G[返回 一个新的 rejected Promise]\n```\n#### 4. 简单示例\n\n```javascript\n// 1. 解析普通值\nconst resolvedPromise1 = Promise.resolve(10);\nresolvedPromise1.then(value => {\n  console.log(\"Promise 1 resolved with:\", value); // 输出: Promise 1 resolved with: 10\n});\n\n// 2. 解析字符串\nconst resolvedPromise2 = Promise.resolve(\"Hello Promise\");\nresolvedPromise2.then(value => {\n  console.log(\"Promise 2 resolved with:\", value); // 输出: Promise 2 resolved with: Hello Promise\n});\n\n// 3. 解析已有的 Promise 对象\nconst originalPromise = new Promise(resolve => setTimeout(() => resolve(\"Original Promise\"), 1000));\nconst resolvedPromise3 = Promise.resolve(originalPromise); // 直接返回 originalPromise，而不是创建新的\nconsole.log(resolvedPromise3 === originalPromise); // 输出: true (它们是同一个对象)\n\nresolvedPromise3.then(value => {\n  console.log(\"Promise 3 resolved with:\", value); // 1秒后输出: Promise 3 resolved with: Original Promise\n});\n```\n\n**基础总结:**\n\n* `Promise.resolve(value)` 用于快速创建一个已成功 (fulfilled) 的 Promise 对象。\n* 它可以将各种类型的值转换为 Promise。\n* 对于普通值和对象，返回的 Promise 立即 fulfilled 并以该值作为成功值。\n* 对于已有的 Promise 对象，直接返回该 Promise 对象本身。\n\n---\n\n### 二、深入篇：`Promise.resolve()` 的 Thenable Resolution 和应用场景\n\n#### [[Thenable Resolution 解析]]\n\n#### 2. `Promise.resolve()` 的应用场景\n\n* **将值快速转换为 Promise:**  当你需要在一个函数中返回一个 Promise，但有时函数可能直接返回一个同步的值时，可以使用 `Promise.resolve()` 确保函数始终返回 Promise 对象，保持接口的一致性。\n\n   ```javascript\n   function getValueAsync(input) {\n     if (typeof input === 'number') {\n       return Promise.resolve(input * 2); // 输入是数字，同步计算后立即 resolve\n     } else {\n       return new Promise(resolve => { // 输入不是数字，模拟异步操作\n         setTimeout(() => {\n           resolve(\"处理了非数字输入: \" + input);\n         }, 500);\n       });\n     }\n   }\n\n   getValueAsync(5).then(result => console.log(result)); // 输出: 10\n   getValueAsync(\"text\").then(result => console.log(result)); // 500ms 后输出: 处理了非数字输入: text\n   ```\n\n* **在 Promise 链中插入已 resolve 的 Promise:**  有时你可能需要在 Promise 链的某个位置插入一个已经成功完成的 Promise，用于提供一个默认值或者提前结束链式调用。\n\n   ```javascript\n   function fetchData() {\n     // 假设 fetchData 可能因为某种原因需要跳过网络请求，直接返回一个默认值\n     const shouldSkipFetch = false; // 假设条件\n\n     if (shouldSkipFetch) {\n       console.log(\"跳过网络请求，使用默认数据\");\n       return Promise.resolve({ data: \"默认数据\" }); // 提前 resolve\n     } else {\n       console.log(\"执行网络请求\");\n       return fetch('/api/data')\n         .then(response => response.json());\n     }\n   }\n\n   fetchData().then(data => console.log(\"获取到的数据:\", data));\n   ```\n\n* **测试和 Mocking:**  在单元测试中，可以使用 `Promise.resolve()` 快速创建一个模拟的 resolved Promise，用于测试 Promise 相关的代码逻辑，而无需实际执行异步操作。\n\n   ```javascript\n   // 单元测试示例 (假设使用 Jest)\n   test('测试某个异步函数，使用 mock resolved Promise', async () => {\n     const mockAsyncFunction = jest.fn().mockReturnValue(Promise.resolve({ name: 'Test User' }));\n\n     const result = await mockAsyncFunction();\n     expect(result).toEqual({ name: 'Test User' });\n     expect(mockAsyncFunction).toHaveBeenCalled();\n   });\n   ```\n\n**深入总结:**\n\n* `Promise.resolve()` 处理 thenable 对象时，会进行 Thenable Resolution，尝试 \"展开\" thenable 对象，使其行为与 Promise 关联。\n* Thenable Resolution 是 Promise 兼容性的关键机制。\n* `Promise.resolve()` 在快速转换值到 Promise、Promise 链操作、测试和 Mocking 等场景中非常有用。\n\n---\n\n### 三、进阶篇：`Promise.resolve()` 与 `async/await` 和性能考量\n\n#### 1. `Promise.resolve()` 与 `async/await`\n\n`async/await` 是建立在 Promise 之上的语法糖，用于更简洁地编写异步代码。`Promise.resolve()` 在 `async` 函数中仍然很有用，尤其是在需要提前返回一个 resolved Promise 的情况下。\n\n* **在 `async` 函数中提前返回 resolved Promise:**  `async` 函数默认返回 Promise。 如果你想在 `async` 函数的某个分支中提前结束并返回一个已知的值，可以使用 `return Promise.resolve(value)` 或者更简洁地直接 `return value` (因为 `async` 函数会自动将返回值包装成 Promise.resolve)。\n\n   ```javascript\n   async function processData(input) {\n     if (!input) {\n       console.log(\"输入为空，提前返回 resolved Promise\");\n       return \"默认结果\"; // 等价于 return Promise.resolve(\"默认结果\");\n     }\n\n     console.log(\"继续处理输入:\", input);\n     // ... 异步处理逻辑 ...\n     return \"最终结果\";\n   }\n\n   processData(null).then(result => console.log(\"结果:\", result)); // 输出: 结果: 默认结果\n   processData(\"some input\").then(result => console.log(\"结果:\", result)); // 输出: 结果: 最终结果\n   ```\n\n* **配合 `await` 使用:**  虽然 `await` 主要用于等待 Promise resolve，但你也可以 `await Promise.resolve(value)`，虽然这通常没有直接 `await value` (如果 value 本身是 Promise) 或直接使用值那么常见，但在某些特定场景下可能更清晰。\n\n   ```javascript\n   async function example() {\n     const resolvedValue = await Promise.resolve(42); // await 一个已 resolved 的 Promise\n     console.log(\"Resolved value:\", resolvedValue); // 输出: Resolved value: 42\n\n     const directValue = await 100; // 直接 await 一个值，也会被隐式转换为 Promise.resolve(100)\n     console.log(\"Direct value:\", directValue); // 输出: Direct value: 100\n   }\n\n   example();\n   ```\n\n#### 2. `Promise.resolve()` 的性能考量\n\n* **轻量级操作:**  `Promise.resolve()` 本身是一个非常轻量级的操作，它的性能开销很小。 创建一个 resolved Promise 的速度非常快。\n\n* **避免不必要的 `Promise.resolve()`:**  在某些情况下，可能不需要显式使用 `Promise.resolve()`。 例如，在 `async` 函数中，直接 `return value` 即可，`async` 函数会自动处理返回值并将其包装成 Promise.resolve(value)。  过度使用 `Promise.resolve()` 可能会增加代码的冗余，但通常不会对性能产生显著影响。\n\n* **与 `new Promise()` 的比较:**  相比于 `new Promise((resolve) => resolve(value))`, `Promise.resolve(value)` 更简洁高效，因为它避免了执行器函数的创建和调用，直接创建并返回一个 resolved Promise。  **在需要创建 resolved Promise 的场景下，优先使用 `Promise.resolve()`。**\n\n#### 3. 最佳实践\n\n* **优先使用 `Promise.resolve()` 创建 resolved Promise:**  当需要创建已 resolved 的 Promise 时，例如在函数返回值、默认值场景，使用 `Promise.resolve()` 比 `new Promise()` 更简洁高效。\n\n* **理解 Thenable Resolution 的作用:**  了解 `Promise.resolve()` 对 thenable 对象的处理方式，有助于理解 Promise 的兼容性和互操作性。\n\n* **在 `async` 函数中灵活使用 `Promise.resolve()` 和直接返回值:**  根据代码的清晰度和需求选择合适的方式，在 `async` 函数中，直接 `return value` 通常更简洁，但在某些需要显式强调返回 resolved Promise 的场景，`return Promise.resolve(value)` 也可以使用。\n\n**进阶总结:**\n\n* `Promise.resolve()` 在 `async/await` 中仍然有用，尤其是在需要提前返回 resolved Promise 的场景。\n* `Promise.resolve()` 是一个轻量级操作，性能开销小。\n* 优先使用 `Promise.resolve()` 创建 resolved Promise，避免不必要的 `new Promise()`。\n* 理解 `Promise.resolve()` 的最佳实践，编写更清晰高效的异步代码。\n\n---\n\n**总结：**\n\n`Promise.resolve()` 是一个简洁而强大的 Promise 静态方法，用于快速创建已成功 (fulfilled) 的 Promise 对象。  我们从基础用法、Thenable Resolution 机制、应用场景，再到与 `async/await` 的结合和性能考量，全面深入地讲解了 `Promise.resolve()` 的各个方面。\n\n掌握 `Promise.resolve()` 可以让你更高效地处理 Promise，编写更清晰、更易于维护的异步 JavaScript 代码。 希望这个全面的讲解能够帮助你更好地理解和运用 `Promise.resolve()`!", "degree": 3, "nameLength": 17}, "position": {"x": 250, "y": 543}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:Thenable Resolution 解析.md", "name": "Thenable Resolution 解析", "path": "Thenable Resolution 解析.md", "content": "\n当 `Promise.resolve(value)` 的参数 `value` 是一个 **thenable 对象** 时，`Promise.resolve()` 的行为会更复杂一些，涉及到 **Thenable Resolution** 机制。\n\n* **什么是 Thenable 对象？**  Thenable 对象是指任何具有 `then` 方法的对象。这个 `then` 方法期望接收两个参数：`onFulfilled` 和 `onRejected`，并且行为应该类似于 Promise 的 `.then()` 方法。  简而言之，它看起来像一个 Promise，但可能不是真正的 Promise 对象。[[Thenable]]\n\n* **Thenable Resolution 的过程:**  当 `Promise.resolve()` 遇到 thenable 对象时，它不会直接将 thenable 对象作为 Promise 的成功值，而是会尝试 \"展开\" 这个 thenable 对象，使其行为和状态与 thenable 对象的 `then` 方法关联起来。\n\n具体步骤如下：\n\n1. **检查 `value` 是否是 thenable 对象:**  判断 `value` 是否具有 `then` 属性，且 `then` 属性是一个函数。\n2. **如果 `value` 是 thenable，则调用其 `then` 方法:**  `Promise.resolve()` 会调用 `value.then(resolvePromise, rejectPromise)`，其中 `resolvePromise` 和 `rejectPromise` 是内部的函数。\n3. **`then` 方法的执行结果决定新 Promise 的状态:**\n    * 如果 `then` 方法调用了 `resolve(y)`，那么 `Promise.resolve()` 返回的 Promise 也会以 `y` 作为成功值变为 fulfilled 状态。\n    * 如果 `then` 方法调用了 `reject(r)`，那么 `Promise.resolve()` 返回的 Promise 就会以 `r` 作为失败原因变为 rejected 状态。\n    * 如果 `then` 方法抛出了异常，那么 `Promise.resolve()` 返回的 Promise 也会以这个异常作为失败原因变为 rejected 状态。\n\n**示例：Thenable Resolution**\n\n```javascript\nconst thenable = {\n  then: function(resolve, reject) {\n    console.log(\"Thenable 的 then 方法被调用\");\n    setTimeout(() => {\n      resolve(\"Thenable resolved value\"); // 1秒后 resolve thenable\n    }, 1000);\n  }\n};\n\nconst resolvedPromise4 = Promise.resolve(thenable); // 解析 thenable 对象\n\nresolvedPromise4.then(value => {\n  console.log(\"Promise 4 resolved with:\", value); // 1秒后输出: Promise 4 resolved with: Thenable resolved value\n});\n\nconsole.log(\"Promise 4 创建完成\"); // 先输出: Promise 4 创建完成\n// 然后 1秒后输出: Thenable 的 then 方法被调用\n// 再过 1秒后输出: Promise 4 resolved with: Thenable resolved value\n```\n\n**注意:**  Thenable Resolution 是 Promise 设计中非常重要的特性，它允许 Promise 与其他 Promise-like 对象（例如早期的 Promise 实现、一些库返回的 Promise-like 对象）进行互操作，实现 Promise 的兼容性和生态系统的统一。\n\n", "degree": 2, "nameLength": 22}, "position": {"x": 250, "y": 576.05}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected has-incoming-inline has-outgoing-inline"}, {"data": {"id": "core:Thenable.md", "name": "Thenable", "path": "Thenable.md", "content": "好的，我们来详细解释一下 **Thenable 对象**，以及它在 JavaScript Promise 中扮演的角色。\n\n**什么是 Thenable 对象？**\n\n简单来说，**Thenable 对象** 是指任何拥有一个符合 Promise 规范的 `then` 方法的对象。  关键在于 **`then` 方法** 的存在和行为，而不是对象本身是否是 `Promise` 的实例。\n\n**关键特征：**\n\n* **`then` 方法存在:**  Thenable 对象的核心特征是它必须有一个名为 `then` 的方法。\n* **`then` 方法签名:**  这个 `then` 方法应该接受两个参数：\n    * `onFulfilled`:  一个在 Thenable 对象的操作成功完成时调用的函数。\n    * `onRejected`:  一个在 Thenable 对象的操作失败时调用的函数。\n* **符合 Promise 规范的行为:**  `then` 方法的行为应该与 Promise 的 `.then()` 方法类似，它应该异步地调用 `onFulfilled` 或 `onRejected`，并且可以返回一个新的 Thenable 对象或值。\n\n**为什么 Thenable 对象很重要？**\n\nThenable 对象在 Promise 中扮演着至关重要的角色，特别是在 **Promise 的解析 (resolution)** 和 **互操作性 (interoperability)** 方面。\n\n1. **Promise 的解析 (Resolution):**\n\n   当一个 Promise 被解析 (resolve) 时，`resolve()` 函数可以接收一个值。这个值可以是：\n\n   * **普通值 (primitive values, objects):**  Promise 直接进入 `fulfilled` 状态，并将这个值作为成功的结果。\n   * **另一个 Promise 对象:**  Promise 会 \"跟随\" 这个 Promise 对象的状态。如果传入的 Promise 成功，则当前 Promise 也成功，并且结果值与传入的 Promise 相同。如果传入的 Promise 失败，则当前 Promise 也失败，并且失败原因与传入的 Promise 相同。\n   * **Thenable 对象:**  **关键点来了！**  如果 `resolve()` 接收到一个 Thenable 对象，Promise 的解析过程会更加复杂。Promise 会尝试 \"展开\" (unwrap) 这个 Thenable 对象，并根据 Thenable 对象的 `then` 方法的行为来决定自身的状态。\n\n2. **互操作性 (Interoperability):**\n\n   Thenable 对象允许不同的 Promise 实现 (例如，来自不同库或环境的 Promise) 能够互相操作。 只要一个对象符合 Thenable 规范 (拥有符合规范的 `then` 方法)，Promise 就可以识别并与其交互，即使这个对象不是原生的 `Promise` 实例。 这对于 JavaScript 生态系统的兼容性和灵活性非常重要。\n\n**Thenable 对象如何工作？ Promise 如何处理 Thenable 对象？**\n\n当 Promise 的 `resolve()` 函数接收到一个 Thenable 对象时，Promise 的解析过程会经历以下步骤：\n\n1. **检查 Thenable 对象:** Promise 会检查传入的对象是否是一个 Thenable 对象，即它是否有一个 `then` 方法。\n2. **尝试获取 `then` 方法:**  Promise 会尝试获取 Thenable 对象的 `then` 方法。如果获取 `then` 方法的过程中发生错误（例如，`then` 是一个属性，但尝试访问时抛出异常），Promise 会立即进入 `rejected` 状态，并将这个错误作为拒绝原因。\n3. **调用 `then` 方法:** 如果成功获取到 `then` 方法，Promise 会**异步地**调用这个 `then` 方法，并传入两个新的回调函数：`resolvePromise` 和 `rejectPromise`。\n   * `resolvePromise`:  当 Thenable 对象的操作成功时，应该调用 `resolvePromise`，并传入成功的值。\n   * `rejectPromise`:  当 Thenable 对象的操作失败时，应该调用 `rejectPromise`，并传入失败的原因。\n4. **Thenable 对象的 `then` 方法的责任:**  Thenable 对象的 `then` 方法需要按照 Promise 规范来处理 `resolvePromise` 和 `rejectPromise` 回调。它应该：\n   * 在操作成功时调用 `resolvePromise`。\n   * 在操作失败时调用 `rejectPromise`。\n   * 确保 `resolvePromise` 和 `rejectPromise` 是异步调用的 (通常使用微任务队列，例如 `queueMicrotask` 或 `Promise.resolve().then(...)`)。\n   * 妥善处理异常，避免重复调用 `resolvePromise` 或 `rejectPromise`，并避免在 `resolvePromise` 或 `rejectPromise` 之后再次调用它们。\n\n**Thenable 对象的例子:**\n\n下面是一个自定义的 Thenable 对象的例子，它模拟了一个异步操作：\n\n```javascript\nclass MyThenable {\n  constructor(value) {\n    this._value = value;\n  }\n\n  then(onFulfilled, onRejected) {\n    return new Promise((resolve, reject) => {\n      setTimeout(() => {\n        if (typeof onFulfilled === 'function') {\n          try {\n            const result = onFulfilled(this._value);\n            resolve(result); // 将 onFulfilled 的结果 resolve 新的 Promise\n          } catch (error) {\n            reject(error); // 如果 onFulfilled 抛出错误，则 reject 新的 Promise\n          }\n        } else {\n          resolve(this._value); // 如果 onFulfilled 不是函数，直接 resolve\n        }\n      }, 1000); // 模拟 1 秒的异步操作\n    });\n  }\n}\n\nfunction createThenable(value) {\n  return new MyThenable(value);\n}\n\nPromise.resolve(createThenable(\"Hello Thenable!\"))\n  .then(result => {\n    console.log(\"Promise resolved with:\", result); // 输出: Promise resolved with: Hello Thenable!\n  });\n```\n\n在这个例子中：\n\n* `MyThenable` 类创建了一个 Thenable 对象，它有一个 `then` 方法。\n* `createThenable` 函数用于创建 `MyThenable` 实例。\n* `Promise.resolve(createThenable(\"Hello Thenable!\"))`  使用 `Promise.resolve()` 来创建一个 Promise，并将 `createThenable(\"Hello Thenable!\")` 返回的 Thenable 对象作为参数传递给 `resolve()`。\n* 当 Promise 解析到 `MyThenable` 对象时，它会调用 `MyThenable` 对象的 `then` 方法。\n* `MyThenable` 的 `then` 方法模拟了一个异步操作，并在 1 秒后将 `_value` 传递给 `onFulfilled` 回调 (如果提供了的话)，并将 `onFulfilled` 的结果 (或原始 `_value` 如果 `onFulfilled` 不存在) `resolve` 了新的 Promise。\n* 最终，Promise 链正常执行，并输出了 \"Promise resolved with: Hello Thenable!\"，证明了 Promise 成功地处理了 Thenable 对象。\n\n**Thenable 对象 vs. Promise 对象**\n\n* **Promise 对象** 是原生的 JavaScript 对象，由 `new Promise()` 创建，遵循完整的 Promise 规范。\n* **Thenable 对象** 是一种更广泛的概念，指的是任何拥有符合 Promise 规范的 `then` 方法的对象。 Thenable 对象可以是 Promise 对象，也可以是自定义的对象，甚至是来自其他 Promise 实现库的对象。\n\n**关键区别:**\n\n* **类型:** Promise 对象是 `Promise` 类型的实例。 Thenable 对象可以是任何类型的对象，只要它有 `then` 方法。\n* **创建方式:** Promise 对象通过 `new Promise()` 创建。 Thenable 对象可以是任何方式创建的，关键是实现 `then` 方法。\n* **规范:** Promise 对象必须完全遵循 Promise 规范。 Thenable 对象只需要拥有一个符合规范的 `then` 方法，其他方面可以自由定义。\n\n**总结**\n\nThenable 对象是 JavaScript Promise 中一个重要的概念。它们允许 Promise 与其他 Promise 实现或自定义的异步对象进行互操作，并且是 Promise 解析过程中的关键环节。 理解 Thenable 对象有助于更深入地理解 Promise 的工作原理，以及如何构建更灵活、更健壮的异步代码。 记住，关键在于 **`then` 方法** 的存在和行为，而不是对象本身是否是 `Promise` 的实例。", "degree": 1, "nameLength": 8}, "position": {"x": 250, "y": 608.65}, "group": "nodes", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": false, "classes": "note protected"}], "edges": [{"data": {"id": "core:this.state&this.setState.md->core:函数式更新 Functional Updates.md1", "source": "core:this.state&this.setState.md", "target": "core:函数式更新 Functional Updates.md", "context": "[[函数式更新 Functional Updates]]：支持基于先前状态计算新状态\n                \n---\n\n\t* 函数式更新器的主要目的是：[[函数式更新 Functional Updates]]", "edgeCount": 2, "line": 11, "start": 0, "end": 28}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React 17 Batching.md->core:函数式更新 Functional Updates.md1", "source": "core:React 17 Batching.md", "target": "core:函数式更新 Functional Updates.md", "context": "在异步代码中尽量使用[[函数式更新 Functional Updates]] 保证", "edgeCount": 1, "line": 151, "start": 10, "end": 38}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React.State.md->core:this.state&this.setState.md1", "source": "core:React.State.md", "target": "core:this.state&this.setState.md", "context": "### **四、 类组件中的 State (使用 `this.state` 和 `this.setState`)** [[this.state&this.setState]]", "edgeCount": 1, "line": 80, "start": 59, "end": 87}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:this.state&this.setState.md->core:浅合并 Shallow Merge.md1", "source": "core:this.state&this.setState.md", "target": "core:浅合并 Shallow Merge.md", "context": "[[浅合并 Shallow Merge]] 自动将新状态与现有状态合并\n                \n---\n\n*   **浅合并 (Shallow Merge)：**  当使用对象形式的 `updater` 时，`this.setState()` 执行的是浅合并。这意味着，如果你只更新了状态对象的一部分属性，其他属性会保持不变。但是，如果状态对象中包含嵌套对象，浅合并只会合并顶层属性，嵌套对象会被替换而不是合并。在更新嵌套对象时，需要小心处理，通常需要手动合并之前的状态对象。[[浅合并 Shallow Merge]]", "edgeCount": 2, "line": 9, "start": 0, "end": 21}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React.组件.md->core:React.State.md1", "source": "core:React.组件.md", "target": "core:React.State.md", "context": "2.  **State (状态)：** [[React.State]]", "edgeCount": 1, "line": 80, "start": 20, "end": 35}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:JSX.md->core:表达式.md1", "source": "core:JSX.md", "target": "core:表达式.md", "context": "- **表达式嵌入**：用 `{}` 包裹 JavaScript 表达式，支持变量、函数调用等动态内容。 (`{}`包裹::[[表达式]])  ", "edgeCount": 1, "line": 17, "start": 62, "end": 69}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React 基础概念 (Fundamentals).md->core:React.组件.md1", "source": "core:React 基础概念 (Fundamentals).md", "target": "core:React.组件.md", "context": "2.  **组件 (Components):**[[React.组件]]", "edgeCount": 1, "line": 7, "start": 24, "end": 36}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:CSR.md->core:React.md1", "source": "core:CSR.md", "target": "core:React.md", "context": "浏览器端执行 JavaScript 生成页面内容，例如 [[React]], [[Vue]], [[Angular]]", "edgeCount": 1, "line": 11, "start": 28, "end": 37}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React Hooks.md->core:useContext.md1", "source": "core:React Hooks.md", "target": "core:useContext.md", "context": "    * `useContext`:  消费 React Context 的值。[[useContext]]", "edgeCount": 1, "line": 20, "start": 41, "end": 55}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:JSX.md->core:虚拟DOM.md1", "source": "core:JSX.md", "target": "core:虚拟DOM.md", "context": "- [生成::[[虚拟DOM]]]", "edgeCount": 1, "line": 9, "start": 7, "end": 16}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Context API.md->core:useContext.md1", "source": "core:Context API.md", "target": "core:useContext.md", "context": "   用于在组件中读取 Context 中的数据，传统方式为 `<Context.Consumer>`，现代方式为 `useContext(Context)` Hook。 [[useContext]]", "edgeCount": 1, "line": 11, "start": 86, "end": 100}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React.md->core:React 基础概念 (Fundamentals).md1", "source": "core:React.md", "target": "core:React 基础概念 (Fundamentals).md", "context": "[[React 基础概念 (Fundamentals)]]", "edgeCount": 1, "line": 13, "start": 0, "end": 29}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React 基础概念 (Fundamentals).md->core:JSX.md1", "source": "core:React 基础概念 (Fundamentals).md", "target": "core:JSX.md", "context": "1.  **[[JSX]] (JavaScript XML):**", "edgeCount": 1, "line": 1, "start": 6, "end": 13}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:JSX.md->core:Babel.md1", "source": "core:JSX.md", "target": "core:Babel.md", "context": "- [借助 Babel的工具转换::[[Babel]]]", "edgeCount": 1, "line": 10, "start": 18, "end": 27}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:AST.md->core:Babel.md1", "source": "core:AST.md", "target": "core:Babel.md", "context": "   - **[[Babel]]**：将 ES6+ 代码转换为 ES5，通过修改 AST 实现语法降级。  ", "edgeCount": 1, "line": 85, "start": 7, "end": 16}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Babel.md->core:AST.md1", "source": "core:Babel.md", "target": "core:AST.md", "context": "1. **解析（Parsing）** ：将源代码转换为 **抽象语法树（AST）**  。(解析为::[[AST]])", "edgeCount": 1, "line": 28, "start": 51, "end": 58}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React.组件.md->core:类组件 Class Components.md1", "source": "core:React.组件.md", "target": "core:类组件 Class Components.md", "context": "2.  **[[类组件 Class Components]]：**", "edgeCount": 1, "line": 46, "start": 6, "end": 30}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:类组件 Class Components.md->core:this.state&this.setState.md1", "source": "core:类组件 Class Components.md", "target": "core:this.state&this.setState.md", "context": "## this.setState的设计特性 [[this.state&this.setState]]", "edgeCount": 1, "line": 20, "start": 22, "end": 50}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:函数式更新 Functional Updates.md->core:闭包陷阱.md1", "source": "core:函数式更新 Functional Updates.md", "target": "core:闭包陷阱.md", "context": "- 确保你总是基于最新的状态值进行更新，避免[[闭包陷阱]]", "edgeCount": 1, "line": 19, "start": 22, "end": 30}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:批处理机制 Batching.md->core:批处理机制 Batching.md1", "source": "core:批处理机制 Batching.md", "target": "core:批处理机制 Batching.md", "context": "[[#什么是 setState 批处理]]\n                \n---\n\n[[#React 17 中的批处理行为]]\n                \n---\n\n[[#React 18 中的自动批处理]]\n                \n---\n\n[[#手动控制批处理]]\n                \n---\n\n[[#批处理的好处]]\n                \n---\n\n[[#常见陷阱和问题]]\n                \n---\n\n[[#最佳实践]]\n                \n---\n\n[[#实际案例分析]]", "edgeCount": 8, "line": 1, "start": 0, "end": 21}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:批处理机制 Batching.md->core:React 17 Batching.md1", "source": "core:批处理机制 Batching.md", "target": "core:React 17 Batching.md", "context": "[[React 17 Batching]]", "edgeCount": 1, "line": 26, "start": 0, "end": 21}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:this.state&this.setState.md->core:批处理机制 Batching.md1", "source": "core:this.state&this.setState.md", "target": "core:批处理机制 Batching.md", "context": "[[批处理机制 Batching]]：状态更新可能被延迟和批处理\n                \n---\n\n*   **状态更新可能是异步的：** 为了性能优化，React 可能会将多个 `setState()` 调用 **批量处理** (batch updates)。这意味着状态更新可能是异步的。因此，在 `setState()` 调用之后立即访问 `this.state`，可能仍然会得到 **旧的** 状态值。 如果你的新状态值依赖于之前的状态值，**务必使用函数形式的 `updater`**，以确保你操作的是最新的状态。[[批处理机制 Batching]]", "edgeCount": 2, "line": 10, "start": 0, "end": 18}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React 基础概念 (Fundamentals).md->core:虚拟DOM.md1", "source": "core:React 基础概念 (Fundamentals).md", "target": "core:虚拟DOM.md", "context": "3.  **虚拟 DOM (Virtual DOM):** [[虚拟DOM|virtual DOM]]", "edgeCount": 1, "line": 17, "start": 30, "end": 51, "alias": "virtual DOM"}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:闭包陷阱.md->core:函数式更新 Functional Updates.md1", "source": "core:闭包陷阱.md", "target": "core:函数式更新 Functional Updates.md", "context": "**1. 使用函数式更新** [[函数式更新 Functional Updates]]", "edgeCount": 1, "line": 85, "start": 15, "end": 43}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:批处理机制 Batching.md->core:React 18 AutoBatching.md1", "source": "core:批处理机制 Batching.md", "target": "core:React 18 AutoBatching.md", "context": "[[React 18 AutoBatching]]", "edgeCount": 1, "line": 77, "start": 0, "end": 25}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:React 18 AutoBatching.md->core:Promise.md1", "source": "core:React 18 AutoBatching.md", "target": "core:Promise.md", "context": "  - **包括**: `Promise` 的 `resolve` 回调, `setTimeout`, 原生事件处理函数, 以及任何在**同一个事件循环**中发生的更新。[[Promise]]", "edgeCount": 1, "line": 31, "start": 85, "end": 96}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:回调地狱 callback hell.md->core:Promise.md1", "source": "core:回调地狱 callback hell.md", "target": "core:Promise.md", "context": "  - **[[Promise]] (承诺)**: **(重点)**", "edgeCount": 1, "line": 67, "start": 6, "end": 17}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:异步.md->core:Promise.md1", "source": "core:异步.md", "target": "core:Promise.md", "context": "[[Promise]]", "edgeCount": 1, "line": 6, "start": 0, "end": 11}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Async_Await.md->core:Promise.md1", "source": "core:Async_Await.md", "target": "core:Promise.md", "context": "(基于::[[Promise]])", "edgeCount": 1, "line": 2, "start": 5, "end": 16}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:回调地狱 callback hell.md->core:Async_Await.md1", "source": "core:回调地狱 callback hell.md", "target": "core:Async_Await.md", "context": "  - **Async/Await (异步/等待)**: **(重点)** [[Async_Await]]", "edgeCount": 1, "line": 88, "start": 38, "end": 53}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Microtask Queue.md->core:Promise.md1", "source": "core:Microtask Queue.md", "target": "core:Promise.md", "context": "    - **`Promise` 回调**：包括 `then/catch/finally`。 [[Promise]]", "edgeCount": 1, "line": 13, "start": 48, "end": 59}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:动态导入 Dynamic Import.md->core:Promise.md1", "source": "core:动态导入 Dynamic Import.md", "target": "core:Promise.md", "context": "- **Promise 处理**：处理模块加载的成功和失败状态 [[Promise]]", "edgeCount": 1, "line": 10, "start": 32, "end": 43}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:异步.md->core:Async_Await.md1", "source": "core:异步.md", "target": "core:Async_Await.md", "context": "[[As<PERSON>_Await]]", "edgeCount": 1, "line": 4, "start": 0, "end": 15}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Promise.md->core:Promise 的基本概念和使用1", "source": "core:Promise.md", "target": "core:Promise 的基本概念和使用", "context": "[[Promise 的基本概念和使用]]", "edgeCount": 1, "line": 10, "start": 0, "end": 20}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Promise.md->core:Promise 的基本概念和使用.md1", "source": "core:Promise.md", "target": "core:Promise 的基本概念和使用.md", "context": "[[Promise 的基本概念和使用]]", "edgeCount": 1, "line": 10, "start": 0, "end": 20}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Promise 的基本概念和使用.md->core:回调地狱 callback hell.md1", "source": "core:Promise 的基本概念和使用.md", "target": "core:回调地狱 callback hell.md", "context": "* **[解决::[[回调地狱 Callback Hell]]]:**  ", "edgeCount": 1, "line": 28, "start": 9, "end": 31}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Promise 的基本概念和使用->core:Promise.resolve().md1", "source": "core:Promise 的基本概念和使用", "target": "core:Promise.resolve().md", "context": "* **`resolve(value)`:**  一个函数，用于将 Promise 的状态从 Pending 变为 Fulfilled，并将 `value` 作为成功的结果传递出去。[[Promise.resolve()]]", "edgeCount": 1, "line": 39, "start": 91, "end": 112}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Promise 的基本概念和使用.md->core:Promise.resolve().md1", "source": "core:Promise 的基本概念和使用.md", "target": "core:Promise.resolve().md", "context": "* **`resolve(value)`:**  一个函数，用于将 Promise 的状态从 Pending 变为 Fulfilled，并将 `value` 作为成功的结果传递出去。[[Promise.resolve()]]", "edgeCount": 1, "line": 39, "start": 91, "end": 112}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Promise 的基本概念和使用->core:回调地狱 callback hell.md1", "source": "core:Promise 的基本概念和使用", "target": "core:回调地狱 callback hell.md", "context": "* **[解决::[[回调地狱 Callback Hell]]]:**  ", "edgeCount": 1, "line": 28, "start": 9, "end": 31}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Promise.resolve().md->core:Thenable Resolution 解析.md1", "source": "core:Promise.resolve().md", "target": "core:Thenable Resolution 解析.md", "context": "#### [[Thenable Resolution 解析]]", "edgeCount": 1, "line": 81, "start": 5, "end": 31}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}, {"data": {"id": "core:Thenable Resolution 解析.md->core:Thenable.md1", "source": "core:Thenable Resolution 解析.md", "target": "core:Thenable.md", "context": "* **什么是 Thenable 对象？**  Thenable 对象是指任何具有 `then` 方法的对象。这个 `then` 方法期望接收两个参数：`onFulfilled` 和 `onRejected`，并且行为应该类似于 Promise 的 `.then()` 方法。  简而言之，它看起来像一个 Promise，但可能不是真正的 Promise 对象。[[Thenable]]", "edgeCount": 1, "line": 3, "start": 181, "end": 193}, "position": {"x": 0, "y": 0}, "group": "edges", "removed": false, "selected": false, "selectable": true, "locked": false, "grabbable": true, "pannable": true, "classes": "inline"}]}, "style": [{"selector": "node", "style": {"background-color": "rgb(153,153,153)", "color": "rgb(209,209,209)", "font-family": "Helvetica Neue", "text-valign": "bottom", "shape": "ellipse", "border-width": "0px", "text-wrap": "wrap", "min-zoomed-font-size": "8px"}}, {"selector": "node[name]", "style": {"label": "data(name)"}}, {"selector": "node[degree]", "style": {"width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-opacity": "mapData(degree, 0, 60, 0.7, 1)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node:selected", "style": {"background-blacken": "0.3", "font-weight": "bold"}}, {"selector": "node:selected[degree]", "style": {"border-width": "mapData(degree, 0, 60, 1, 3)"}}, {"selector": ".dangling", "style": {"background-color": "rgb(89,89,89)"}}, {"selector": ".image", "style": {"shape": "round-rectangle", "width": "50px", "height": "50px", "background-opacity": "0", "background-image": "data(resource_url)", "background-image-crossorigin": "anonymous", "background-image-opacity": "1", "background-fit": "contain", "font-size": "0px", "background-clip": "node"}}, {"selector": ".image.note", "style": {"font-size": "mapData(degree, 0, 60, 5, 11)"}}, {"selector": "edge", "style": {"line-color": "rgb(63,63,63)", "loop-sweep": "-50deg", "loop-direction": "-45deg", "width": "0.7px", "target-arrow-shape": "vee", "target-arrow-fill": "filled", "target-arrow-color": "rgb(63,63,63)", "arrow-scale": "0.55", "font-size": "6px", "font-family": "Helvetica Neue", "color": "rgb(209,209,209)", "curve-style": "straight"}}, {"selector": "edge[edgeCount]", "style": {"width": "mapData(edgeCount, 1, 50, 0.55, 3)", "arrow-scale": "mapData(edgeCount, 1, 50, 0.35, 1.5)"}}, {"selector": "edge:selected", "style": {"width": "0.7px", "font-weight": "bold", "line-color": "rgb(66,154,136)"}}, {"selector": ":loop", "style": {"display": "none"}}, {"selector": "edge[type]", "style": {"label": "data(type)"}}, {"selector": ".inactive-node, .unhover", "style": {"opacity": "0.3"}}, {"selector": "node.active-node, node.hover", "style": {"background-color": "rgb(66,154,136)", "font-weight": "bold", "border-width": "0.4px", "border-color": "rgb(73,171,152)", "opacity": "1"}}, {"selector": "edge.hover, edge.connected-active-node, edge.connected-hover", "style": {"width": "1px", "opacity": "1"}}, {"selector": "edge.hover, edge.connected-hover", "style": {"font-weight": "bold", "line-color": "rgb(66,154,136)", "target-arrow-color": "rgb(66,154,136)"}}, {"selector": "node.pinned", "style": {"border-style": "dotted", "border-width": "2px"}}, {"selector": "node.hard-filtered, node.filtered", "style": {"display": "none"}}, {"selector": "node.global-0", "style": {"background-color": "rgb(89,89,89)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-1", "style": {"background-color": "rgb(153,153,153)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-2", "style": {"background-color": "rgb(0,137,186)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-3", "style": {"background-color": "rgb(214,93,177)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-4", "style": {"background-color": "rgb(255,150,113)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-5", "style": {"background-color": "rgb(255,199,95)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-6", "style": {"background-color": "rgb(255,111,145)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-7", "style": {"background-color": "rgb(249,248,113)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-8", "style": {"background-color": "rgb(44,115,210)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-9", "style": {"background-color": "rgb(0,130,193)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-10", "style": {"background-color": "rgb(163,106,170)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": "node.global-11", "style": {"background-color": "rgb(76,154,82)", "shape": "ellipse", "background-fit": "contain", "width": "mapData(degree, 0, 60, 5, 35)", "height": "mapData(degree, 0, 60, 5, 35)", "font-size": "mapData(degree, 0, 60, 5, 11)", "text-max-width": "mapData(degree, 0, 60, 65px, 100px)"}}, {"selector": ".tag-paper", "style": {"shape": "rectangle", "width": "50px", "height": "45px", "font-size": "5px", "text-valign": "center", "text-max-width": "45px", "text-opacity": "1"}}, {"selector": "edge[edgeCount]", "style": {"width": "mapData(edgeCount, 1, 15, 0.5, 3)", "line-opacity": "mapData(edgeCount, 1, 15, 0.5, 0.9)"}}, {"selector": "edge.inline", "style": {"label": "data(context)", "text-opacity": "0.2", "font-size": "2px", "font-weight": "200", "text-wrap": "wrap", "text-valign": "center", "text-max-width": "35px", "text-overflow-wrap": "anywhere"}}, {"selector": "edge.inline.hover", "style": {"text-opacity": "1"}}, {"selector": "node[title]", "style": {"label": "data(title)"}}, {"selector": "node[color]", "style": {"background-color": "data(color)"}}, {"selector": "node[shape]", "style": {"shape": "data(shape)"}}, {"selector": "node[width]", "style": {"width": "data(width)"}}, {"selector": "node[height]", "style": {"height": "data(height)"}}, {"selector": "node[image]", "style": {"background-image": "data(image)"}}], "data": {}, "zoomingEnabled": true, "userZoomingEnabled": true, "zoom": 3.8965102286401927, "minZoom": 0.3, "maxZoom": 10, "panningEnabled": true, "userPanningEnabled": true, "pan": {"x": -414.80685920577616, "y": -794.8880866425993}, "boxSelectionEnabled": true, "renderer": {"name": "canvas"}, "wheelSensitivity": 0.99}