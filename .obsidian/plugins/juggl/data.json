{"splitDirection": "vertical", "typedLinkPrefix": ":", "useImgServer": false, "imgServerPort": 3837, "debug": false, "globalStyleGroups": [{"filter": "class:dangling", "color": "rgb(89, 89, 89)", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "class:file -class:image", "color": "rgb(153, 153, 153)", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#Node12", "color": "#0089BA", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#fixme", "color": "#D65DB1", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#utf8", "color": "#FF9671", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#relation", "color": "#FFC75F", "shape": "ellipse", "icon": {"name": "Relation One To One", "path": "M22 15V17H19V19H17V17H11V9H7V11H5V9H2V7H5V5H7V7H13V15H17V13H19V15Z", "color": "white"}, "showInPane": true, "show": true, "size": 0.461149009146341}, {"filter": "tag:#utf16be", "color": "#FF6F91", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#ucs2", "color": "#F9F871", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#base64", "color": "#2C73D2", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#latin1", "color": "#0082C1", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#binary", "color": "#A36AAA", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}, {"filter": "tag:#hex", "color": "#4C9A52", "shape": "ellipse", "icon": {"name": "No icon", "path": "", "color": "white"}, "showInPane": true, "show": true, "size": 1}], "globalGraphRibbon": true, "graphSettings": {"animateLayout": true, "autoAddNodes": true, "autoExpand": false, "autoZoom": true, "coreStore": "Obsidian", "expandInitial": true, "fdgdLayout": "d3-force", "filter": "", "height": "100%", "hoverEdges": true, "layout": {"name": "dagre", "animate": true, "animationDuration": 500, "spacingFactor": 0.5, "fit": false, "padding": 30, "nodeDimensionsIncludeLabels": true, "avoidOverlap": true}, "limit": 10000, "mergeEdges": true, "mode": "workspace", "navigator": false, "openWithShift": true, "readContent": true, "styleGroups": [], "toolbar": true, "width": "100%", "zoomSpeed": 0.99}, "embedSettings": {"animateLayout": true, "autoAddNodes": false, "autoExpand": false, "autoZoom": false, "coreStore": "Obsidian", "expandInitial": true, "fdgdLayout": "d3-force", "filter": "", "height": "400px", "hoverEdges": true, "layout": "force-directed", "limit": 1000, "mergeEdges": true, "mode": "local", "navigator": false, "openWithShift": true, "readContent": true, "styleGroups": [], "toolbar": false, "width": "100%", "zoomSpeed": 0.99}, "globalGraphSettings": {"animateLayout": true, "autoAddNodes": true, "autoExpand": false, "autoZoom": true, "coreStore": "Obsidian", "expandInitial": false, "fdgdLayout": "d3-force", "filter": "-class:dangling -class:file", "height": "100%", "width": "100%", "limit": 10000, "hoverEdges": true, "layout": "force-directed", "mergeEdges": true, "mode": "workspace", "navigator": false, "openWithShift": true, "readContent": false, "styleGroups": [], "toolbar": true, "zoomSpeed": 0.99}}