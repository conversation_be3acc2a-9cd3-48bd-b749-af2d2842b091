/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var y=Object.defineProperty;var b=Object.getOwnPropertyDescriptor;var C=Object.getOwnPropertyNames;var E=Object.prototype.hasOwnProperty;var k=(f,l)=>{for(var t in l)y(f,t,{get:l[t],enumerable:!0})},S=(f,l,t,r)=>{if(l&&typeof l=="object"||typeof l=="function")for(let s of C(l))!E.call(f,s)&&s!==t&&y(f,s,{get:()=>l[s],enumerable:!(r=b(l,s))||r.enumerable});return f};var N=f=>S(y({},"__esModule",{value:!0}),f);var P={};k(P,{default:()=>v});module.exports=N(P);var h=require("obsidian");function m(f,l){let t=Object.keys(l).map(r=>D(f,r,l[r]));return t.length===1?t[0]:function(){t.forEach(r=>r())}}function D(f,l,t){let r=f[l],s=f.hasOwnProperty(l),n=s?r:function(){return Object.getPrototypeOf(f)[l].apply(this,arguments)},i=t(n);return r&&Object.setPrototypeOf(i,r),Object.setPrototypeOf(e,i),f[l]=e,a;function e(...o){return i===n&&f[l]===e&&a(),i.apply(this,o)}function a(){f[l]===e&&(s?f[l]=n:delete f[l]),i!==n&&(i=n,Object.setPrototypeOf(e,r||Function))}}var v=class extends h.Plugin{async onload(){this.registerCustomCommands(),this.registerCustomSuggester(),this.registerCanvasAutoLink()}registerCustomCommands(){this.addCommand({id:"link-between-selection-nodes",name:"Link Between Selection Nodes",checkCallback:t=>{let r=this.app.workspace.getActiveViewOfType(h.ItemView);if((r==null?void 0:r.getViewType())==="canvas"){if(!t){let s=r.canvas,n=s.selection,i=s.getData(),e=Array.from(n).filter(c=>(c==null?void 0:c.filePath)!==void 0);if(e.length===0)return;let a=this.app.metadataCache.resolvedLinks,o=[];e.forEach(c=>{let u=Object.keys(a[c.filePath]);for(let d=0;d<e.length;d++)if(u.includes(e[d].filePath)&&c!==e[d]){let p=this.createEdge(c,e[d]);if(i.edges.some(g=>g.fromNode===p.fromNode&&g.toNode===p.toNode))continue;o.push(p)}}),i.edges=[...i.edges,...o],s.setData(i),s.requestSave()}return!0}}})}registerCustomSuggester(){this.registerEditorSuggest(new w(this))}registerCanvasAutoLink(){let t=(0,h.debounce)(async e=>{var c;if(!e.to.node.filePath||!((c=e.from.node)!=null&&c.filePath)&&!Object.hasOwn(e.from.node,"text"))return;let a=this.app.vault.getFileByPath(e.to.node.filePath);if(!a)return;let o=this.app.fileManager.generateMarkdownLink(a,e.canvas.view.file.path);if(e.from.node.filePath){let u=this.app.vault.getFileByPath(e.from.node.filePath);if(!u)return;let d=await this.app.vault.cachedRead(u);await this.app.vault.append(u,`
${o}`)}else{let u=e.from.node;u.setText(`${u.text}
${o}`),e.canvas.requestSave()}},1e3),r=async(e,a)=>{var p;if(!a.to.node.filePath||!((p=a.from.node)!=null&&p.filePath)&&!Object.hasOwn(a.from.node,"text"))return;let o=a.to.node,c=a.from.node,u=this.app.vault.getFileByPath(o.filePath);if(!u)return;let d=this.app.fileManager.generateMarkdownLink(u,a.to.node.filePath);if(c!=null&&c.filePath){let g=this.app.vault.getFileByPath(c.filePath);if(!g)return;let x=(await this.app.vault.read(g)).replaceAll(d,"");await this.app.vault.modify(g,x)}else{let g=a.from.node;g.setText(g.text.replaceAll(d,"")),e.requestSave()}},s=e=>{this.patchedEdge=!0,m(e.constructor.prototype,{update:a=>function(...o){let c=a.call(this,...o);return t(this),c}})},n=this,i=()=>{var c;let e=(c=this.app.workspace.getLeavesOfType("canvas")[0])==null?void 0:c.view;if(!e)return!1;let a=e.canvas;if(!a)return!1;let o=a.edges.values().next().value;if(o)return this.patchedEdge=!0,s(o),m(a.constructor.prototype,{removeEdge:u=>function(d){let p=u.call(this,d);return r(this,d),p}}),!0;m(a.constructor.prototype,{addEdge:u=>function(d){let p=u.call(this,d);return n.patchedEdge||s(d),p},deleteEdge:u=>function(d){let p=u.call(this,d);return r(this,d),p}})};this.app.workspace.onLayoutReady(()=>{if(!i()){let e=this.app.workspace.on("layout-change",()=>{i()&&this.app.workspace.offref(e)});this.registerEvent(e)}})}createEdge(t,r){return{id:(i=>{let e=[];for(let a=0;a<i;a++)e.push((16*Math.random()|0).toString(16));return e.join("")})(16),fromSide:"right",fromNode:t.id,toSide:"left",toNode:r.id}}createEdgeBasedOnNodes(t,r,s,n){let i=u=>{let d=[];for(let p=0;p<u;p++)d.push((16*Math.random()|0).toString(16));return d.join("")},e,a,o;switch(n){case"left":a="left",o="right";break;case"right":a="right",o="left";break;case"top":a="top",o="bottom";break;case"bottom":a="bottom",o="top";break;case"top-left":a="top",o="right";break;case"top-right":a="top",o="left";break;case"bottom-left":a="bottom",o="right";break;case"bottom-right":a="bottom",o="left";break}e={id:i(16),fromSide:a,fromNode:t.id,toSide:o,toNode:r.id};let c=s.getData();if(c.edges.some(u=>u.fromNode===e.fromNode&&u.toNode===e.toNode)){new h.Notice("Edge already exists between nodes");return}c.edges=[...c.edges,e],s.setData(c),s.requestSave()}onunload(){}},w=class extends h.EditorSuggest{constructor(t){super(t.app);this.nodes=[];this.plugin=t,this.setInstructions([{command:"Ctrl/Cmd + Enter",purpose:"Link to node and generate link"}]),this.scope.register(["Mod"],"Enter",r=>{r.preventDefault(),this.suggestions.useSelectedItem(r)})}getNodes(){let t=this.plugin.app.workspace.getActiveViewOfType(h.ItemView);if((t==null?void 0:t.getViewType())==="canvas"){this.canvas=t.canvas;let r=this.canvas.getData().nodes;return Array.from(r.values())}return[]}onTrigger(t,r,s){this.lineContents=r.getLine(t.line).toLowerCase();let n=this.lineContents.slice(0,t.ch),i=this.lineContents.slice(t.ch);this.end=i.indexOf("}}");let e=n.lastIndexOf("{{"),a=n.lastIndexOf("}}");if(!(e>a&&a===-1))return null;let o=n.slice(e+2);return this.nodes=this.getNodes(),this.original=Array.from(this.canvas.selection)[0],{end:t,start:{ch:e,line:t.line},query:o}}getSuggestions(t){let r=t.query.toLowerCase()||"";return this.fuzzySearch=(0,h.prepareFuzzySearch)(r),this.nodes.filter(n=>{var i,e;switch(n.type){case"text":return n.id===this.original.id||n.text.trim()===""?!1:this.fuzzySearch(n.text.toLowerCase());case"file":return this.fuzzySearch(n.file.toLowerCase());case"group":return(i=n.label)!=null&&i.trim()?this.fuzzySearch((e=n.label)==null?void 0:e.toLowerCase()):!1;case"link":return n.url.trim().length===0?!1:this.fuzzySearch(n.url.toLowerCase())}})}renderSuggestion(t,r){let s,n;switch(s=r.createDiv({cls:"ltn-suggester-container"}),t.type){case"text":n=s.createDiv({cls:"ltn-suggester-icon"}),(0,h.setIcon)(n,"sticky-note"),s.createDiv({cls:"ltn-text-node"}).setText(`${t.text}`);break;case"file":n=s.createDiv({cls:"ltn-suggester-icon"}),(0,h.setIcon)(n,"file-text"),s.createDiv({cls:"ltn-file-node"}).setText(`${t.file}`);break;case"group":n=s.createDiv({cls:"ltn-suggester-icon"}),(0,h.setIcon)(n,"box-select"),s.createDiv({cls:"ltn-group-node"}).setText(`${t.label}`);break;case"link":n=s.createDiv({cls:"ltn-suggester-icon"}),(0,h.setIcon)(n,"link"),s.createDiv({cls:"ltn-link-node"}).setText(`${t.url}`);break}}selectSuggestion(t,r){if(this.context){let s=this.context.editor,n=(r.ctrlKey||r.metaKey)&&t.type==="file"?`[[${t.file}]]`:"";s.replaceRange(n,this.context.start,this.end===0?{ch:this.context.end.ch+2,line:this.context.end.line}:this.context.end),s.setCursor({line:this.context.end.line,ch:this.context.end.ch+(n==null?void 0:n.length)-2});let i=this.canvas.nodes.get(t.id),e=this.getDirectionText(this.original.x,this.original.y,i.x,i.y);this.plugin.createEdgeBasedOnNodes(this.original,i,this.canvas,e),this.close()}}getDirectionText(t,r,s,n){let i=t-s,e=r-n,a=Math.atan2(e,i)*180/Math.PI;switch(Math.round((a+180)/45)%8){case 0:return"right";case 1:return"bottom-right";case 2:return"bottom";case 3:return"bottom-left";case 4:return"left";case 5:return"top-left";case 6:return"top";case 7:return"top-right";default:return"right"}}};

/* nosourcemap */