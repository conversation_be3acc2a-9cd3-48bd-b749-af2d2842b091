/* @settings

name: Dynamic Outline
id: obsidian-dynamic-outline
settings:
	-
		id: active-heading
		title: 'Active heading'
		type: heading
		level: 2
		collapsed: true
	-
        id: dynamic-outline-highlight-font-weight
        title: 'Active heading font weight'
        description: 'Font weight of active heading'
        type: variable-select
        allowEmpty: false
        default: var(--font-normal)
        options:
            -
                label: Normal
                value: var(--font-normal)
            -
                label: Semibold
                value: var(--font-semibold)
            -
                label: Bold
                value: var(--font-bold)
    -
        id: dynamic-outline-active-heading-pill-color
        title: 'Active heading indication color'
        description: 'Color of active heading pill'
        type: variable-select
        allowEmpty: false
        default: var(--text-accent)
        options:
            -
                label: Accent
                value: var(--text-accent)
            -
                label: Mono
                value: var(--text-normal)
    -
        id: dynamic-outline-active-heading-background
        title: 'Active heading background color'
        description: 'Background color of the active heading'
        type: variable-select
        allowEmpty: false
        default: var(--background-modifier-hover)
        options:
            -
                label: Mono
                value: var(--background-modifier-hover)
            -
                label: Accent
                value: var(--dynamic-outline-background-accent)
            -
                label: None
                value: none
	-
		id: button
		title: 'Button'
		type: heading
		level: 2
		collapsed: true
	-
		id: hide-button-from-toolbar
		title: 'Hide button'
		description: 'Hide button from the toolbar'
		type: class-toggle
		default: false
	-
		id: headings
		title: 'Headings'
		type: heading
		level: 2
		collapsed: true
	-
        id: dynamic-outline-font-size
        title: 'Font size'
        description: 'Font size of the outline headings'
        type: variable-select
        default: var(--font-text-size)
        options:
            -
                label: 'Default'
                value: var(--font-text-size)
            -
                label: 'Small'
                value: var(--font-small)
            -
                label: 'Smaller'
                value: var(--font-smaller)
            -
                label: 'Smallest'
                value: var(--font-smallest)
    -
        id: dynamic-outline-li-padding-tab-size
        title: 'Tab size'
        description: 'Left padding multiplier size (in pixels). Will be applied on next outline refresh'
        type: variable-number-slider
        default: 16
        min: 0
        max: 32
        step: 1
        format: px
    -
		id: heading-level-styling
		title: 'Heading level styling'
		description: 'Add heading level styling to the outline'
		type: class-select
        allowEmpty: true
		default: none
		options:
			-
				label: "H1, H2…"
				value: heading-level-styling-1
			-
				label: "#, ##…"
				value: heading-level-styling-2
	-
		id: disable-heading-word-wrap
		title: "Disable heading word wrap"
		description: 'Prevent line breaks in headings, ensuring they are displayed on a single line without wrapping'
		type: class-toggle
		default: false
	-
		id: headings-level-1
		title: 'Headings level 1'
		type: heading
		level: 3
		collapsed: true
	-
		id: dynamic-outline-h1-text-color
		title: 'H1 text color'
		description: 'Use `--h1-color` from theme or apply monochrome color'
		markdown: true
		type: variable-select
		default: var(--h1-color)
		options:
			-
				label: Theme
				value: var(--h1-color)
			-
				label: Mono
				value: var(--text-normal)
	-
		id: dynamic-outline-h1-font-style
		title: 'H1 font style'
		description: 'Use `--h1-style` from theme or apply custom styling'
		type: variable-select
		default: var(--h1-style)
		options:
			-
				label: Theme
				value: var(--h1-style)
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: dynamic-outline-h1-font-variant
		title: 'H1 font variant'
		description: 'Use `--h1-variant` from theme of apply custom variant'
		type: variable-select
		default: var(--h1-variant)
		options:
			-
				label: Theme
				value: var(--h1-variant)
			-
				label: Normal
				value: normal
			-
				label: Small-caps
				value: small-caps
			-
				label: All small-caps
				value: all-small-caps
	-
		id: dynamic-outline-h1-font-weight
		title: 'H1 font weight'
		description: 'Use `--h1-weight` from theme or apply custom weight'
		type: variable-select
		default: var(--font-bold)
		options:
			-
				label: Theme
				value: var(--h1-weight)
			-
				label: Normal
				value: var(--font-normal)
			-
				label: Semibold
				value: var(--font-semibold)
			-
				label: Bold
				value: var(--font-bold)
	-
		id: headings-level-2
		title: 'Headings level 2'
		type: heading
		level: 3
		collapsed: true
	-
		id: dynamic-outline-h2-text-color
		title: 'H2 text color'
		description: 'Use `--h2-color` from theme or apply monochrome color'
		markdown: true
		type: variable-select
		default: var(--h2-color)
		options:
			-
				label: Theme
				value: var(--h2-color)
			-
				label: Mono
				value: var(--text-normal)
	-
		id: dynamic-outline-h2-font-style
		title: 'H2 font style'
		description: 'Use `--h2-style` from theme or apply custom styling'
		type: variable-select
		default: var(--h2-style)
		options:
			-
				label: Theme
				value: var(--h2-style)
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: dynamic-outline-h2-font-variant
		title: 'H2 font variant'
		description: 'Use `--h2-variant` from theme of apply custom variant'
		type: variable-select
		default: var(--h2-variant)
		options:
			-
				label: Theme
				value: var(--h2-variant)
			-
				label: Normal
				value: normal
			-
				label: Small-caps
				value: small-caps
			-
				label: All small-caps
				value: all-small-caps
	-
		id: dynamic-outline-h2-font-weight
		title: 'H2 font weight'
		description: 'Use `--h2-weight` from theme or apply custom weight'
		type: variable-select
		default: var(--font-normal)
		options:
			- 
				label: Theme
				value: var(--h2-weight)
			-
				label: Normal
				value: var(--font-normal)
			-
				label: Semibold
				value: var(--font-semibold)
			-
				label: Bold
				value: var(--font-bold)
	-
		id: headings-level-3
		title: 'Headings level 3'
		type: heading
		level: 3
		collapsed: true
	-
		id: dynamic-outline-h3-text-color
		title: 'H3 text color'
		description: 'Use `--h3-color` from theme or apply monochrome color'
		markdown: true
		type: variable-select
		default: var(--h3-color)
		options:
			-
				label: Theme
				value: var(--h3-color)
			-
				label: Mono
				value: var(--text-normal)
	-
		id: dynamic-outline-h3-font-style
		title: 'H3 font style'
		description: 'Use `--h3-style` from theme or apply custom styling'
		type: variable-select
		default: var(--h3-style)
		options:
			-
				label: Theme
				value: var(--h3-style)
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: dynamic-outline-h3-font-variant
		title: 'H3 font variant'
		description: 'Use `--h3-variant` from theme of apply custom variant'
		type: variable-select
		default: var(--h3-variant)
		options:
			-
				label: Theme
				value: var(--h3-variant)
			-
				label: Normal
				value: normal
			-
				label: Small-caps
				value: small-caps
			-
				label: All small-caps
				value: all-small-caps
	-
		id: dynamic-outline-h3-font-weight
		title: 'H3 font weight'
		description: 'Use `--h3-weight` from theme or apply custom weight'
		type: variable-select
		default: var(--font-normal)
		options:
			- 
				label: Theme
				value: var(--h3-weight)
			-
				label: Normal
				value: var(--font-normal)
			-
				label: Semibold
				value: var(--font-semibold)
			-
				label: Bold
				value: var(--font-bold)
	-
		id: headings-level-4
		title: 'Headings level 4'
		type: heading
		level: 3
		collapsed: true
	-
		id: dynamic-outline-h4-text-color
		title: 'H4 text color'
		description: 'Use `--h4-color` from theme or apply monochrome color'
		markdown: true
		type: variable-select
		default: var(--h4-color)
		options:
			-
				label: Theme
				value: var(--h4-color)
			-
				label: Mono
				value: var(--text-normal)
	-
		id: dynamic-outline-h4-font-style
		title: 'H4 font style'
		description: 'Use `--h4-style` from theme or apply custom styling'
		type: variable-select
		default: var(--h4-style)
		options:
			-
				label: Theme
				value: var(--h4-style)
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: dynamic-outline-h4-font-variant
		title: 'H4 font variant'
		description: 'Use `--h4-variant` from theme of apply custom variant'
		type: variable-select
		default: var(--h4-variant)
		options:
			-
				label: Theme
				value: var(--h4-variant)
			-
				label: Normal
				value: normal
			-
				label: Small-caps
				value: small-caps
			-
				label: All small-caps
				value: all-small-caps
	-
		id: dynamic-outline-h4-font-weight
		title: 'H4 font weight'
		description: 'Use `--h4-weight` from theme or apply custom weight'
		type: variable-select
		default: var(--font-normal)
		options:
			- 
				label: Theme
				value: var(--h4-weight)
			-
				label: Normal
				value: var(--font-normal)
			-
				label: Semibold
				value: var(--font-semibold)
			-
				label: Bold
				value: var(--font-bold)
	-
		id: headings-level-5
		title: 'Headings level 5'
		type: heading
		level: 3
		collapsed: true
	-
		id: dynamic-outline-h5-text-color
		title: 'H5 text color'
		description: 'Use `--h5-color` from theme or apply monochrome color'
		markdown: true
		type: variable-select
		default: var(--h5-color)
		options:
			-
				label: Theme
				value: var(--h5-color)
			-
				label: Mono
				value: var(--text-normal)
	-
		id: dynamic-outline-h5-font-style
		title: 'H5 font style'
		description: 'Use `--h5-style` from theme or apply custom styling'
		type: variable-select
		default: var(--h5-style)
		options:
			-
				label: Theme
				value: var(--h5-style)
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: dynamic-outline-h5-font-variant
		title: 'H5 font variant'
		description: 'Use `--h5-variant` from theme of apply custom variant'
		type: variable-select
		default: var(--h5-variant)
		options:
			-
				label: Theme
				value: var(--h5-variant)
			-
				label: Normal
				value: normal
			-
				label: Small-caps
				value: small-caps
			-
				label: All small-caps
				value: all-small-caps
	-
		id: dynamic-outline-h5-font-weight
		title: 'H5 font weight'
		description: 'Use `--h5-weight` from theme or apply custom weight'
		type: variable-select
		default: var(--font-normal)
		options:
			- 
				label: Theme
				value: var(--h5-weight)
			-
				label: Normal
				value: var(--font-normal)
			-
				label: Semibold
				value: var(--font-semibold)
			-
				label: Bold
				value: var(--font-bold)
	-
		id: headings-level-6
		title: 'Headings level 6'
		type: heading
		level: 3
		collapsed: true
	-
		id: dynamic-outline-h6-text-color
		title: 'H6 text color'
		description: 'Use `--h6-color` from theme or apply monochrome color'
		markdown: true
		type: variable-select
		default: var(--h6-color)
		options:
			-
				label: Theme
				value: var(--h6-color)
			-
				label: Mono
				value: var(--text-normal)
	-
		id: dynamic-outline-h6-font-style
		title: 'H6 font style'
		description: 'Use `--h6-style` from theme or apply custom styling'
		type: variable-select
		default: var(--h6-style)
		options:
			-
				label: Theme
				value: var(--h6-style)
			-
				label: Normal
				value: normal
			-
				label: Italic
				value: italic
	-
		id: dynamic-outline-h6-font-variant
		title: 'H6 font variant'
		description: 'Use `--h6-variant` from theme of apply custom variant'
		type: variable-select
		default: var(--h6-variant)
		options:
			-
				label: Theme
				value: var(--h6-variant)
			-
				label: Normal
				value: normal
			-
				label: Small-caps
				value: small-caps
			-
				label: All small-caps
				value: all-small-caps
	-
		id: dynamic-outline-h6-font-weight
		title: 'H6 font weight'
		description: 'Use `--h6-weight` from theme or apply custom weight'
		type: variable-select
		default: var(--font-normal)
		options:
			- 
				label: Theme
				value: var(--h6-weight)
			-
				label: Normal
				value: var(--font-normal)
			-
				label: Semibold
				value: var(--font-semibold)
			-
				label: Bold
				value: var(--font-bold)
	-
		id: window-layout
		title: 'Window layout'
		type: heading
		level: 2
		collapsed: true
	-
        id: dynamic-outline-window-width
        title: 'Window width'
        description: 'Outline window width (in pixels)'
        type: variable-number-slider
        default: 256
        min: 128
        max: 512
        step: 32
        format: px
    -
		id: dynamic-outline-window-offset-top
		title: 'Window offset top'
		description: 'Add more space before the outline window'
		type: variable-number-slider
		allowEmpty: false
		default: 0
		min: -100
		max: 100
		step: 1
		format: px
	-
		id: dynamic-outline-window-offset-bottom
		title: 'Window offset bottom'
		description: 'Add more space after the outline window'
		type: variable-number-slider
		allowEmpty: false
		default: 0
		min: -100
		max: 100
		step: 1
		format: px
	-
        id: dynamic-outline-opacity
        title: 'Window opacity'
        description: 'Outline window opacity when the outline is not hovered over'
        type: variable-number-slider
        default: 1.0
        min: 0.0
        max: 1.0
        step: 0.05
*/

/* TODO: transfer variables to :root. */
:root {
	/* Applied to the li.style directly with dynamic tab levels. */
	--dynamic-outline-li-padding-tab-size: 16px;

	--dynamic-outline-collapse-icon-width: 12px;
}

.theme-dark,
.theme-light {
	/* New color variables */
	--dynamic-outline-background-modifier-hover-lighter: rgba(
		var(--mono-rgb-100),
		0.045
	);
	--dynamic-outline-background-accent: hsla(
		var(--accent-h),
		var(--accent-s),
		var(--accent-l),
		0.075
	);

	/* Outline Window */
	--dynamic-outline-input-height: var(--input-height);
	--dynamic-outline-input-background-color: var(--background-primary);
	--dynamic-outline-background: var(--background-primary);
	--dynamic-outline-text-color: var(--text-normal);
	--dynamic-outline-font-size: var(--font-text-size);
	--dynamic-outline-color: var(--text-normal);
	--dynamic-outline-border-color: var(--background-modifier-border);
	--dynamic-outline-window-width: 256px;
	--dynamic-outline-opacity: 1;

	--dynamic-outline-window-gap: 4px;
	--dynamic-outline-window-offset-top: 0px;
	--dynamic-outline-window-offset-bottom: 0px;
	--dynamic-outline-status-bar-height-offset-bottom: 30px;
	--dynamic-outline-top: calc(
		var(--header-height) + var(--dynamic-outline-window-gap) +
			var(--dynamic-outline-window-offset-top)
	);
	--dynamic-outline-top-editing-toolbar: calc(
		var(--header-height) + 38px + var(--dynamic-outline-window-gap) +
			var(--dynamic-outline-window-offset-top)
	);
	--dynamic-outline-bottom: calc(
		var(--dynamic-outline-window-gap) +
			var(--dynamic-outline-window-offset-bottom) +
			var(--dynamic-outline-status-bar-height-offset-bottom)
	);

	/* List items */
	--dynamic-outline-background-hover: var(
		--dynamic-outline-background-modifier-hover-lighter
	);

	/* Active Heading */
	--dynamic-outline-active-heading-pill-color: var(--interactive-accent);
	--dynamic-outline-highlight-font-weight: var(--font-normal);
	--dynamic-outline-active-heading-background: var(
		--background-modifier-hover
	);

	/* Headings */
	--dynamic-outline-h1-text-color: var(--h1-color);
	--dynamic-outline-h2-text-color: var(--h2-color);
	--dynamic-outline-h3-text-color: var(--h3-color);
	--dynamic-outline-h4-text-color: var(--h4-color);
	--dynamic-outline-h5-text-color: var(--h5-color);
	--dynamic-outline-h6-text-color: var(--h6-color);

	--dynamic-outline-h1-font-weight: var(--font-bold);
	--dynamic-outline-h2-font-weight: var(--font-normal);
	--dynamic-outline-h3-font-weight: var(--font-normal);
	--dynamic-outline-h4-font-weight: var(--font-normal);
	--dynamic-outline-h5-font-weight: var(--font-normal);
	--dynamic-outline-h6-font-weight: var(--font-normal);

	--dynamic-outline-h1-font-style: var(--h1-style);
	--dynamic-outline-h2-font-style: var(--h2-style);
	--dynamic-outline-h3-font-style: var(--h3-style);
	--dynamic-outline-h4-font-style: var(--h4-style);
	--dynamic-outline-h5-font-style: var(--h5-style);
	--dynamic-outline-h6-font-style: var(--h6-style);

	--dynamic-outline-h1-font-variant: var(--h1-variant);
	--dynamic-outline-h2-font-variant: var(--h2-variant);
	--dynamic-outline-h3-font-variant: var(--h3-variant);
	--dynamic-outline-h4-font-variant: var(--h4-variant);
	--dynamic-outline-h5-font-variant: var(--h5-variant);
	--dynamic-outline-h6-font-variant: var(--h6-variant);
}

.theme-light {
	--dynamic-outline-shadow-color: rgba(99, 99, 99, 0.2);
}

.theme-dark {
	--dynamic-outline-shadow-color: rgba(0, 0, 0, 0.2);
}

#dynamic-outline,
#dynamic-outline ul,
#dynamic-outline li {
	margin: 0;
	padding: 0;
	list-style: none;
}

.setting-item.is-disabled {
	pointer-events: none;
	opacity: 0.5;
}

/* 
 * Dynamic Outline Window
 */

#dynamic-outline {
	position: fixed;
	display: flex;
	flex-direction: column;
	z-index: var(--layer-popover);

	top: var(--dynamic-outline-top);
	max-height: calc(
		100% - var(--dynamic-outline-top) - var(--dynamic-outline-bottom)
	);

	width: var(--dynamic-outline-window-width);
	/* padding: 10px 0 5px; */
	padding: 5px 0 5px;
	right: 20px;

	font-size: var(--dynamic-outline-font-size);

	background-color: var(--dynamic-outline-background);
	border: 1px solid var(--dynamic-outline-border-color);
	border-radius: 12px;
	box-shadow: var(--dynamic-outline-shadow-color) 0px 2px 8px 0px;
	box-sizing: border-box;

	opacity: var(--dynamic-outline-opacity);
}

#dynamic-outline.hidden {
	display: none;
}

/* Corner case for the Editing Toolbar plugin */
.view-content
	> .markdown-source-view[style=""]
	~ #dynamic-outline.obstruction-top {
	top: var(--dynamic-outline-top-editing-toolbar);
	max-height: calc(
		100% - var(--dynamic-outline-top-editing-toolbar) -
			var(--dynamic-outline-bottom)
	);
}

#dynamic-outline.location-left {
	right: unset;
	left: 20px;
}

#dynamic-outline:hover {
	opacity: 1;
}

button.dynamic-outline-reload[disabled] {
	display: none;
}

/* 
 * Dynamic Outline Search Container
 */

.dynamic-outline-search-container.hidden {
	display: none;
}

.dynamic-outline-search-container {
	display: flex;
	align-items: center;

	margin-top: 5px;
	margin-bottom: 5px;
}

.dynamic-outline-search-container::before {
	display: block;
	content: "";
	height: var(--search-icon-size);
	width: var(--search-icon-size);
	position: absolute;
	inset-inline-start: 22px;
	background-color: var(--search-icon-color);
	-webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><circle cx='11' cy='11' r='8'></circle><line x1='21' y1='21' x2='16.65' y2='16.65'></line></svg>");
	-webkit-mask-repeat: no-repeat;
}

/* Tweak pixels */
.dynamic-outline-search-container input {
	display: block;
	width: 100%;
	padding-inline-start: 32px;
}

.dynamic-outline-search-container input.has-content {
	padding-inline-end: 32px;
}

.dynamic-outline-search-clear-button {
	display: none;
	margin: 0;
	top: calc((var(--input-height) - var(--search-icon-size)) + 6px);
	right: 10px;
}

.dynamic-outline-search-clear-button.visible {
	display: block;
}

/* 
 * Dynamic Outline Content Container
 */

.dynamic-outline-search-container.hidden ~ .dynamic-outline-content-container {
	margin-top: 10px;
	margin-bottom: 5px;
}

.dynamic-outline-content-container {
	overflow-y: auto;
	padding: 0 15px;
}

.dynamic-outline-search-container {
	padding: 0 15px;
}

/* 
 * Dynamic Outline List Items
 */

#dynamic-outline li {
	display: flex;
	align-items: center;

	padding: 7px 0;
	margin: 1px 0;
	transition: background 25ms linear;

	border-radius: 6px;
}

.dynamic-outline-collapse-icon {
	/* background-color: var(--color-green); */
	display: flex;
	align-items: center;
	justify-content: center;
	width: var(--dynamic-outline-collapse-icon-width);
	height: var(--dynamic-outline-collapse-icon-width);
	margin-left: 8px;
	color: var(--text-muted);
	transition: transform 0.1s ease-in-out, visibility 0s linear;
	flex-shrink: 0;
}

.dynamic-outline-collapse-icon svg {
	width: var(--dynamic-outline-collapse-icon-width);
	height: var(--dynamic-outline-collapse-icon-width);
	stroke-width: 3;
}

#dynamic-outline li:not(.has-children) .dynamic-outline-collapse-icon {
	visibility: hidden !important;
	cursor: default;
}

/* Hide collapse icon for single top-level items, even if they have children */
#dynamic-outline li.is-single-top-level .dynamic-outline-collapse-icon {
	visibility: hidden !important;
	cursor: default;
}

#dynamic-outline li.collapsed > .dynamic-outline-collapse-icon {
	color: var(--text-accent);
	transform: rotate(-90deg);
}

.dynamic-outline-collapse-icon:hover {
	color: var(--text-normal);
}

#dynamic-outline li:hover,
#dynamic-outline li.highlight:hover,
#dynamic-outline li.hovered,
#dynamic-outline li.highlight.hovered {
	background-color: var(--dynamic-outline-background-hover);
}

#dynamic-outline li.highlight {
	position: relative;
	background-color: var(--dynamic-outline-active-heading-background);
}

#dynamic-outline li.highlight::before {
	color: var(--dynamic-outline-active-heading-pill-color) !important;
}

#dynamic-outline li.highlight::after {
	content: "";
	position: absolute;

	inset-inline-start: -8px;
	width: 4px;
	background-color: var(--dynamic-outline-active-heading-pill-color);
	border-radius: 8px;

	/*
	 * Center the highlight indicator vertically.
	 */
	top: 50%;
	transform: translateY(-50%);
	height: 22px;
}

.heading-level-styling-1 #dynamic-outline li::before,
.heading-level-styling-2 #dynamic-outline li::before {
	font-size: var(--font-smallest);
	color: var(--text-faint);
	margin-left: 0.5em;
	margin-right: 0.25em;
}

.heading-level-styling-1 #dynamic-outline li.li-heading-level-1::before {
	content: "H1";
}

.heading-level-styling-1 #dynamic-outline li.li-heading-level-2::before {
	content: "H2";
}

.heading-level-styling-1 #dynamic-outline li.li-heading-level-3::before {
	content: "H3";
}

.heading-level-styling-1 #dynamic-outline li.li-heading-level-4::before {
	content: "H4";
}

.heading-level-styling-1 #dynamic-outline li.li-heading-level-5::before {
	content: "H5";
}

.heading-level-styling-1 #dynamic-outline li.li-heading-level-6::before {
	content: "H6";
}

.heading-level-styling-2 #dynamic-outline li.li-heading-level-1::before {
	content: "#";
}

.heading-level-styling-2 #dynamic-outline li.li-heading-level-2::before {
	content: "##";
}

.heading-level-styling-2 #dynamic-outline li.li-heading-level-3::before {
	content: "###";
}

.heading-level-styling-2 #dynamic-outline li.li-heading-level-4::before {
	content: "####";
}

.heading-level-styling-2 #dynamic-outline li.li-heading-level-5::before {
	content: "#####";
}

.heading-level-styling-2 #dynamic-outline li.li-heading-level-6::before {
	content: "######";
}

#dynamic-outline li a {
	padding: 0px 8px;
	color: var(--dynamic-outline-text-color);
	text-decoration: none;
	cursor: default;
	flex-grow: 1; /* Allow text to take remaining space */
	min-width: 0; /* Prevent overflow issues with flex */
}

.disable-heading-word-wrap #dynamic-outline li a {
	/* Text Handling */
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* Default padding */
#dynamic-outline li.tab-level-2 {
	padding-left: var(--dynamic-outline-li-padding-tab-size);
}

#dynamic-outline li.tab-level-3 {
	padding-left: calc(var(--dynamic-outline-li-padding-tab-size) * 2);
}

#dynamic-outline li.tab-level-4 {
	padding-left: calc(var(--dynamic-outline-li-padding-tab-size) * 3);
}

#dynamic-outline li.tab-level-5 {
	padding-left: calc(var(--dynamic-outline-li-padding-tab-size) * 4);
}

#dynamic-outline li.tab-level-6 {
	padding-left: calc(var(--dynamic-outline-li-padding-tab-size) * 5);
}

/* Override padding when there is only one top-level heading */
#dynamic-outline.has-single-top-level li.tab-level-2 {
	padding-left: 0;
}

#dynamic-outline.has-single-top-level li.tab-level-3 {
	padding-left: var(--dynamic-outline-li-padding-tab-size); /* Effectively level 2 */
}

#dynamic-outline.has-single-top-level li.tab-level-4 {
	padding-left: calc(var(--dynamic-outline-li-padding-tab-size) * 2); /* Effectively level 3 */
}

#dynamic-outline.has-single-top-level li.tab-level-5 {
	padding-left: calc(var(--dynamic-outline-li-padding-tab-size) * 3); /* Effectively level 4 */
}

#dynamic-outline.has-single-top-level li.tab-level-6 {
	padding-left: calc(var(--dynamic-outline-li-padding-tab-size) * 4); /* Effectively level 5 */
}

#dynamic-outline li a.heading-level-1 {
	color: var(--dynamic-outline-h1-text-color);
	font-weight: var(--dynamic-outline-h1-font-weight);
	font-style: var(--dynamic-outline-h1-font-style);
	font-variant: var(--dynamic-outline-h1-font-variant);
}

#dynamic-outline li a.heading-level-2 {
	color: var(--dynamic-outline-h2-text-color);
	font-weight: var(--dynamic-outline-h2-font-weight);
	font-style: var(--dynamic-outline-h2-font-style);
	font-variant: var(--dynamic-outline-h2-font-variant);
}

#dynamic-outline li a.heading-level-3 {
	color: var(--dynamic-outline-h3-text-color);
	font-weight: var(--dynamic-outline-h3-font-weight);
	font-style: var(--dynamic-outline-h3-font-style);
	font-variant: var(--dynamic-outline-h3-font-variant);
}

#dynamic-outline li a.heading-level-4 {
	color: var(--dynamic-outline-h4-text-color);
	font-weight: var(--dynamic-outline-h4-font-weight);
	font-style: var(--dynamic-outline-h4-font-style);
	font-variant: var(--dynamic-outline-h4-font-variant);
}

#dynamic-outline li a.heading-level-5 {
	color: var(--dynamic-outline-h5-text-color);
	font-weight: var(--dynamic-outline-h5-font-weight);
	font-style: var(--dynamic-outline-h5-font-style);
	font-variant: var(--dynamic-outline-h5-font-variant);
}

#dynamic-outline li a.heading-level-6 {
	color: var(--dynamic-outline-h6-text-color);
	font-weight: var(--dynamic-outline-h6-font-weight);
	font-style: var(--dynamic-outline-h6-font-style);
	font-variant: var(--dynamic-outline-h6-font-variant);
}

#dynamic-outline li.highlight .heading-level-1 {
	font-weight: max(
		var(--dynamic-outline-h1-font-weight),
		var(--dynamic-outline-highlight-font-weight)
	); /* Preserve the maximum font weight on highlight. */
}

#dynamic-outline li.highlight .heading-level-2 {
	font-weight: max(
		var(--dynamic-outline-h2-font-weight),
		var(--dynamic-outline-highlight-font-weight)
	); /* Preserve the maximum font weight on highlight. */
}

#dynamic-outline li.highlight .heading-level-3 {
	font-weight: max(
		var(--dynamic-outline-h3-font-weight),
		var(--dynamic-outline-highlight-font-weight)
	); /* Preserve the maximum font weight on highlight. */
}

#dynamic-outline li.highlight .heading-level-4 {
	font-weight: max(
		var(--dynamic-outline-h4-font-weight),
		var(--dynamic-outline-highlight-font-weight)
	); /* Preserve the maximum font weight on highlight. */
}

#dynamic-outline li.highlight .heading-level-5 {
	font-weight: max(
		var(--dynamic-outline-h5-font-weight),
		var(--dynamic-outline-highlight-font-weight)
	); /* Preserve the maximum font weight on highlight. */
}

#dynamic-outline li.highlight .heading-level-6 {
	font-weight: max(
		var(--dynamic-outline-h6-font-weight),
		var(--dynamic-outline-highlight-font-weight)
	); /* Preserve the maximum font weight on highlight. */
}

#dynamic-outline .outline-item-hidden { /* Rule for items hidden by search */
	display: none !important;
}

/* Hide by collapse ONLY when NOT searching */
#dynamic-outline:not(.is-searching) li.hidden-by-collapse {
	display: none !important;
}

/* 
 * Dynamic Outline Button
 */

.dynamic-outline-button.button-active {
	box-shadow: none;
	opacity: var(--icon-opacity-hover);
	color: var(--icon-color-hover);
	background-color: var(--background-modifier-hover);
}

.dynamic-outline-button.pinned {
	color: var(--text-normal);
}

.dynamic-outline-button.hidden,
.hide-button-from-toolbar .dynamic-outline-button {
	display: none;
}

/* 
 * Other
 */

/* Custom Style Settings left padding for the headings sublist. */
.setting-item[data-id="obsidian-dynamic-outline"]
	+ .style-settings-container
	.setting-item[data-id="headings"]
	+ .style-settings-container {
	padding-left: 17px;
}

/* Custom hide logic for settings options */
.setting-item.dynamic-outline-setting-item-hidden.is-disabled {
	display: none;
}
