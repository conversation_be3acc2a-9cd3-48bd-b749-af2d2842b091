/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var vt=Object.create;var Se=Object.defineProperty;var mt=Object.getOwnPropertyDescriptor;var ft=Object.getOwnPropertyNames;var wt=Object.getPrototypeOf,bt=Object.prototype.hasOwnProperty;var _t=(p,e)=>()=>(e||p((e={exports:{}}).exports,e),e.exports),yt=(p,e)=>{for(var t in e)Se(p,t,{get:e[t],enumerable:!0})},Ke=(p,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let l of ft(e))!bt.call(p,l)&&l!==t&&Se(p,l,{get:()=>e[l],enumerable:!(n=mt(e,l))||n.enumerable});return p};var Et=(p,e,t)=>(t=p!=null?vt(wt(p)):{},Ke(e||!p||!p.__esModule?Se(t,"default",{value:p,enumerable:!0}):t,p)),Ot=p=>Ke(Se({},"__esModule",{value:!0}),p);var Qe=_t((Ye,De)=>{((p,e)=>{typeof define=="function"&&define.amd?define([],e):typeof De=="object"&&De.exports?De.exports=e():p.fuzzysort=e()})(Ye,p=>{"use strict";var e=(s,i)=>{if(!s||!i)return B;var a=T(s);ee(i)||(i=w(i));var c=a.bitflags;return(c&i._bitflags)!==c?B:$(a,i)},t=(s,i,a)=>{if(!s)return a!=null&&a.all?z(i,a):Ne;var c=T(s),d=c.bitflags,u=c.containsSpace,o=I((a==null?void 0:a.threshold)||0),g=(a==null?void 0:a.limit)||Fe,h=0,f=0,b=i.length;function N(te){h<g?(Te.add(te),++h):(++f,te._score>Te.peek()._score&&Te.replaceTop(te))}if(a!=null&&a.key)for(var A=a.key,E=0;E<b;++E){var F=i[E],O=Ce(F,A);if(O&&(ee(O)||(O=w(O)),(d&O._bitflags)===d)){var H=$(c,O);H!==B&&(H._score<o||(H.obj=F,N(H)))}}else if(a!=null&&a.keys){var Q=a.keys,J=Q.length;e:for(var E=0;E<b;++E){var F=i[E];{for(var P=0,_=0;_<J;++_){var A=Q[_],O=Ce(F,A);if(!O){We[_]=le;continue}ee(O)||(O=w(O)),We[_]=O,P|=O._bitflags}if((d&P)!==d)continue}if(u)for(let L=0;L<c.spaceSearches.length;L++)K[L]=R;for(var _=0;_<J;++_){if(O=We[_],O===le){se[_]=le;continue}if(se[_]=$(c,O,!1,u),se[_]===B){se[_]=le;continue}if(u)for(let C=0;C<c.spaceSearches.length;C++){if(j[C]>-1e3&&K[C]>R){var x=(K[C]+j[C])/4;x>K[C]&&(K[C]=x)}j[C]>K[C]&&(K[C]=j[C])}}if(u){for(let L=0;L<c.spaceSearches.length;L++)if(K[L]===R)continue e}else{var m=!1;for(let L=0;L<J;L++)if(se[L]._score!==R){m=!0;break}if(!m)continue}var q=new y(J);for(let L=0;L<J;L++)q[L]=se[L];if(u){var V=0;for(let L=0;L<c.spaceSearches.length;L++)V+=K[L]}else{var V=R;for(let C=0;C<J;C++){var H=q[C];if(H._score>-1e3&&V>R){var x=(V+H._score)/4;x>V&&(V=x)}H._score>V&&(V=H._score)}}if(q.obj=F,q._score=V,a!=null&&a.scoreFn){if(V=a.scoreFn(q),!V)continue;V=I(V),q._score=V}V<o||N(q)}}else for(var E=0;E<b;++E){var O=i[E];if(O&&(ee(O)||(O=w(O)),(d&O._bitflags)===d)){var H=$(c,O);H!==B&&(H._score<o||N(H))}}if(h===0)return Ne;for(var X=new Array(h),E=h-1;E>=0;--E)X[E]=Te.poll();return X.total=h+f,X},n=(s,i="<b>",a="</b>")=>{for(var c=typeof i=="function"?i:void 0,d=s.target,u=d.length,o=s.indexes,g="",h=0,f=0,b=!1,N=[],A=0;A<u;++A){var E=d[A];if(o[f]===A){if(++f,b||(b=!0,c?(N.push(g),g=""):g+=i),f===o.length){c?(g+=E,N.push(c(g,h++)),g="",N.push(d.substr(A+1))):g+=E+a+d.substr(A+1);break}}else b&&(b=!1,c?(N.push(c(g,h++)),g=""):g+=a);g+=E}return c?N:g},l=s=>{typeof s=="number"?s=""+s:typeof s!="string"&&(s="");var i=ke(s);return S(s,{_targetLower:i._lower,_targetLowerCodes:i.lowerCodes,_bitflags:i.bitflags})},r=()=>{Ae.clear(),Pe.clear()};class v{get indexes(){return this._indexes.slice(0,this._indexes.len).sort((i,a)=>i-a)}set indexes(i){return this._indexes=i}highlight(i,a){return n(this,i,a)}get score(){return D(this._score)}set score(i){this._score=I(i)}}class y extends Array{get score(){return D(this._score)}set score(i){this._score=I(i)}}var S=(s,i)=>{var c,d,u,o,g,h,f;let a=new v;return a.target=s,a.obj=(c=i.obj)!=null?c:B,a._score=(d=i._score)!=null?d:R,a._indexes=(u=i._indexes)!=null?u:[],a._targetLower=(o=i._targetLower)!=null?o:"",a._targetLowerCodes=(g=i._targetLowerCodes)!=null?g:B,a._nextBeginningIndexes=(h=i._nextBeginningIndexes)!=null?h:B,a._bitflags=(f=i._bitflags)!=null?f:0,a},D=s=>s===R?0:s>1?s:Math.E**(((-s+1)**.04307-1)*-2),I=s=>s===0?R:s>1?s:1-Math.pow(Math.log(s)/-2+1,1/.04307),W=s=>{typeof s=="number"?s=""+s:typeof s!="string"&&(s=""),s=s.trim();var i=ke(s),a=[];if(i.containsSpace){var c=s.split(/\s+/);c=[...new Set(c)];for(var d=0;d<c.length;d++)if(c[d]!==""){var u=ke(c[d]);a.push({lowerCodes:u.lowerCodes,_lower:c[d].toLowerCase(),containsSpace:!1})}}return{lowerCodes:i.lowerCodes,_lower:i._lower,containsSpace:i.containsSpace,bitflags:i.bitflags,spaceSearches:a}},w=s=>{if(s.length>999)return l(s);var i=Ae.get(s);return i!==void 0||(i=l(s),Ae.set(s,i)),i},T=s=>{if(s.length>999)return W(s);var i=Pe.get(s);return i!==void 0||(i=W(s),Pe.set(s,i)),i},z=(s,i)=>{var a=[];a.total=s.length;var c=(i==null?void 0:i.limit)||Fe;if(i!=null&&i.key)for(var d=0;d<s.length;d++){var u=s[d],o=Ce(u,i.key);if(o!=B){ee(o)||(o=w(o));var g=S(o.target,{_score:o._score,obj:u});if(a.push(g),a.length>=c)return a}}else if(i!=null&&i.keys)for(var d=0;d<s.length;d++){for(var u=s[d],h=new y(i.keys.length),f=i.keys.length-1;f>=0;--f){var o=Ce(u,i.keys[f]);if(!o){h[f]=le;continue}ee(o)||(o=w(o)),o._score=R,o._indexes.len=0,h[f]=o}if(h.obj=u,h._score=R,a.push(h),a.length>=c)return a}else for(var d=0;d<s.length;d++){var o=s[d];if(o!=B&&(ee(o)||(o=w(o)),o._score=R,o._indexes.len=0,a.push(o),a.length>=c))return a}return a},$=(s,i,a=!1,c=!1)=>{if(a===!1&&s.containsSpace)return ct(s,i,c);for(var d=s._lower,u=s.lowerCodes,o=u[0],g=i._targetLowerCodes,h=u.length,f=g.length,E=0,b=0,N=0;;){var A=o===g[b];if(A){if(G[N++]=b,++E,E===h)break;o=u[E]}if(++b,b>=f)return B}var E=0,F=!1,O=0,H=i._nextBeginningIndexes;H===B&&(H=i._nextBeginningIndexes=ht(i.target)),b=G[0]===0?0:H[G[0]-1];var Q=0;if(b!==f)for(;;)if(b>=f){if(E<=0||(++Q,Q>200))break;--E;var J=Le[--O];b=H[J]}else{var A=u[E]===g[b];if(A){if(Le[O++]=b,++E,E===h){F=!0;break}++b}else b=H[b]}var P=h<=1?-1:i._targetLower.indexOf(d,G[0]),_=!!~P,x=_?P===0||i._nextBeginningIndexes[P-1]===P:!1;if(_&&!x){for(var m=0;m<H.length;m=H[m])if(!(m<=P)){for(var q=0;q<h&&u[q]===i._targetLowerCodes[m+q];q++);if(q===h){P=m,x=!0;break}}}var V=L=>{for(var C=0,Ge=0,U=1;U<h;++U)L[U]-L[U-1]!==1&&(C-=L[U],++Ge);var pt=L[h-1]-L[0]-(h-1);if(C-=(12+pt)*Ge,L[0]!==0&&(C-=L[0]*L[0]*.2),!F)C*=1e3;else{for(var ze=1,U=H[0];U<f;U=H[U])++ze;ze>24&&(C*=(ze-24)*10)}return C-=(f-h)/2,_&&(C/=1+h*h*1),x&&(C/=1+h*h*1),C-=(f-h)/2,C};if(F)if(x){for(var m=0;m<h;++m)G[m]=P+m;var X=G,te=V(G)}else var X=Le,te=V(Le);else{if(_)for(var m=0;m<h;++m)G[m]=P+m;var X=G,te=V(X)}i._score=te;for(var m=0;m<h;++m)i._indexes[m]=X[m];i._indexes.len=h;let Me=new v;return Me.target=i.target,Me._score=i._score,Me._indexes=i._indexes,Me},ct=(s,i,a)=>{for(var c=new Set,d=0,u=B,o=0,g=s.spaceSearches,h=g.length,f=0,b=()=>{for(let x=f-1;x>=0;x--)i._nextBeginningIndexes[He[x*2+0]]=He[x*2+1]},N=!1,_=0;_<h;++_){j[_]=R;var A=g[_];if(u=$(A,i),a){if(u===B)continue;N=!0}else if(u===B)return b(),B;var E=_===h-1;if(!E){var F=u._indexes,O=!0;for(let m=0;m<F.len-1;m++)if(F[m+1]-F[m]!==1){O=!1;break}if(O){var H=F[F.len-1]+1,Q=i._nextBeginningIndexes[H-1];for(let m=H-1;m>=0&&Q===i._nextBeginningIndexes[m];m--)i._nextBeginningIndexes[m]=H,He[f*2+0]=m,He[f*2+1]=Q,f++}}d+=u._score/h,j[_]=u._score/h,u._indexes[0]<o&&(d-=(o-u._indexes[0])*2),o=u._indexes[0];for(var J=0;J<u._indexes.len;++J)c.add(u._indexes[J])}if(a&&!N)return B;b();var P=$(s,i,!0);if(P!==B&&P._score>d){if(a)for(var _=0;_<h;++_)j[_]=P._score/h;return P}a&&(u=i),u._score=d;var _=0;for(let x of c)u._indexes[_++]=x;return u._indexes.len=_,u},$e=s=>s.replace(/\p{Script=Latin}+/gu,i=>i.normalize("NFD")).replace(/[\u0300-\u036f]/g,""),ke=s=>{s=$e(s);for(var i=s.length,a=s.toLowerCase(),c=[],d=0,u=!1,o=0;o<i;++o){var g=c[o]=a.charCodeAt(o);if(g===32){u=!0;continue}var h=g>=97&&g<=122?g-97:g>=48&&g<=57?26:g<=127?30:31;d|=1<<h}return{lowerCodes:c,bitflags:d,containsSpace:u,_lower:a}},dt=s=>{for(var i=s.length,a=[],c=0,d=!1,u=!1,o=0;o<i;++o){var g=s.charCodeAt(o),h=g>=65&&g<=90,f=h||g>=97&&g<=122||g>=48&&g<=57,b=h&&!d||!u||!f;d=h,u=f,b&&(a[c++]=o)}return a},ht=s=>{s=$e(s);for(var i=s.length,a=dt(s),c=[],d=a[0],u=0,o=0;o<i;++o)d>o?c[o]=d:(d=a[++u],c[o]=d===void 0?i:d);return c},Ae=new Map,Pe=new Map,G=[],Le=[],He=[],K=[],j=[],We=[],se=[],Ce=(s,i)=>{var a=s[i];if(a!==void 0)return a;if(typeof i=="function")return i(s);var c=i;Array.isArray(i)||(c=i.split("."));for(var d=c.length,u=-1;s&&++u<d;)s=s[c[u]];return s},ee=s=>typeof s=="object"&&typeof s._bitflags=="number",Fe=1/0,R=-Fe,Ne=[];Ne.total=0;var B=null,le=l(""),gt=s=>{var i=[],a=0,c={},d=u=>{for(var o=0,g=i[o],h=1;h<a;){var f=h+1;o=h,f<a&&i[f]._score<i[h]._score&&(o=f),i[o-1>>1]=i[o],h=1+(o<<1)}for(var b=o-1>>1;o>0&&g._score<i[b]._score;b=(o=b)-1>>1)i[o]=i[b];i[o]=g};return c.add=u=>{var o=a;i[a++]=u;for(var g=o-1>>1;o>0&&u._score<i[g]._score;g=(o=g)-1>>1)i[o]=i[g];i[o]=u},c.poll=u=>{if(a!==0){var o=i[0];return i[0]=i[--a],d(),o}},c.peek=u=>{if(a!==0)return i[0]},c.replaceTop=u=>{i[0]=u,d()},c},Te=gt();return{single:e,go:t,prepare:l,cleanup:r}})});var Lt={};yt(Lt,{BUTTON_CLASS:()=>Re,LUCID_ICON_NAME:()=>qe,WINDOW_ID:()=>Oe,default:()=>xe});module.exports=Ot(Lt);var Y=require("obsidian");var Je=require("obsidian");var Ue=require("obsidian"),oe=class{constructor(e,t){this._plugin=e,this._outline=t,this._containerEl=this._createElement(),this._setupEventListeners()}get visible(){let e=this._containerEl.isConnected,t=this._containerEl.classList.contains("hidden");return e&&!t}set visible(e){this._containerEl.isConnected||this._connectToDOM(this._containerEl),this._containerEl.classList.toggle("hidden",!e)}get active(){return this._containerEl.classList.contains("button-active")}set active(e){this._containerEl.classList.toggle("button-active",e)}set pinned(e){this._containerEl.classList.toggle("pinned",e)}getContainerElement(){return this._containerEl}_setupEventListeners(){this._plugin.registerDomEvent(this._containerEl,"click",()=>this.handleClick()),this._plugin.settings.revealOnHover&&(this._plugin.registerDomEvent(this._containerEl,"mouseenter",()=>this._handleMouseEnter()),this._plugin.registerDomEvent(this._containerEl,"mouseleave",()=>this._handleMouseLeave()))}_createElement(){let e=createEl("button",{cls:`clickable-icon view-action ${Re} hidden`,attr:{"aria-label":"Toggle Dynamic Outline"}});return(0,Ue.setIcon)(e,qe),this._connectToDOM(e),e}_connectToDOM(e){if(this._plugin.settings.outlinePosition==="right"){let t=this._outline.view.containerEl.querySelector(".view-actions");t==null||t.insertBefore(e,t==null?void 0:t.firstChild)}else if(this._plugin.settings.outlinePosition==="left"){let t=this._outline.view.containerEl.querySelector(".view-header-left .view-header-nav-buttons");t==null||t.appendChild(e)}else console.error("Invalid window location: ",this._plugin.settings.outlinePosition)}_handleMouseEnter(){this._outline.windowVisible||this._outline.showWindow({scrollBlock:"start"}),this._plugin.settings.revealOnHover&&this._outline.clearWindowHideTimeout()}_handleMouseLeave(){this._outline.windowVisible&&!this._outline.windowPinned&&this._outline.hideWindow({timeout:100})}handleClick(){if(this._outline.windowVisible){if(this._plugin.settings.revealOnHover)if(this._outline.windowPinned)this._outline.windowPinned=!1,this._outline.clearWindowHideTimeout();else{this._outline.windowPinned=!0;return}this._outline.hideWindow()}else this._outline.showWindow({scrollBlock:"start"}),this._plugin.settings.revealOnHover&&(this._outline.windowPinned=!0)}show(){this.visible||(this.active=this.visible,this.visible=!0)}hide(){this.visible&&(this.visible=!1)}destroy(){this._containerEl.remove()}};var ie=class{constructor(e,t){this._plugin=e,t?(this.element=t,this.inputField=this.element.querySelector("input"),this.clearButton=this.element.querySelector(".dynamic-outline-search-clear-button")):(this.element=createEl("div",{cls:"dynamic-outline-search-container"}),this.inputField=createEl("input",{attr:{placeholder:"Search headings\u2026",type:"search"}}),this.element.appendChild(this.inputField),this.clearButton=createEl("div",{cls:"search-input-clear-button dynamic-outline-search-clear-button",attr:{"aria-label":"Clear search"}}),this.element.appendChild(this.clearButton),this.setupEventListeners())}setupEventListeners(){this._plugin.registerDomEvent(this.inputField,"input",()=>{this.handleInput()}),this._plugin.registerDomEvent(this.clearButton,"click",()=>{this.clearInput()})}clearInput(e=!0){this.inputField.value="";let t=new Event("input",{bubbles:!0,cancelable:!0});this.inputField.dispatchEvent(t),e&&this.inputField.focus()}handleInput(){this.inputField.value.length>0?(this.clearButton.classList.add("visible"),this.inputField.classList.add("has-content")):(this.clearButton.classList.remove("visible"),this.inputField.classList.remove("has-content"))}};var re=class{constructor(e,t){this.COLLAPSE_ICON_SVG='<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon"><path d="m6 9 6 6 6-6"/></svg>';this._plugin=e,this._outline=t}createLiElement(e,t,n,l,r,v){let y=l+1<n.length&&n[l+1].level>e.level,S=r&&y,D=t===1&&!v,I=[`tab-level-${t}`,`li-heading-level-${e.level}`];S&&I.push("has-children"),D&&I.push("is-single-top-level");let W=createEl("li",{cls:I,attr:{"data-heading-line":e.position.start.line,"data-level":t}});if(r&&!D){let T=createEl("span",{cls:"dynamic-outline-collapse-icon"});T.innerHTML=this.COLLAPSE_ICON_SVG,S?T.addEventListener("click",z=>this._handleCollapseToggle(z)):T.style.cursor="default",W.append(T)}let w=createEl("a",{cls:`heading-level-${e.level}`,text:e.heading});return W.append(w),this._setupEventListener(W,e),W}updateLiElementLine(e,t){e.setAttribute("data-heading-line",t.position.start.line.toString()),this._setupEventListener(e,t)}_setupEventListener(e,t){e.onclick=n=>{n.target.closest(".dynamic-outline-collapse-icon")||this._handleClick(t)},e.addEventListener("mouseenter",()=>{e.classList.add("hovered")}),e.addEventListener("mouseleave",()=>{e.classList.remove("hovered")})}_handleCollapseToggle(e){e.stopPropagation();let n=e.currentTarget.parentElement;if(!n)return;let l=parseInt(n.dataset.level||"0"),r=!n.classList.contains("collapsed");n.classList.toggle("collapsed");let v=[],y=n.nextElementSibling;for(;y&&!(parseInt(y.dataset.level||"0")<=l);)v.push(y),y=y.nextElementSibling;requestAnimationFrame(()=>{let S=r?-1:l+1;v.forEach(D=>{let I=parseInt(D.dataset.level||"0");r?D.classList.add("hidden-by-collapse"):I<=S?(D.classList.remove("hidden-by-collapse"),S=D.classList.contains("collapsed")?I:I+1):D.classList.add("hidden-by-collapse")})})}_handleClick(e){this._outline.view.file&&(this._navigateToHeading(e),this._resetSearchField(),this._plugin.runCommand("editor:focus"))}_navigateToHeading(e){let t=this._outline.view.file;t&&(this._outline.view.leaf.openFile(t,{eState:{line:e.position.start.line}}),setTimeout(()=>{this._outline.view.currentMode.applyScroll(e.position.start.line)},0),this._plugin.settings.hideOutlineOnJump&&this._outline.outlineWindow.hide())}_resetSearchField(){if(this._plugin.settings.disableSearchClearOnJump)return;let e=this._outline.outlineWindow,t=e.getContainerElement().querySelector(".dynamic-outline-search-container");if(!t)return;new ie(this._plugin,t).clearInput(!1),e.removeHovered()}};var Xe=Et(Qe()),ne=class ne{constructor(e,t){this.hiddenOnResize=!1;this._latestHeadings=[];this._pinned=!1;this._plugin=e,this._outline=t,this._containerEl=this._createElement(),this._setupEventListeners()}get visible(){let e=this._containerEl.isConnected,t=this._containerEl.classList.contains("hidden");return e&&!t}set visible(e){this._containerEl.isConnected||this._connectToDOM(this._containerEl),this._containerEl.classList.toggle("hidden",!e)}get pinned(){return this._pinned}set pinned(e){this._pinned=e,this._outline.buttonPinned=e,this._plugin.settings.revealOnHover&&!e&&this.hide()}getContainerElement(){return this._containerEl}toggle(){this.visible?this.hide():this.show()}show(e){if(!this.visible){if(this._checkForLocation(),this._setVisibilityBasedOnEditingToolbar(),this.update(),this._outline.buttonActive=!0,this.hiddenOnResize=!1,!this._plugin.settings.disableSearchFieldAutofocus){let t=this._containerEl.querySelector("input");t==null||t.focus()}this._plugin.settings.disableActiveHeadingHighlighting||this.highlightCurrentHeading(e==null?void 0:e.scrollBlock)}}hide(){this.visible&&(this.visible=!1,this.removeHovered(),this._outline.buttonActive=!1,this._plugin.runCommand("editor:focus"),this._plugin.settings.revealOnHover&&(this.pinned=!1))}destroy(){this._clearHideTimeout(),this._containerEl.remove()}update(){if(!this.visible)return;let e=(w,T)=>w.length===T.length&&w.every((z,$)=>z.heading===T[$].heading&&z.level===T[$].level),t=this._containerEl.querySelector("ul");if(!t)return;let n=new re(this._plugin,this._outline),l=this._outline.outlineHeadings.headings;if(l.length>0&&e(l,this._latestHeadings)){t.querySelectorAll("li").forEach((T,z)=>{n.updateLiElementLine(T,l[z])});return}this._latestHeadings=l,t.empty();let r=!1;if(!this._plugin.settings.disableHeadingCollapsing){let w=l.map(z=>z.level);new Set(w).size>1&&(r=!0)}let v=Math.min(...l.map(w=>w.level)),S=l.filter(w=>w.level===v).length>1,D=document.createDocumentFragment();if(this._plugin.settings.disableDynamicHeadingIndentation)l==null||l.forEach((w,T)=>{D.append(n.createLiElement(w,w.level,l,T,r,S))});else{let w=[];l==null||l.forEach((T,z)=>{for(;w.length>0&&T.level<=w[w.length-1];)w.pop();w.push(T.level),D.append(n.createLiElement(T,w.length,l,z,r,S))})}t.appendChild(D),this._containerEl.classList.toggle("has-single-top-level",!S);let I=!this._plugin.settings.disableSearchBarAutoHide&&l.length<this._plugin.settings.minHeadingsToHideSearchBar,W=this._containerEl.querySelector(".dynamic-outline-search-container");W==null||W.classList.toggle("hidden",I),this._plugin.settings.disableActiveHeadingHighlighting||this.highlightCurrentHeading()}highlightCurrentHeading(e="nearest"){let t=(S,D)=>{let I=0,W=0,w=S.length-1;for(;W<=w;){let T=Math.floor((W+w)/2);S[T].position.start.line<=D?(I=T,W=T+1):w=T-1}return I},n=this._outline.view.currentMode.getScroll(),l=this._outline.outlineHeadings.headings;if(l.length==0)return;let r=t(l,n+1);this._containerEl.querySelectorAll("li").forEach((S,D)=>S.classList.toggle("highlight",D===r));let y=this._containerEl.querySelector("li.highlight");y==null||y.scrollIntoView({behavior:"instant",block:e})}removeHovered(){this._getVisibleLiItems().forEach(t=>{t.classList.remove("hovered")})}_setupEventListeners(){this._plugin.registerDomEvent(this._containerEl.querySelector("input"),"input",()=>{this._filterItems()}),this._plugin.registerDomEvent(this._containerEl.querySelector("input"),"keydown",e=>{this._handleKeyDown(e)}),this._plugin.settings.revealOnHover&&(this._plugin.registerDomEvent(this._containerEl,"mouseenter",()=>this._handleMouseEnter()),this._plugin.registerDomEvent(this._containerEl,"mouseleave",()=>this._handleMouseLeave()))}_handleKeyDown(e){let t=()=>{let y=n.findIndex(S=>S.classList.contains("hovered"));return y!==-1?y:n.findIndex(S=>S.classList.contains("highlight"))||0},n=this._getVisibleLiItems(),l=n.length,r=t(),v=r;switch(e.key){case"ArrowDown":case"Tab":e.preventDefault(),v=e.shiftKey?(r+l-1)%l:(r+1)%l;break;case"ArrowUp":e.preventDefault(),v=(r+l-1)%l;break;case"Enter":e.preventDefault(),r>=0&&n[r].click();break;case"Escape":e.preventDefault(),this.hide();break}v!==r&&(this._setHovered(n,v),n[v].scrollIntoView({block:"nearest"}))}_handleMouseEnter(){this._clearHideTimeout(),this._getVisibleLiItems().forEach(t=>{t.classList.remove("hovered")})}_handleMouseLeave(){this._plugin.settings.revealOnHover&&!this.pinned&&(ne.hideTimeout=setTimeout(()=>{this.hide()},100))}_createElement(){let e=createEl("div",{cls:"hidden",attr:{id:"dynamic-outline"}}),t=new ie(this._plugin);e.appendChild(t.element);let n=createEl("div",{cls:"dynamic-outline-content-container"});return n.createEl("ul",{}),e.appendChild(n),e}_getVisibleLiItems(){return Array.from(this._containerEl.querySelectorAll("li:not(.outline-item-hidden):not(.hidden-by-collapse)"))}_setHovered(e,t){e.forEach((n,l)=>{n.classList.toggle("hovered",l===t)})}_filterItems(){let t=this._containerEl.querySelector("input").value.toLowerCase(),n=this._containerEl.querySelectorAll("li"),l=t!=="";this._containerEl.classList.toggle("is-searching",l);let r;l?r=Xe.go(t,Array.from(n),{key:"textContent"}).map(y=>y.obj):r=Array.from(n),n.forEach(y=>{let S=r.includes(y);y.classList.toggle("outline-item-hidden",!S)});let v=this._getVisibleLiItems();this._setHovered(v,0)}_clearHideTimeout(){ne.hideTimeout&&(clearTimeout(ne.hideTimeout),ne.hideTimeout=null)}_setVisibilityBasedOnEditingToolbar(){let e=document.getElementById("editingToolbarModalBar");if(!e){this.visible=!0;return}let t=e.classList.contains("top");if(this._containerEl.classList.toggle("obstruction-top",t),!t){let n=e.style.display;e.style.setProperty("display","none","important"),this.visible=!0,setTimeout(()=>{e.style.display=n},0);return}this.visible=!0}_checkForLocation(){this._containerEl.classList.toggle("location-left",this._plugin.settings.outlinePosition==="left")}_connectToDOM(e){this._outline.view.contentEl.append(e)}};ne.hideTimeout=null;var ae=ne;var Ze=require("obsidian"),ue=class{constructor(e,t){this._headings=[];this._plugin=e,this._outline=t}get headings(){return this._headings=this._getHeadingsForView(this._outline.view),this._headings}_getHeadingsForView(e){var v;let t=e==null?void 0:e.file;if(!t)return[];let l=(v=(this._plugin.app.metadataCache.getFileCache(t)||{}).headings)!=null?v:[];return this._cleanupHeadings(l)}_cleanupHeadings(e){let t=r=>(0,Ze.htmlToMarkdown)(r).replaceAll("*","").replaceAll("_","").replaceAll("`","").replaceAll("==","").replaceAll("~~",""),n=r=>r.replace(/\[(.*?)\]\(.*?\)/g,"$1").replace(/\[\[([^\]]+)\|([^\]]+)\]\]/g,"$2").replace(/\[\[([^\]]+)\]\]/g,"$1"),l=e;return l.forEach(r=>{let v=r.heading;v=t(v),v=n(v),r.heading=v}),l}};var ce=class{constructor(e,t){this._plugin=e,this._view=t,this.outlineWindow=new ae(this._plugin,this),this.outlineButton=new oe(this._plugin,this),this.outlineHeadings=new ue(this._plugin,this),this.toggledAutomaticallyOnce=!1}get view(){return this._view}set view(e){this._view=e}get window(){return this.outlineWindow}get button(){return this.outlineButton}get headings(){return this.outlineHeadings.headings}get isButtonVisible(){return this.outlineButton.visible}set buttonActive(e){this.outlineButton.active=e}set buttonPinned(e){this.outlineButton.pinned=e}get windowVisible(){return this.outlineWindow.visible}get windowPinned(){return this.outlineWindow.pinned}set windowPinned(e){this.outlineWindow.pinned=e}toggleButton(e){e?this.showButton():this.hideButton()}showButton(){this.isButtonVisible||this.outlineButton.show()}hideButton(){this.isButtonVisible&&this.outlineButton.hide()}toggleWindow(e){e?this.showWindow():this.hideWindow()}showWindow(e){this.windowVisible||(this.outlineWindow.show(e),e!=null&&e.hiddenOnResize&&(this.outlineWindow.hiddenOnResize=e.hiddenOnResize))}hideWindow(e){this.windowVisible&&(e!=null&&e.timeout?ae.hideTimeout=setTimeout(()=>{this.outlineWindow.hide()},e==null?void 0:e.timeout):this.outlineWindow.hide(),e!=null&&e.hiddenOnResize&&(this.outlineWindow.hiddenOnResize=e.hiddenOnResize))}updateWindow(){this.outlineWindow.update()}clearWindowHideTimeout(){this.outlineWindow._clearHideTimeout()}};var de=class p{constructor(e){this._outlines=new Map;this.mobileOutsideClickHandler=e=>{if(!this._plugin.app.isMobile)return;let t=this.getActiveMDView();if(!t)return;let n=this.getOutlineInView(t);if(!n.windowVisible)return;let l=n.window.getContainerElement(),r=n.button.getContainerElement(),v=e.target;!l.contains(v)&&!r.contains(v)&&n.hideWindow()};this._plugin=e,this._setupEventListeners()}static initialize(e){return p.instance||(p.instance=new p(e)),p.instance}static getInstance(){if(!p.instance)throw new Error("OutlineStateManager not initialized");return p.instance}getActiveMDView(){return this._plugin.app.workspace.getActiveViewOfType(Je.MarkdownView)}getVisibleMDViews(){return this._plugin.app.workspace.getLeavesOfType("markdown").map(t=>t.view).filter(t=>t.contentEl)}getOutlineInView(e){let t=this._getViewId(e);return this._outlines.has(t)||this._outlines.set(t,new ce(this._plugin,e)),this._outlines.get(t)}updateViewForOutline(e){let t=this.getOutlineInView(e);t.view=e}handleActiveLeafChange(e){this._updateOutlineVisibility(e)}handleMetadataChanged(){let e=this.getActiveMDView();e&&this._updateOutlineVisibility(e,!0)}handleResize(){if(!this._plugin.settings.avoidContentOverlap)return;let e=this.getVisibleMDViews();e.length!==0&&e.forEach(t=>{let n=this.getOutlineInView(t),l=n.windowVisible,r=this._plugin.settings.revealAutomaticallyOnFileOpen?this._isEnoughWidthForAutomaticToggle(t):this._isEnoughWidthForHideOnResize(t);l?r||n.hideWindow({hiddenOnResize:!0}):n.window.hiddenOnResize&&r&&(n.showWindow({hiddenOnResize:!1}),n.windowPinned=!0)})}createButtonsInOpenViews(){let e=this.getVisibleMDViews();e.length!==0&&e.map(t=>this._createButtonInView(t))}removeAll(){this._outlines.forEach(e=>{e.window.destroy(),e.button.destroy()}),this._outlines.clear()}_setupEventListeners(){this._plugin.registerEvent(this._plugin.app.workspace.on("active-leaf-change",e=>{if((e==null?void 0:e.view)instanceof Je.MarkdownView){let t=e.view,n=this.getOutlineInView(t);n.view=t}}))}_getViewId(e){return e.leaf.id}_createButtonInView(e){let t=this.getOutlineInView(e);!t.isButtonVisible&&t.headings&&t.headings.length>1&&t.showButton()}_updateOutlineVisibility(e,t=!1){let n=this.getOutlineInView(e),l=n.headings&&n.headings.length>1,r=l&&n.headings.length>=this._plugin.settings.minimumHeadingsToRevealAutomatically;n.toggleButton(l);let v=!l||!t&&this._plugin.settings.revealAutomaticallyOnFileOpen&&!r,y=!t&&!n.toggledAutomaticallyOnce&&this._plugin.settings.revealAutomaticallyOnFileOpen&&r&&this._isEnoughWidthForAutomaticToggle(e);v?(n.hideWindow(),n.windowPinned=!1):y&&(n.showWindow(),n.windowPinned=!0),n.windowVisible&&(n.toggledAutomaticallyOnce=!0,n.updateWindow())}_isEnoughWidthForAutomaticToggle(e){if(this._plugin.settings.handleContentOverlap==="allow")return!0;let t=this._plugin.settings.handleContentOverlap==="partial"?1:2;return this._calculateAvailableWidth(e,t)>=0}_isEnoughWidthForHideOnResize(e){return this._calculateAvailableWidth(e,2)>=0}_calculateAvailableWidth(e,t=1){var r;let n=e.contentEl.innerWidth,l=(r=this._plugin.getCssVariableAsNumber("--dynamic-outline-window-width"))!=null?r:256;return(n-700)/t-l}};var Z=require("obsidian");var je=require("obsidian");var M=class{constructor(e,t){this.plugin=e,this.containerEl=t}};var he=class extends M{display(){new je.Setting(this.containerEl).setName("Disable search field autofocus").setDesc("Turn off the automatic focusing of the search field when the outline window opens.").addToggle(e=>{e.setValue(this.plugin.settings.disableSearchFieldAutofocus).onChange(async t=>{this.plugin.settings.disableSearchFieldAutofocus=t,await this.plugin.saveSettings()})})}};var et=require("obsidian");var k=class{constructor(e,t,n){this.button=t,this.initialValue=n,this.currentValue=n,this.button.setButtonText("Reload plugin"),this.button.setTooltip("Requires a plugin reload to take effect."),this.button.setDisabled(!0),this.button.setClass("dynamic-outline-reload"),this.button.setCta(),this.button.onClick(()=>{e.reloadPlugin()})}updateValue(e){this.currentValue=e,this.button.setDisabled(this.initialValue===this.currentValue)}};var ge=class extends M{display(){let e,t=this.plugin.settings.disableActiveHeadingHighlighting;new et.Setting(this.containerEl).setName("Disable active heading highlighting").setDesc("Turn off the highlighting of the corresponding heading in the outline as you scroll.").addButton(n=>{e=n}).addToggle(n=>{let l=new k(this.plugin,e,t);n.setValue(this.plugin.settings.disableActiveHeadingHighlighting).onChange(async r=>{this.plugin.settings.disableActiveHeadingHighlighting=r,await this.plugin.saveSettings(),l.updateValue(r)})})}};var tt=require("obsidian");var pe=class extends M{display(){new tt.Setting(this.containerEl).setName("Disable search clear on jump").setDesc("Turn off the ability to keep the search field text after clicking a heading.").addToggle(e=>{e.setValue(this.plugin.settings.disableSearchClearOnJump).onChange(async t=>{this.plugin.settings.disableSearchClearOnJump=t,await this.plugin.saveSettings()})})}};var Ve=require("obsidian");var ve=class extends M{display(){let e,t=this.plugin.settings.revealAutomaticallyOnFileOpen;new Ve.Setting(this.containerEl).setName("Reveal automatically on file open").setDesc("Automatically show or hide the outline when opening a file, based on heading count.").addButton(r=>{e=r}).addToggle(r=>{let v=new k(this.plugin,e,t);r.setValue(this.plugin.settings.revealAutomaticallyOnFileOpen).onChange(async y=>{this.plugin.settings.revealAutomaticallyOnFileOpen=y,await this.plugin.saveSettings(),v.updateValue(y),l.setDisabled(!y),n.setDisabled(!y)})});let n=new Ve.Setting(this.containerEl).setName("Handle content overlap").setDesc("Choose if the outline can overlap page content when space is limited, or if it should hide to prevent overlap.").addDropdown(r=>{r.addOption("allow","Allow").addOption("partial","Partial").addOption("prevent","Prevent").setValue(this.plugin.settings.handleContentOverlap).onChange(async v=>{this.plugin.settings.handleContentOverlap=v,await this.plugin.saveSettings()})}).setClass("dynamic-outline-setting-item-hidden").setDisabled(!this.plugin.settings.revealAutomaticallyOnFileOpen),l=new Ve.Setting(this.containerEl).setName("Minimum number of headings").setDesc("Set the minimum heading count required to automatically show the outline on file open.").addSlider(r=>{r.setLimits(2,10,1).setDynamicTooltip().setValue(this.plugin.settings.minimumHeadingsToRevealAutomatically).onChange(async v=>{this.plugin.settings.minimumHeadingsToRevealAutomatically=v,await this.plugin.saveSettings()})}).setClass("dynamic-outline-setting-item-hidden").setDisabled(!this.plugin.settings.revealAutomaticallyOnFileOpen)}};var it=require("obsidian");var me=class extends M{display(){let e,t=this.plugin.settings.revealOnHover;new it.Setting(this.containerEl).setName("Reveal on hover").setDesc("Show the outline when hovering over its button. Click the button to pin it open.").addButton(n=>{e=n}).addToggle(n=>{let l=new k(this.plugin,e,t);n.setValue(this.plugin.settings.revealOnHover).onChange(async r=>{this.plugin.settings.revealOnHover=r,await this.plugin.saveSettings(),l.updateValue(r)})})}};var nt=require("obsidian");var fe=class extends M{display(){let e,t=this.plugin.settings.outlinePosition;new nt.Setting(this.containerEl).setName("Outline position").setDesc("Set the screen location for the outline window and its trigger button.").addButton(n=>{e=n}).addDropdown(n=>{let l=new k(this.plugin,e,t);n.addOption("right","Right").addOption("left","Left").setValue(this.plugin.settings.outlinePosition).onChange(async r=>{this.plugin.settings.outlinePosition=r,await this.plugin.saveSettings(),l.updateValue(r)})})}};var st=require("obsidian");var we=class extends M{display(){let e,t=this.plugin.settings.disableDynamicHeadingIndentation;new st.Setting(this.containerEl).setName("Disable dynamic heading indentation").setDesc("Turn off automatic heading indentation adjustments based on the previous heading's level.").addButton(n=>{e=n}).addToggle(n=>{let l=new k(this.plugin,e,t);n.setValue(this.plugin.settings.disableDynamicHeadingIndentation).onChange(async r=>{this.plugin.settings.disableDynamicHeadingIndentation=r,await this.plugin.saveSettings(),l.updateValue(r)})})}};var at=require("obsidian");var be=class extends M{display(){new at.Setting(this.containerEl).setName("Disable search bar auto-hide").setDesc("Turn off the automatic hiding of the search bar when the outline contains only a few headings.").addToggle(e=>{e.setValue(this.plugin.settings.disableSearchBarAutoHide).onChange(async t=>{this.plugin.settings.disableSearchBarAutoHide=t,await this.plugin.saveSettings()})})}};var lt=require("obsidian");var _e=class extends M{display(){new lt.Setting(this.containerEl).setName("Hide on jump").setDesc("Hide outline automatically when selecting a heading.").addToggle(e=>{e.setValue(this.plugin.settings.hideOutlineOnJump).onChange(async t=>{this.plugin.settings.hideOutlineOnJump=t,await this.plugin.saveSettings()})})}};var ot=require("obsidian");var ye=class extends M{display(){let e,t=this.plugin.settings.avoidContentOverlap;new ot.Setting(this.containerEl).setName("Avoid content overlap").setDesc("Automatically hide the outline when the note is too narrow.").addButton(n=>{e=n}).addToggle(n=>{let l=new k(this.plugin,e,t);n.setValue(this.plugin.settings.avoidContentOverlap).onChange(async r=>{this.plugin.settings.avoidContentOverlap=r,await this.plugin.saveSettings(),l.updateValue(r)})})}};var rt=require("obsidian");var Ee=class extends M{display(){let e,t=this.plugin.settings.disableHeadingCollapsing;new rt.Setting(this.containerEl).setName("Disable heading collapsing").setDesc("Turn off the ability to collapse/expand heading sections within the outline.").addButton(n=>{e=n}).addToggle(n=>{let l=new k(this.plugin,e,t);n.setValue(this.plugin.settings.disableHeadingCollapsing).onChange(async r=>{this.plugin.settings.disableHeadingCollapsing=r,await this.plugin.saveSettings(),l.updateValue(r)})})}};var ut={handleContentOverlap:"allow",disableActiveHeadingHighlighting:!1,disableDynamicHeadingIndentation:!1,disableSearchBarAutoHide:!1,disableSearchClearOnJump:!1,disableSearchFieldAutofocus:!1,disableHeadingCollapsing:!1,hideOutlineOnJump:!1,minHeadingsToHideSearchBar:5,minimumHeadingsToRevealAutomatically:2,revealAutomaticallyOnFileOpen:!1,revealOnHover:!1,avoidContentOverlap:!1,outlinePosition:"right"};function Be(p){return(0,Z.sanitizeHTMLToDom)(p)}var Ie=class extends Z.PluginSettingTab{constructor(e,t){super(e,t),this.plugin=t}display(){let{containerEl:e}=this;e.empty(),new Z.Setting(e).setName(Be("Window behavior")).setHeading().setDesc("Customize the visibility and behavior of the outline window."),new fe(this.plugin,e).display(),new me(this.plugin,e).display(),new ve(this.plugin,e).display(),new ye(this.plugin,e).display(),new _e(this.plugin,e).display(),new Z.Setting(e).setName(Be("Search bar")).setHeading().setDesc("Customize the search bar behavior."),new be(this.plugin,e).display(),new he(this.plugin,e).display(),new pe(this.plugin,e).display(),new Z.Setting(e).setName(Be("Outline content")).setHeading().setDesc(Be('To customize the appearance of the Dynamic Outline, please use the <a href="https://obsidian.md/plugins?id=obsidian-style-settings">Style Settings</a> plugin.')),new Ee(this.plugin,e).display(),new ge(this.plugin,e).display(),new we(this.plugin,e).display()}};var Oe="dynamic-outline",Re="dynamic-outline-button",qe="list",xe=class extends Y.Plugin{constructor(){super(...arguments);this.highlightCurrentHeadingDebounceHandler=(0,Y.debounce)(t=>{let n=t.target;if(!(n!=null&&n.classList.contains("dynamic-outline-content-container"))){let l=this.stateManager.getActiveMDView();l&&this.stateManager.getOutlineInView(l).window.highlightCurrentHeading()}},0);this.resizeDebounceHandler=(0,Y.debounce)(()=>{this.stateManager.handleResize()},100)}async onload(){await this.loadSettings(),this.addSettingTab(new Ie(this.app,this)),this.app.workspace.trigger("parse-style-settings"),this.stateManager=de.initialize(this),this.app.workspace.onLayoutReady(()=>{this.stateManager.createButtonsInOpenViews()}),this.registerEvent(this.app.workspace.on("active-leaf-change",t=>{if(!((t==null?void 0:t.view)instanceof Y.MarkdownView))return;let n=t.view;this.stateManager.updateViewForOutline(n),this.stateManager.handleActiveLeafChange(n)})),this.app.isMobile&&activeWindow.document.addEventListener("click",this.stateManager.mobileOutsideClickHandler,!0),this.registerEvent(this.app.metadataCache.on("changed",()=>{this.stateManager.handleMetadataChanged()})),this.settings.avoidContentOverlap&&this.registerEvent(this.app.workspace.on("resize",this.resizeDebounceHandler)),this.settings.disableActiveHeadingHighlighting||(activeWindow.document.addEventListener("scroll",this.highlightCurrentHeadingDebounceHandler,!0),this.registerEvent(this.app.metadataCache.on("changed",()=>{let t=this.stateManager.getActiveMDView();t&&this.stateManager.getOutlineInView(t).window.highlightCurrentHeading()}))),this.addCommand({id:"toggle-dynamic-outline",name:"Toggle for current file",checkCallback:t=>{let n=this.stateManager.getActiveMDView();return n?(t||this.stateManager.getOutlineInView(n).button.handleClick(),!0):!1}})}onunload(){this.stateManager.removeAll(),activeWindow.document.removeEventListener("scroll",this.highlightCurrentHeadingDebounceHandler,!0),activeWindow.document.removeEventListener("click",this.stateManager.mobileOutsideClickHandler,!0)}async loadSettings(){this.settings=Object.assign({},ut,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}async reloadPlugin(){let t=this.app.plugins,n=this.app.setting;t.enabledPlugins.has(Oe)&&(await t.disablePlugin(Oe),await t.enablePlugin(Oe),await n.openTabById(Oe),new Y.Notice("Dynamic Outline has been reloaded"))}getCssVariableAsNumber(t,n=document.body){let l=getComputedStyle(n).getPropertyValue(t).trim(),r=parseFloat(l);return isNaN(r)?null:r}runCommand(t){this.app.commands.executeCommandById(t)}};

/* nosourcemap */