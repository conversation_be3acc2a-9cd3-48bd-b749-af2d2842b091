/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var me=Object.create;var k=Object.defineProperty;var he=Object.getOwnPropertyDescriptor;var Se=Object.getOwnPropertyNames;var be=Object.getPrototypeOf,ye=Object.prototype.hasOwnProperty;var y=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Ee=(e,t)=>{for(var n in t)k(e,n,{get:t[n],enumerable:!0})},X=(e,t,n,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of Se(t))!ye.call(e,s)&&s!==n&&k(e,s,{get:()=>t[s],enumerable:!(i=he(t,s))||i.enumerable});return e};var we=(e,t,n)=>(n=e!=null?me(be(e)):{},X(t||!e||!e.__esModule?k(n,"default",{value:e,enumerable:!0}):n,e)),Re=e=>X(k({},"__esModule",{value:!0}),e);var J=y(N=>{"use strict";Object.defineProperty(N,"__esModule",{value:!0});N.CallbacksRegistry=void 0;var F=class{constructor(){this.nextId=0,this.callbacks={},this.callbackIds=new WeakMap,this.locationInfo=new WeakMap}add(t){let n=this.callbackIds.get(t);if(n!=null)return n;n=this.nextId+=1,this.callbacks[n]=t,this.callbackIds.set(t,n);let i=/at (.*)/gi,s=new Error().stack;if(!s)return n;let o,r;for(;(r=i.exec(s))!==null;){let c=r[1];if(c.includes("(native)")||c.includes("(<anonymous>)")||c.includes("callbacks-registry.js")||c.includes("remote.js")||c.includes("@electron/remote/dist"))continue;let m=/([^/^)]*)\)?$/gi.exec(c);m&&(o=m[1]);break}return this.locationInfo.set(t,o),n}get(t){return this.callbacks[t]||function(){}}getLocation(t){return this.locationInfo.get(t)}apply(t,...n){return this.get(t).apply(global,...n)}remove(t){let n=this.callbacks[t];n&&(this.callbackIds.delete(n),delete this.callbacks[t])}};N.CallbacksRegistry=F});var te=y(f=>{"use strict";Object.defineProperty(f,"__esModule",{value:!0});f.deserialize=f.serialize=f.isSerializableObject=f.isPromise=void 0;var Te=require("electron");function Me(e){return e&&e.then&&e.then instanceof Function&&e.constructor&&e.constructor.reject&&e.constructor.reject instanceof Function&&e.constructor.resolve&&e.constructor.resolve instanceof Function}f.isPromise=Me;var ke=[Boolean,Number,String,Date,Error,RegExp,ArrayBuffer];function q(e){return e===null||ArrayBuffer.isView(e)||ke.some(t=>e instanceof t)}f.isSerializableObject=q;var ee=function(e,t){let i=Object.entries(e).map(([s,o])=>[s,t(o)]);return Object.fromEntries(i)};function ve(e){let t=[],n=e.getScaleFactors();if(n.length===1){let i=n[0],s=e.getSize(i),o=e.toBitmap({scaleFactor:i});t.push({scaleFactor:i,size:s,buffer:o})}else for(let i of n){let s=e.getSize(i),o=e.toDataURL({scaleFactor:i});t.push({scaleFactor:i,size:s,dataURL:o})}return{__ELECTRON_SERIALIZED_NativeImage__:!0,representations:t}}function Ne(e){let t=Te.nativeImage.createEmpty();if(e.representations.length===1){let{buffer:n,size:i,scaleFactor:s}=e.representations[0],{width:o,height:r}=i;t.addRepresentation({buffer:n,scaleFactor:s,width:o,height:r})}else for(let n of e.representations){let{dataURL:i,size:s,scaleFactor:o}=n,{width:r,height:c}=s;t.addRepresentation({dataURL:i,scaleFactor:o,width:r,height:c})}return t}function U(e){return e&&e.constructor&&e.constructor.name==="NativeImage"?ve(e):Array.isArray(e)?e.map(U):q(e)?e:e instanceof Object?ee(e,U):e}f.serialize=U;function V(e){return e&&e.__ELECTRON_SERIALIZED_NativeImage__?Ne(e):Array.isArray(e)?e.map(V):q(e)?e:e instanceof Object?ee(e,V):e}f.deserialize=V});var $=y(L=>{"use strict";Object.defineProperty(L,"__esModule",{value:!0});L.getElectronBinding=void 0;var Le=e=>process._linkedBinding?process._linkedBinding("electron_common_"+e):process.electronBinding?process.electronBinding(e):null;L.getElectronBinding=Le});var ne=y(h=>{"use strict";var G,H;Object.defineProperty(h,"__esModule",{value:!0});h.browserModuleNames=h.commonModuleNames=void 0;var We=$();h.commonModuleNames=["clipboard","nativeImage","shell"];h.browserModuleNames=["app","autoUpdater","BaseWindow","BrowserView","BrowserWindow","contentTracing","crashReporter","dialog","globalShortcut","ipcMain","inAppPurchase","Menu","MenuItem","nativeTheme","net","netLog","MessageChannelMain","Notification","powerMonitor","powerSaveBlocker","protocol","pushNotifications","safeStorage","screen","session","ShareMenu","systemPreferences","TopLevelWindow","TouchBar","Tray","utilityProcess","View","webContents","WebContentsView","webFrameMain"].concat(h.commonModuleNames);var S=We.getElectronBinding("features");((G=S==null?void 0:S.isDesktopCapturerEnabled)===null||G===void 0?void 0:G.call(S))!==!1&&h.browserModuleNames.push("desktopCapturer");((H=S==null?void 0:S.isViewApiEnabled)===null||H===void 0?void 0:H.call(S))!==!1&&h.browserModuleNames.push("ImageView")});var ae=y(a=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0});a.createFunctionWithReturnValue=a.getGlobal=a.getCurrentWebContents=a.getCurrentWindow=a.getBuiltin=void 0;var Ae=J(),W=te(),u=require("electron"),Pe=ne(),Ie=$(),{Promise:ze}=global,A=new Ae.CallbacksRegistry,P=new Map,Be=new FinalizationRegistry(e=>{let t=P.get(e);t!==void 0&&t.deref()===void 0&&(P.delete(e),u.ipcRenderer.send("REMOTE_BROWSER_DEREFERENCE",p,e,0))}),Y=new WeakMap,se=new WeakSet;function De(e){let t=P.get(e);if(t!==void 0){let n=t.deref();if(n!==void 0)return n}}function xe(e,t){let n=new WeakRef(t);return P.set(e,n),Be.register(t,e),t}function je(){let e=Ie.getElectronBinding("v8_util");if(e)return e.getHiddenValue(global,"contextId");throw new Error("Electron >=v13.0.0-beta.6 required to support sandboxed renderers")}var p=process.contextId||je();process.on("exit",()=>{let e="REMOTE_BROWSER_CONTEXT_RELEASE";u.ipcRenderer.send(e,p)});var oe=Symbol("is-remote-proxy");function I(e,t=new Set){let n=i=>{if(t.has(i))return{type:"value",value:null};if(i&&i.constructor&&i.constructor.name==="NativeImage")return{type:"nativeimage",value:W.serialize(i)};if(Array.isArray(i)){t.add(i);let s={type:"array",value:I(i,t)};return t.delete(i),s}else{if(i instanceof Buffer)return{type:"buffer",value:i};if(W.isSerializableObject(i))return{type:"value",value:i};if(typeof i=="object"){if(W.isPromise(i))return{type:"promise",then:n(function(o,r){i.then(o,r)})};if(Y.has(i))return{type:"remote-object",id:Y.get(i)};let s={type:"object",name:i.constructor?i.constructor.name:"",members:[]};t.add(i);for(let o in i)s.members.push({name:o,value:n(i[o])});return t.delete(i),s}else return typeof i=="function"&&se.has(i)?{type:"function-with-return-value",value:n(i())}:typeof i=="function"?{type:"function",id:A.add(i),location:A.getLocation(i),length:i.length}:{type:"value",value:i}}};return e.map(n)}function K(e,t,n,i){if(Array.isArray(i))for(let s of i){if(Object.prototype.hasOwnProperty.call(t,s.name))continue;let o={enumerable:s.enumerable};if(s.type==="method"){let r=function(...m){let l;this&&this.constructor===r?l="REMOTE_BROWSER_MEMBER_CONSTRUCTOR":l="REMOTE_BROWSER_MEMBER_CALL";let fe=u.ipcRenderer.sendSync(l,p,n,s.name,I(m));return g(fe)},c=Fe(r,n,s.name);o.get=()=>(c.ref=e,c),o.set=m=>(c=m,m),o.configurable=!0}else s.type==="get"&&(o.get=()=>{let r="REMOTE_BROWSER_MEMBER_GET",c=u.ipcRenderer.sendSync(r,p,n,s.name);return g(c)},s.writable&&(o.set=r=>{let c=I([r]),m="REMOTE_BROWSER_MEMBER_SET",l=u.ipcRenderer.sendSync(m,p,n,s.name,c);return l!=null&&g(l),r}));Object.defineProperty(t,s.name,o)}}function re(e,t,n,i){if(i===null)return;let s={};K(e,s,n,i.members),re(e,s,n,i.proto),Object.setPrototypeOf(t,s)}function Fe(e,t,n){let i=!1,s=()=>{if(i)return;i=!0;let o="REMOTE_BROWSER_MEMBER_GET",r=u.ipcRenderer.sendSync(o,p,t,n);K(e,e,r.id,r.members)};return new Proxy(e,{set:(o,r,c)=>(r!=="ref"&&s(),o[r]=c,!0),get:(o,r)=>{if(r===oe)return!0;Object.prototype.hasOwnProperty.call(o,r)||s();let c=o[r];return r==="toString"&&typeof c=="function"?c.bind(o):c},ownKeys:o=>(s(),Object.getOwnPropertyNames(o)),getOwnPropertyDescriptor:(o,r)=>{let c=Object.getOwnPropertyDescriptor(o,r);return c||(s(),Object.getOwnPropertyDescriptor(o,r))}})}function g(e){if(!e)return{};if(e.type==="value")return e.value;if(e.type==="array")return e.members.map(t=>g(t));if(e.type==="nativeimage")return W.deserialize(e.value);if(e.type==="buffer")return Buffer.from(e.value.buffer,e.value.byteOffset,e.value.byteLength);if(e.type==="promise")return ze.resolve({then:g(e.then)});if(e.type==="error")return ie(e);if(e.type==="exception")throw e.value.type==="error"?ie(e.value):new Error(`Unexpected value type in exception: ${e.value.type}`);{let t;if("id"in e){let n=De(e.id);if(n!==void 0)return n}if(e.type==="function"){let n=function(...i){let s;this&&this.constructor===n?s="REMOTE_BROWSER_CONSTRUCTOR":s="REMOTE_BROWSER_FUNCTION_CALL";let o=u.ipcRenderer.sendSync(s,p,e.id,I(i));return g(o)};t=n}else t={};return K(t,t,e.id,e.members),re(t,t,e.id,e.proto),t.constructor&&t.constructor[oe]&&Object.defineProperty(t.constructor,"name",{value:e.name}),Y.set(t,e.id),xe(e.id,t),t}}function ie(e){let t=e.value;for(let{name:n,value:i}of e.members)t[n]=g(i);return t}function Ue(e){return typeof e.senderId=="number"}function ce(e,t){u.ipcRenderer.on(e,(n,i,s,...o)=>{if(Ue(n)&&n.senderId!==0&&n.senderId!==void 0){console.error(`Message ${e} sent by unexpected WebContents (${n.senderId})`);return}i===p?t(s,...o):u.ipcRenderer.send("REMOTE_BROWSER_WRONG_CONTEXT_ERROR",p,i,s)})}var Ve=process.argv.includes("--enable-api-filtering-logging");function w(){let e={stack:void 0};return Ve&&Error.captureStackTrace(e,w),e.stack}ce("REMOTE_RENDERER_CALLBACK",(e,t)=>{A.apply(e,g(t))});ce("REMOTE_RENDERER_RELEASE_CALLBACK",e=>{A.remove(e)});a.require=e=>{let t="REMOTE_BROWSER_REQUIRE",n=u.ipcRenderer.sendSync(t,p,e,w());return g(n)};function qe(e){let t="REMOTE_BROWSER_GET_BUILTIN",n=u.ipcRenderer.sendSync(t,p,e,w());return g(n)}a.getBuiltin=qe;function $e(){let e="REMOTE_BROWSER_GET_CURRENT_WINDOW",t=u.ipcRenderer.sendSync(e,p,w());return g(t)}a.getCurrentWindow=$e;function Ge(){let e="REMOTE_BROWSER_GET_CURRENT_WEB_CONTENTS",t=u.ipcRenderer.sendSync(e,p,w());return g(t)}a.getCurrentWebContents=Ge;function He(e){let t="REMOTE_BROWSER_GET_GLOBAL",n=u.ipcRenderer.sendSync(t,p,e,w());return g(n)}a.getGlobal=He;Object.defineProperty(a,"process",{enumerable:!0,get:()=>a.getGlobal("process")});function Ye(e){let t=()=>e;return se.add(t),t}a.createFunctionWithReturnValue=Ye;var Ke=e=>{Object.defineProperty(a,e,{enumerable:!0,get:()=>a.getBuiltin(e)})};Pe.browserModuleNames.forEach(Ke)});var le=y(E=>{"use strict";var Xe=E&&E.__createBinding||(Object.create?function(e,t,n,i){i===void 0&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,i){i===void 0&&(i=n),e[i]=t[n]}),Ze=E&&E.__exportStar||function(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&Xe(t,e,n)};Object.defineProperty(E,"__esModule",{value:!0});if(process.type==="browser")throw new Error('"@electron/remote" cannot be required in the browser process. Instead require("@electron/remote/main").');Ze(ae(),E)});var ue=y((bt,de)=>{de.exports=le()});var Qe={};Ee(Qe,{default:()=>D});module.exports=Re(Qe);var O=require("obsidian");var d=class{static enable(){this.isEnabled=!0}static disable(){this.isEnabled=!1}static info(t,...n){this.isEnabled&&console.log(`[sticky-notes-plugin]: ${t}`,...n)}static warn(t,...n){this.isEnabled&&console.warn(`[sticky-notes-plugin]: ${t}`,...n)}static error(t,...n){this.isEnabled&&console.error(`[sticky-notes-plugin]: ${t}`,...n)}};d.isEnabled=!1;var _=(l=>(l.DEFAULT="Default",l.RED="Red",l.ORANGE="Orange",l.YELLOW="Yellow",l.GREEN="Green",l.CYAN="Cyan",l.BLUE="Blue",l.PURPLE="Purple",l.PINK="Pink",l))(_||{});var C=(i=>(i.DEFAULT="Default",i.CUSTOM="Custom",i.REMEMBER_LAST="Remember last",i))(C||{});var Oe=300,_e=300,Z={sizeOption:"Default",dimensions:`${Oe}x${_e}`,resizable:!1,defaultColor:"Default"};var v=class{constructor(t){this.plugin=t}get settings(){return this._settings}async initSettings(){await this.loadSettings()}async updateSettings(t){d.info("Updated settings",this.updateSettings),this._settings={...this._settings,...t},await this.saveSettings()}async updateWindowDimensions(t,n){let i=`${t}x${n}`;this.updateSettings({dimensions:i})}getWindowDimensions(){return this._settings.dimensions.split("x").map(Number)}async loadSettings(){this._settings=await Object.assign({},Z,await this.plugin.loadData()),this.plugin.globalSettings=this._settings}async saveSettings(){await this.plugin.saveData(this._settings)}};function Ce(e){return e==="Default"?"--color-base-00":`--sticky-note-${e.toLowerCase()}`}function x(e){let t=Ce(e);return e==="Default"?`var(${t})`:`rgb(var(${t}))`}function Q(e){return`sticky-note-text-${e.toLowerCase()}`}var j=Object.values(_).map(e=>({color:x(e),label:e}));var R=require("obsidian"),ge=we(ue());var pe=require("obsidian"),z=class extends pe.Menu{constructor(n){super();this.items=[];this.tempState=!1;this.body=n,this.addColorItems()}addColorItems(){for(let n of j)this.addItem(i=>this.items.push(i.setTitle(n.label).onClick(()=>{this.body.setCssProps({"--background-primary":n.color})})))}onload(){super.onload();let n=this.body.querySelector(".menu-scroll");if(n){n.addClass("color-menu");for(let i=0;i<n.children.length;i++){let s=n.children.item(i);s==null||s.addClasses([Q(j[i].label),"color-menu-item"])}}}};var T=class{constructor(t,n){this.DEFAULT_COLOR="Yellow";this.settingService=n,this.leaf=t,this.view=t.view,this.document=this.leaf.getContainer().win.activeDocument,this.id=T.stickyNoteId,T.stickyNoteId++,T.leafsList.add(this)}get title(){return`sticky-note-${this.id}`}async initStickyNote(t=null){d.info(`Init Sticky Note ${this.id} ...`),this.document.title=this.title,this.document.documentElement.setAttribute("note-id",this.title),this.initColorMenu(),this.initView(),this.initMainWindow(),t&&await this.leaf.openFile(t)}initView(){d.info("Updaing Sticky Note view"),this.view=this.leaf.view,this.removeDefaultActionsMenu(),this.removeHeader(),this.addStickyNoteActions()}initMainWindow(){let n=ge.BrowserWindow.getAllWindows().find(o=>o.title===this.title);if(!n){d.warn(`Sticky note ${this.title} does not have an electron window`);return}this.mainWindow=n;let[i,s]=this.settingService.getWindowDimensions();this.mainWindow.setSize(i,s),this.mainWindow.setResizable(this.settingService.settings.resizable),this.settingService.settings.sizeOption==="Remember last"&&this.mainWindow.on("resize",()=>this.saveDimensions()),this.pinAction(!0)}saveDimensions(){if(!this.mainWindow)return;let[t,n]=this.mainWindow.getSize();this.settingService.updateWindowDimensions(t,n)}removeDefaultActionsMenu(){let t=this.view.containerEl.querySelector(".view-actions"),n=this.view.containerEl.querySelector(".view-header-left");t==null||t.empty(),n==null||n.empty()}removeHeader(){let t=this.document.querySelector(".workspace-tab-header-container"),n=this.document.querySelector(".titlebar");t==null||t.remove(),n==null||n.empty()}addStickyNoteActions(){var n;if(!(this.view instanceof R.ItemView))return;let t=this.document.querySelector(".view-header-title-container");t==null||t.setCssProps({"app-region":"drag","-webkit-app-region":"drag"}),this.view.addAction("x","Close",()=>this.leaf.detach()).addClass("sticky-note-button"),this.view.addAction("minus","Minimize",()=>{var i;return(i=this.mainWindow)==null?void 0:i.minimize()}).addClass("sticky-note-button"),this.view.addAction((n=this.mainWindow)!=null&&n.isAlwaysOnTop()?"pin-off":"pin","Pin",()=>this.pinAction()).addClasses(["pinButton","sticky-note-button"]),this.view.addAction("palette","Color",i=>this.colorMenu.showAtMouseEvent(i)).addClass("sticky-note-button")}pinAction(t){if(!this.mainWindow)return;let n=t!==void 0?t:!this.mainWindow.isAlwaysOnTop();this.mainWindow.setAlwaysOnTop(n);let i=this.view.containerEl.querySelector(".pinButton");i&&((0,R.setIcon)(i,n?"pin-off":"pin"),(0,R.setTooltip)(i,n?"UnPin":"Pin"))}initColorMenu(){this.colorMenu=new z(this.document.body),this.setDefaultColor()}setDefaultColor(){this.document.body.setCssProps({"--background-primary":x(this.DEFAULT_COLOR)})}},b=T;b.stickyNoteId=0,b.leafsList=new Set;var M=require("obsidian");var B=class extends M.PluginSettingTab{constructor(n,i,s){super(n,i);this.settingService=s}async display(){if(this.containerEl.empty(),!this.settingService.settings){this.containerEl.createEl("p",{text:"falied-to-load-settings"});return}this.addSizeSetting(),this.addResizableSetting()}addSizeSetting(){return new M.Setting(this.containerEl).setName("Default size").setDesc("Select what default size each new sticky note window should take. Make sure its in the correct format e.g (width x height)").addDropdown(n=>n.addOptions(Object.fromEntries(Object.values(C).map(i=>[i,i]))).setValue(this.settingService.settings.sizeOption).onChange(async i=>{this.settingService.updateSettings({sizeOption:i}),this.disabledDimensionSetting(i!=="Custom"),i==="Default"&&(this.settingService.updateWindowDimensions(300,300),this.dimensionsSettingComponent.setValue(this.settingService.settings.dimensions)),i==="Remember last"?(this.settingService.updateSettings({resizable:!0}),this.resizableSettingComponent.setValue(!0),this.disabledResizableSetting(!0)):this.disabledResizableSetting(!1)})).addText(n=>(this.dimensionsSettingComponent=n.setPlaceholder("eg.: 300x300").setValue(this.settingService.settings.dimensions).onChange(async i=>{let s="300x300";if(i.trim()==="")s="300x300";else if(i.match(/^\d+x\d+$/))s=i;else return;await this.settingService.updateSettings({dimensions:s})}),this.disabledDimensionSetting(this.settingService.settings.sizeOption!=="Custom"),this.dimensionsSettingComponent))}addResizableSetting(){return new M.Setting(this.containerEl).setName("Resizable Window").setDesc("Enable or disable window resizing for new sticky notes.").addToggle(n=>(this.resizableSettingComponent=n.setValue(this.settingService.settings.resizable||this.settingService.settings.sizeOption==="Remember last").onChange(async i=>{await this.settingService.updateSettings({resizable:i})}),this.disabledResizableSetting(this.settingService.settings.sizeOption==="Remember last"),this.resizableSettingComponent))}disabledDimensionSetting(n){this.dimensionsSettingComponent&&this.disableSettingComponent(this.dimensionsSettingComponent,this.dimensionsSettingComponent.inputEl,n)}disabledResizableSetting(n){this.resizableSettingComponent&&this.disableSettingComponent(this.resizableSettingComponent,this.resizableSettingComponent.toggleEl,n)}disableSettingComponent(n,i,s){return n.setDisabled(s),s?i.addClass("disabled-setting"):i.removeClass("disabled-setting"),n}};var D=class extends O.Plugin{async onload(){d.disable(),d.info("Sticky Notes : plugin loading...."),this.addSettings(),this.addStickyNoteRibbonAction(),this.addStickyNoteCommand(),this.addStickyNoteMenuOptions(),this.addLeafChangeListner()}onunload(){d.info("Stiky Notes : plugin UN-loading ....")}destroyAllStickyNotes(){b.leafsList.forEach(n=>n.leaf.detach())}addStickyNoteCommand(){this.addCommand({id:"open-sticky-note-view",name:"Open sticky note window",icon:"sticky-note",callback:()=>this.openStickyNotePopup()}),this.addCommand({id:"destroy-sticky-note-views",name:"Destroy all sticky notes",icon:"copy-x",callback:()=>this.destroyAllStickyNotes()})}addStickyNoteRibbonAction(){let n=this.addRibbonIcon("sticky-note","Open sticky note",()=>this.openStickyNotePopup());(0,O.setTooltip)(n,"Sticky note popup")}addStickyNoteMenuOptions(){let n=this.app.workspace.on("file-menu",(s,o)=>o instanceof O.TFile&&this.addStickyNoteMenuItem(s,o)),i=this.app.workspace.on("editor-menu",(s,o,r)=>this.addStickyNoteMenuItem(s,r.file));this.registerEvent(n),this.registerEvent(i)}addStickyNoteMenuItem(n,i){n.addItem(s=>{s.setTitle("Open sticky note").setIcon("sticky-note").onClick(()=>this.openStickyNotePopup(i))})}async addSettings(){this.settingsManager=new v(this),await this.settingsManager.initSettings(),this.addSettingTab(new B(this.app,this,this.settingsManager))}addLeafChangeListner(){let n=this.app.workspace.on("active-leaf-change",i=>{let s=i==null?void 0:i.getContainer().win.activeDocument.documentElement.getAttribute("note-id");b.leafsList.forEach(o=>{o.title===s&&o.initView()})});this.registerEvent(n)}async openStickyNotePopup(n=null){d.info("Opened Sticky Note Popup"),n=n!=null?n:this.app.workspace.getActiveFile();let i=this.app.workspace.openPopoutLeaf({size:{height:300,width:300}});await new b(i,this.settingsManager).initStickyNote(n)}};

/* nosourcemap */