/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => PerplexityConverter
});
module.exports = __toCommonJS(main_exports);
var import_obsidian = require("obsidian");
var DEFAULT_SETTINGS = {
  sourceHeaders: ["Quellen", "Sources", "Citations:"]
};
var PerplexityConverter = class extends import_obsidian.Plugin {
  async onload() {
    await this.loadSettings();
    this.addCommand({
      id: "process-selected-text",
      name: "Process selected text",
      editorCallback: (editor) => {
        const selectedText = editor.getSelection();
        const processedText = this.process_text(selectedText);
        editor.replaceSelection(processedText);
      }
    });
    this.addSettingTab(new PerplexityConverterSettingTab(this.app, this));
  }
  onunload() {
  }
  // Text processing function
  process_text(text) {
    if (!text) {
      return "";
    }
    const lines = text.split("\n");
    if (!lines.some((line) => this.settings.sourceHeaders.includes(line.trim()))) {
      return text;
    }
    const urls = {};
    let inSourcesSection = false;
    for (const line of lines) {
      if (this.settings.sourceHeaders.includes(line)) {
        inSourcesSection = true;
        continue;
      }
      if (inSourcesSection && line) {
        const numberMatch = line.match(/\[(\d+)\]/);
        if (numberMatch) {
          const number = numberMatch[1];
          const urlMatch = line.match(/(https?:\/\/\S+)/);
          if (urlMatch) {
            const url = urlMatch[1].trim();
            urls[number] = url;
          }
        }
      }
    }
    const modifiedLines = [];
    inSourcesSection = false;
    for (const line of lines) {
      if (this.settings.sourceHeaders.includes(line)) {
        inSourcesSection = true;
        modifiedLines.push(line);
        continue;
      }
      if (inSourcesSection && line) {
        const numberMatch = line.match(/\[(\d+)\]/);
        if (numberMatch) {
          const number = numberMatch[1];
          const urlMatch = line.match(/(https?:\/\/\S+)/);
          if (urlMatch) {
            const url = urlMatch[1].trim();
            const textStart = line.indexOf("]") + 1;
            const textEnd = line.indexOf("http");
            if (textStart > 0 && textEnd > 0) {
              const text2 = line.slice(textStart, textEnd).trim();
              if (text2) {
                modifiedLines.push(`\\[${number}\\] [${text2}](${url})`);
              } else {
                modifiedLines.push(`\\[${number}\\] ${url}`);
              }
              continue;
            }
          }
        }
        modifiedLines.push(line);
      } else {
        let currentLine = line;
        for (const [number, url] of Object.entries(urls)) {
          currentLine = currentLine.replaceAll(
            `[${number}]`,
            `[\`[${number}]\`](${url})`
          );
        }
        modifiedLines.push(currentLine);
      }
    }
    return modifiedLines.join("\n");
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
};
var PerplexityConverterSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    new import_obsidian.Setting(containerEl).setName("References start line tags").setDesc("Strings (complete lines) that indicate the start of the references section").addTextArea((text) => text.setPlaceholder("Enter your start line tags").setValue(this.plugin.settings.sourceHeaders.join("\n")).then((textArea) => {
      textArea.inputEl.addClass("reference-start-line-tags-textarea");
    }).onChange(async (value) => {
      this.plugin.settings.sourceHeaders = value.trimEnd().split("\n").filter((item) => item.trim());
      await this.plugin.saveSettings();
    }));
  }
};


/* nosourcemap */