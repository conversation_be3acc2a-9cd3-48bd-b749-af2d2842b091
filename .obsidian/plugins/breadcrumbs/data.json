{"addDendronNotes": false, "addDateNotes": false, "aliasesInIndex": false, "alphaSortAsc": true, "altLinkFields": [], "CSVPaths": "", "createIndexIndent": "  ", "dateFormat": "YYYY-MM-DD", "dateNoteFormat": "yyyy-MM-dd", "dateNoteField": "next", "dataviewNoteField": "up", "dateNoteAddMonth": "", "dateNoteAddYear": "", "debugMode": "WARN", "dendronNoteDelimiter": ".", "dendronNoteField": "up", "dvWaitTime": 5000, "enableAlphaSort": true, "enableRelationSuggestor": true, "fieldSuggestor": true, "filterImpliedSiblingsOfDifferentTypes": false, "jugglLayout": "concentric", "limitWriteBCCheckboxes": ["↑", "↔️", "↓", "→", "←"], "CHECKBOX_STATES_OVERWRITTEN": false, "gridDefaultDepth": 25, "hierarchyNotes": [""], "hierarchyNoteIsParent": false, "HNUpField": "", "indexNotes": [], "namingSystemField": "", "namingSystemRegex": "", "namingSystemSplit": ".", "namingSystemEndsWithDelimiter": false, "refreshOnNoteChange": false, "useAllMetadata": true, "openMatrixOnLoad": false, "openDuckOnLoad": false, "openDownOnLoad": false, "parseJugglLinksWithoutJuggl": true, "showNameOrType": true, "showRelationType": true, "regexNoteField": "", "relSuggestorTrigger": "\\", "rlLeaf": true, "showAllPathsIfNoneToIndexNote": true, "showAllAliases": false, "showBCs": false, "showBCsInEditLPMode": false, "showRefreshNotice": true, "showImpliedRelations": true, "showTrail": false, "showGrid": false, "showJuggl": true, "showPrevNext": true, "sortByNameShowAlias": false, "squareDirectionsOrder": [0, 1, 2, 3, 4], "limitTrailCheckboxes": ["up", "↑"], "limitJumpToFirstFields": ["↑", "↔️", "↓", "→", "←"], "showAll": "Shortest", "noPathMessage": "This note has no real or implied parents", "tagNoteField": "", "threadIntoNewPane": false, "threadingTemplate": "{{field}} of {{current}}", "threadingDirTemplates": {"up": "", "same": "", "down": "", "next": "", "prev": ""}, "threadUnderCursor": false, "trailSeperator": "→", "treatCurrNodeAsImpliedSibling": true, "trimDendronNotes": false, "respectReadableLineLength": true, "userHiers": [{"up": ["up"], "same": ["same"], "down": ["down"], "next": ["next"], "prev": ["prev"]}, {"up": ["↑"], "same": ["↔️"], "down": ["↓"], "next": ["→"], "prev": ["←"]}], "writeBCsInline": false, "showWriteAllBCsCmd": false, "visGraph": "Arc Diagram", "visRelation": "Parent", "visClosed": "Real", "visAll": "All", "wikilinkIndex": true, "impliedRelations": {"siblingIdentity": false, "sameParentIsSibling": true, "siblingsSiblingIsSibling": true, "siblingsParentIsParent": true, "parentsSiblingsIsParents": true, "parentsParentsIsParent": false, "cousinsIsSibling": true}, "refreshOnNoteSave": true, "showUpInJuggl": true}