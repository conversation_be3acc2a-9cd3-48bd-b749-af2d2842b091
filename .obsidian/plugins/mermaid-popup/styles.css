
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000; /* Ensure the overlay is on top */
}

.popup-content {
    display: flex;
    justify-content: center;
    align-items: center;

    z-index: 1001;
    cursor: pointer; /* 悬停时鼠标变为手掌 */
}
  
.popup-content.dragging {
    cursor: grabbing; /* 拖动时鼠标变为抓手 */
}

.popup-content > * {
    max-width: 300% !important;
}

.popup-content > img {
    pointer-events: none
}

.theme-light .popup-content {
    background-color: var(--background-primary) !important; /* 白色背景 */
}

/* 深色模式下的弹窗背景 */
.theme-dark .popup-content {
    background-color: var(--background-primary) !important; /* 深灰色背景 */
}

/*开启弹窗按钮*/
div.mermaid-popup-button, div.mermaid-popup-button-reading {
    position: absolute !important;
    right: 35px;
    top: 4px;
    color: var(--text-muted);
    width: 30px;
    height: 26px;

    padding: 4px 3px 3px 6px;
    border-radius: 4px;
    cursor: move; /* 鼠标显示为拖动图标 */
    z-index: 9999;
}

div.mermaid-popup-button:hover, div.mermaid-popup-button-reading:hover {
    background-color: rgb(92, 92, 92, 0.1); /* 鼠标悬停时颜色变化 */
    cursor:default;
}

/*操作弹窗按钮*/
.button-container {
    position: absolute;
    bottom: 50px; /* Adjusted for desired position */
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 5px;
    justify-content: center;
    background: rgba(255, 255, 255, 0.7); /* Semi-transparent background for the button container */
    border-radius: 5px;
    padding: 5px;
    backdrop-filter: blur(5px); /* Background semi-blur */
    z-index: 1002; /* Ensure the button container is on top of the content */
}
.control-button {
    background: rgba(255, 255, 255, 0.7); /* Semi-transparent background */
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 5px;
    cursor: pointer;
    font-size: 16px;
}

.control-button:hover {
    background: rgba(255, 255, 255, 0.9);
}

.arrow-up, .arrow-down, .arrow-left, .arrow-right, .zoom-in, .zoom-out, .close-popup {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 按钮的 hover 样式 */
.arrow-up:hover, .arrow-down:hover, .arrow-left:hover, .arrow-right:hover, 
.zoom-in:hover, .zoom-out:hover, .close-popup:hover {
    background-color: var(--button-bg-hover);
    color: var(--button-color-hover);
}

.setting-item-on-top-line{
    border-top: 0px;
}

/*以下是配置中已存图表来源的表格样式*/

/* 定义 kv-row 的 flex 布局样式 */
.kv-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

/* 输入框样式，可以定义宽度和边距 */
.kv-row input {
    margin-right: 10px;
    width: 200px;
}

/* 保存按钮的样式 */
.kv-row button {
    margin-right: 10px;
}

/* 键值对显示区域的样式 */
.kv-display {
    margin-top: 20px;
}
/* 布局 */
.setting-table table {
    width: 100%;  
}

.setting-table td {
    padding-right: 30px;
    text-align: left;
}

.setting-table .ori_diagram_height{
    display:ruby;
}

.setting-table .ori_diagram_height_cur{
    margin-left: 30px;
}

.setting-table .ori_diagram_height_val{
    width: 50px;
}

.setting-table .settings-icon{
    display: block;
    border-top: 0px;
}

.open_btn_pos_slide_title{
    margin-right: 5px !important;
}

.open_btn_pos_slide_width{
    width: 20% !important;
}
.open_btn_pos_cur_title{
    margin-left: 20px;
}
.open_btn_pos_cur_val{
    width: 30px;
}
.open_btn_pos_cur_per{
    margin-right: 30px;
}

.open_btn_pos .settings-icon{
    display: block;
    border-top: 0px;
}

/* 表格样式 */
.kv-display table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    border: 2px solid #444 !important;  /* 外边框 */
}

/* 表格标题栏样式 */
.kv-display th {
    background-color: var(--interactive-accent) !important;
    padding: 10px;
    border: 1px solid #444 !important;  /* 表头的边框 */
    text-align: left;
}

/* 表格行样式 */
.kv-display td {
    padding: 10px;
    border: 1px solid #444 !important;  /* 单元格边框 */
}

/* 表格行的背景颜色交替 */
.kv-display tr:nth-child(even) {
    background-color: var(--interactive-accent);
}

/* 鼠标悬停时突出显示表格行 */
.kv-display tr:hover {
    background-color: #4a4a4a;
}

.config-text {
    border-bottom: 1px solid gray; /* 灰色横线 */
    padding-bottom: 10px; /* 给横线和文字之间添加一些间距 */
    margin-bottom: 10px; /* 横线与下面内容之间的间距 */
}

.config-text-connect{
    border-bottom: 1px solid gray; /* 灰色横线 */
    padding-bottom: 10px; /* 给横线和文字之间添加一些间距 */
    margin-bottom: 10px; /* 横线与下面内容之间的间距 */   
    margin-top: 30px;
}

