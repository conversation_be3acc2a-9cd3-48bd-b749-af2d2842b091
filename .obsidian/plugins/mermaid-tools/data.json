{"categories": {"Flowchart": "Flowchart", "SequenceDiagram": "SequenceDiagram", "ClassDiagram": "ClassDiagram", "StateDiagram": "StateDiagram", "EntityRelationshipDiagram": "EntityRelationshipDiagram", "UserJourneyDiagram": "UserJourneyDiagram", "GanttChart": "<PERSON><PERSON><PERSON><PERSON>hart", "PieChart": "<PERSON><PERSON><PERSON>", "RequirementDiagram": "RequirementDiagram", "GitGraph": "GitGraph", "Mindmap": "Mindmap", "Timeline": "Timeline", "C4Diagram": "C4Diagram", "QuadrantChart": "QuadrantChart", "SankeyDiagram": "SankeyDiagram", "XyChart": "XyChart", "Kanban": "Ka<PERSON><PERSON>", "Architecture": "Architecture", "Block": "Block", "Packet": "Packet"}, "elements": [{"id": "4d2a6e88-6a3a-4631-9cda-1d4c352af869", "category": "Flowchart", "description": "a simple flowchart with top to down direction", "content": "flowchart TD\nStart --> Stop", "sortingOrder": 1, "isPinned": false}, {"id": "2fd5d3f3-4682-446e-8a2a-22e3db040798", "category": "Flowchart", "description": "a simple flowchart with left to right direction", "content": "flowchart LR\nStart --> Stop", "sortingOrder": 2, "isPinned": false}, {"id": "5b459936-e527-45ce-9594-89b956aa0f50", "category": "Flowchart", "description": "A node with round edges", "content": "id1(Some text)", "sortingOrder": 3, "isPinned": false}, {"id": "8ab02169-2d12-4250-9b1b-699abe55ee0d", "category": "Flowchart", "description": "A stadium-shaped node", "content": "id1([Some text])", "sortingOrder": 4, "isPinned": false}, {"id": "969b2e12-0549-47d0-8b92-341df5e5c4c5", "category": "Flowchart", "description": "A node in a cylindrical shape", "content": "id1[(Database)]", "sortingOrder": 5, "isPinned": false}, {"id": "3bba34c3-77bf-42b7-978c-008266a65627", "category": "Flowchart", "description": "Circle", "content": "id1((Some text))", "sortingOrder": 6, "isPinned": false}, {"id": "6cd5a13d-237c-407a-bcc5-9b52b7f0dd7d", "category": "Flowchart", "description": "Rhombus", "content": "id1{Some text}", "sortingOrder": 7, "isPinned": false}, {"id": "5936c84c-090d-49d0-aebe-f0ea4443fe32", "category": "Flowchart", "description": "Hexagon", "content": "id1{{Some text}}", "sortingOrder": 8, "isPinned": false}, {"id": "17492a98-5bb1-4f16-bd03-99e4583e6de6", "category": "Flowchart", "description": "Parallelogram skewed right", "content": "id1[/Some text/]", "sortingOrder": 9, "isPinned": false}, {"id": "bf773b8a-b698-4a5a-ae8b-d49428c83ab3", "category": "Flowchart", "description": "Parallelogram skewed left", "content": "id1[\\Some text\\]", "sortingOrder": 10, "isPinned": false}, {"id": "f9c7201d-777f-4959-bd58-fb74ab2bf954", "category": "Flowchart", "description": "Trapezoid", "content": "A[/Some text\\]", "sortingOrder": 11, "isPinned": false}, {"id": "7c3cbc14-d0b3-4b65-bc2d-4ad62a93a171", "category": "Flowchart", "description": "Trapezoid upside down", "content": "A[\\Some text/]", "sortingOrder": 12, "isPinned": false}, {"id": "172beea7-e753-4ed1-a094-daeea2ee2d6c", "category": "Flowchart", "description": "Double circle node", "content": "id1(((Some text)))", "sortingOrder": 13, "isPinned": false}, {"id": "da693564-19a6-478d-ba77-3956fd74fff9", "category": "Flowchart", "description": "A link with arrow head", "content": "A-->B", "sortingOrder": 14, "isPinned": false}, {"id": "061cc650-680c-408b-839a-e6dc101a6d12", "category": "Flowchart", "description": "An open link", "content": "A --- B", "sortingOrder": 15, "isPinned": false}, {"id": "a281b571-690e-48db-bead-575d4d205064", "category": "Flowchart", "description": "Text on links", "content": "A-- This is the text! ---B", "sortingOrder": 16, "isPinned": false}, {"id": "1cb29c71-49a5-4c02-836a-9ad14f5b460a", "category": "Flowchart", "description": "A link with arrow head and text", "content": "A-->|text|B", "sortingOrder": 17, "isPinned": false}, {"id": "38af869d-44f7-4c79-817f-efacc2cfe8f2", "category": "Flowchart", "description": "Dotted link", "content": "A-.->B", "sortingOrder": 18, "isPinned": false}, {"id": "ba2a057d-ac8c-4fd0-9898-1b29754d9305", "category": "Flowchart", "description": "Thick link", "content": "A ==> B", "sortingOrder": 19, "isPinned": false}, {"id": "b7d9d436-8fdd-48de-9d2c-30f3de92e898", "category": "Flowchart", "description": "Invisible link", "content": "A ~~~ B", "sortingOrder": 20, "isPinned": false}, {"id": "72fbc8a5-cfe3-438f-869d-e533986a6cb2", "category": "Flowchart", "description": "Link with circle edge", "content": "A --o B", "sortingOrder": 21, "isPinned": false}, {"id": "61d86bc8-50a6-46d9-bf0a-20dbaa65101f", "category": "Flowchart", "description": "Link with cross edge", "content": "A --x B", "sortingOrder": 22, "isPinned": false}, {"id": "596c17c8-cc60-410e-84d6-1515d09e8dd3", "category": "Flowchart", "description": "Subgraph", "content": "subgraph one\na1-->a2\nend", "sortingOrder": 14, "isPinned": false}, {"id": "934da035-b97d-4544-840c-f9d28cbb066b", "category": "SequenceDiagram", "description": "a simple sequence diagram", "content": "sequenceDiagram\nAlice->>John: Hello <PERSON>, how are you?\nJohn-->>Alice: <PERSON>!\nAlice-)<PERSON>: See you later!", "sortingOrder": 0, "isPinned": false}, {"id": "0c87a36d-940d-4ecb-be56-c1157b63cde1", "category": "SequenceDiagram", "description": "a simple sequence diagram with actors", "content": "sequenceDiagram\nactor <PERSON>\nactor <PERSON>->>John: Hello <PERSON>, how are you?\nJohn-->><PERSON>: <PERSON>!\nAlice-)<PERSON>: See you later!", "sortingOrder": 1, "isPinned": false}, {"id": "877533ba-aa35-47ca-beff-8b3872987936", "category": "ClassDiagram", "description": "sample class", "content": "class Duck{\n            +String beakColor\n            +swim()\n            +quack()\n        }", "sortingOrder": 0, "isPinned": false}, {"id": "01690dcd-df3f-4b1c-a2b6-601d174e53a9", "category": "ClassDiagram", "description": "sample class", "content": "class BankAccount\n        BankAccount : +String owner\n        BankAccount : +Bigdecimal balance\n        BankAccount : +deposit(amount)\n        BankAccount : +withdrawal(amount)", "sortingOrder": 1, "isPinned": false}, {"id": "9ce5ae0b-212f-48c3-9714-62d426bc0bd4", "category": "ClassDiagram", "description": "generic class", "content": "class Square~Shape~{\n            int id\n            List~int~ position\n            setPoints(List~int~ points)\n            getPoints() List~int~\n        }\n        \n        Square : -List~string~ messages\n        Square : +setMessages(List~string~ messages)\n        Square : +getMessages() List~string~", "sortingOrder": 2, "isPinned": false}, {"id": "1534700b-1c0e-4e23-ae3c-a6bcef6b9ffe", "category": "ClassDiagram", "description": "inheritance", "content": "classA <|-- classB", "sortingOrder": 3, "isPinned": false}, {"id": "d3a6d043-fbfe-446a-baf3-44e3b977046a", "category": "ClassDiagram", "description": "composition", "content": "classC *-- classD", "sortingOrder": 4, "isPinned": false}, {"id": "4bf31564-6092-42ad-aac1-a1867d6e40e9", "category": "ClassDiagram", "description": "aggregation", "content": "classE o-- classF", "sortingOrder": 5, "isPinned": false}, {"id": "3da4fc59-9bc1-4e0d-ba93-206c28cd9ed0", "category": "ClassDiagram", "description": "association", "content": "classG <-- classH", "sortingOrder": 6, "isPinned": false}, {"id": "f59e6d21-e86e-4389-ae03-49e19e47e34d", "category": "ClassDiagram", "description": "solid link", "content": "classI -- classJ", "sortingOrder": 7, "isPinned": false}, {"id": "ad324112-bb0e-47da-94e4-e46ee820b9ee", "category": "ClassDiagram", "description": "dependency", "content": "classK <.. classL", "sortingOrder": 8, "isPinned": false}, {"id": "99bd4790-8fd6-4d64-a111-00f5021cc5fe", "category": "ClassDiagram", "description": "realization", "content": "classM <|.. classN", "sortingOrder": 9, "isPinned": false}, {"id": "805eaf15-4faa-45df-8a89-8584f84ced0b", "category": "ClassDiagram", "description": "dashed link", "content": "classO .. classP", "sortingOrder": 10, "isPinned": false}, {"id": "4039cfbb-cb9b-438d-9b1f-91b7f95cd7c1", "category": "ClassDiagram", "description": "two-way relation", "content": "Animal <|--|> Zebra", "sortingOrder": 11, "isPinned": false}, {"id": "16f50a08-22a1-4846-aeaa-1e8d250a5782", "category": "ClassDiagram", "description": "sample class diagram", "content": "classDiagram\n        Animal <|-- <PERSON>\n        Animal <|-- <PERSON>\n        Animal <|-- Zebra\n        Animal : +int age\n        Animal : +String gender\n        Animal: +isMammal()\n        Animal: +mate()\n        class Duck{\n            +String beakColor\n            +swim()\n            +quack()\n        }\n        class Fish{\n            -int sizeInFeet\n            -canEat()\n        }\n        class Zebra{\n            +bool is_wild\n            +run()\n        }", "sortingOrder": 12, "isPinned": false}, {"id": "759b11ce-eda4-47d9-8573-09b09e2ae3f3", "category": "StateDiagram", "description": "a sample state diagram", "content": "stateDiagram-v2\n        [*] --> Still\n        Still --> [*]\n    \n        Still --> Moving\n        Moving --> Still\n        Moving --> Crash\n        Crash --> [*]", "sortingOrder": 0, "isPinned": false}, {"id": "6d526320-1040-4e83-ad38-65bc4cf4cd24", "category": "StateDiagram", "description": "a sample state diagram with left-to-right direction", "content": "stateDiagram-v2\n        direction LR\n        [*] --> Still\n        Still --> [*]\n    \n        Still --> Moving\n        Moving --> Still\n        Moving --> Crash\n        Crash --> [*]", "sortingOrder": 1, "isPinned": false}, {"id": "4e2360fa-10ad-431c-93c2-41b25b9f3399", "category": "StateDiagram", "description": "node with description", "content": "s2 : This is a state description", "sortingOrder": 2, "isPinned": false}, {"id": "14e504d1-3f8d-4ea9-a814-26e786f27d42", "category": "StateDiagram", "description": "a transition", "content": "s1 --> s2", "sortingOrder": 3, "isPinned": false}, {"id": "68dc849c-a430-40a2-9813-5e1842543a80", "category": "StateDiagram", "description": "a transition with label", "content": "s1 --> s2: A transition", "sortingOrder": 4, "isPinned": false}, {"id": "c29ec257-e30f-42cd-972a-52db2524dd0b", "category": "StateDiagram", "description": "composite state", "content": "\n        [*] --> First\n        state First {\n            [*] --> second\n            second --> [*]\n        }", "sortingOrder": 5, "isPinned": false}, {"id": "c6a078f3-2870-4882-bfa0-93c17cb9cc2b", "category": "StateDiagram", "description": "diagram with choice", "content": "stateDiagram-v2\n        state if_state <<choice>>\n        [*] --> IsPositive\n        IsPositive --> if_state\n        if_state --> False: if n < 0\n        if_state --> True : if n >= 0", "sortingOrder": 6, "isPinned": false}, {"id": "ceb62075-24a2-49f7-aace-20e2d2583de5", "category": "StateDiagram", "description": "diagram with fork", "content": "stateDiagram-v2\n        state fork_state <<fork>>\n          [*] --> fork_state\n          fork_state --> State2\n          fork_state --> State3\n    \n          state join_state <<join>>\n          State2 --> join_state\n          State3 --> join_state\n          join_state --> State4\n          State4 --> [*]", "sortingOrder": 7, "isPinned": false}, {"id": "1f366bb5-602c-468c-a4b3-5d9ec37ec47a", "category": "StateDiagram", "description": "a diagram with concurrency", "content": "stateDiagram-v2\n        [*] --> Active\n    \n        state Active {\n            [*] --> NumLockOff\n            NumLockOff --> NumLockOn : EvNumLockPressed\n            NumLockOn --> NumLockOff : EvNumLockPressed\n            --\n            [*] --> CapsLockOff\n            CapsLockOff --> CapsLockOn : EvCapsLockPressed\n            CapsLockOn --> CapsLockOff : EvCapsLockPressed\n            --\n            [*] --> ScrollLockOff\n            ScrollLockOff --> ScrollLockOn : EvScrollLockPressed\n            ScrollLockOn --> ScrollLockOff : EvScrollLockPressed\n        }", "sortingOrder": 8, "isPinned": false}, {"id": "0854edd4-2e47-4d9f-9483-57ca1acd78fa", "category": "EntityRelationshipDiagram", "description": "a sample entity relationship diagram", "content": "erDiagram\n        CUSTOMER ||--o{ ORDER : places\n        ORDER ||--|{ LINE-ITEM : contains\n        CUSTOMER }|..|{ DELIVERY-ADDRESS : uses", "sortingOrder": 0, "isPinned": false}, {"id": "dacd5813-3cc0-4f5f-a596-1271dec01f71", "category": "EntityRelationshipDiagram", "description": "an entity", "content": "    CUSTOMER {\n            string name\n            string custNumber\n            string sector\n        }", "sortingOrder": 1, "isPinned": false}, {"id": "6249d8cd-a5a1-4f52-b0da-58bf915e9942", "category": "EntityRelationshipDiagram", "description": "one-to-many relationship", "content": "A ||--|{ B : label", "sortingOrder": 2, "isPinned": false}, {"id": "fd32f504-8080-46d3-9641-21c8252352c2", "category": "EntityRelationshipDiagram", "description": "many-to-many relationship", "content": "A }|--|{ B : label", "sortingOrder": 3, "isPinned": false}, {"id": "825411da-3e70-4bad-8289-a9806ce87bf7", "category": "EntityRelationshipDiagram", "description": "one-to-one relationship", "content": "A ||--|| B : label", "sortingOrder": 4, "isPinned": false}, {"id": "5252ac53-7000-4913-960d-35b844ba0f6e", "category": "EntityRelationshipDiagram", "description": "many-to-one relationship", "content": "A }|--|| B : label", "sortingOrder": 5, "isPinned": false}, {"id": "b61e86d1-9e7d-4def-90dc-edd83bd2524f", "category": "EntityRelationshipDiagram", "description": "zero/one-to-one relationship", "content": "A |o--|| B : label", "sortingOrder": 6, "isPinned": false}, {"id": "25001657-27fe-4667-b190-a5af3297e917", "category": "EntityRelationshipDiagram", "description": "one-to-one/zero relationship", "content": "A ||--o| B : label", "sortingOrder": 7, "isPinned": false}, {"id": "74f2cd97-8080-42c5-b4f9-57b92916d9a9", "category": "EntityRelationshipDiagram", "description": "zero-or-more-to-one relationship", "content": "A }o--|| B : label", "sortingOrder": 8, "isPinned": false}, {"id": "c59e82ae-b3ca-4395-9e88-d30064605baf", "category": "EntityRelationshipDiagram", "description": "one-to-zero-or-more relationship", "content": "A ||--o{ B : label", "sortingOrder": 9, "isPinned": false}, {"id": "2cc46b43-793a-46fa-a0ab-4bc27e49e8c4", "category": "EntityRelationshipDiagram", "description": "zero-or-more-to-many relationship", "content": "A }o--|{ B : label", "sortingOrder": 10, "isPinned": false}, {"id": "ef3f944a-5560-4f16-8a6c-62679fbb9c6b", "category": "EntityRelationshipDiagram", "description": "many-to-zero-or-more relationship", "content": "A }|--o{ B : label", "sortingOrder": 11, "isPinned": false}, {"id": "c1a17a22-61f8-4759-9f45-b6b11fc2a03d", "category": "UserJourneyDiagram", "description": "a sample user journey diagram", "content": "journey\n        title My working day\n        section Go to work\n          Make tea: 5: Me\n          Go upstairs: 3: Me\n          Do work: 1: Me, Cat\n        section Go home\n          Go downstairs: 5: Me\n          Sit down: 5: Me", "sortingOrder": 0, "isPinned": false}, {"id": "b51d90b6-9197-472f-b507-d56cae2858f9", "category": "UserJourneyDiagram", "description": "a step in user journey", "content": "      Step Title: 5: <PERSON><PERSON><PERSON>", "sortingOrder": 1, "isPinned": false}, {"id": "c4a84a67-05ba-44c2-bcc7-26979d9a3017", "category": "<PERSON><PERSON><PERSON><PERSON>hart", "description": "simple gantt chart", "content": "gantt\n        title A Gantt Diagram\n        dateFormat  YYYY-MM-DD\n        section Section\n        A task           :a1, 2014-01-01, 30d\n        Another task     :after a1  , 20d\n        section Another\n        Task in sec      :2014-01-12  , 12d\n        another task      : 24d", "sortingOrder": 0, "isPinned": false}, {"id": "a6f56736-916a-4c07-b997-5f554e6381df", "category": "<PERSON><PERSON><PERSON><PERSON>hart", "description": "rich gantt chart", "content": "gantt\n        dateFormat  YYYY-MM-DD\n        title       Adding GANTT diagram functionality to mermaid\n        excludes    weekends\n    \n        section A section\n        Completed task            :done,    des1, 2014-01-06,2014-01-08\n        Active task               :active,  des2, 2014-01-09, 3d\n        Future task               :         des3, after des2, 5d\n        Future task2              :         des4, after des3, 5d\n    \n        section Critical tasks\n        Completed task in the critical line :crit, done, 2014-01-06,24h\n        Implement parser and jison          :crit, done, after des1, 2d\n        Create tests for parser             :crit, active, 3d\n        Future task in critical line        :crit, 5d\n        Create tests for renderer           :2d\n        Add to mermaid                      :1d\n        Functionality added                 :milestone, 2014-01-25, 0d\n    \n        section Documentation\n        Describe gantt syntax               :active, a1, after des1, 3d\n        Add gantt diagram to demo page      :after a1  , 20h\n        Add another diagram to demo page    :doc1, after a1  , 48h\n    \n        section Last section\n        Describe gantt syntax               :after doc1, 3d\n        Add gantt diagram to demo page      :20h\n        Add another diagram to demo page    :48h", "sortingOrder": 1, "isPinned": false}, {"id": "44bef34c-d215-4213-84f3-ab40df2fa9ae", "category": "<PERSON><PERSON><PERSON><PERSON>hart", "description": "milestones example", "content": "gantt\n        dateFormat HH:mm\n        axisFormat %H:%M\n        Initial milestone : milestone, m1, 17:49,2min\n        taska2 : 10min\n        taska3 : 5min\n        Final milestone : milestone, m2, 18:14, 2min", "sortingOrder": 2, "isPinned": false}, {"id": "e4e00a4b-c11c-4312-a735-2c36fb41412f", "category": "<PERSON><PERSON><PERSON>", "description": "sample pie chart", "content": "pie title /r/obsidianmd posts by type\n        \"Graphs\" : 85\n        \"Dashboards\" : 14\n        \"Tips\" : 1", "sortingOrder": 0, "isPinned": false}, {"id": "3a2b4a63-f242-471e-85d8-193822690aff", "category": "<PERSON><PERSON><PERSON>", "description": "sample pie chart with values shown in legend", "content": "pie showData title /r/obsidianmd posts by type\n        \"Graphs\" : 85\n        \"Dashboards\" : 14\n        \"Tips\" : 1", "sortingOrder": 1, "isPinned": false}, {"id": "bc058035-9297-4a37-a32f-0d891f3140e7", "category": "RequirementDiagram", "description": "sample requirements diagram", "content": "    requirementDiagram\n\n        requirement test_req {\n        id: 1\n        text: the test text.\n        risk: high\n        verifymethod: test\n        }\n    \n        element test_entity {\n        type: simulation\n        }\n    \n        test_entity - satisfies -> test_req", "sortingOrder": 0, "isPinned": false}, {"id": "b36b6b9f-9c19-4c5a-ad3f-8257346e41b2", "category": "RequirementDiagram", "description": "sample requirements diagram", "content": "element customElement {\n            type: customType\n            docref: customDocRef\n        }", "sortingOrder": 1, "isPinned": false}, {"id": "9a3ecd72-dd2a-4aa3-990c-6a1368dc470b", "category": "RequirementDiagram", "description": "a requirement with high risk", "content": "functionalRequirement myReq {\n            id: reqId\n            text: someText\n            risk: High\n            verifymethod: analysis\n        }", "sortingOrder": 2, "isPinned": false}, {"id": "7652927f-51c6-4a55-a12c-cfe2e51b3136", "category": "RequirementDiagram", "description": "sample requirements diagram", "content": "interfaceRequirement myReq2 {\n            id: reqId\n            text: someText\n            risk: Medium\n            verifymethod: demonstration\n        }", "sortingOrder": 3, "isPinned": false}, {"id": "ed1d76c2-6ebd-44fc-b840-25917fad1938", "category": "RequirementDiagram", "description": "sample requirements diagram", "content": "designConstraint myReq3 {\n            id: reqId\n            text: someText\n            risk: Low\n            verifymethod: test\n        }", "sortingOrder": 4, "isPinned": false}, {"id": "cdaeaa55-a2af-4a0f-83b3-ebc4ac090135", "category": "GitGraph", "description": "simple git graph", "content": "gitGraph\n        commit\n        commit\n        branch develop\n        checkout develop\n        commit\n        commit\n        checkout main\n        merge develop\n        commit\n        commit", "sortingOrder": 0, "isPinned": false}, {"id": "635be932-4213-4e4a-b850-3d91bff7ea2e", "category": "GitGraph", "description": "tagged commit", "content": "commit id: \"Normal\" tag: \"v1.0.0\"", "sortingOrder": 1, "isPinned": false}, {"id": "53abe42a-e9b0-4d11-915d-fa2955578320", "category": "GitGraph", "description": "reverse commit", "content": "commit id: \"Reverse\" type: REVERSE", "sortingOrder": 2, "isPinned": false}, {"id": "fd805b06-7040-49e4-ba7c-23425957dc92", "category": "GitGraph", "description": "highlighted commit", "content": "commit id: \"Highlight\" type: HIGHLIGHT", "sortingOrder": 3, "isPinned": false}, {"id": "086d3afc-44c3-4249-af95-0f43051154cf", "category": "GitGraph", "description": "reverse commit", "content": "commit id: \"Reverse\" type: REVERSE", "sortingOrder": 4, "isPinned": false}, {"id": "3fdebb74-3e1b-4fa9-81d4-c740d7ead735", "category": "GitGraph", "description": "git graph with cherry-pick", "content": "gitGraph\n        commit id: \"ZERO\"\n        branch develop\n        commit id:\"A\"\n        checkout main\n        commit id:\"ONE\"\n        checkout develop\n        commit id:\"B\"\n        checkout main\n        commit id:\"TWO\"\n        cherry-pick id:\"A\"\n        commit id:\"THREE\"\n        checkout develop\n        commit id:\"C\"", "sortingOrder": 5, "isPinned": false}, {"id": "66b80c90-2b04-4b63-9dc4-8423f4dd9239", "category": "Mindmap", "description": "a simple mindmap", "content": "mindmap\n        Root\n            A\n              B\n              C", "sortingOrder": 1, "isPinned": false}, {"id": "893fbe3a-db48-461d-bf88-8d46c246ef5c", "category": "Mindmap", "description": "square", "content": "id[I am a square]", "sortingOrder": 2, "isPinned": false}, {"id": "ca863434-e625-4d4c-a256-e4d54c0272c6", "category": "Mindmap", "description": "rounded square", "content": "id(I am a rounded square)", "sortingOrder": 3, "isPinned": false}, {"id": "aa1fcd25-1329-4328-b2da-bb78d902d020", "category": "Mindmap", "description": "circle", "content": "id((I am a circle))", "sortingOrder": 4, "isPinned": false}, {"id": "396aa665-f6a0-4d93-807d-e3f24f4c32b5", "category": "Mindmap", "description": "bang", "content": "id))I am a bang((", "sortingOrder": 5, "isPinned": false}, {"id": "5ef7b9dd-0249-4a7f-8e59-0d78b307a058", "category": "Mindmap", "description": "cloud", "content": "id)I am a cloud(", "sortingOrder": 6, "isPinned": false}, {"id": "147528e6-8422-427c-95b2-e3df7df90ea8", "category": "Mindmap", "description": "hexagon", "content": "id{{I am a hexagon}}", "sortingOrder": 7, "isPinned": false}, {"id": "48beffd2-1074-4222-8d1f-e2aa608c2617", "category": "Mindmap", "description": "default", "content": "I am the default shape", "sortingOrder": 8, "isPinned": false}, {"id": "34d83fba-43ce-4376-862e-5015e202b887", "category": "Mindmap", "description": "sample mindmap", "content": "mindmap\n        root((mindmap))\n          Origins\n            Long history\n            Popularisation\n              British popular psychology author <PERSON>\n          Research\n            On effectiveness<br/>and features\n            On Automatic creation\n              Uses\n                  Creative techniques\n                  Strategic planning\n                  Argument mapping\n          Tools\n            Pen and paper\n            Mermaid", "sortingOrder": 9, "isPinned": false}, {"id": "f590837c-042b-45e8-aea9-0394200c1c6e", "category": "Timeline", "description": "sample timeline", "content": "timeline\n\t\ttitle History of Social Media Platform\n\t\t2002 : LinkedIn\n\t\t2004 : Facebook\n\t\t\t : Google\n\t\t2005 : Youtube\n\t\t2006 : Twitter", "sortingOrder": 1, "isPinned": false}, {"id": "e3e7f8a4-5faa-4c23-81a9-53381759f7a7", "category": "Timeline", "description": "timeline with grouping", "content": "timeline\n\t\ttitle Timeline of Industrial Revolution\n\t\tsection 17th-20th century\n\t\t\tIndustry 1.0 : Machinery, Water power, Steam <br>power\n\t\t\tIndustry 2.0 : Electricity, Internal combustion engine, Mass production\n\t\t\tIndustry 3.0 : Electronics, Computers, Automation\n\t\tsection 21st century\n\t\t\tIndustry 4.0 : Internet, Robotics, Internet of Things\n\t\t\tIndustry 5.0 : Artificial intelligence, Big data,3D printing", "sortingOrder": 2, "isPinned": false}, {"id": "96db36b3-6347-4b4a-982e-3446265f815f", "category": "Timeline", "description": "timeline with Forest theme. see the docs for additional themes", "content": "%%{init: { 'logLevel': 'debug', 'theme': 'forest' } }%%\n\t\ttimeline\n\t\t\ttitle History of Social Media Platform\n\t\t\t  2002 : LinkedIn\n\t\t\t  2004 : Facebook : Google\n\t\t\t  2005 : Youtube\n\t\t\t  2006 : Twitter\n\t\t\t  2007 : Tumblr\n\t\t\t  2008 : Instagram\n\t\t\t  2010 : Pinterest", "sortingOrder": 3, "isPinned": false}, {"id": "628dcca1-dcf4-404d-ae16-6845e090acda", "category": "QuadrantChart", "description": "sample quadrant chart", "content": "quadrantChart\n\t\ttitle Reach and engagement of campaigns\n\t\tx-axis Low Reach --> High Reach\n\t\ty-axis Low Engagement --> High Engagement\n\t\tquadrant-1 We should expand\n\t\tquadrant-2 Need to promote\n\t\tquadrant-3 Re-evaluate\n\t\tquadrant-4 May be improved\n\t\tCampaign A: [0.3, 0.6]\n\t\tCampaign B: [0.45, 0.23]\n\t\tCampaign C: [0.57, 0.69]\n\t\tCampaign D: [0.78, 0.34]\n\t\tCampaign E: [0.40, 0.34]\n\t\tCampaign F: [0.35, 0.78]", "sortingOrder": 1, "isPinned": false}, {"id": "05f2bb8c-9cc8-47ec-9354-04e5d163d3fc", "category": "QuadrantChart", "description": "themed quadrant chart", "content": "%%{init: {\"quadrantChart\": {\"chartWidth\": 400, \"chartHeight\": 400}, \"themeVariables\": {\"quadrant1TextFill\": \"#ff0000\"} }}%%\n\t\tquadrantChart\n\t\t  x-axis Urgent --> Not Urgent\n\t\t  y-axis Not Important --> \"Important ❤\"\n\t\t  quadrant-1 Plan\n\t\t  quadrant-2 Do\n\t\t  quadrant-3 Delegate\n\t\t  quadrant-4 Delete", "sortingOrder": 1, "isPinned": false}, {"id": "f3cb480c-48ca-421f-a1ff-8f16ba50d61b", "category": "C4Diagram", "description": "sample C4 diagram (compatible with PlantUML)", "content": "C4Context\n\t\ttitle System Context diagram for Internet Banking System\n\t\tEnterprise_Boundary(b0, \"BankBoundary0\") {\n\t\t  Person(customerA, \"Banking Customer A\", \"A customer of the bank, with personal bank accounts.\")\n\t\t  Person(customerB, \"Banking Customer B\")\n\t\t  Person_Ext(customerC, \"Banking Customer C\", \"desc\")\n  \n\t\t  Person(customerD, \"Banking Customer D\", \"A customer of the bank, <br/> with personal bank accounts.\")\n  \n\t\t  System(SystemAA, \"Internet Banking System\", \"Allows customers to view information about their bank accounts, and make payments.\")\n  \n\t\t  Enterprise_Boundary(b1, \"BankBoundary\") {\n  \n\t\t\tSystemDb_Ext(SystemE, \"Mainframe Banking System\", \"Stores all of the core banking information about customers, accounts, transactions, etc.\")\n  \n\t\t\tSystem_Boundary(b2, \"BankBoundary2\") {\n\t\t\t  System(SystemA, \"Banking System A\")\n\t\t\t  System(SystemB, \"Banking System B\", \"A system of the bank, with personal bank accounts. next line.\")\n\t\t\t}\n  \n\t\t\tSystem_Ext(SystemC, \"E-mail system\", \"The internal Microsoft Exchange e-mail system.\")\n\t\t\tSystemDb(SystemD, \"Banking System D Database\", \"A system of the bank, with personal bank accounts.\")\n  \n\t\t\tBoundary(b3, \"BankBoundary3\", \"boundary\") {\n\t\t\t  SystemQueue(SystemF, \"Banking System F Queue\", \"A system of the bank.\")\n\t\t\t  SystemQueue_Ext(SystemG, \"Banking System G Queue\", \"A system of the bank, with personal bank accounts.\")\n\t\t\t}\n\t\t  }\n\t\t}\n  \n\t\tBiRel(customerA, SystemAA, \"Uses\")\n\t\tBiRel(SystemAA, SystemE, \"Uses\")\n\t\tRel(SystemAA, SystemC, \"Sends e-mails\", \"SMTP\")\n\t\tRel(SystemC, customerA, \"Sends e-mails to\")\n  \n\t\tUpdateElementStyle(customerA, $fontColor=\"red\", $bgColor=\"grey\", $borderColor=\"red\")\n\t\tUpdateRelStyle(customerA, SystemAA, $textColor=\"blue\", $lineColor=\"blue\", $offsetX=\"5\")\n\t\tUpdateRelStyle(SystemAA, SystemE, $textColor=\"blue\", $lineColor=\"blue\", $offsetY=\"-10\")\n\t\tUpdateRelStyle(SystemAA, SystemC, $textColor=\"blue\", $lineColor=\"blue\", $offsetY=\"-40\", $offsetX=\"-50\")\n\t\tUpdateRelStyle(SystemC, customerA, $textColor=\"red\", $lineColor=\"red\", $offsetX=\"-50\", $offsetY=\"20\")\n  \n\t\tUpdateLayoutConfig($c4ShapeInRow=\"3\", $c4BoundaryInRow=\"1\")", "sortingOrder": 1, "isPinned": false}, {"id": "e69397d2-7aa5-4389-bf6a-688de89bd318", "category": "SankeyDiagram", "description": "", "content": "sankey-beta\n        %% source,target,value\n        Electricity grid,Over generation / exports,104.453\n        Electricity grid,Heating and cooling - homes,113.726\n        Electricity grid,H2 conversion,27.14", "sortingOrder": 0, "isPinned": false}, {"id": "fa24bed6-fe70-4f07-bab8-76642f0c1142", "category": "XyChart", "description": "a sample XYChart diagram", "content": "xychart-beta\n        title \"Sales Revenue\"\n        x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]\n        y-axis \"Revenue (in $)\" 4000 --> 11000\n        bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\n        line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]", "sortingOrder": 0, "isPinned": false}, {"id": "7a2f2bef-51a6-493d-be6a-e99b5720265d", "category": "Packet", "description": "a sample packet diagram", "content": "packet-beta\ntitle UDP Packet\n0-15: \"Source Port\"\n16-31: \"Destination Port\"\n32-47: \"Length\"\n48-63: \"Checksum\"\n64-95: \"Data (variable length)\"\n", "sortingOrder": 0, "isPinned": false}, {"id": "c24defa0-5758-4df8-bc94-b383864b8e4f", "category": "Ka<PERSON><PERSON>", "description": "a sample kanban diagram", "content": "kanban\n  <PERSON>\n    [Create Documentation]\n    docs[Create Blog about the new diagram]\n  [In progress]\n    id6[Create renderer so that it works in all cases. We also add som extra text here for testing purposes. And some more just for the extra flare.]\n  id9[Ready for deploy]\n    id8[Design grammar]@{ assigned: 'knsv' }\n  id10[Ready for test]\n    id4[Create parsing tests]@{ ticket: MC-2038, assigned: '<PERSON><PERSON>', priority: 'High' }\n    id66[last item]@{ priority: 'Very Low', assigned: 'knsv' }\n  id11[Done]\n    id5[define getData]\n    id2[Title of diagram is more than 100 chars when user duplicates diagram with 100 char]@{ ticket: MC-2036, priority: 'Very High'}\n    id3[Update DB function]@{ ticket: MC-2037, assigned: knsv, priority: 'High' }\n\n  id12[Can't reproduce]\n    id3[Weird flickering in Firefox]", "sortingOrder": 0, "isPinned": false}, {"id": "5aab5992-34b6-4a0c-911c-53e12f18a6b7", "category": "Block", "description": "a sample block diagram", "content": "block-beta\ncolumns 1\n  db((\"DB\"))\n  blockArrowId6<[\"&nbsp;&nbsp;&nbsp;\"]>(down)\n  block:ID\n    A\n    B[\"A wide one in the middle\"]\n    C\n  end\n  space\n  D\n  ID --> D\n  C --> D\n  style B fill:#969,stroke:#333,stroke-width:4px\n", "sortingOrder": 0, "isPinned": false}, {"id": "07a149c5-793e-4f1f-bcba-71e3ca928ac8", "category": "Architecture", "description": "a sample architecture diagram", "content": "architecture-beta\n    group api(cloud)[API]\n\n    service db(database)[Database] in api\n    service disk1(disk)[Storage] in api\n    service disk2(disk)[Storage] in api\n    service server(server)[Server] in api\n\n    db:L -- R:server\n    disk1:T -- B:server\n    disk2:T -- B:db\n", "sortingOrder": 0, "isPinned": false}], "selectedCategory": "StateDiagram"}