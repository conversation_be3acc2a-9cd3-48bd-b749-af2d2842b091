"use strict";var e,t=require("obsidian");!function(e){e.Default=".diagram-zoom-drag",e.Me<PERSON>=".mermaid",e.<PERSON><PERSON><PERSON><PERSON>=".block-language-mehrmaid",e.PlantUML=".block-language-plantuml",e.Graphviz=".block-language-dot"}(e||(e={}));class n{plugin;constructor(e){this.plugin=e,this.plugin=e}get defaultSettings(){return{supported_diagrams:Object.entries(e).map((([e,t])=>({name:e,selector:t,on:!0,panels:{move:{on:!0},zoom:{on:!0},service:{on:!0}}}))),panelsConfig:{service:{enabled:!0,position:{top:"0px",right:"0px"}},move:{enabled:!0,position:{bottom:"0px",right:"0px"}},zoom:{enabled:!0,position:{top:"50%",right:"0px"}}},diagramsPerPage:5,collapseByDefault:!1,automaticCollapsingOnFocusChange:!1,hideOnMouseOutDiagram:!1,diagramExpandedWidth:400,diagramExpandedHeight:400,diagramCollapsedWidth:200,diagramCollapsedHeight:200,addHidingButton:!0}}async loadSettings(){const e=await this.plugin.loadData(),t=this.defaultSettings,n=Object.assign({},t,e);this.plugin.settings={...n}}async saveSettings(){const e={...this.plugin.settings};await this.plugin.saveData(e)}async resetSettings(){const e=this.plugin.manifest.dir;if(e){const n=t.normalizePath(`${e}/data.json`);await this.plugin.app.vault.adapter.exists(n)&&await this.plugin.app.vault.adapter.remove(n),await this.loadSettings()}}}var a,i,r,o,s,l,c,u,d,p,h,m={},g=[],f=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,v=Array.isArray;function _(e,t){for(var n in t)e[n]=t[n];return e}function y(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function b(e,t,n){var i,r,o,s={};for(o in t)"key"==o?i=t[o]:"ref"==o?r=t[o]:s[o]=t[o];if(arguments.length>2&&(s.children=arguments.length>3?a.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===s[o]&&(s[o]=e.defaultProps[o]);return w(e,s,i,r,null)}function w(e,t,n,a,o){var s={type:e,props:t,key:n,ref:a,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==o?++r:o,__i:-1,__u:0};return null==o&&null!=i.vnode&&i.vnode(s),s}function E(){return{current:null}}function C(e){return e.children}function S(e,t){this.props=e,this.context=t}function x(e,t){if(null==t)return e.__?x(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?x(e):null}function D(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return D(e)}}function P(e){(!e.__d&&(e.__d=!0)&&o.push(e)&&!k.__r++||s!==i.debounceRendering)&&((s=i.debounceRendering)||l)(k)}function k(){var e,t,n,a,r,s,l,u;for(o.sort(c);e=o.shift();)e.__d&&(t=o.length,a=void 0,s=(r=(n=e).__v).__e,l=[],u=[],n.__P&&((a=_({},r)).__v=r.__v+1,i.vnode&&i.vnode(a),$(n.__P,a,r,n.__n,n.__P.namespaceURI,32&r.__u?[s]:null,l,null==s?x(r):s,!!(32&r.__u),u),a.__v=r.__v,a.__.__k[a.__i]=a,A(l,a,u),a.__e!=s&&D(a)),o.length>t&&o.sort(c));k.__r=0}function T(e,t,n,a,i,r,o,s,l,c,u){var d,p,h,f,_,y=a&&a.__k||g,b=t.length;for(n.__d=l,function(e,t,n){var a,i,r,o,s,l=t.length,c=n.length,u=c,d=0;for(e.__k=[],a=0;a<l;a++)null!=(i=t[a])&&"boolean"!=typeof i&&"function"!=typeof i?(o=a+d,(i=e.__k[a]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?w(null,i,null,null,null):v(i)?w(C,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?w(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,r=null,-1!==(s=i.__i=N(i,n,o,u))&&(u--,(r=n[s])&&(r.__u|=131072)),null==r||null===r.__v?(-1==s&&d--,"function"!=typeof i.type&&(i.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,i.__u|=65536))):i=e.__k[a]=null;if(u)for(a=0;a<c;a++)null!=(r=n[a])&&!(131072&r.__u)&&(r.__e==e.__d&&(e.__d=x(r)),F(r,r))}(n,t,y),l=n.__d,d=0;d<b;d++)null!=(h=n.__k[d])&&(p=-1===h.__i?m:y[h.__i]||m,h.__i=d,$(e,h,p,i,r,o,s,l,c,u),f=h.__e,h.ref&&p.ref!=h.ref&&(p.ref&&L(p.ref,null,h),u.push(h.ref,h.__c||f,h)),null==_&&null!=f&&(_=f),65536&h.__u||p.__k===h.__k?l=I(h,l,e):"function"==typeof h.type&&void 0!==h.__d?l=h.__d:f&&(l=f.nextSibling),h.__d=void 0,h.__u&=-196609);n.__d=l,n.__e=_}function I(e,t,n){var a,i;if("function"==typeof e.type){for(a=e.__k,i=0;a&&i<a.length;i++)a[i]&&(a[i].__=e,t=I(a[i],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=x(e)),n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8===t.nodeType);return t}function O(e,t){return t=t||[],null==e||"boolean"==typeof e||(v(e)?e.some((function(e){O(e,t)})):t.push(e)),t}function N(e,t,n,a){var i=e.key,r=e.type,o=n-1,s=n+1,l=t[n];if(null===l||l&&i==l.key&&r===l.type&&!(131072&l.__u))return n;if(a>(null==l||131072&l.__u?0:1))for(;o>=0||s<t.length;){if(o>=0){if((l=t[o])&&!(131072&l.__u)&&i==l.key&&r===l.type)return o;o--}if(s<t.length){if((l=t[s])&&!(131072&l.__u)&&i==l.key&&r===l.type)return s;s++}}return-1}function M(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||f.test(t)?n:n+"px"}function z(e,t,n,a,i){var r;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof a&&(e.style.cssText=a=""),a)for(t in a)n&&t in n||M(e.style,t,"");if(n)for(t in n)a&&n[t]===a[t]||M(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])r=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+r]=n,n?a?n.u=a.u:(n.u=u,e.addEventListener(t,r?p:d,r)):e.removeEventListener(t,r?p:d,r);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function R(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=u++;else if(t.t<n.u)return;return n(i.event?i.event(t):t)}}}function $(e,t,n,a,r,o,s,l,c,u){var d,p,h,m,g,f,y,b,w,E,x,D,P,k,I,O,N=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(c=!!(32&n.__u),o=[l=t.__e=n.__e]),(d=i.__b)&&d(t);e:if("function"==typeof N)try{if(b=t.props,w="prototype"in N&&N.prototype.render,E=(d=N.contextType)&&a[d.__c],x=d?E?E.props.value:d.__:a,n.__c?y=(p=t.__c=n.__c).__=p.__E:(w?t.__c=p=new N(b,x):(t.__c=p=new S(b,x),p.constructor=N,p.render=H),E&&E.sub(p),p.props=b,p.state||(p.state={}),p.context=x,p.__n=a,h=p.__d=!0,p.__h=[],p._sb=[]),w&&null==p.__s&&(p.__s=p.state),w&&null!=N.getDerivedStateFromProps&&(p.__s==p.state&&(p.__s=_({},p.__s)),_(p.__s,N.getDerivedStateFromProps(b,p.__s))),m=p.props,g=p.state,p.__v=t,h)w&&null==N.getDerivedStateFromProps&&null!=p.componentWillMount&&p.componentWillMount(),w&&null!=p.componentDidMount&&p.__h.push(p.componentDidMount);else{if(w&&null==N.getDerivedStateFromProps&&b!==m&&null!=p.componentWillReceiveProps&&p.componentWillReceiveProps(b,x),!p.__e&&(null!=p.shouldComponentUpdate&&!1===p.shouldComponentUpdate(b,p.__s,x)||t.__v===n.__v)){for(t.__v!==n.__v&&(p.props=b,p.state=p.__s,p.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some((function(e){e&&(e.__=t)})),D=0;D<p._sb.length;D++)p.__h.push(p._sb[D]);p._sb=[],p.__h.length&&s.push(p);break e}null!=p.componentWillUpdate&&p.componentWillUpdate(b,p.__s,x),w&&null!=p.componentDidUpdate&&p.__h.push((function(){p.componentDidUpdate(m,g,f)}))}if(p.context=x,p.props=b,p.__P=e,p.__e=!1,P=i.__r,k=0,w){for(p.state=p.__s,p.__d=!1,P&&P(t),d=p.render(p.props,p.state,p.context),I=0;I<p._sb.length;I++)p.__h.push(p._sb[I]);p._sb=[]}else do{p.__d=!1,P&&P(t),d=p.render(p.props,p.state,p.context),p.state=p.__s}while(p.__d&&++k<25);p.state=p.__s,null!=p.getChildContext&&(a=_(_({},a),p.getChildContext())),w&&!h&&null!=p.getSnapshotBeforeUpdate&&(f=p.getSnapshotBeforeUpdate(m,g)),T(e,v(O=null!=d&&d.type===C&&null==d.key?d.props.children:d)?O:[O],t,n,a,r,o,s,l,c,u),p.base=t.__e,t.__u&=-161,p.__h.length&&s.push(p),y&&(p.__E=p.__=null)}catch(e){if(t.__v=null,c||null!=o){for(t.__u|=c?160:128;l&&8===l.nodeType&&l.nextSibling;)l=l.nextSibling;o[o.indexOf(l)]=null,t.__e=l}else t.__e=n.__e,t.__k=n.__k;i.__e(e,t,n)}else null==o&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=B(n.__e,t,n,a,r,o,s,c,u);(d=i.diffed)&&d(t)}function A(e,t,n){t.__d=void 0;for(var a=0;a<n.length;a++)L(n[a],n[++a],n[++a]);i.__c&&i.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){i.__e(e,t.__v)}}))}function B(e,t,n,r,o,s,l,c,u){var d,p,h,g,f,_,b,w=n.props,E=t.props,C=t.type;if("svg"===C?o="http://www.w3.org/2000/svg":"math"===C?o="http://www.w3.org/1998/Math/MathML":o||(o="http://www.w3.org/1999/xhtml"),null!=s)for(d=0;d<s.length;d++)if((f=s[d])&&"setAttribute"in f==!!C&&(C?f.localName===C:3===f.nodeType)){e=f,s[d]=null;break}if(null==e){if(null===C)return document.createTextNode(E);e=document.createElementNS(o,C,E.is&&E),c&&(i.__m&&i.__m(t,s),c=!1),s=null}if(null===C)w===E||c&&e.data===E||(e.data=E);else{if(s=s&&a.call(e.childNodes),w=n.props||m,!c&&null!=s)for(w={},d=0;d<e.attributes.length;d++)w[(f=e.attributes[d]).name]=f.value;for(d in w)if(f=w[d],"children"==d);else if("dangerouslySetInnerHTML"==d)h=f;else if(!(d in E)){if("value"==d&&"defaultValue"in E||"checked"==d&&"defaultChecked"in E)continue;z(e,d,null,f,o)}for(d in E)f=E[d],"children"==d?g=f:"dangerouslySetInnerHTML"==d?p=f:"value"==d?_=f:"checked"==d?b=f:c&&"function"!=typeof f||w[d]===f||z(e,d,f,w[d],o);if(p)c||h&&(p.__html===h.__html||p.__html===e.innerHTML)||(e.innerHTML=p.__html),t.__k=[];else if(h&&(e.innerHTML=""),T(e,v(g)?g:[g],t,n,r,"foreignObject"===C?"http://www.w3.org/1999/xhtml":o,s,l,s?s[0]:n.__k&&x(n,0),c,u),null!=s)for(d=s.length;d--;)y(s[d]);c||(d="value","progress"===C&&null==_?e.removeAttribute("value"):void 0!==_&&(_!==e[d]||"progress"===C&&!_||"option"===C&&_!==w[d])&&z(e,d,_,w[d],o),d="checked",void 0!==b&&b!==e[d]&&z(e,d,b,w[d],o))}return e}function L(e,t,n){try{if("function"==typeof e){var a="function"==typeof e.__u;a&&e.__u(),a&&null==t||(e.__u=e(t))}else e.current=t}catch(e){i.__e(e,n)}}function F(e,t,n){var a,r;if(i.unmount&&i.unmount(e),(a=e.ref)&&(a.current&&a.current!==e.__e||L(a,null,t)),null!=(a=e.__c)){if(a.componentWillUnmount)try{a.componentWillUnmount()}catch(e){i.__e(e,t)}a.base=a.__P=null}if(a=e.__k)for(r=0;r<a.length;r++)a[r]&&F(a[r],t,n||"function"!=typeof e.type);n||y(e.__e),e.__c=e.__=e.__e=e.__d=void 0}function H(e,t,n){return this.constructor(e,n)}function U(e,t,n){var r,o,s,l;i.__&&i.__(e,t),o=(r="function"==typeof n)?null:n&&n.__k||t.__k,s=[],l=[],$(t,e=(!r&&n||t).__k=b(C,null,[e]),o||m,m,t.namespaceURI,!r&&n?[n]:o?null:t.firstChild?a.call(t.childNodes):null,s,!r&&n?n:o?o.__e:t.firstChild,r,l),A(s,e,l)}function j(e,t){U(e,t,j)}function W(e,t,n){var i,r,o,s,l=_({},e.props);for(o in e.type&&e.type.defaultProps&&(s=e.type.defaultProps),t)"key"==o?i=t[o]:"ref"==o?r=t[o]:l[o]=void 0===t[o]&&void 0!==s?s[o]:t[o];return arguments.length>2&&(l.children=arguments.length>3?a.call(arguments,2):n),w(e.type,l,i||e.key,r||e.ref,null)}function V(e,t){var n={__c:t="__cC"+h++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,a;return this.getChildContext||(n=new Set,(a={})[t]=this,this.getChildContext=function(){return a},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.forEach((function(e){e.__e=!0,P(e)}))},this.sub=function(e){n.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.delete(e),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}a=g.slice,i={__e:function(e,t,n,a){for(var i,r,o;t=t.__;)if((i=t.__c)&&!i.__)try{if((r=i.constructor)&&null!=r.getDerivedStateFromError&&(i.setState(r.getDerivedStateFromError(e)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,a||{}),o=i.__d),o)return i.__E=i}catch(t){e=t}throw e}},r=0,S.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=_({},this.state),"function"==typeof e&&(e=e(_({},n),this.props)),e&&_(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),P(this))},S.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),P(this))},S.prototype.render=C,o=[],l="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,c=function(e,t){return e.__v.__b-t.__v.__b},k.__r=0,u=0,d=R(!1),p=R(!0),h=0;var Y,q,X,G,Z=0,J=[],K=i,Q=K.__b,ee=K.__r,te=K.diffed,ne=K.__c,ae=K.unmount,ie=K.__;function re(e,t){K.__h&&K.__h(q,e,Z||t),Z=0;var n=q.__H||(q.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function oe(e){return Z=1,se(Ce,e)}function se(e,t,n){var a=re(Y++,2);if(a.t=e,!a.__c&&(a.__=[n?n(t):Ce(void 0,t),function(e){var t=a.__N?a.__N[0]:a.__[0],n=a.t(t,e);t!==n&&(a.__N=[n,a.__[1]],a.__c.setState({}))}],a.__c=q,!q.u)){var i=function(e,t,n){if(!a.__c.__H)return!0;var i=a.__c.__H.__.filter((function(e){return!!e.__c}));if(i.every((function(e){return!e.__N})))return!r||r.call(this,e,t,n);var o=!1;return i.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}})),!(!o&&a.__c.props===e)&&(!r||r.call(this,e,t,n))};q.u=!0;var r=q.shouldComponentUpdate,o=q.componentWillUpdate;q.componentWillUpdate=function(e,t,n){if(this.__e){var a=r;r=void 0,i(e,t,n),r=a}o&&o.call(this,e,t,n)},q.shouldComponentUpdate=i}return a.__N||a.__}function le(e,t){var n=re(Y++,3);!K.__s&&Ee(n.__H,t)&&(n.__=e,n.i=t,q.__H.__h.push(n))}function ce(e,t){var n=re(Y++,4);!K.__s&&Ee(n.__H,t)&&(n.__=e,n.i=t,q.__h.push(n))}function ue(e){return Z=5,pe((function(){return{current:e}}),[])}function de(e,t,n){Z=6,ce((function(){return"function"==typeof e?(e(t()),function(){return e(null)}):e?(e.current=t(),function(){return e.current=null}):void 0}),null==n?n:n.concat(e))}function pe(e,t){var n=re(Y++,7);return Ee(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function he(e,t){return Z=8,pe((function(){return e}),t)}function me(e){var t=q.context[e.__c],n=re(Y++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(q)),t.props.value):e.__}function ge(e,t){K.useDebugValue&&K.useDebugValue(t?t(e):e)}function fe(){var e=re(Y++,11);if(!e.__){for(var t=q.__v;null!==t&&!t.__m&&null!==t.__;)t=t.__;var n=t.__m||(t.__m=[0,0]);e.__="P"+n[0]+"-"+n[1]++}return e.__}function ve(){for(var e;e=J.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(be),e.__H.__h.forEach(we),e.__H.__h=[]}catch(t){e.__H.__h=[],K.__e(t,e.__v)}}K.__b=function(e){q=null,Q&&Q(e)},K.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),ie&&ie(e,t)},K.__r=function(e){ee&&ee(e),Y=0;var t=(q=e.__c).__H;t&&(X===q?(t.__h=[],q.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0}))):(t.__h.forEach(be),t.__h.forEach(we),t.__h=[],Y=0)),X=q},K.diffed=function(e){te&&te(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==J.push(t)&&G===K.requestAnimationFrame||((G=K.requestAnimationFrame)||ye)(ve)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.i=void 0}))),X=q=null},K.__c=function(e,t){t.some((function(e){try{e.__h.forEach(be),e.__h=e.__h.filter((function(e){return!e.__||we(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],K.__e(n,e.__v)}})),ne&&ne(e,t)},K.unmount=function(e){ae&&ae(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{be(e)}catch(e){t=e}})),n.__H=void 0,t&&K.__e(t,n.__v))};var _e="function"==typeof requestAnimationFrame;function ye(e){var t,n=function(){clearTimeout(a),_e&&cancelAnimationFrame(t),setTimeout(e)},a=setTimeout(n,100);_e&&(t=requestAnimationFrame(n))}function be(e){var t=q,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),q=t}function we(e){var t=q;e.__c=e.__(),q=t}function Ee(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function Ce(e,t){return"function"==typeof t?t(e):t}function Se(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var a in t)if("__source"!==a&&e[a]!==t[a])return!0;return!1}function xe(e,t){this.props=e,this.context=t}function De(e,t){function n(e){var n=this.props.ref,a=n==e.ref;return!a&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!a:Se(this.props,e)}function a(t){return this.shouldComponentUpdate=n,b(e,t)}return a.displayName="Memo("+(e.displayName||e.name)+")",a.prototype.isReactComponent=!0,a.__f=!0,a}(xe.prototype=new S).isPureReactComponent=!0,xe.prototype.shouldComponentUpdate=function(e,t){return Se(this.props,e)||Se(this.state,t)};var Pe=i.__b;i.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),Pe&&Pe(e)};var ke="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function Te(e){function t(t){if(!("ref"in t))return e(t,null);var n=t.ref;delete t.ref;var a=e(t,n);return t.ref=n,a}return t.$$typeof=ke,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var Ie=function(e,t){return null==e?null:O(O(e).map(t))},Oe={map:Ie,forEach:Ie,count:function(e){return e?O(e).length:0},only:function(e){var t=O(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:O},Ne=i.__e;i.__e=function(e,t,n,a){if(e.then)for(var i,r=t;r=r.__;)if((i=r.__c)&&i.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),i.__c(e,t);Ne(e,t,n,a)};var Me=i.unmount;function ze(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),e.__c.__H=null),null!=(e=function(e,t){for(var n in t)e[n]=t[n];return e}({},e)).__c&&(e.__c.__P===n&&(e.__c.__P=t),e.__c=null),e.__k=e.__k&&e.__k.map((function(e){return ze(e,t,n)}))),e}function Re(e,t,n){return e&&n&&(e.__v=null,e.__k=e.__k&&e.__k.map((function(e){return Re(e,t,n)})),e.__c&&e.__c.__P===t&&(e.__e&&n.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=n)),e}function $e(){this.__u=0,this.t=null,this.__b=null}function Ae(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function Be(e){var t,n,a;function i(i){if(t||(t=e()).then((function(e){n=e.default||e}),(function(e){a=e})),a)throw a;if(!n)throw t;return b(n,i)}return i.displayName="Lazy",i.__f=!0,i}function Le(){this.u=null,this.o=null}i.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),Me&&Me(e)},($e.prototype=new S).__c=function(e,t){var n=t.__c,a=this;null==a.t&&(a.t=[]),a.t.push(n);var i=Ae(a.__v),r=!1,o=function(){r||(r=!0,n.__R=null,i?i(s):s())};n.__R=o;var s=function(){if(! --a.__u){if(a.state.__a){var e=a.state.__a;a.__v.__k[0]=Re(e,e.__c.__P,e.__c.__O)}var t;for(a.setState({__a:a.__b=null});t=a.t.pop();)t.forceUpdate()}};a.__u++||32&t.__u||a.setState({__a:a.__b=a.__v.__k[0]}),e.then(o,o)},$e.prototype.componentWillUnmount=function(){this.t=[]},$e.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),a=this.__v.__k[0].__c;this.__v.__k[0]=ze(this.__b,n,a.__O=a.__P)}this.__b=null}var i=t.__a&&b(C,null,e.fallback);return i&&(i.__u&=-33),[b(C,null,t.__a?null:e.children),i]};var Fe=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function He(e){return this.getChildContext=function(){return e.context},e.children}function Ue(e){var t=this,n=e.i;t.componentWillUnmount=function(){U(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],contains:function(){return!0},appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),U(b(He,{context:t.context},e.__v),t.l)}function je(e,t){var n=b(Ue,{__v:e,i:t});return n.containerInfo=t,n}(Le.prototype=new S).__a=function(e){var t=this,n=Ae(t.__v),a=t.o.get(e);return a[0]++,function(i){var r=function(){t.props.revealOrder?(a.push(i),Fe(t,e,a)):i()};n?n(r):r()}},Le.prototype.render=function(e){this.u=null,this.o=new Map;var t=O(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},Le.prototype.componentDidUpdate=Le.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,n){Fe(e,n,t)}))};var We="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Ve=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Ye=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,qe=/[A-Z0-9]/g,Xe="undefined"!=typeof document,Ge=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};function Ze(e,t,n){return null==t.__k&&(t.textContent=""),U(e,t),"function"==typeof n&&n(),e?e.__c:null}function Je(e,t,n){return j(e,t),"function"==typeof n&&n(),e?e.__c:null}S.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(S.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var Ke=i.event;function Qe(){}function et(){return this.cancelBubble}function tt(){return this.defaultPrevented}i.event=function(e){return Ke&&(e=Ke(e)),e.persist=Qe,e.isPropagationStopped=et,e.isDefaultPrevented=tt,e.nativeEvent=e};var nt,at={enumerable:!1,configurable:!0,get:function(){return this.class}},it=i.vnode;i.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,n=e.type,a={},i=-1===n.indexOf("-");for(var r in t){var o=t[r];if(!("value"===r&&"defaultValue"in t&&null==o||Xe&&"children"===r&&"noscript"===n||"class"===r||"className"===r)){var s=r.toLowerCase();"defaultValue"===r&&"value"in t&&null==t.value?r="value":"download"===r&&!0===o?o="":"translate"===s&&"no"===o?o=!1:"o"===s[0]&&"n"===s[1]?"ondoubleclick"===s?r="ondblclick":"onchange"!==s||"input"!==n&&"textarea"!==n||Ge(t.type)?"onfocus"===s?r="onfocusin":"onblur"===s?r="onfocusout":Ye.test(r)&&(r=s):s=r="oninput":i&&Ve.test(r)?r=r.replace(qe,"-$&").toLowerCase():null===o&&(o=void 0),"oninput"===s&&a[r=s]&&(r="oninputCapture"),a[r]=o}}"select"==n&&a.multiple&&Array.isArray(a.value)&&(a.value=O(t.children).forEach((function(e){e.props.selected=-1!=a.value.indexOf(e.props.value)}))),"select"==n&&null!=a.defaultValue&&(a.value=O(t.children).forEach((function(e){e.props.selected=a.multiple?-1!=a.defaultValue.indexOf(e.props.value):a.defaultValue==e.props.value}))),t.class&&!t.className?(a.class=t.class,Object.defineProperty(a,"className",at)):(t.className&&!t.class||t.class&&t.className)&&(a.class=a.className=t.className),e.props=a}(e),e.$$typeof=We,it&&it(e)};var rt=i.__r;i.__r=function(e){rt&&rt(e),nt=e.__c};var ot=i.diffed;i.diffed=function(e){ot&&ot(e);var t=e.props,n=e.__e;null!=n&&"textarea"===e.type&&"value"in t&&t.value!==n.value&&(n.value=null==t.value?"":t.value),nt=null};var st={ReactCurrentDispatcher:{current:{readContext:function(e){return nt.__n[e.__c].props.value},useCallback:he,useContext:me,useDebugValue:ge,useDeferredValue:yt,useEffect:le,useId:fe,useImperativeHandle:de,useInsertionEffect:wt,useLayoutEffect:ce,useMemo:pe,useReducer:se,useRef:ue,useState:oe,useSyncExternalStore:Ct,useTransition:bt}}};function lt(e){return b.bind(null,e)}function ct(e){return!!e&&e.$$typeof===We}function ut(e){return ct(e)&&e.type===C}function dt(e){return!!e&&!!e.displayName&&("string"==typeof e.displayName||e.displayName instanceof String)&&e.displayName.startsWith("Memo(")}function pt(e){return ct(e)?W.apply(null,arguments):e}function ht(e){return!!e.__k&&(U(null,e),!0)}function mt(e){return e&&(e.base||1===e.nodeType&&e)||null}var gt=function(e,t){return e(t)},ft=function(e,t){return e(t)},vt=C;function _t(e){e()}function yt(e){return e}function bt(){return[!1,_t]}var wt=ce,Et=ct;function Ct(e,t){var n=t(),a=oe({h:{__:n,v:t}}),i=a[0].h,r=a[1];return ce((function(){i.__=n,i.v=t,St(i)&&r({h:i})}),[e,n,t]),le((function(){return St(i)&&r({h:i}),e((function(){St(i)&&r({h:i})}))}),[e]),n}function St(e){var t,n,a=e.v,i=e.__;try{var r=a();return!((t=i)===(n=r)&&(0!==t||1/t==1/n)||t!=t&&n!=n)}catch(e){return!0}}var xt={useState:oe,useId:fe,useReducer:se,useEffect:le,useLayoutEffect:ce,useInsertionEffect:wt,useTransition:bt,useDeferredValue:yt,useSyncExternalStore:Ct,startTransition:_t,useRef:ue,useImperativeHandle:de,useMemo:pe,useCallback:he,useContext:me,useDebugValue:ge,version:"18.3.1",Children:Oe,render:Ze,hydrate:Je,unmountComponentAtNode:ht,createPortal:je,createElement:b,createContext:V,createFactory:lt,cloneElement:pt,createRef:E,Fragment:C,isValidElement:ct,isElement:Et,isFragment:ut,isMemo:dt,findDOMNode:mt,Component:S,PureComponent:xe,memo:De,forwardRef:Te,flushSync:ft,unstable_batchedUpdates:gt,StrictMode:vt,Suspense:$e,SuspenseList:Le,lazy:Be,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:st},Dt=Object.freeze({__proto__:null,Children:Oe,Component:S,Fragment:C,PureComponent:xe,StrictMode:vt,Suspense:$e,SuspenseList:Le,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:st,cloneElement:pt,createContext:V,createElement:b,createFactory:lt,createPortal:je,createRef:E,default:xt,findDOMNode:mt,flushSync:ft,forwardRef:Te,hydrate:Je,isElement:Et,isFragment:ut,isMemo:dt,isValidElement:ct,lazy:Be,memo:De,render:Ze,startTransition:_t,unmountComponentAtNode:ht,unstable_batchedUpdates:gt,useCallback:he,useContext:me,useDebugValue:ge,useDeferredValue:yt,useEffect:le,useErrorBoundary:function(e){var t=re(Y++,10),n=oe();return t.__=e,q.componentDidCatch||(q.componentDidCatch=function(e,a){t.__&&t.__(e,a),n[1](e)}),[n[0],function(){n[1](void 0)}]},useId:fe,useImperativeHandle:de,useInsertionEffect:wt,useLayoutEffect:ce,useMemo:pe,useReducer:se,useRef:ue,useState:oe,useSyncExternalStore:Ct,useTransition:bt,version:"18.3.1"});function Pt(e){return{render:function(t){Ze(t,e)},unmount:function(){ht(e)}}}const kt=V(void 0),Tt=({app:e,plugin:t,children:n})=>{const[a,i]=oe(0),[r,o]=oe("/diagram-section"),s=he((()=>{i((e=>e+1))}),[]),l=pe((()=>({app:e,plugin:t,forceReload:s,reloadCount:a,currentPath:r,setCurrentPath:o})),[e,t,s,a,r,o]);return xt.createElement(kt.Provider,{value:l},n)},It=()=>{const e=me(kt);if(void 0===e)throw new Error("useSettingsContext must be used within a SettingProvider");return e};
/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function Ot(){return Ot=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ot.apply(this,arguments)}var Nt,Mt;function zt(e){void 0===e&&(e={});let t,{initialEntries:n=["/"],initialIndex:a,v5Compat:i=!1}=e;t=n.map(((e,t)=>u(e,"string"==typeof e?null:e.state,0===t?"default":void 0)));let r=l(null==a?t.length-1:a),o=Nt.Pop,s=null;function l(e){return Math.min(Math.max(e,0),t.length-1)}function c(){return t[r]}function u(e,n,a){void 0===n&&(n=null);let i=function(e,t,n,a){void 0===n&&(n=null);let i=Ot({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?Lt(t):t,{state:n,key:t&&t.key||a||At()});return i}(t?c().pathname:"/",e,n,a);return $t("/"===i.pathname.charAt(0),"relative pathnames are not supported in memory history: "+JSON.stringify(e)),i}function d(e){return"string"==typeof e?e:Bt(e)}let p={get index(){return r},get action(){return o},get location(){return c()},createHref:d,createURL:e=>new URL(d(e),"http://localhost"),encodeLocation(e){let t="string"==typeof e?Lt(e):e;return{pathname:t.pathname||"",search:t.search||"",hash:t.hash||""}},push(e,n){o=Nt.Push;let a=u(e,n);r+=1,t.splice(r,t.length,a),i&&s&&s({action:o,location:a,delta:1})},replace(e,n){o=Nt.Replace;let a=u(e,n);t[r]=a,i&&s&&s({action:o,location:a,delta:0})},go(e){o=Nt.Pop;let n=l(r+e),a=t[n];r=n,s&&s({action:o,location:a,delta:e})},listen:e=>(s=e,()=>{s=null})};return p}function Rt(e,t){if(!1===e||null==e)throw new Error(t)}function $t(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function At(){return Math.random().toString(36).substr(2,8)}function Bt(e){let{pathname:t="/",search:n="",hash:a=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),a&&"#"!==a&&(t+="#"===a.charAt(0)?a:"#"+a),t}function Lt(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}function Ft(e,t,n){return void 0===n&&(n="/"),function(e,t,n,a){let i="string"==typeof t?Lt(t):t,r=en(i.pathname||"/",n);if(null==r)return null;let o=Ht(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let s=null;for(let e=0;null==s&&e<o.length;++e){let t=Qt(r);s=Jt(o[e],t,a)}return s}(e,t,n,!1)}function Ht(e,t,n,a){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===a&&(a="");let i=(e,i,r)=>{let o={relativePath:void 0===r?e.path||"":r,caseSensitive:!0===e.caseSensitive,childrenIndex:i,route:e};o.relativePath.startsWith("/")&&(Rt(o.relativePath.startsWith(a),'Absolute route path "'+o.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(a.length));let s=rn([a,o.relativePath]),l=n.concat(o);e.children&&e.children.length>0&&(Rt(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),Ht(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:Zt(s,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let n of Ut(e.path))i(e,t,n);else i(e,t)})),t}function Ut(e){let t=e.split("/");if(0===t.length)return[];let[n,...a]=t,i=n.endsWith("?"),r=n.replace(/\?$/,"");if(0===a.length)return i?[r,""]:[r];let o=Ut(a.join("/")),s=[];return s.push(...o.map((e=>""===e?r:[r,e].join("/")))),i&&s.push(...o),s.map((t=>e.startsWith("/")&&""===t?"/":t))}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(Nt||(Nt={})),function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(Mt||(Mt={}));const jt=/^:[\w-]+$/,Wt=3,Vt=2,Yt=1,qt=10,Xt=-2,Gt=e=>"*"===e;function Zt(e,t){let n=e.split("/"),a=n.length;return n.some(Gt)&&(a+=Xt),t&&(a+=Vt),n.filter((e=>!Gt(e))).reduce(((e,t)=>e+(jt.test(t)?Wt:""===t?Yt:qt)),a)}function Jt(e,t,n){let{routesMeta:a}=e,i={},r="/",o=[];for(let e=0;e<a.length;++e){let s=a[e],l=e===a.length-1,c="/"===r?t:t.slice(r.length)||"/",u=Kt({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},c),d=s.route;if(!u&&l&&n&&!a[a.length-1].route.index&&(u=Kt({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},c)),!u)return null;Object.assign(i,u.params),o.push({params:i,pathname:rn([r,u.pathname]),pathnameBase:on(rn([r,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(r=rn([r,u.pathnameBase]))}return o}function Kt(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,a]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);$t("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let a=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(a.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(a.push({paramName:"*"}),i+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":""!==e&&"/"!==e&&(i+="(?:(?=\\/|$))");let r=new RegExp(i,t?void 0:"i");return[r,a]}(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let r=i[0],o=r.replace(/(.)\/+$/,"$1"),s=i.slice(1);return{params:a.reduce(((e,t,n)=>{let{paramName:a,isOptional:i}=t;if("*"===a){let e=s[n]||"";o=r.slice(0,r.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[a]=i&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{}),pathname:r,pathnameBase:o,pattern:e}}function Qt(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return $t(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function en(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,a=e.charAt(n);return a&&"/"!==a?null:e.slice(n)||"/"}function tn(e,t,n,a){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function nn(e,t){let n=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function an(e,t,n,a){let i;void 0===a&&(a=!1),"string"==typeof e?i=Lt(e):(i=Ot({},e),Rt(!i.pathname||!i.pathname.includes("?"),tn("?","pathname","search",i)),Rt(!i.pathname||!i.pathname.includes("#"),tn("#","pathname","hash",i)),Rt(!i.search||!i.search.includes("#"),tn("#","search","hash",i)));let r,o=""===e||""===i.pathname,s=o?"/":i.pathname;if(null==s)r=n;else{let e=t.length-1;if(!a&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}r=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:a="",hash:i=""}="string"==typeof e?Lt(e):e,r=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:r,search:sn(a),hash:ln(i)}}(i,r),c=s&&"/"!==s&&s.endsWith("/"),u=(o||"."===s)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!u||(l.pathname+="/"),l}const rn=e=>e.join("/").replace(/\/\/+/g,"/"),on=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),sn=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",ln=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const cn=["post","put","patch","delete"];new Set(cn);const un=["get",...cn];
/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function dn(){return dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},dn.apply(this,arguments)}new Set(un);const pn=V(null),hn=V(null),mn=V(null),gn=V(null),fn=V({outlet:null,matches:[],isDataRoute:!1}),vn=V(null);function _n(){return null!=me(gn)}function yn(){return _n()||Rt(!1),me(gn).location}function bn(e){me(mn).static||ce(e)}function wn(){let{isDataRoute:e}=me(fn);return e?function(){let{router:e}=function(){let e=me(pn);return e||Rt(!1),e}(kn.UseNavigateStable),t=In(Tn.UseNavigateStable),n=ue(!1);return bn((()=>{n.current=!0})),he((function(a,i){void 0===i&&(i={}),n.current&&("number"==typeof a?e.navigate(a):e.navigate(a,dn({fromRouteId:t},i)))}),[e,t])}():function(){_n()||Rt(!1);let e=me(pn),{basename:t,future:n,navigator:a}=me(mn),{matches:i}=me(fn),{pathname:r}=yn(),o=JSON.stringify(nn(i,n.v7_relativeSplatPath)),s=ue(!1);return bn((()=>{s.current=!0})),he((function(n,i){if(void 0===i&&(i={}),!s.current)return;if("number"==typeof n)return void a.go(n);let l=an(n,JSON.parse(o),r,"path"===i.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:rn([t,l.pathname])),(i.replace?a.replace:a.push)(l,i.state,i)}),[t,a,o,r,e])}()}function En(e,t){let{relative:n}=void 0===t?{}:t,{future:a}=me(mn),{matches:i}=me(fn),{pathname:r}=yn(),o=JSON.stringify(nn(i,a.v7_relativeSplatPath));return pe((()=>an(e,JSON.parse(o),r,"path"===n)),[e,o,r,n])}function Cn(e,t){return function(e,t,n,a){_n()||Rt(!1);let{navigator:i}=me(mn),{matches:r}=me(fn),o=r[r.length-1],s=o?o.params:{};!o||o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let c,u=yn();if(t){var d;let e="string"==typeof t?Lt(t):t;"/"===l||(null==(d=e.pathname)?void 0:d.startsWith(l))||Rt(!1),c=e}else c=u;let p=c.pathname||"/",h=p;if("/"!==l){let e=l.replace(/^\//,"").split("/");h="/"+p.replace(/^\//,"").split("/").slice(e.length).join("/")}let m=Ft(e,{pathname:h}),g=function(e,t,n,a){var i;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===a&&(a=null);if(null==e){var r;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(r=a)&&r.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let o=e,s=null==(i=n)?void 0:i.errors;if(null!=s){let e=o.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||Rt(!1),o=o.slice(0,Math.min(o.length,e+1))}let l=!1,c=-1;if(n&&a&&a.v7_partialHydration)for(let e=0;e<o.length;e++){let t=o[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(c=e),t.route.id){let{loaderData:e,errors:a}=n,i=t.route.loader&&void 0===e[t.route.id]&&(!a||void 0===a[t.route.id]);if(t.route.lazy||i){l=!0,o=c>=0?o.slice(0,c+1):[o[0]];break}}}return o.reduceRight(((e,a,i)=>{let r,u=!1,d=null,p=null;n&&(r=s&&a.route.id?s[a.route.id]:void 0,d=a.route.errorElement||xn,l&&(c<0&&0===i?(u=!0,p=null):c===i&&(u=!0,p=a.route.hydrateFallbackElement||null)));let h=t.concat(o.slice(0,i+1)),m=()=>{let t;return t=r?d:u?p:a.route.Component?b(a.route.Component,null):a.route.element?a.route.element:e,b(Pn,{match:a,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(a.route.ErrorBoundary||a.route.errorElement||0===i)?b(Dn,{location:n.location,revalidation:n.revalidation,component:d,error:r,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}(m&&m.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:rn([l,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?l:rn([l,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),r,n,a);if(t&&g)return b(gn.Provider,{value:{location:dn({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Nt.Pop}},g);return g}(e,t)}function Sn(){let e=function(){var e;let t=me(vn),n=function(){let e=me(hn);return e||Rt(!1),e}(Tn.UseRouteError),a=In(Tn.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[a]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return b(C,null,b("h2",null,"Unexpected Application Error!"),b("h3",{style:{fontStyle:"italic"}},t),n?b("pre",{style:a},n):null,null)}const xn=b(Sn,null);class Dn extends S{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?b(fn.Provider,{value:this.props.routeContext},b(vn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Pn(e){let{routeContext:t,match:n,children:a}=e,i=me(pn);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),b(fn.Provider,{value:t},a)}var kn=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(kn||{}),Tn=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Tn||{});function In(e){let t=function(){let e=me(fn);return e||Rt(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||Rt(!1),n.route.id}const On=Dt.startTransition;function Nn(e){let{basename:t,children:n,initialEntries:a,initialIndex:i,future:r}=e,o=ue();null==o.current&&(o.current=zt({initialEntries:a,initialIndex:i,v5Compat:!0}));let s=o.current,[l,c]=oe({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},d=he((e=>{u&&On?On((()=>c(e))):c(e)}),[c,u]);return ce((()=>s.listen(d)),[s,d]),b(zn,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:s,future:r})}function Mn(e){Rt(!1)}function zn(e){let{basename:t="/",children:n=null,location:a,navigationType:i=Nt.Pop,navigator:r,static:o=!1,future:s}=e;_n()&&Rt(!1);let l=t.replace(/^\/*/,"/"),c=pe((()=>({basename:l,navigator:r,static:o,future:dn({v7_relativeSplatPath:!1},s)})),[l,s,r,o]);"string"==typeof a&&(a=Lt(a));let{pathname:u="/",search:d="",hash:p="",state:h=null,key:m="default"}=a,g=pe((()=>{let e=en(u,l);return null==e?null:{location:{pathname:e,search:d,hash:p,state:h,key:m},navigationType:i}}),[l,u,d,p,h,m,i]);return null==g?null:b(mn.Provider,{value:c},b(gn.Provider,{children:n,value:g}))}function Rn(e){let{children:t,location:n}=e;return Cn($n(t),n)}function $n(e,t){void 0===t&&(t=[]);let n=[];return Oe.forEach(e,((e,a)=>{if(!ct(e))return;let i=[...t,a];if(e.type===C)return void n.push.apply(n,$n(e.props.children,i));e.type!==Mn&&Rt(!1),e.props.index&&e.props.children&&Rt(!1);let r={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(r.children=$n(e.props.children,i)),n.push(r)})),n}
/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function An(){return An=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},An.apply(this,arguments)}function Bn(e,t){if(null==e)return{};var n,a,i={},r=Object.keys(e);for(a=0;a<r.length;a++)n=r[a],t.indexOf(n)>=0||(i[n]=e[n]);return i}new Promise((()=>{}));const Ln=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Fn=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"];try{window.__reactRouterVersion="6"}catch(Q){}const Hn=V({isTransitioning:!1}),Un="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,jn=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Wn=Te((function(e,t){let n,{onClick:a,relative:i,reloadDocument:r,replace:o,state:s,target:l,to:c,preventScrollReset:u,viewTransition:d}=e,p=Bn(e,Ln),{basename:h}=me(mn),m=!1;if("string"==typeof c&&jn.test(c)&&(n=c,Un))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=en(t.pathname,h);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:m=!0}catch(e){}let g=function(e,t){let{relative:n}=void 0===t?{}:t;_n()||Rt(!1);let{basename:a,navigator:i}=me(mn),{hash:r,pathname:o,search:s}=En(e,{relative:n}),l=o;return"/"!==a&&(l="/"===o?a:rn([a,o])),i.createHref({pathname:l,search:s,hash:r})}(c,{relative:i}),f=function(e,t){let{target:n,replace:a,state:i,preventScrollReset:r,relative:o,viewTransition:s}=void 0===t?{}:t,l=wn(),c=yn(),u=En(e,{relative:o});return he((t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,n)){t.preventDefault();let n=void 0!==a?a:Bt(c)===Bt(u);l(e,{replace:n,state:i,preventScrollReset:r,relative:o,viewTransition:s})}}),[c,l,u,a,i,n,e,r,o,s])}(c,{replace:o,state:s,target:l,preventScrollReset:u,relative:i,viewTransition:d});return b("a",An({},p,{href:n||g,onClick:m||r?a:function(e){a&&a(e),e.defaultPrevented||f(e)},ref:t,target:l}))})),Vn=Te((function(e,t){let{"aria-current":n="page",caseSensitive:a=!1,className:i="",end:r=!1,style:o,to:s,viewTransition:l,children:c}=e,u=Bn(e,Fn),d=En(s,{relative:u.relative}),p=yn(),h=me(hn),{navigator:m,basename:g}=me(mn),f=null!=h&&function(e,t){void 0===t&&(t={});let n=me(Hn);null==n&&Rt(!1);let{basename:a}=function(){let e=me(pn);return e||Rt(!1),e}(Yn.useViewTransitionState),i=En(e,{relative:t.relative});if(!n.isTransitioning)return!1;let r=en(n.currentLocation.pathname,a)||n.currentLocation.pathname,o=en(n.nextLocation.pathname,a)||n.nextLocation.pathname;return null!=Kt(i.pathname,o)||null!=Kt(i.pathname,r)}(d)&&!0===l,v=m.encodeLocation?m.encodeLocation(d).pathname:d.pathname,_=p.pathname,y=h&&h.navigation&&h.navigation.location?h.navigation.location.pathname:null;a||(_=_.toLowerCase(),y=y?y.toLowerCase():null,v=v.toLowerCase()),y&&g&&(y=en(y,g)||y);const w="/"!==v&&v.endsWith("/")?v.length-1:v.length;let E,C=_===v||!r&&_.startsWith(v)&&"/"===_.charAt(w),S=null!=y&&(y===v||!r&&y.startsWith(v)&&"/"===y.charAt(v.length)),x={isActive:C,isPending:S,isTransitioning:f},D=C?n:void 0;E="function"==typeof i?i(x):[i,C?"active":null,S?"pending":null,f?"transitioning":null].filter(Boolean).join(" ");let P="function"==typeof o?o(x):o;return b(Wn,An({},u,{"aria-current":D,className:E,ref:t,style:P,to:s,viewTransition:l}),"function"==typeof c?c(x):c)}));var Yn,qn;!function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"}(Yn||(Yn={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(qn||(qn={}));class Xn extends S{element=null;constructor(e){super(e),e.containerEl&&(this.element=e.containerEl)}componentDidMount(){const e=document.createElement("div");this.element=e,this.props.containerEl&&this.props.containerEl.appendChild(e)}addDesc(e){if(this.element){const t=document.createElement("div");t.textContent=e,this.element.appendChild(t)}}addDescriptions(e){return this.element&&e.forEach((e=>this.addDesc(e))),this}render(){return null}}function Gn(e){return void 0!==e.priority}const Zn=({name:e,desc:n,setHeading:a,setDisabled:i,noBorder:r,class:o,addToggles:s,addTexts:l,addTextAreas:c,addMomentFormats:u,addDropdowns:d,addSearches:p,addButtons:h,addExtraButtons:m,addSliders:g,addMultiDesc:f,setupSettingManually:v})=>{const _=xt.useRef(),y=xt.useRef(null),b=he(((e,t)=>{if(!e?.length)return;const n=function(e){return e.filter((e=>void 0!==e&&!1!==e)).map(((e,t)=>({callback:Gn(e)?e.callback:e,priority:Gn(e)?e.priority:t,originalIndex:t}))).sort(((e,t)=>e.priority===t.priority?e.originalIndex-t.originalIndex:e.priority-t.priority)).map((({callback:e})=>e))}(e);return e=>{n.forEach((n=>t(e,n)))}}),[]),w=he((e=>{b(s,((e,t)=>e.addToggle(t)))?.(e)}),[s,b]),E=he((e=>{b(h,((e,t)=>e.addButton(t)))?.(e)}),[h,b]),C=he((e=>{b(l,((e,t)=>e.addText(t)))?.(e)}),[l,b]),S=he((e=>{b(c,((e,t)=>e.addTextArea(t)))?.(e)}),[c,b]),x=he((e=>{b(u,((e,t)=>e.addMomentFormat(t)))?.(e)}),[u,b]),D=he((e=>{b(d,((e,t)=>e.addDropdown(t)))?.(e)}),[d,b]),P=he((e=>{b(p,((e,t)=>e.addSearch(t)))?.(e)}),[p,b]),k=he((e=>{b(m,((e,t)=>e.addExtraButton(t)))?.(e)}),[m,b]),T=he((e=>{b(g,((e,t)=>e.addSlider(t)))?.(e)}),[g,b]),I=he((e=>{if(!f)return;const t=Gn(f)?f.callback:f,n=document.createElement("div");n.addClass("setting-item-description"),e.infoEl&&e.infoEl.appendChild(n);t(new Xn({containerEl:n}))}),[f]),O=he((t=>{v&&v(t),e&&t.setName(e),n&&t.setDesc(n),a&&t.setHeading(),i&&t.setDisabled(i),o&&t.setClass(o),C(t),S(t),w(t),I(t),E(t),x(t),D(t),P(t),k(t),T(t)}),[e,n,a,o,w,E,C,S,x,D,P,k,T,I]);return ce((()=>{if(y.current)return y.current.empty(),_.current=new t.Setting(y.current),O(_.current),()=>{y.current?.empty()}}),[O]),xt.createElement("div",{ref:y,className:"react-obsidian-settings-item "+(r?"no-border":"")})},Jn=()=>{const{plugin:e}=It();return xt.createElement(xt.Fragment,null,xt.createElement(Zn,{name:"Visit GitHub page of this plugin",addButtons:[e=>(e.setIcon("github"),e.setTooltip("Go to GitHub page of this plugin"),e.onClick((e=>{open("https://github.com/gitcpy/diagram-zoom-drag/",void 0)})),e)]}),xt.createElement(Zn,{name:"Do you have any feedback?",addButtons:[e=>(e.setIcon("message-circle-question"),e.setTooltip("Leave feedback"),e.onClick((()=>{open("https://github.com/gitcpy/diagram-zoom-drag/issues",void 0)})),e)]}),xt.createElement("div",{style:{position:"absolute",bottom:10,left:"50%"}},xt.createElement("div",{style:{fontSize:"small",color:"gray",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"}},xt.createElement("div",null,"Current version: ",e.manifest.version),xt.createElement("div",null,"•"," ",xt.createElement("a",{href:"https://github.com/gitcpy/diagram-zoom-drag/blob/main/LICENSE",target:"_blank"},"MIT License")))))};var Kn;!function(e){e.Collapsed="collapsed",e.Expanded="expanded"}(Kn||(Kn={}));const Qn=()=>{const{plugin:e}=It(),[t,n]=oe(e.settings.diagramExpandedHeight),[a,i]=oe(e.settings.diagramExpandedWidth),[r,o]=oe(e.settings.diagramCollapsedHeight),[s,l]=oe(e.settings.diagramCollapsedWidth),c=e=>{const t=parseInt(e,10);return t>=100&&t<=1e3},u=e=>e.match(/^\d+$/),d=d=>{const p=d===Kn.Collapsed?"Collapsed":"Expanded",h=d===Kn.Collapsed?r:t,m=d===Kn.Collapsed?s:a;return xt.createElement(xt.Fragment,null,xt.createElement(Zn,{name:`${p} diagram container size`,addMultiDesc:e=>(e.addDescriptions([`Set the container dimensions for ${p.toLowerCase()} state in pixels.`,"Click Save button to apply changes."]),e),setHeading:!0,noBorder:!0}),xt.createElement(Zn,{addTexts:[t=>(t.setValue(h.toString()),t.inputEl.id=`input${p.toLowerCase()}Height`,t.inputEl.type="number",t.inputEl.min="100",t.inputEl.max="1000",t.inputEl.ariaLabel=`${p} height in pixels`,t.inputEl.onblur=()=>{u(t.inputEl.value)?c(t.inputEl.value)||e.showNotice("Invalid range. Please enter number in range 100-1000px"):e.showNotice("Please enter valid number")},t),t=>(t.setValue(m.toString()),t.inputEl.id=`input${p.toLowerCase()}Width`,t.inputEl.type="number",t.inputEl.min="100",t.inputEl.max="1000",t.inputEl.ariaLabel=`${p} width in pixels`,t.inputEl.onblur=()=>{u(t.inputEl.value)?c(t.inputEl.value)||e.showNotice("Invalid range. Please enter number in range 100-1000px"):e.showNotice("Please enter valid number")},t)],addButtons:[t=>(t.setIcon("save"),t.onClick((async t=>{const a=document.querySelector(`#input${p.toLowerCase()}Width`),r=document.querySelector(`#input${p.toLowerCase()}Height`);if(!a||!r)return;if(!u(a.value)||!u(r.value))return void e.showNotice("Please enter valid numbers");if(!c(a.value)||!c(r.value))return void e.showNotice("Invalid range. Please enter number in range 100-1000px");const s=parseInt(a.value,10),h=parseInt(r.value,10);d===Kn.Collapsed?(l(s),o(h),e.settings.diagramCollapsedHeight=h,e.settings.diagramCollapsedWidth=s):(i(s),n(h),e.settings.diagramExpandedHeight=h,e.settings.diagramExpandedWidth=s),await e.settingsManager.saveSettings(),e.updateCssProperties(),e.showNotice("Saved successfully")})),t)],noBorder:!0}))};return xt.createElement(xt.Fragment,null,d(Kn.Expanded),d(Kn.Collapsed))},ea=()=>{const{plugin:e}=It();return xt.createElement(xt.Fragment,null,xt.createElement(Zn,{name:"Collapse",setHeading:!0}),xt.createElement(Zn,{name:"Collapse diagrams by default?",addToggles:[t=>(t.setValue(e.settings.collapseByDefault).onChange((async t=>{e.settings.collapseByDefault=t,await e.settingsManager.saveSettings()})),t)]}),xt.createElement(Zn,{name:"Automatically collapse diagrams on focus change?",addToggles:[t=>(t.setValue(e.settings.automaticCollapsingOnFocusChange).onChange((async t=>{e.settings.automaticCollapsingOnFocusChange=t,await e.settingsManager.saveSettings()})),t)]}),xt.createElement(Qn,null))},ta=({modal:e})=>{const n=e.plugin.manifest.dir,[a,i]=oe(!0),[r,o]=oe(""),s=ue(t.normalizePath(`${n}/assets/user-guide-video.mp4`));return le((()=>{i(!0),e.loadVideo().then((t=>{t&&(async()=>{try{const t=await e.app.vault.adapter.readBinary(s.current),n=Buffer.from(t).toString("base64");o(`data:video/mp4;base64,${n}`)}catch(t){console.error(t),e.plugin.showNotice("Something went wrong. The video is missing.")}finally{i(!1)}})()})).catch((e=>console.error(e)))}),[e]),xt.createElement(xt.Fragment,null,xt.createElement(Zn,{name:"How this plugin does work",setHeading:!0}),xt.createElement(Zn,{addMultiDesc:e=>(e.addDesc("This plugin stores data related to your selected elements."),e.addDesc("When you open another Markdown file with a diagram code in it and switch to preview mode, the plugin attempts to find the corresponding diagram in preview."),e.addDesc("If a matching diagram is found, the plugin creates a container, applies CSS styles, and enables diagram movement, zooming, and adds a control panel."),e)}),xt.createElement(Zn,{name:"How to find selectors in DevTool",setHeading:!0,desc:"To identify the CSS selectors for diagrams on this page, follow these steps below using your browser’s DevTools:"}),xt.createElement(Zn,{name:"Steps to find selectors:",addMultiDesc:e=>(e.addDesc("1. Open the markdown file in Obsidian where the diagram is. You should switch to preview mode."),e.addDesc("2. Open the DevTools window. You can do it by pressing CTRL + SHIFT + I."),e.addDesc('3. Click the "Select an element on this page to inspect it" button (usually a arrow icon) in the top-left corner of the DevTools window. You can also press CTRL + SHIFT + C'),e.addDesc("4. Move your cursor over the diagram and click on it to select the element."),e.addDesc("5. In the Elements tab of DevTools, you will see the HTML element corresponding to the diagram highlighted."),e.addDesc("6. Look at the classes applied to this element in the DevTools panel to identify the CSS selectors you need."),e)}),a&&xt.createElement("p",null,"Loading video..."),!a&&r&&xt.createElement("video",{src:r,controls:!0,autoPlay:!1,style:{width:"100%",maxHeight:"400px"}}))};class na extends t.Modal{plugin;root=void 0;constructor(e,t){super(e),this.plugin=t,this.setTitle("Guide")}async onOpen(){const{contentEl:e}=this;this.root=Pt(e.createEl("div")),this.root.render(xt.createElement(ta,{modal:this}))}onClose(){this.root?.unmount(),this.contentEl.empty()}async loadVideo(){const e=await this.plugin.pluginStateChecker.isFirstPluginStart(),n=this.plugin.manifest.dir;if(!n)return!1;const a=t.normalizePath(`${n}/assets`),i=t.normalizePath(`${a}/user-guide-video.mp4`);if(await this.app.vault.adapter.exists(a)||await this.app.vault.adapter.mkdir(a),e)await this.downloadVideo(i);else{await this.app.vault.adapter.exists(i)||await this.downloadVideo(i)}return this.app.vault.adapter.exists(i)}async downloadVideo(e){try{const n="https://raw.githubusercontent.com/gitcpy/diagram-zoom-drag/main/assets/videos/find-class.mp4",a=await t.requestUrl(n);if(200!==a.status)throw new Error(`Error downloading video: ${a.status}`);return await this.app.vault.adapter.writeBinary(e,a.arrayBuffer),!0}catch(e){return console.error("Error downloading video:",e),!1}}}var aa;!function(e){e.PanelsChangedVisibility="PanelsChangedVisibility",e.VisibilityOptionChanged="VisibilityOptionChanged",e.ItemsPerPageChanged="ItemsPerPageChanged"}(aa||(aa={}));var ia=function(){return ia=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},ia.apply(this,arguments)};function ra(e,t,n){if(n||2===arguments.length)for(var a,i=0,r=t.length;i<r;i++)!a&&i in t||(a||(a=Array.prototype.slice.call(t,0,i)),a[i]=t[i]);return e.concat(a||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var oa="-ms-",sa="-moz-",la="-webkit-",ca="comm",ua="rule",da="decl",pa="@keyframes",ha=Math.abs,ma=String.fromCharCode,ga=Object.assign;function fa(e){return e.trim()}function va(e,t){return(e=t.exec(e))?e[0]:e}function _a(e,t,n){return e.replace(t,n)}function ya(e,t,n){return e.indexOf(t,n)}function ba(e,t){return 0|e.charCodeAt(t)}function wa(e,t,n){return e.slice(t,n)}function Ea(e){return e.length}function Ca(e){return e.length}function Sa(e,t){return t.push(e),e}function xa(e,t){return e.filter((function(e){return!va(e,t)}))}var Da=1,Pa=1,ka=0,Ta=0,Ia=0,Oa="";function Na(e,t,n,a,i,r,o,s){return{value:e,root:t,parent:n,type:a,props:i,children:r,line:Da,column:Pa,length:o,return:"",siblings:s}}function Ma(e,t){return ga(Na("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function za(e){for(;e.root;)e=Ma(e.root,{children:[e]});Sa(e,e.siblings)}function Ra(){return Ia=Ta>0?ba(Oa,--Ta):0,Pa--,10===Ia&&(Pa=1,Da--),Ia}function $a(){return Ia=Ta<ka?ba(Oa,Ta++):0,Pa++,10===Ia&&(Pa=1,Da++),Ia}function Aa(){return ba(Oa,Ta)}function Ba(){return Ta}function La(e,t){return wa(Oa,e,t)}function Fa(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Ha(e){return fa(La(Ta-1,Wa(91===e?e+2:40===e?e+1:e)))}function Ua(e){for(;(Ia=Aa())&&Ia<33;)$a();return Fa(e)>2||Fa(Ia)>3?"":" "}function ja(e,t){for(;--t&&$a()&&!(Ia<48||Ia>102||Ia>57&&Ia<65||Ia>70&&Ia<97););return La(e,Ba()+(t<6&&32==Aa()&&32==$a()))}function Wa(e){for(;$a();)switch(Ia){case e:return Ta;case 34:case 39:34!==e&&39!==e&&Wa(Ia);break;case 40:41===e&&Wa(e);break;case 92:$a()}return Ta}function Va(e,t){for(;$a()&&e+Ia!==57&&(e+Ia!==84||47!==Aa()););return"/*"+La(t,Ta-1)+"*"+ma(47===e?e:$a())}function Ya(e){for(;!Fa(Aa());)$a();return La(e,Ta)}function qa(e){return function(e){return Oa="",e}(Xa("",null,null,null,[""],e=function(e){return Da=Pa=1,ka=Ea(Oa=e),Ta=0,[]}(e),0,[0],e))}function Xa(e,t,n,a,i,r,o,s,l){for(var c=0,u=0,d=o,p=0,h=0,m=0,g=1,f=1,v=1,_=0,y="",b=i,w=r,E=a,C=y;f;)switch(m=_,_=$a()){case 40:if(108!=m&&58==ba(C,d-1)){-1!=ya(C+=_a(Ha(_),"&","&\f"),"&\f",ha(c?s[c-1]:0))&&(v=-1);break}case 34:case 39:case 91:C+=Ha(_);break;case 9:case 10:case 13:case 32:C+=Ua(m);break;case 92:C+=ja(Ba()-1,7);continue;case 47:switch(Aa()){case 42:case 47:Sa(Za(Va($a(),Ba()),t,n,l),l);break;default:C+="/"}break;case 123*g:s[c++]=Ea(C)*v;case 125*g:case 59:case 0:switch(_){case 0:case 125:f=0;case 59+u:-1==v&&(C=_a(C,/\f/g,"")),h>0&&Ea(C)-d&&Sa(h>32?Ja(C+";",a,n,d-1,l):Ja(_a(C," ","")+";",a,n,d-2,l),l);break;case 59:C+=";";default:if(Sa(E=Ga(C,t,n,c,u,i,s,y,b=[],w=[],d,r),r),123===_)if(0===u)Xa(C,t,E,E,b,r,d,s,w);else switch(99===p&&110===ba(C,3)?100:p){case 100:case 108:case 109:case 115:Xa(e,E,E,a&&Sa(Ga(e,E,E,0,0,i,s,y,i,b=[],d,w),w),i,w,d,s,a?b:w);break;default:Xa(C,E,E,E,[""],w,0,s,w)}}c=u=h=0,g=v=1,y=C="",d=o;break;case 58:d=1+Ea(C),h=m;default:if(g<1)if(123==_)--g;else if(125==_&&0==g++&&125==Ra())continue;switch(C+=ma(_),_*g){case 38:v=u>0?1:(C+="\f",-1);break;case 44:s[c++]=(Ea(C)-1)*v,v=1;break;case 64:45===Aa()&&(C+=Ha($a())),p=Aa(),u=d=Ea(y=C+=Ya(Ba())),_++;break;case 45:45===m&&2==Ea(C)&&(g=0)}}return r}function Ga(e,t,n,a,i,r,o,s,l,c,u,d){for(var p=i-1,h=0===i?r:[""],m=Ca(h),g=0,f=0,v=0;g<a;++g)for(var _=0,y=wa(e,p+1,p=ha(f=o[g])),b=e;_<m;++_)(b=fa(f>0?h[_]+" "+y:_a(y,/&\f/g,h[_])))&&(l[v++]=b);return Na(e,t,n,0===i?ua:s,l,c,u,d)}function Za(e,t,n,a){return Na(e,t,n,ca,ma(Ia),wa(e,2,-2),0,a)}function Ja(e,t,n,a,i){return Na(e,t,n,da,wa(e,0,a),wa(e,a+1,-1),a,i)}function Ka(e,t,n){switch(function(e,t){return 45^ba(e,0)?(((t<<2^ba(e,0))<<2^ba(e,1))<<2^ba(e,2))<<2^ba(e,3):0}(e,t)){case 5103:return la+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return la+e+e;case 4789:return sa+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return la+e+sa+e+oa+e+e;case 5936:switch(ba(e,t+11)){case 114:return la+e+oa+_a(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return la+e+oa+_a(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return la+e+oa+_a(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return la+e+oa+e+e;case 6165:return la+e+oa+"flex-"+e+e;case 5187:return la+e+_a(e,/(\w+).+(:[^]+)/,la+"box-$1$2"+oa+"flex-$1$2")+e;case 5443:return la+e+oa+"flex-item-"+_a(e,/flex-|-self/g,"")+(va(e,/flex-|baseline/)?"":oa+"grid-row-"+_a(e,/flex-|-self/g,""))+e;case 4675:return la+e+oa+"flex-line-pack"+_a(e,/align-content|flex-|-self/g,"")+e;case 5548:return la+e+oa+_a(e,"shrink","negative")+e;case 5292:return la+e+oa+_a(e,"basis","preferred-size")+e;case 6060:return la+"box-"+_a(e,"-grow","")+la+e+oa+_a(e,"grow","positive")+e;case 4554:return la+_a(e,/([^-])(transform)/g,"$1"+la+"$2")+e;case 6187:return _a(_a(_a(e,/(zoom-|grab)/,la+"$1"),/(image-set)/,la+"$1"),e,"")+e;case 5495:case 3959:return _a(e,/(image-set\([^]*)/,la+"$1$`$1");case 4968:return _a(_a(e,/(.+:)(flex-)?(.*)/,la+"box-pack:$3"+oa+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+la+e+e;case 4200:if(!va(e,/flex-|baseline/))return oa+"grid-column-align"+wa(e,t)+e;break;case 2592:case 3360:return oa+_a(e,"template-","")+e;case 4384:case 3616:return n&&n.some((function(e,n){return t=n,va(e.props,/grid-\w+-end/)}))?~ya(e+(n=n[t].value),"span",0)?e:oa+_a(e,"-start","")+e+oa+"grid-row-span:"+(~ya(n,"span",0)?va(n,/\d+/):+va(n,/\d+/)-+va(e,/\d+/))+";":oa+_a(e,"-start","")+e;case 4896:case 4128:return n&&n.some((function(e){return va(e.props,/grid-\w+-start/)}))?e:oa+_a(_a(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return _a(e,/(.+)-inline(.+)/,la+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Ea(e)-1-t>6)switch(ba(e,t+1)){case 109:if(45!==ba(e,t+4))break;case 102:return _a(e,/(.+:)(.+)-([^]+)/,"$1"+la+"$2-$3$1"+sa+(108==ba(e,t+3)?"$3":"$2-$3"))+e;case 115:return~ya(e,"stretch",0)?Ka(_a(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return _a(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(t,n,a,i,r,o,s){return oa+n+":"+a+s+(i?oa+n+"-span:"+(r?o:+o-+a)+s:"")+e}));case 4949:if(121===ba(e,t+6))return _a(e,":",":"+la)+e;break;case 6444:switch(ba(e,45===ba(e,14)?18:11)){case 120:return _a(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+la+(45===ba(e,14)?"inline-":"")+"box$3$1"+la+"$2$3$1"+oa+"$2box$3")+e;case 100:return _a(e,":",":"+oa)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return _a(e,"scroll-","scroll-snap-")+e}return e}function Qa(e,t){for(var n="",a=0;a<e.length;a++)n+=t(e[a],a,e,t)||"";return n}function ei(e,t,n,a){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case da:return e.return=e.return||e.value;case ca:return"";case pa:return e.return=e.value+"{"+Qa(e.children,a)+"}";case ua:if(!Ea(e.value=e.props.join(",")))return""}return Ea(n=Qa(e.children,a))?e.return=e.value+"{"+n+"}":""}function ti(e,t,n,a){if(e.length>-1&&!e.return)switch(e.type){case da:return void(e.return=Ka(e.value,e.length,n));case pa:return Qa([Ma(e,{value:_a(e.value,"@","@"+la)})],a);case ua:if(e.length)return function(e,t){return e.map(t).join("")}(n=e.props,(function(t){switch(va(t,a=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":za(Ma(e,{props:[_a(t,/:(read-\w+)/,":-moz-$1")]})),za(Ma(e,{props:[t]})),ga(e,{props:xa(n,a)});break;case"::placeholder":za(Ma(e,{props:[_a(t,/:(plac\w+)/,":"+la+"input-$1")]})),za(Ma(e,{props:[_a(t,/:(plac\w+)/,":-moz-$1")]})),za(Ma(e,{props:[_a(t,/:(plac\w+)/,oa+"input-$1")]})),za(Ma(e,{props:[t]})),ga(e,{props:xa(n,a)})}return""}))}}var ni={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},ai="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",ii="active",ri="data-styled-version",oi="6.1.13",si="/*!sc*/\n",li="undefined"!=typeof window&&"HTMLElement"in window,ci=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&("false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY)),ui=Object.freeze([]),di=Object.freeze({});var pi=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),hi=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,mi=/(^-|-$)/g;function gi(e){return e.replace(hi,"-").replace(mi,"")}var fi=/(a)(d)/gi,vi=function(e){return String.fromCharCode(e+(e>25?39:97))};function _i(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=vi(t%52)+n;return(vi(t%52)+n).replace(fi,"$1-$2")}var yi,bi=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},wi=function(e){return bi(5381,e)};function Ei(e){return"string"==typeof e&&!0}var Ci="function"==typeof Symbol&&Symbol.for,Si=Ci?Symbol.for("react.memo"):60115,xi=Ci?Symbol.for("react.forward_ref"):60112,Di={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Pi={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},ki={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Ti=((yi={})[xi]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},yi[Si]=ki,yi);function Ii(e){return("type"in(t=e)&&t.type.$$typeof)===Si?ki:"$$typeof"in e?Ti[e.$$typeof]:Di;var t}var Oi=Object.defineProperty,Ni=Object.getOwnPropertyNames,Mi=Object.getOwnPropertySymbols,zi=Object.getOwnPropertyDescriptor,Ri=Object.getPrototypeOf,$i=Object.prototype;function Ai(e,t,n){if("string"!=typeof t){if($i){var a=Ri(t);a&&a!==$i&&Ai(e,a,n)}var i=Ni(t);Mi&&(i=i.concat(Mi(t)));for(var r=Ii(e),o=Ii(t),s=0;s<i.length;++s){var l=i[s];if(!(l in Pi||n&&n[l]||o&&l in o||r&&l in r)){var c=zi(t,l);try{Oi(e,l,c)}catch(e){}}}}return e}function Bi(e){return"function"==typeof e}function Li(e){return"object"==typeof e&&"styledComponentId"in e}function Fi(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function Hi(e,t){if(0===e.length)return"";for(var n=e[0],a=1;a<e.length;a++)n+=e[a];return n}function Ui(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function ji(e,t,n){if(void 0===n&&(n=!1),!n&&!Ui(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var a=0;a<t.length;a++)e[a]=ji(e[a],t[a]);else if(Ui(t))for(var a in t)e[a]=ji(e[a],t[a]);return e}function Wi(e,t){Object.defineProperty(e,"toString",{value:t})}function Vi(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var Yi=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,a=n.length,i=a;e>=i;)if((i<<=1)<0)throw Vi(16,"".concat(e));this.groupSizes=new Uint32Array(i),this.groupSizes.set(n),this.length=i;for(var r=a;r<i;r++)this.groupSizes[r]=0}for(var o=this.indexOfGroup(e+1),s=(r=0,t.length);r<s;r++)this.tag.insertRule(o,t[r])&&(this.groupSizes[e]++,o++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),a=n+t;this.groupSizes[e]=0;for(var i=n;i<a;i++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],a=this.indexOfGroup(e),i=a+n,r=a;r<i;r++)t+="".concat(this.tag.getRule(r)).concat(si);return t},e}(),qi=new Map,Xi=new Map,Gi=1,Zi=function(e){if(qi.has(e))return qi.get(e);for(;Xi.has(Gi);)Gi++;var t=Gi++;return qi.set(e,t),Xi.set(t,e),t},Ji=function(e,t){Gi=t+1,qi.set(e,t),Xi.set(t,e)},Ki="style[".concat(ai,"][").concat(ri,'="').concat(oi,'"]'),Qi=new RegExp("^".concat(ai,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),er=function(e,t,n){for(var a,i=n.split(","),r=0,o=i.length;r<o;r++)(a=i[r])&&e.registerName(t,a)},tr=function(e,t){for(var n,a=(null!==(n=t.textContent)&&void 0!==n?n:"").split(si),i=[],r=0,o=a.length;r<o;r++){var s=a[r].trim();if(s){var l=s.match(Qi);if(l){var c=0|parseInt(l[1],10),u=l[2];0!==c&&(Ji(u,c),er(e,u,l[3]),e.getTag().insertRules(c,i)),i.length=0}else i.push(s)}}},nr=function(e){for(var t=document.querySelectorAll(Ki),n=0,a=t.length;n<a;n++){var i=t[n];i&&i.getAttribute(ai)!==ii&&(tr(e,i),i.parentNode&&i.parentNode.removeChild(i))}};var ar=function(e){var t=document.head,n=e||t,a=document.createElement("style"),i=function(e){var t=Array.from(e.querySelectorAll("style[".concat(ai,"]")));return t[t.length-1]}(n),r=void 0!==i?i.nextSibling:null;a.setAttribute(ai,ii),a.setAttribute(ri,oi);var o="undefined"!=typeof __webpack_nonce__?__webpack_nonce__:null;return o&&a.setAttribute("nonce",o),n.insertBefore(a,r),a},ir=function(){function e(e){this.element=ar(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,a=t.length;n<a;n++){var i=t[n];if(i.ownerNode===e)return i}throw Vi(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),rr=function(){function e(e){this.element=ar(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),or=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),sr=li,lr={isServer:!li,useCSSOMInjection:!ci},cr=function(){function e(e,t,n){void 0===e&&(e=di),void 0===t&&(t={});var a=this;this.options=ia(ia({},lr),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&li&&sr&&(sr=!1,nr(this)),Wi(this,(function(){return function(e){for(var t=e.getTag(),n=t.length,a="",i=function(n){var i=function(e){return Xi.get(e)}(n);if(void 0===i)return"continue";var r=e.names.get(i),o=t.getGroup(n);if(void 0===r||!r.size||0===o.length)return"continue";var s="".concat(ai,".g").concat(n,'[id="').concat(i,'"]'),l="";void 0!==r&&r.forEach((function(e){e.length>0&&(l+="".concat(e,","))})),a+="".concat(o).concat(s,'{content:"').concat(l,'"}').concat(si)},r=0;r<n;r++)i(r);return a}(a)}))}return e.registerId=function(e){return Zi(e)},e.prototype.rehydrate=function(){!this.server&&li&&nr(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(ia(ia({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new or(n):t?new ir(n):new rr(n)}(this.options),new Yi(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(Zi(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(Zi(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(Zi(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),ur=/&/g,dr=/^\s*\/\/.*$/gm;function pr(e,t){return e.map((function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map((function(e){return"".concat(t," ").concat(e)}))),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=pr(e.children,t)),e}))}var hr=new cr,mr=function(){var e,t,n,a=di,i=a.options,r=void 0===i?di:i,o=a.plugins,s=void 0===o?ui:o,l=function(n,a,i){return i.startsWith(t)&&i.endsWith(t)&&i.replaceAll(t,"").length>0?".".concat(e):n},c=s.slice();c.push((function(e){e.type===ua&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(ur,t).replace(n,l))})),r.prefix&&c.push(ti),c.push(ei);var u=function(a,i,o,s){void 0===i&&(i=""),void 0===o&&(o=""),void 0===s&&(s="&"),e=s,t=i,n=new RegExp("\\".concat(t,"\\b"),"g");var l=a.replace(dr,""),u=qa(o||i?"".concat(o," ").concat(i," { ").concat(l," }"):l);r.namespace&&(u=pr(u,r.namespace));var d,p=[];return Qa(u,function(e){var t=Ca(e);return function(n,a,i,r){for(var o="",s=0;s<t;s++)o+=e[s](n,a,i,r)||"";return o}}(c.concat((d=function(e){return p.push(e)},function(e){e.root||(e=e.return)&&d(e)})))),p};return u.hash=s.length?s.reduce((function(e,t){return t.name||Vi(15),bi(e,t.name)}),5381).toString():"",u}(),gr=xt.createContext({shouldForwardProp:void 0,styleSheet:hr,stylis:mr});function fr(){return me(gr)}gr.Consumer,xt.createContext(void 0);var vr=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=mr);var a=n.name+t.hash;e.hasNameForId(n.id,a)||e.insertRules(n.id,a,t(n.rules,a,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,Wi(this,(function(){throw Vi(12,String(n.name))}))}return e.prototype.getName=function(e){return void 0===e&&(e=mr),this.name+e.hash},e}(),_r=function(e){return e>="A"&&e<="Z"};function yr(e){for(var t="",n=0;n<e.length;n++){var a=e[n];if(1===n&&"-"===a&&"-"===e[0])return e;_r(a)?t+="-"+a.toLowerCase():t+=a}return t.startsWith("ms-")?"-"+t:t}var br=function(e){return null==e||!1===e||""===e},wr=function(e){var t,n,a=[];for(var i in e){var r=e[i];e.hasOwnProperty(i)&&!br(r)&&(Array.isArray(r)&&r.isCss||Bi(r)?a.push("".concat(yr(i),":"),r,";"):Ui(r)?a.push.apply(a,ra(ra(["".concat(i," {")],wr(r),!1),["}"],!1)):a.push("".concat(yr(i),": ").concat((t=i,null==(n=r)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in ni||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return a};function Er(e,t,n,a){return br(e)?[]:Li(e)?[".".concat(e.styledComponentId)]:Bi(e)?!Bi(i=e)||i.prototype&&i.prototype.isReactComponent||!t?[e]:Er(e(t),t,n,a):e instanceof vr?n?(e.inject(n,a),[e.getName(a)]):[e]:Ui(e)?wr(e):Array.isArray(e)?Array.prototype.concat.apply(ui,e.map((function(e){return Er(e,t,n,a)}))):[e.toString()];var i}var Cr=wi(oi),Sr=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&function(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Bi(n)&&!Li(n))return!1}return!0}(e),this.componentId=t,this.baseHash=bi(Cr,t),this.baseStyle=n,cr.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var a=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))a=Fi(a,this.staticRulesId);else{var i=Hi(Er(this.rules,e,t,n)),r=_i(bi(this.baseHash,i)>>>0);if(!t.hasNameForId(this.componentId,r)){var o=n(i,".".concat(r),void 0,this.componentId);t.insertRules(this.componentId,r,o)}a=Fi(a,r),this.staticRulesId=r}else{for(var s=bi(this.baseHash,n.hash),l="",c=0;c<this.rules.length;c++){var u=this.rules[c];if("string"==typeof u)l+=u;else if(u){var d=Hi(Er(u,e,t,n));s=bi(s,d+c),l+=d}}if(l){var p=_i(s>>>0);t.hasNameForId(this.componentId,p)||t.insertRules(this.componentId,p,n(l,".".concat(p),void 0,this.componentId)),a=Fi(a,p)}}return a},e}(),xr=xt.createContext(void 0);xr.Consumer;var Dr={};new Set;function Pr(e,t,n){var a=Li(e),i=e,r=!Ei(e),o=t.attrs,s=void 0===o?ui:o,l=t.componentId,c=void 0===l?function(e,t){var n="string"!=typeof e?"sc":gi(e);Dr[n]=(Dr[n]||0)+1;var a="".concat(n,"-").concat(function(e){return _i(wi(e)>>>0)}(oi+n+Dr[n]));return t?"".concat(t,"-").concat(a):a}(t.displayName,t.parentComponentId):l,u=t.displayName,d=void 0===u?function(e){return Ei(e)?"styled.".concat(e):"Styled(".concat(function(e){return e.displayName||e.name||"Component"}(e),")")}(e):u,p=t.displayName&&t.componentId?"".concat(gi(t.displayName),"-").concat(t.componentId):t.componentId||c,h=a&&i.attrs?i.attrs.concat(s).filter(Boolean):s,m=t.shouldForwardProp;if(a&&i.shouldForwardProp){var g=i.shouldForwardProp;if(t.shouldForwardProp){var f=t.shouldForwardProp;m=function(e,t){return g(e,t)&&f(e,t)}}else m=g}var v=new Sr(n,p,a?i.componentStyle:void 0);function _(e,t){return function(e,t,n){var a=e.attrs,i=e.componentStyle,r=e.defaultProps,o=e.foldedComponentIds,s=e.styledComponentId,l=e.target,c=xt.useContext(xr),u=fr(),d=e.shouldForwardProp||u.shouldForwardProp,p=function(e,t,n){return void 0===n&&(n=di),e.theme!==n.theme&&e.theme||t||n.theme}(t,c,r)||di,h=function(e,t,n){for(var a,i=ia(ia({},t),{className:void 0,theme:n}),r=0;r<e.length;r+=1){var o=Bi(a=e[r])?a(i):a;for(var s in o)i[s]="className"===s?Fi(i[s],o[s]):"style"===s?ia(ia({},i[s]),o[s]):o[s]}return t.className&&(i.className=Fi(i.className,t.className)),i}(a,t,p),m=h.as||l,g={};for(var f in h)void 0===h[f]||"$"===f[0]||"as"===f||"theme"===f&&h.theme===p||("forwardedAs"===f?g.as=h.forwardedAs:d&&!d(f,m)||(g[f]=h[f]));var v=function(e,t){var n=fr();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(i,h),_=Fi(o,s);return v&&(_+=" "+v),h.className&&(_+=" "+h.className),g[Ei(m)&&!pi.has(m)?"class":"className"]=_,g.ref=n,b(m,g)}(y,e,t)}_.displayName=d;var y=xt.forwardRef(_);return y.attrs=h,y.componentStyle=v,y.displayName=d,y.shouldForwardProp=m,y.foldedComponentIds=a?Fi(i.foldedComponentIds,i.styledComponentId):"",y.styledComponentId=p,y.target=a?i.target:e,Object.defineProperty(y,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=a?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var a=0,i=t;a<i.length;a++)ji(e,i[a],!0);return e}({},i.defaultProps,e):e}}),Wi(y,(function(){return".".concat(y.styledComponentId)})),r&&Ai(y,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),y}function kr(e,t){for(var n=[e[0]],a=0,i=t.length;a<i;a+=1)n.push(t[a],e[a+1]);return n}var Tr=function(e){return Object.assign(e,{isCss:!0})};function Ir(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(Bi(e)||Ui(e))return Tr(Er(kr(ui,ra([e],t,!0))));var a=e;return 0===t.length&&1===a.length&&"string"==typeof a[0]?Er(a):Tr(Er(kr(a,t)))}function Or(e,t,n){if(void 0===n&&(n=di),!t)throw Vi(1,t);var a=function(a){for(var i=[],r=1;r<arguments.length;r++)i[r-1]=arguments[r];return e(t,n,Ir.apply(void 0,ra([a],i,!1)))};return a.attrs=function(a){return Or(e,t,ia(ia({},n),{attrs:Array.prototype.concat(n.attrs,a).filter(Boolean)}))},a.withConfig=function(a){return Or(e,t,ia(ia({},n),a))},a}var Nr=function(e){return Or(Pr,e)},Mr=Nr;pi.forEach((function(e){Mr[e]=Nr(e)}));const zr=Mr.div`
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;

    &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 1px;
        background-color: var(--color-base-30);
        margin-top: 20px;
    }
`,Rr=Mr.button`
    &:disabled {
        background-color: var(--color-base-50);
        cursor: not-allowed;
    }
`,$r=/^[\w-]+$/,Ar=/^[.#][\w\s._>+~-]+$/;function Br(e,t,n,a){const i=function(e,t,n){const a=t.value,i=n.value;return!(!$r.test(a)||!Ar.test(i))||(e.showNotice("Input is not valid!"),t.addClass("snake"),n.addClass("snake"),setTimeout((()=>{t.removeClass("shake"),n.removeClass("shake")}),500),!1)}(e,t,n),r=function(e,t,n,a){const i=t.value,r=n.value,o=a.find((e=>e.name===i||e.selector===r));return!o||(t.addClass("shake"),n.addClass("shake"),setTimeout((()=>{t.removeClass("shake"),n.removeClass("shake")}),500),e.showNotice("Diagram already exists!"),!1)}(e,t,n,a);return i&&r}function Lr(e,t){const n=t.value,a=$r.test(n);n?(t.toggleClass("invalid",!a),t.ariaLabel=a?"":"Incorrect input. Should be only `A-Za-z0-9-`"):(t.removeClass("invalid"),t.ariaLabel="")}function Fr(e,t){const n=t.value,a=Ar.test(n);n?(t.toggleClass("invalid",!a),t.ariaLabel=a?"":"Input incorrect. It seems to be not a valid CSS selector?"):(t.removeClass("invalid"),t.ariaLabel="")}class Hr extends t.Modal{onSubmit;constructor(e,t,n){super(e),this.onSubmit=n,this.setTitle(`Editing ${t}...`)}onOpen(){new t.Setting(this.contentEl).setName("Are you sure you want to switch the page? You will lose your unsaved changes.").setHeading().addButton((e=>{e.setButtonText("Proceed without saving"),e.onClick((e=>{this.onSubmit("Yes"),this.close()}))})).addButton((e=>{e.setButtonText("Cancel"),e.onClick((e=>{this.onSubmit("No"),this.close()}))})).addButton((e=>{e.setButtonText("Save and continue"),e.onClick((e=>{this.onSubmit("Save"),this.close()}))}))}onClose(){this.contentEl.empty()}}class Ur extends t.Modal{name;initial;callback;constructor(e,t,n,a){super(e),this.name=t,this.initial=n,this.callback=a,this.setTitle(`Set diagram controls for ${this.name} diagram`)}onOpen(){const{contentEl:e}=this;new t.Setting(e).setDesc("These settings will only apply to this diagram."),new t.Setting(e).setName("Move panel").addToggle((e=>{e.setValue(this.initial.move.on),e.onChange((async e=>{await this.callback({on:e,panel:"move"})}))})),new t.Setting(e).setName("Zoom panel").addToggle((e=>{e.setValue(this.initial.zoom.on),e.onChange((async e=>{await this.callback({on:e,panel:"zoom"})}))})),new t.Setting(e).setName("Service panel").addToggle((e=>{e.setValue(this.initial.service.on),e.onChange((async e=>{await this.callback({on:e,panel:"service"})}))}))}hide(){this.contentEl.empty()}}const jr=()=>{const{app:e,plugin:t}=It(),[n,a]=oe(void 0),[i,r]=oe(t.settings.supported_diagrams),[o,s]=oe(t.settings.diagramsPerPage);le((()=>(t.observer.subscribe(e.workspace,aa.ItemsPerPageChanged,(async()=>{s(t.settings.diagramsPerPage)})),()=>{t.observer.unsubscribeFromEvent(e.workspace,aa.ItemsPerPageChanged)})),[e.workspace,t.settings]);const[l,c]=oe(1),u=Math.ceil(i.length/o),d=(l-1)*o,p=d+o,h=async e=>{const n=document.querySelector("#editing-name-input"),o=document.querySelector("#editing-selector-input");if(!n||!o)return;const s=Br(t,n,o,i.slice(0,e).concat(i.slice(e+1)));return s&&(i[e].name=n.value,i[e].selector=o.value,r([...i]),t.settings.supported_diagrams=i,await t.settingsManager.saveSettings(),n.removeAttribute("id"),o.removeAttribute("id"),a(void 0)),s},m=e=>d+e,g=e=>{c((t=>Math.min(u,Math.max(t+e,1))))},f=r=>{void 0!==n?new Hr(e,i[m(n)].name,(async e=>{if("Yes"===e)a(void 0),g(r);else if("Save"===e){await h(m(n))||t.showNotice("Could not save diagram"),g(r)}})).open():g(r)};return xt.createElement(xt.Fragment,null,xt.createElement(zr,null,xt.createElement(Rr,{onClick:()=>f(-1),disabled:1===l},"←"),`Page ${l} of ${u} (Total diagrams: ${i.length})`,xt.createElement(Rr,{onClick:()=>f(1),disabled:l===u},"→")),t.settings.supported_diagrams.slice(d,p).map(((o,s)=>{const{name:u,selector:p}=o;return n===s?xt.createElement(Zn,{addTexts:[e=>(e.setValue(i[m(s)].name),e.inputEl.id="editing-name-input",e.onChange((t=>{Lr(0,e.inputEl)})),e),e=>(e.setValue(i[m(s)].selector),e.inputEl.id="editing-selector-input",e.onChange((t=>{Fr(0,e.inputEl)})),e)],addButtons:[e=>(e.setIcon("circle-x"),e.setTooltip("Cancel operation? All changes will be lost."),e.onClick((e=>{a(void 0)})),e),e=>(e.setIcon("save"),e.setTooltip(`Save changes for ${i[m(s)].name}?`),e.onClick((async e=>{await h(m(s))})),e)]}):xt.createElement(Zn,{name:u,desc:p,addToggles:[e=>(e.setValue(i[m(s)].on),e.setTooltip(`${i[m(s)].on?"Disable":"Enable"} ${i[m(s)].name} diagram`),e.onChange((async e=>{i[m(s)].on=e,r([...i]),t.settings.supported_diagrams=i,await t.settingsManager.saveSettings()})),e)],addButtons:["Default"!==i[m(s)].name&&(e=>(e.setIcon("edit"),e.setTooltip(`Edit ${i[m(s)].name} diagram`),e.onClick((async()=>{a(s)})),e)),"Default"!==i[m(s)].name&&(e=>(e.setIcon("trash"),e.setTooltip(`Delete ${i[m(s)].name} diagram`),e.onClick((async()=>{await(async e=>{const n=[...i];n.splice(e,1),r(n),t.settings.supported_diagrams=n,await t.settingsManager.saveSettings(),l>1&&d>=n.length&&c((e=>e-1))})(m(s))})),e))],addExtraButtons:[n=>(n.setTooltip(`Set what controls will be active for ${i[m(s)].name} diagram`),n.onClick((()=>{const n=i[m(s)].panels;new Ur(e,i[m(s)].name,n,(async e=>{i[m(s)].panels[e.panel].on=e.on,await t.settingsManager.saveSettings()})).open()})),n)]})})))},Wr=()=>{const{app:e,plugin:n}=It(),[a,i]=se((e=>e+1),0);return xt.createElement(xt.Fragment,null,xt.createElement(Zn,{name:"Add new diagram",setHeading:!0,noBorder:!0,desc:"Here you can configure which diagrams will receive enhanced controls and UI.",addMultiDesc:e=>(e.addDescriptions(["Adding a Diagram Type:","1. Enter a unique name using only Latin letters, numbers and `-` (A-Z, a-z, 0-9, -)","2. Specify a valid CSS selector for your diagram","Once added, matching diagrams will get:","• Mouse and keyboard navigation","• Additional control buttons","Note: Red border indicates invalid input - hover to see details"]),e)}),xt.createElement(Zn,{addTexts:[e=>(e.inputEl.id="diagram-name",e.setPlaceholder("Example Diagram"),e.onChange((t=>{e.setValue(t),Lr(0,e.inputEl)})),e),e=>(e.inputEl.id="diagram-selector",e.setPlaceholder(".example-diagram"),e.onChange((t=>{e.setValue(t),Fr(0,e.inputEl)})),e)],addButtons:[e=>(e.setIcon("save"),e.setTooltip("Add this diagram"),e.onClick((async()=>{const e=document.querySelector("#diagram-name"),t=document.querySelector("#diagram-selector");e&&t&&await(async(e,t)=>{Br(n,e,t,n.settings.supported_diagrams)&&(n.settings.supported_diagrams.push({name:e.value,selector:t.value,on:!0,panels:{move:{on:!0},zoom:{on:!0},service:{on:!0}}}),await n.settingsManager.saveSettings(),n.showNotice("New diagram was added"),i())})(e,t)})),e)],addExtraButtons:[t.Platform.isDesktopApp&&(t=>(t.setIcon("info"),t.setTooltip("Click for more information on how the plugin works and how you can find diagram selectors"),t.onClick((()=>{new na(e,n).open()})),t))]}),xt.createElement(Zn,{name:"Available diagrams",setHeading:!0}),xt.createElement(Zn,{name:"Diagrams per page",addSliders:[t=>(t.setValue(n.settings.diagramsPerPage),t.setDynamicTooltip(),t.setLimits(1,50,1),t.onChange((async t=>{n.settings.diagramsPerPage=t,await n.settingsManager.saveSettings(),n.publisher.publish({emitter:e.workspace,eventID:aa.ItemsPerPageChanged,timestamp:new Date})})),t)]}),xt.createElement(jr,null))},Vr=()=>{const e=wn(),t=yn();return xt.createElement("div",null,xt.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",borderBottom:"1px solid var(--color-base-30)",marginTop:"20px"}},xt.createElement(Zn,{addButtons:[n=>(n.setIcon("settings"),n.setTooltip("Settings"),n.onClick((()=>{e("/diagram-section/settings")})),"/diagram-section"!==t.pathname&&"/diagram-section/settings"!==t.pathname||n.setClass("button-active"),n),n=>(n.setIcon("folder-plus"),n.setTooltip("Diagram Management"),n.onClick((()=>{e("/diagram-section/management")})),"/diagram-section/management"===t.pathname&&n.setClass("button-active"),n)]})),xt.createElement(Rn,null,xt.createElement(Mn,{index:!0,element:xt.createElement(ea,null)}),xt.createElement(Mn,{path:"settings",element:xt.createElement(ea,null)}),xt.createElement(Mn,{path:"management",element:xt.createElement(Wr,null)})))},Yr=Mr.div`
    display: flex;
    flex-direction: column;
    gap: 20px;
`,qr=Mr.div`
    position: relative;
    width: 400px;
    height: 300px;
    border: 2px solid var(--color-base-30);
    margin: 0 auto;
`,Xr=Mr.div`
    position: absolute;
    width: 60px;
    height: 40px; 
    padding: 8px;
    background: var(--color-base-20);
    border-radius: 4px;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    cursor: move;
    opacity: ${({dragging:e})=>e?.5:1};
    transition: ${({dragging:e})=>e?"all 0.3s ease":"none"}}
`,Gr=Mr(Xr)`
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: none;
    text-align: justify;
`,Zr=Mr.div`
    display: flex;
    justify-content: center;
    gap: 20px;
`,Jr=Mr.label`
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9em;
`,Kr=()=>{const{plugin:e}=It(),[n,a]=oe(e.settings.panelsConfig),[i,r]=oe(null),o=ue(null);le((()=>{a(e.settings.panelsConfig)}),[e.settings]);const s=async t=>{a(t),e.settings.panelsConfig=t,await e.settingsManager.saveSettings()},l=(e,t,n)=>{const a={},i=30,r=e,o=e+60,s=t,l=t+40,c=[{edge:"left",value:r},{edge:"right",value:n.width-o},{edge:"top",value:s},{edge:"bottom",value:n.height-l}].reduce(((e,t)=>Math.abs(e.value)<Math.abs(t.value)?e:t));if(Math.abs(c.value)<=i)switch(c.edge){case"left":a.left="0px",s<=i?a.top="0px":n.height-l<=i?a.bottom="0px":a.top=`${(s/n.height*100).toFixed(1)}%`;break;case"right":a.right="0px",s<=i?a.top="0px":n.height-l<=i?a.bottom="0px":a.top=`${(s/n.height*100).toFixed(1)}%`;break;case"top":a.top="0px",r<=i?a.left="0px":n.width-o<=i?a.right="0px":a.left=`${(r/n.width*100).toFixed(1)}%`;break;case"bottom":a.bottom="0px",r<=i?a.left="0px":n.width-o<=i?a.right="0px":a.left=`${(r/n.width*100).toFixed(1)}%`}else a.left=`${(r/n.width*100).toFixed(1)}%`,a.top=`${(s/n.height*100).toFixed(1)}%`;return a},c=e=>{const t=o.current;if(!t||!i)return;e.preventDefault();const n=e.touches[0],a=e.currentTarget,r=JSON.parse(a.dataset.dragData??"{}"),s=t.getBoundingClientRect(),c=n.clientX-s.left-r.offsetX,u=n.clientY-s.top-r.offsetY,d=l(c,u,s);a.style.left=d.left,a.style.top=d.top},u=async e=>{const t=o.current;if(!t||!i)return;const a=e.currentTarget,c=JSON.parse(a.dataset.dragData??"{}"),u=e.changedTouches[0],d=t.getBoundingClientRect(),p=u.clientX-d.left-c.offsetX,h=u.clientY-d.top-c.offsetY,m=l(p,h,d),g={...n};g[c.panelName]={...n[c.panelName],position:m},await s(g),r(null)},d=e=>t.Platform.isDesktop?{draggable:!0,onDragStart:t=>((e,t)=>{const n=e.currentTarget.getBoundingClientRect(),a=e.clientX-n.left,i=e.clientY-n.top;e.dataTransfer.setData("application/json",JSON.stringify({panelName:t,offsetX:a,offsetY:i})),r(t)})(t,e)}:{onTouchStart:t=>((e,t)=>{const n=e.touches[0],a=e.target,i=a.getBoundingClientRect(),o=n.clientX-i.left,s=n.clientY-i.top;r(t),a.dataset.dragData=JSON.stringify({panelName:t,offsetX:o,offsetY:s})})(t,e),onTouchMove:c,onTouchEnd:u};return xt.createElement(Yr,null,xt.createElement(qr,{ref:o,onDragOver:e=>e.preventDefault(),onDrop:async e=>{e.preventDefault();const t=o.current;if(!t)return;const a=t.getBoundingClientRect(),i=JSON.parse(e.dataTransfer.getData("application/json")),c=e.clientX-a.left-i.offsetX,u=e.clientY-a.top-i.offsetY,d=l(c,u,a),p={...n};p[i.panelName]={...n[i.panelName],position:d},await s(p),r(null)}},Object.entries(n).map((([e,t])=>t.enabled&&xt.createElement(Xr,{key:e,dragging:i===e,style:{...t.position},...d(e)},e))),xt.createElement(Gr,null,"fold")),xt.createElement(Zr,null,Object.entries(n).map((([e,t])=>xt.createElement(Jr,{key:e},xt.createElement("input",{type:"checkbox",checked:t.enabled,onChange:()=>(async e=>{const t={...n,[e]:{...n[e],enabled:!n[e].enabled}};await s(t)})(e)}),e)))))},Qr=()=>xt.createElement(xt.Fragment,null,xt.createElement(Zn,{name:"Panel configuration",desc:"Configure the visibility and position of control panels on your diagrams",setHeading:!0,noBorder:!0}),xt.createElement(Zn,{name:"Available panels",addMultiDesc:e=>(e.addDesc("• Move Panel: By default located at bottom right - Contains 8 directional buttons for diagram movement"),e.addDesc("• Zoom Panel: By default located at center right - Features zoom in/out and reset controls"),e.addDesc("• Service Panel: By default located at upper right - Contains additional functionality buttons"),e),noBorder:!0}),xt.createElement(Zn,{name:"How to customize panels",addMultiDesc:e=>(e.addDesc("1. Use checkboxes below to toggle panel visibility on/off"),e.addDesc("2. Click and drag any panel to reposition it on the diagram"),e.addDesc("3. Panel positions are saved automatically"),e.addDesc("4. Reload the view to see your changes take effect"),e),noBorder:!0}),xt.createElement(Kr,null)),eo=()=>{const{plugin:e}=It();return xt.createElement(xt.Fragment,null,t.Platform.isDesktopApp&&xt.createElement(xt.Fragment,null,xt.createElement(Zn,{name:"Panels behavior",desc:"Configure how panels interact with mouse movement",setHeading:!0}),xt.createElement(Zn,{name:"Hide panels when mouse leaves diagram?",addToggles:[t=>(t.setValue(e.settings.hideOnMouseOutDiagram),t.onChange((async t=>{e.settings.hideOnMouseOutDiagram=t,await e.settingsManager.saveSettings()})),t)]}),xt.createElement(Zn,{name:"Serivce panel",setHeading:!0}),xt.createElement(Zn,{name:"Add a hiding button to service panel?",addToggles:[t=>(t.setValue(e.settings.addHidingButton),t.onChange((async t=>{e.settings.addHidingButton=t,await e.settingsManager.saveSettings()})),t)]})))},to=()=>{const e=wn(),n=yn();return xt.createElement("div",null,xt.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",borderBottom:"1px solid var(--color-base-30)",marginTop:"20px"}},t.Platform.isDesktopApp&&xt.createElement(Zn,{addButtons:[t=>(t.setIcon("settings"),t.setTooltip("Panels Settings"),t.onClick((()=>{e("/panel-section/settings")})),"/panel-section/settings"!==n.pathname&&"/panel-section"!==n.pathname||t.setClass("button-active"),t),t=>(t.setIcon("layout-grid"),t.setTooltip("Panels Management"),t.onClick((()=>{e("/panel-section/management")})),"/panel-section/management"===n.pathname&&t.setClass("button-active"),t)]})),xt.createElement(Rn,null,xt.createElement(Mn,{index:!0,element:t.Platform.isDesktopApp?xt.createElement(eo,null):xt.createElement(Qr,null)}),xt.createElement(Mn,{path:"settings",element:xt.createElement(eo,null)}),xt.createElement(Mn,{path:"management",element:xt.createElement(Qr,null)})))},no=Mr.nav`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
    background-color: var(--background-primary);
    color: var(--text-normal);
    border-bottom: 2px solid var(--background-modifier-border);
`,ao=Mr.div`
    display: flex;
    gap: 16px;
`,io=Mr.button`
    display: flex;
    align-items: center;
    background: none;
    border: none;
    text-decoration: none;
    color: var(--text-normal);
    font-size: 16px;
    padding: 8px 12px;
    gap: 10px;
    border-radius: 4px;
    transition:
        background-color 0.3s,
        color 0.3s;
    cursor: pointer;
    position: relative;

    &:hover {
        background-color: var(--background-modifier-hover);
        color: var(--text-accent-hover);
    }

    &.active {
        background-color: var(--background-modifier-active-hover);
        color: var(--text-accent);
    }

    &.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 3px;
        background-color: var(--text-accent);
        border-radius: 2px 2px 0 0;
    }
`,ro=()=>xt.createElement(no,null,xt.createElement(ao,null,xt.createElement(io,{as:Vn,to:"/diagram-section"},"Diagram"),xt.createElement(io,{as:Vn,to:"/panel-section"},"Panel"),xt.createElement(io,{as:Vn,to:"/about"},"About"))),oo=()=>{const{plugin:e,forceReload:n,setCurrentPath:a}=It(),i=yn();return xt.createElement(Zn,{addButtons:[r=>(r.setIcon("rotate-ccw"),r.setTooltip("Reset settings to default"),r.onClick((async()=>{a(i.pathname),await e.settingsManager.resetSettings(),n(),e.updateCssProperties(),new t.Notice("Settings have been reset to default.")})),r)]})},so=()=>t.Platform.isDesktopApp?xt.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr auto 1fr",alignItems:"center",width:"100%"}},xt.createElement("div",null),xt.createElement(ro,null),xt.createElement("div",{style:{justifySelf:"end",display:"flex",alignItems:"center",marginTop:"35px"}},xt.createElement(oo,null))):xt.createElement(xt.Fragment,null,xt.createElement("div",{style:{display:"flex",justifyContent:"flex-end",marginTop:"-50px",marginRight:"0px",padding:0,width:"100%",marginBottom:0}},xt.createElement(oo,null)),xt.createElement(ro,null)),lo=()=>{const{reloadCount:e,currentPath:t}=It();return xt.createElement(Nn,{initialEntries:[t],key:e},xt.createElement(so,null),xt.createElement(Rn,null,xt.createElement(Mn,{path:"/diagram-section/*",element:xt.createElement(Vr,null)}),xt.createElement(Mn,{path:"/panel-section/*",element:xt.createElement(to,null)}),xt.createElement(Mn,{path:"/about",element:xt.createElement(Jn,null)})))},co=({app:e,plugin:t})=>xt.createElement(Tt,{app:e,plugin:t},xt.createElement(lo,null));class uo extends t.PluginSettingTab{app;plugin;root=void 0;constructor(e,t){super(e,t),this.app=e,this.plugin=t,this.containerEl.addClass("diagram-zoom-drag-settings")}async display(){const e=this.containerEl.createDiv();this.root=Pt(e),this.root.render(xt.createElement(co,{app:this.app,plugin:this.plugin}))}hide(){this.root?.unmount(),this.containerEl.empty()}}class po{plugin;constructor(e){this.plugin=e}async isFirstPluginStart(){const e=await this.getPluginMetadata(),t=localStorage.getItem("diagram-zoom-drag-metadata");if(!t)return localStorage.setItem("diagram-zoom-drag-metadata",e.toString()),!0;const n=parseInt(t,10);return!(!isNaN(n)&&e===n)&&(localStorage.setItem("diagram-zoom-drag-metadata",e.toString()),!0)}async getPluginMetadata(){const{dir:e}=this.plugin.manifest;if(!e)throw new Error("No plugin dir found.");const t=await this.plugin.app.vault.adapter.stat(e);return t?.ctime??0}}class ho{plugin;constructor(e){this.plugin=e}}class mo extends ho{constructor(e){super(e)}publish(e){e.emitter.trigger(e.eventID,e)}}class go{plugin;constructor(e){this.plugin=e}}class fo extends go{subscriptions=new Map;constructor(e){super(e)}subscribe(e,t,n){const a=e.on(t,(async(...e)=>{const t=e[0];await n(t)}));this.subscriptions.has(e)||this.subscriptions.set(e,new Map);const i=this.subscriptions.get(e);i.has(t)||i.set(t,[]),i.get(t).push((()=>e.offref(a)))}subscribeForView(e,t,n,a){this.subscribe(t,n,a),e.register((()=>this.unsubscribeFromEvent(t,n)))}unsubscribeAll(){this.subscriptions.forEach(((e,t)=>{e.forEach((e=>{e.forEach((e=>e()))}))})),this.subscriptions.clear()}unsubscribeFromEmitter(e){const t=this.subscriptions.get(e);t&&(t.forEach((e=>{e.forEach((e=>e()))})),this.subscriptions.delete(e))}unsubscribeFromEvent(e,t){const n=this.subscriptions.get(e);if(n){const a=n.get(t);a&&(a.forEach((e=>e())),n.delete(t)),0===n.size&&this.subscriptions.delete(e)}}}function vo(e,n,a){n&&t.setIcon(e,n),a&&(e.ariaLabel=a)}class _o{diagram;data=new Map;constructor(e){this.diagram=e,Object.defineProperties(this.diagram,{dx:{get:()=>this.dx,set:e=>{this.dx=e}},dy:{get:()=>this.dy,set:e=>{this.dy=e}},scale:{get:()=>this.scale,set:e=>{this.scale=e}},nativeTouchEventsEnabled:{get:()=>this.nativeTouchEventsEnabled,set:e=>{this.nativeTouchEventsEnabled=e}},source:{get:()=>this.source,set:e=>{this.source=e}},panelsData:{get:()=>this.panelsData,set:e=>{this.panelsData=e}},livePreviewObserver:{get:()=>this.livePreviewObserver,set:e=>{this.livePreviewObserver=e}}})}initializeLeafData(e){this.data.get(e)||this.data.set(e,{containers:{}})}initializeContainer(e,t){const n=this.diagram.plugin.leafID,a=this.data.get(n);a&&(a.containers[e]={dx:0,dy:0,scale:1,nativeTouchEventsEnabled:!0,panelsData:{},source:t})}initializeContainerPanels(e,t,n,a,i){this.panelsData={panels:{move:t,fold:n,zoom:a,service:i},controlPanel:e}}async cleanupContainers(){const e=this.data.get(this.diagram.plugin.leafID);if(!e)return;const t=this.diagram.plugin.view?.file?.stat.ctime,n=Object.keys(e);for(const a of n){t!==parseInt(a.split("-")[1],10)&&delete e.containers[a]}}cleanupData(e){const t=this.data.get(e);t?.livePreviewObserver?.disconnect(),this.data.delete(e)}getData(e){const t=this.diagram.activeContainer;if(!t)return;const n=this.diagram.plugin.leafID;if(!n)return;const a=this.data.get(n);return a?.containers[t.id]?a?.containers[t.id][e]:void 0}setData(e,t){const n=this.diagram.activeContainer;if(!n)return;const a=this.diagram.plugin.leafID;if(!a)return;const i=this.data.get(a);i&&i.containers[n.id]&&(i.containers[n.id][e]=t)}get dx(){return this.getData("dx")??0}set dx(e){this.setData("dx",e)}get dy(){return this.getData("dy")??0}set dy(e){this.setData("dy",e)}get scale(){return this.getData("scale")??1}set scale(e){this.setData("scale",e)}get nativeTouchEventsEnabled(){return this.getData("nativeTouchEventsEnabled")??!0}set nativeTouchEventsEnabled(e){this.setData("nativeTouchEventsEnabled",e)}get source(){return this.getData("source")??"No source available"}set source(e){this.setData("source",e)}get panelsData(){return this.getData("panelsData")??{}}set panelsData(e){this.setData("panelsData",e)}get livePreviewObserver(){const e=this.data.get(this.diagram.plugin.leafID);return e?.livePreviewObserver}set livePreviewObserver(e){const t=this.data.get(this.diagram.plugin.leafID);t&&(t.livePreviewObserver=e)}}class yo{diagram;diagramControlPanel;panel;constructor(e,t){this.diagram=e,this.diagramControlPanel=t}initialize(){this.panel=this.createPanel()}getButtons(e){return[{icon:"arrow-up-left",action:()=>this.diagram.actions.moveElement(e,50,50,!0),title:"Move up left"},{icon:"arrow-up",action:()=>this.diagram.actions.moveElement(e,0,50,!0),title:"Move up"},{icon:"arrow-up-right",action:()=>this.diagram.actions.moveElement(e,-50,50,!0),title:"Move up right"},{icon:"arrow-left",action:()=>this.diagram.actions.moveElement(e,50,0,!0),title:"Move left"},{icon:"",action:()=>{},title:"",active:!1,id:""},{icon:"arrow-right",action:()=>this.diagram.actions.moveElement(e,-50,0,!0),title:"Move right"},{icon:"arrow-down-left",action:()=>this.diagram.actions.moveElement(e,50,-50,!0),title:"Move down left"},{icon:"arrow-down",action:()=>this.diagram.actions.moveElement(e,0,-50,!0),title:"Move down"},{icon:"arrow-down-right",action:()=>this.diagram.actions.moveElement(e,-50,-50,!0),title:"Move down right"}]}createPanel(){const e=this.diagramControlPanel.createPanel("diagram-move-panel",{...this.diagram.plugin.settings.panelsConfig.move.position,gridTemplateColumns:"repeat(3, 1fr)",gridTemplateRows:"repeat(3, 1fr)"});return this.getButtons(this.diagram.activeContainer).forEach((t=>e.appendChild(this.diagramControlPanel.createButton(t.icon,t.action,t.title,t.active,t.id)))),e}}class bo{diagram;diagramControlPanel;panel;constructor(e,t){this.diagram=e,this.diagramControlPanel=t}initialize(){this.panel=this.createPanel()}getButtons(e){return[{icon:"zoom-in",action:()=>this.diagram.actions.zoomElement(e,1.1,!0),title:"Zoom In"},{icon:"refresh-cw",action:()=>this.diagram.actions.resetZoomAndMove(e,!0),title:"Reset Zoom and Position"},{icon:"zoom-out",action:()=>this.diagram.actions.zoomElement(e,.9,!0),title:"Zoom Out"}]}createPanel(){const e=this.diagramControlPanel.createPanel("diagram-zoom-panel",{...this.diagram.plugin.settings.panelsConfig.zoom.position,transform:"translateY(-50%)",gridTemplateColumns:"1fr"});return this.getButtons(this.diagram.activeContainer).forEach((t=>e.appendChild(this.diagramControlPanel.createButton(t.icon,t.action,t.title,!0)))),e}}class wo{diagram;diagramControlPanel;panel;constructor(e,t){this.diagram=e,this.diagramControlPanel=t}initialize(){this.panel=this.createPanel()}getButtons(e){const t=this.diagram.activeContainer.hasClass("folded");return[{icon:t?"unfold-vertical":"fold-vertical",action:()=>{e.classList.toggle("folded"),this.diagram.plugin.livePreview&&e.parentElement?.classList.toggle("folded")},title:t?"Expand diagram":"Fold diagram",id:"diagram-fold-button"}]}createPanel(){const e=this.diagramControlPanel.createPanel("diagram-fold-panel",{position:"absolute",left:"50%",bottom:"0",transform:"translateX(-50%)",gridTemplateColumns:"1fr"});return this.getButtons(this.diagram.activeContainer).forEach((t=>{const n=this.diagramControlPanel.createButton(t.icon,t.action,t.title,!0,t.id);e.appendChild(n)})),e}}class Eo{diagram;diagramControlPanel;panel;hiding=!1;constructor(e,t){this.diagram=e,this.diagramControlPanel=t}initialize(){this.panel=this.createPanel(),this.setupEventListeners()}getButtons(e){const n=[];return this.diagram.plugin.settings.addHidingButton&&n.push({icon:this.hiding?"eye-off":"eye",action:()=>{const e=this.diagram.state.panelsData;if(!e?.panels)return;this.hiding=!this.hiding,[e.panels.move,e.panels.zoom].forEach((e=>{e.panel&&(e.panel.toggleClass("hidden",this.hiding),e.panel.toggleClass("visible",!this.hiding))}));const t=this.panel.querySelector("#hide-show-button-diagram");t&&vo(t,this.hiding?"eye-off":"eye",(this.hiding?"Show":"Hide")+" move and zoom panels")},title:"Hide move and zoom panels",id:"hide-show-button-diagram"}),n.push({icon:"maximize",action:async()=>{const t=e.querySelector("#fullscreen-button");t&&(document.fullscreenElement?(e.removeClass("is-fullscreen"),await document.exitFullscreen(),vo(t,"maximize","Exit fullscreen mode")):(e.addClass("is-fullscreen"),await e.requestFullscreen({navigationUI:"auto"}),vo(t,"minimize","Open in fullscreen mode")))},title:"Open in fullscreen mode",id:"fullscreen-button"}),t.Platform.isMobileApp&&n.push({icon:this.diagram.nativeTouchEventsEnabled?"circle-slash-2":"hand",action:()=>{this.diagram.nativeTouchEventsEnabled=!this.diagram.nativeTouchEventsEnabled;const e=this.panel.querySelector("#native-touch-event");if(!e)return;const t=this.diagram.nativeTouchEventsEnabled;vo(e,this.diagram.nativeTouchEventsEnabled?"circle-slash-2":"hand",(t?"Enable":"Disable")+" move and pinch zoom"),this.diagram.plugin.showNotice(`Native touches are ${t?"enabled":"disabled"} now. \n            You ${t?"cannot":"can"} move and pinch zoom diagram diagram.`)},title:(this.diagram.nativeTouchEventsEnabled?"Enable":"Disable")+" move and pinch zoom",id:"native-touch-event"}),n}createPanel(){const e=this.diagramControlPanel.createPanel("diagram-service-panel",{...this.diagram.plugin.settings.panelsConfig.service.position,gridTemplateColumns:"repeat(auto-fit, minmax(24px, 1fr))",gridAutoFlow:"column"});return this.getButtons(this.diagram.activeContainer).forEach((t=>e.appendChild(this.diagramControlPanel.createButton(t.icon,t.action,t.title,!0,t.id)))),e}setupEventListeners(){const e=this.panel.querySelector("#fullscreen-button"),n=this.diagram.activeContainer;if(!e)return;this.diagram.plugin.view?.registerDomEvent(n,"fullscreenchange",this.onFullScreenChange.bind(this,n,e));const a=this.panel.querySelector("#hide-show-button-diagram");this.diagram.plugin.observer.subscribe(this.diagram.plugin.app.workspace,aa.PanelsChangedVisibility,(async e=>{const n=e.data.visible;a&&(this.hiding=!n,vo(a,this.hiding?"eye-off":"eye",(this.hiding?"Show":"Hide")+" move and zoom panels"),t.setIcon(a,this.hiding?"eye-off":"eye"))}))}onFullScreenChange(e,t){document.fullscreenElement?(requestAnimationFrame((()=>{this.diagram.actions.resetZoomAndMove(e)})),vo(t,"minimize","Exit fullscreen mode")):(requestAnimationFrame((()=>{this.diagram.actions.resetZoomAndMove(e)})),vo(t,"maximize","Open in fullscreen mode"))}}class Co{diagram;constructor(e){this.diagram=e}initialize(e,t){this.diagram.activeContainer=e;const n=e.createDiv();n.addClass("diagram-zoom-drag-control-panel");const a=new yo(this.diagram,this),i=new bo(this.diagram,this),r=new wo(this.diagram,this),o=new Eo(this.diagram,this);this.diagram.state.initializeContainerPanels(n,a,r,i,o),r.initialize(),this.diagram.plugin.settings.panelsConfig.move.enabled&&t.panels.move.on&&a.initialize(),this.diagram.plugin.settings.panelsConfig.zoom.enabled&&t.panels.zoom.on&&i.initialize(),this.diagram.plugin.settings.panelsConfig.service.enabled&&t.panels.service.on&&o.initialize(),(this.diagram.plugin.settings.hideOnMouseOutDiagram||this.diagram.activeContainer?.hasClass("folded"))&&[a,i,o].forEach((e=>{e.panel.removeClass("visible"),e.panel.addClass("hidden")})),this.diagram.activeContainer?.appendChild(n)}createPanel(e,t){const n=this.diagram.panelsData?.controlPanel,a=n.createEl("div");return a.addClass(e),a.addClass("diagram-zoom-drag-panel"),a.setCssStyles(t),a}createButton(e,t,n,a=!0,i=void 0){const r=document.createElement("button");return r.className="button",r.id=i??"",a?(r.setCssStyles({background:"transparent",border:"none",color:"var(--text-muted)",cursor:"pointer",padding:"4px",borderRadius:"3px",display:"flex",justifyContent:"center",alignItems:"center",transition:"background-color 0.2s ease"}),vo(r,e,n),this.diagram.plugin.view.registerDomEvent(r,"click",t),this.diagram.plugin.view.registerDomEvent(r,"mouseenter",(()=>{r.setCssStyles({color:"var(--interactive-accent)"})})),this.diagram.plugin.view.registerDomEvent(r,"mouseleave",(()=>{r.setCssStyles({color:"var(--text-muted)"})}))):r.setCssStyles({visibility:"hidden"}),r}}class So{diagramEvents;startX;startY;initialX;initialY;isDragging=!1;constructor(e){this.diagramEvents=e}initialize(e){const t=e.querySelector(this.diagramEvents.diagram.compoundSelector);t&&(t.hasClass("eventHandlers-bound")||this.diagramEvents.diagram.plugin.view&&(t.addClass("eventHandlers-bound"),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"wheel",this.wheel.bind(this,e,t),{passive:!0}),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"mousedown",this.mouseDown.bind(this,e,t)),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"mousemove",this.mouseMove.bind(this,e,t)),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"mouseup",this.mouseUp.bind(this,e,t)),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"mouseleave",this.mouseLeave.bind(this,e,t)),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"mouseenter",this.mouseEnterOnDiagram.bind(this,e)),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"mouseleave",this.mouseLeaveOutDiagram.bind(this,e))))}wheel(e,t,n){if(!n.ctrlKey&&document.fullscreenElement!==e)return;this.diagramEvents.diagram.activeContainer=e;const a=t.getBoundingClientRect(),i=n.clientX-a.left,r=n.clientY-a.top,o=this.diagramEvents.diagram.scale;this.diagramEvents.diagram.scale+=-.001*n.deltaY,this.diagramEvents.diagram.scale=Math.max(.125,this.diagramEvents.diagram.scale);const s=i*(1-this.diagramEvents.diagram.scale/o),l=r*(1-this.diagramEvents.diagram.scale/o);this.diagramEvents.diagram.dx+=s,this.diagramEvents.diagram.dy+=l,t.setCssStyles({transform:`translate(${this.diagramEvents.diagram.dx}px, ${this.diagramEvents.diagram.dy}px) scale(${this.diagramEvents.diagram.scale})`})}mouseDown(e,t,n){0===n.button&&(this.diagramEvents.diagram.activeContainer=e,e.focus({preventScroll:!0}),this.isDragging=!0,this.startX=n.clientX,this.startY=n.clientY,this.initialX=this.diagramEvents.diagram.dx,this.initialY=this.diagramEvents.diagram.dy,t.setCssStyles({cursor:"grabbing"}),n.preventDefault())}mouseMove(e,t,n){if(!this.isDragging)return;this.diagramEvents.diagram.activeContainer=e;const a=n.clientX-this.startX,i=n.clientY-this.startY;this.diagramEvents.diagram.dx=this.initialX+a,this.diagramEvents.diagram.dy=this.initialY+i,t.setCssStyles({transform:`translate(${this.diagramEvents.diagram.dx}px, ${this.diagramEvents.diagram.dy}px) scale(${this.diagramEvents.diagram.scale})`})}mouseUp(e,t,n){this.diagramEvents.diagram.activeContainer=e,this.isDragging=!1,t.setCssStyles({cursor:"grab"})}mouseLeave(e,t,n){this.mouseUp(e,t,n)}mouseEnterOnDiagram(e,t){if(!this.diagramEvents.diagram.plugin.settings.hideOnMouseOutDiagram)return;if(e.hasClass("folded"))return;const n=this.diagramEvents.diagram.state.panelsData;n?.panels&&[n.panels.move.panel,n.panels.zoom.panel,n.panels.service.panel].forEach((e=>{e.removeClass("hidden"),e.addClass("visible")}))}mouseLeaveOutDiagram(e,t){if(!this.diagramEvents.diagram.plugin.settings.hideOnMouseOutDiagram)return;if(e.hasClass("folded"))return;const n=this.diagramEvents.diagram.state.panelsData;n?.panels&&[n.panels.move.panel,n.panels.zoom.panel,n.panels.service.panel].forEach((e=>{e.removeClass("visible"),e.addClass("hidden")}))}}class xo{diagramEvents;startX;startY;initialDistance;isDragging=!1;isPinching=!1;constructor(e){this.diagramEvents=e}initialize(e){this.diagramEvents.diagram.plugin.view&&(this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"touchstart",this.touchStart.bind(this,e),{passive:!1}),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"touchmove",this.touchMove.bind(this,e),{passive:!1}),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"touchend",this.touchEnd.bind(this,e),{passive:!1}))}touchStart(e,t){if(this.diagramEvents.diagram.nativeTouchEventsEnabled)return;this.diagramEvents.diagram.activeContainer=e;t.target.closest(".diagram-zoom-drag-panel")||(t.preventDefault(),t.stopPropagation(),1===t.touches.length?(this.isDragging=!0,this.startX=t.touches[0].clientX,this.startY=t.touches[0].clientY):2===t.touches.length&&(this.isPinching=!0,this.initialDistance=this.calculateDistance(t.touches)))}touchMove(e,t){if(this.diagramEvents.diagram.nativeTouchEventsEnabled)return;this.diagramEvents.diagram.activeContainer=e,t.preventDefault(),t.stopPropagation();if(e.querySelector(this.diagramEvents.diagram.compoundSelector))if(this.isDragging&&1===t.touches.length){const n=t.touches[0].clientX-this.startX,a=t.touches[0].clientY-this.startY;this.diagramEvents.diagram.actions.moveElement(e,n,a),this.startX=t.touches[0].clientX,this.startY=t.touches[0].clientY}else if(this.isPinching&&2===t.touches.length){const n=this.calculateDistance(t.touches),a=n/this.initialDistance;this.diagramEvents.diagram.actions.zoomElement(e,a),this.initialDistance=n}}touchEnd(e,t){if(this.diagramEvents.diagram.nativeTouchEventsEnabled)return;this.diagramEvents.diagram.activeContainer=e;t.target.closest(".diagram-zoom-drag-panel")||(t.preventDefault(),t.stopPropagation(),this.isDragging=!1,this.isPinching=!1)}calculateDistance(e){const[t,n]=[e[0],e[1]],a=n.clientX-t.clientX,i=n.clientY-t.clientY;return Math.sqrt(a*a+i*i)}}class Do{diagramEvents;constructor(e){this.diagramEvents=e}initialize(e){this.diagramEvents.diagram.plugin.view&&this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"keydown",this.keyDown.bind(this,e))}keyDown(e,t){const n=t.code;if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Equal","Minus","Digit0"].includes(n)){switch(t.preventDefault(),t.stopPropagation(),this.diagramEvents.diagram.activeContainer=e,n){case"ArrowUp":this.diagramEvents.diagram.actions.moveElement(e,0,50,!0);break;case"ArrowDown":this.diagramEvents.diagram.actions.moveElement(e,0,-50,!0);break;case"ArrowLeft":this.diagramEvents.diagram.actions.moveElement(e,50,0,!0);break;case"ArrowRight":this.diagramEvents.diagram.actions.moveElement(e,-50,0,!0)}if(t.ctrlKey)switch(n){case"Equal":this.diagramEvents.diagram.actions.zoomElement(e,1.1,!0);break;case"Minus":this.diagramEvents.diagram.actions.zoomElement(e,.9,!0);break;case"Digit0":this.diagramEvents.diagram.actions.resetZoomAndMove(e,!0)}}}}class Po{observe(e){new MutationObserver((t=>{t.forEach((t=>{"attributes"===t.type&&"class"===t.attributeName&&this.handleClassChange(e,t)}))})).observe(e,{attributes:!0,attributeOldValue:!0,attributeFilter:["class"]})}handleClassChange(e,t){const n=t.target,a=(t.oldValue??"").includes("folded"),i=n.hasClass("folded");if(a!==i){e.querySelectorAll(".diagram-zoom-drag-panel:not(.diagram-fold-panel)").forEach((e=>{e.toggleClass("hidden",i),e.toggleClass("visible",!i)}));const t=e.querySelector("#diagram-fold-button");t&&vo(t,i?"unfold-vertical":"fold-vertical",i?"Expand diagram":"Fold diagram")}}}class ko{diagramEvents;constructor(e){this.diagramEvents=e}initialize(e){this.diagramEvents.diagram.plugin.view&&(this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"focusin",this.focusIn.bind(this,e)),this.diagramEvents.diagram.plugin.view.registerDomEvent(e,"focusout",this.focusOut.bind(this,e)))}focusIn(e){this.diagramEvents.diagram.plugin.settings.automaticCollapsingOnFocusChange&&(e.removeClass("folded"),this.diagramEvents.diagram.plugin.livePreview&&e.parentElement?.removeClass("folded")),this.diagramEvents.diagram.activeContainer=e}focusOut(e){this.diagramEvents.diagram.plugin.settings.automaticCollapsingOnFocusChange&&(e.addClass("folded"),this.diagramEvents.diagram.plugin.livePreview&&e.parentElement?.addClass("folded"))}}class To{diagram;mouse;touch;keyboard;focus;foldingObserver;constructor(e){this.diagram=e,this.mouse=new So(this),this.touch=new xo(this),this.keyboard=new Do(this),this.focus=new ko(this),this.foldingObserver=new Po}initialize(e,t){this.mouse.initialize(e),this.touch.initialize(e),this.keyboard.initialize(e),this.focus.initialize(e),this.foldingObserver.observe(e)}}class Io{diagram;constructor(e){this.diagram=e}moveElement(e,t,n,a){this.diagram.activeContainer=e;const i=e.querySelector(this.diagram.compoundSelector);i&&(this.diagram.dx+=t,this.diagram.dy+=n,i.setCssStyles({transition:a?"transform 0.3s ease-out":"none",transform:`translate(${this.diagram.dx}px, ${this.diagram.dy}px) scale(${this.diagram.scale})`}),a&&this.diagram.plugin.view.registerDomEvent(i,"transitionend",(()=>{i.setCssStyles({transition:"none"})}),{once:!0}))}zoomElement(e,t,n){this.diagram.activeContainer=e;const a=e.querySelector(this.diagram.compoundSelector);if(!a)return;const i=e.getBoundingClientRect(),r=i.width/2,o=i.height/2,s=(r-this.diagram.dx)/this.diagram.scale,l=(o-this.diagram.dy)/this.diagram.scale;this.diagram.scale*=t,this.diagram.scale=Math.max(.125,this.diagram.scale),this.diagram.dx=r-s*this.diagram.scale,this.diagram.dy=o-l*this.diagram.scale,a.setCssStyles({transition:n?"transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1)":"none",transform:`translate(${this.diagram.dx}px, ${this.diagram.dy}px) scale(${this.diagram.scale})`}),n&&this.diagram.plugin.view.registerDomEvent(a,"transitionend",(()=>{a.setCssStyles({transition:"none"})}),{once:!0})}resetZoomAndMove(e,t){const n=e.querySelector(this.diagram.compoundSelector);n&&this.fitToContainer(n,e,t)}fitToContainer(e,t,n){this.diagram.activeContainer=t;const a=t.clientWidth,i=t.clientHeight,r=e.clientWidth,o=e.clientHeight;this.diagram.scale=Math.min(a/r,i/o,1),this.diagram.dx=(a-r*this.diagram.scale)/2,this.diagram.dy=(i-o*this.diagram.scale)/2,e.setCssStyles({transition:n?"transform 0.3s cubic-bezier(0.42, 0, 0.58, 1)":"none",transform:`translate(${this.diagram.dx}px, ${this.diagram.dy}px) scale(${this.diagram.scale})`,transformOrigin:"top left"}),n&&this.diagram.plugin.view.registerDomEvent(e,"transitionend",(()=>{e.setCssStyles({transition:"none"})}),{once:!0})}}class Oo{diagramContextMenu;constructor(e){this.diagramContextMenu=e}export(e){const t=e.querySelector(this.diagramContextMenu.diagram.compoundSelector);if(!t)return;const n=t.querySelector("svg"),a=t.querySelector("img");n?this.exportSVG(n):a?this.exportIMG(a):this.diagramContextMenu.diagram.plugin.showNotice("Oops! We couldn't find any elements to export. It seems something is wrong with this diagram?.")}exportSVG(e){const t=(new XMLSerializer).serializeToString(e),n=new Blob(['<?xml version="1.0" standalone="no"?>\r\n',t],{type:"image/svg+xml;charset=utf-8"});this.downloadFile(n,"svg")}exportIMG(e){fetch(e.src).then((e=>e.blob())).then((e=>{this.downloadFile(e,"png")})).catch((e=>{this.diagramContextMenu.diagram.plugin.showNotice("Error exporting image"),console.error("Error exporting image:",e)}))}downloadFile(e,n){const{diagram:a}=this.diagramContextMenu,i=`dzg_export_${a.plugin.view?.file?.basename??"diagram"}_${a.activeContainer?.id??"unknown"}}_${t.moment().format("YYYYMMDDHHmmss")}.${n}`,r=URL.createObjectURL(e),o=document.createElement("a");o.href=r,o.download=i,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(r)}}class No{diagramContextMenu;constructor(e){this.diagramContextMenu=e}async copy(e){const t=e.querySelector(this.diagramContextMenu.diagram.compoundSelector);if(!t)return;const n=t.querySelector("svg"),a=t.querySelector("img");n?(await this.copySvg(n),this.diagramContextMenu.diagram.plugin.showNotice("Copied")):a?(await this.copyImg(a),this.diagramContextMenu.diagram.plugin.showNotice("Copied")):console.error("Neither SVG nor IMG element found in the container")}async copyImg(e){fetch(e.src).then((e=>e.blob())).then((async e=>{await navigator.clipboard.write([new ClipboardItem({"image/png":e})])})).catch((e=>console.error("Error copy image:",e)))}async copySvg(e){try{e.focus();const t=(new XMLSerializer).serializeToString(e),n=new Blob([t],{type:"image/svg+xml"});await navigator.clipboard.write([new ClipboardItem({"image/svg+xml":n})])}catch(e){console.error("Failed to copy SVG:",e)}}}class Mo{diagramContextMenu;constructor(e){this.diagramContextMenu=e}async copy(e){const t=this.diagramContextMenu.diagram.source;t&&(await navigator.clipboard.writeText(t),this.diagramContextMenu.diagram.plugin.showNotice("Copied"))}}class zo{diagram;export;copy;copySource;constructor(e){this.diagram=e,this.export=new Oo(this),this.copy=new No(this),this.copySource=new Mo(this)}initialize(e,n){this.diagram.plugin.view?.registerDomEvent(e,"contextmenu",(()=>{e.addEventListener("contextmenu",(n=>{const a=n.target.closest(".diagram-container");if(!a)return;a.focus(),n.preventDefault(),n.stopPropagation();const i=new t.Menu;i.addItem((e=>{e.setTitle("Export diagram"),e.onClick((async()=>{this.export.export(this.diagram.activeContainer)}))})),i.addItem((e=>{e.setTitle("Copy diagram"),e.onClick((async()=>{await this.copy.copy(this.diagram.activeContainer)}))})),i.addItem((t=>{t.setTitle("Copy diagram source"),t.onClick((async()=>{await this.copySource.copy(e)}))})),i.showAtMouseEvent(n)}),!0)}))}}class Ro{plugin;state;controlPanel;events;actions;contextMenu;activeContainer=void 0;dx;dy;scale;nativeTouchEventsEnabled;source;panelsData;livePreviewObserver;constructor(e){this.plugin=e,this.state=new _o(this),this.actions=new Io(this),this.events=new To(this),this.controlPanel=new Co(this),this.contextMenu=new zo(this)}get compoundSelector(){return this.plugin.settings.supported_diagrams.reduce(((e,t)=>t.on?e?`${e}, ${t.selector}`:t.selector:e),"")}async initialize(e,t){t?await this.initializePreview(e,t):this.initializeLivePreview(e)}async initializePreview(e,t){if(await this.processDiagramsInPreview(e,t))return;const n=new MutationObserver((async()=>{await this.processDiagramsInPreview(e,t)&&n.disconnect()}));n.observe(e,{childList:!0,subtree:!0,attributes:!1}),setTimeout((()=>{n.disconnect()}),5e3)}initializeLivePreview(e){if(this.livePreviewObserver)return;const t=new Map,n=e=>{const n=new MutationObserver((async(e,n)=>{for(const a of e){const e=a.target;if("DIV"!==e.tagName)continue;const i=this.querySelectorWithData(e);i&&(await this.setDiagramContainer(i),n.disconnect(),t.delete(e))}}));return t.set(e,n),n.observe(e,{childList:!0,subtree:!0}),setTimeout((()=>{n.disconnect(),t.delete(e)}),5e3),n};this.livePreviewObserver=new MutationObserver((async e=>{if(this.plugin.livePreview)for(const t of e)if("childList"===t.type)for(const e of Array.from(t.addedNodes)){const t=e;if("DIV"===t.tagName&&t?.matches(".cm-preview-code-block.cm-embed-block")){const e=this.querySelectorWithData(t);if(e){await this.setDiagramContainer(e);continue}n(t)}}})),this.livePreviewObserver.observe(e,{childList:!0,subtree:!0})}async processDiagramsInPreview(e,t){const n=this.querySelectorWithData(e);return!!n&&(await this.setDiagramContainer(n,{context:t,contextElement:e}),!0)}async setDiagramContainer(e,t){const n=e.element;if(!n.parentElement)return;if(n.parentElement.hasClass("diagram-container"))return;if(n.hasClass("diagram-content"))return;const a=this.plugin.livePreview;let i,r,o;if(n.addClass("centered"),n.addClass("diagram-content"),a){const e=this.plugin.view?.editor,t=e.cm.posAtDOM(n.parentElement),a=this.plugin.view?.editor.getValue().slice(t);i=a?.match(/^"?(```.+?```)/ms)?.[1]??"No source";const s=t+i.length;r=e.cm.state.doc.lineAt(t).number,o=e.cm.state.doc.lineAt(s).number}else{if(!t)return;const e=t.context.getSectionInfo(t.contextElement);if(!e)return;const{lineStart:n,lineEnd:a,text:s}=e;r=n,o=a;i=s.split("\n").slice(r,o+1).join("\n")}const s=document.createElement("div");s.addClass("diagram-container"),a&&(s.addClass("live-preview"),n.parentElement.addClass("live-preview-parent")),n.parentNode?.insertBefore(s,n),s.appendChild(n),s.id=await this.genID(r,o,e.diagram),s.toggleClass("folded",this.plugin.settings.collapseByDefault),a&&s.parentElement?.toggleClass("folded",this.plugin.settings.collapseByDefault),s.setAttribute("tabindex","0"),this.activeContainer=s,this.state.initializeContainer(s.id,i),this.controlPanel.initialize(s,e.diagram),this.events.initialize(s,e.diagram),this.contextMenu.initialize(s,e.diagram);const l=new ResizeObserver((()=>{const e=s.clientWidth,t=s.clientHeight,a=n.clientWidth,i=n.clientHeight;e>0&&t>0&&a>0&&i>0&&(this.actions.fitToContainer(n,s),l.disconnect())}));l.observe(s),l.observe(n),setTimeout((()=>{l.disconnect(),s.clientWidth>0&&s.clientHeight>0&&n.clientWidth>0&&n.clientHeight>0&&this.actions.fitToContainer(n,s)}),5e3)}querySelectorWithData(e){for(const t of this.plugin.settings.supported_diagrams){if(!t.on)continue;const n=e.querySelector(t.selector);if(n)return{element:n,diagram:t}}return null}async genID(e,t,n){const a=`${n.name}:${e}-${t}`,i=(new TextEncoder).encode(a),r=await crypto.subtle.digest("SHA-256",i),o=Array.from(new Uint8Array(r)).map((e=>e.toString(16).padStart(2,"0"))).join("");return`id-${this.plugin.view?.file?.stat.ctime??0}-${o}`}}class $o extends t.Plugin{view=null;leafID;settings;settingsManager;pluginStateChecker;publisher;observer;diagram;livePreview=!1;async initializePlugin(){await this.initializeCore(),await this.initializeUI(),await this.initializeEventSystem(),await this.initializeUtils()}async initializeCore(){this.settingsManager=new n(this),await this.settingsManager.loadSettings(),this.addSettingTab(new uo(this.app,this)),this.updateCssProperties()}async initializeEventSystem(){this.publisher=new mo(this),this.observer=new fo(this),this.registerMarkdownPostProcessor((async(e,t)=>{this.initializeView(),this.livePreview||await this.diagram.initialize(e,t)})),this.registerEvent(this.app.workspace.on("layout-change",(async()=>{this.cleanupView(),await this.diagram.state.cleanupContainers(),this.initializeView(),this.view&&this.livePreview&&await this.diagram.initialize(this.view.contentEl)}))),this.registerEvent(this.app.workspace.on("active-leaf-change",(()=>{this.cleanupView(),this.initializeView()})))}async initializeUI(){this.diagram=new Ro(this),this.addCommand({id:"diagram-zoom-drag-toggle-panels-management-state",name:"Toggle control panel visibility of current active diagram",checkCallback:e=>{if(e)return!!this.diagram.activeContainer;const t=this.diagram.panelsData.panels;if(!t)return;const n=t.zoom.panel.hasClass("hidden");var a,i;t.zoom.panel.toggleClass("hidden",!n),t.zoom.panel.toggleClass("visible",n),t.move.panel.toggleClass("hidden",!n),t.move.panel.toggleClass("visible",n),t.service.panel.toggleClass("hidden",!n),t.service.panel.toggleClass("visible",n),i=!0,(a=this).publisher.publish({eventID:aa.PanelsChangedVisibility,timestamp:new Date,emitter:a.app.workspace,data:{visible:i}})}})}async initializeUtils(){this.pluginStateChecker=new po(this)}async onload(){await this.initializePlugin()}async onunload(){this.observer.unsubscribeAll()}initializeView(){const e=this.app.workspace.getActiveViewOfType(t.MarkdownView);if(!e)return;this.leafID=e.leaf.id,this.diagram.state.initializeLeafData(this.leafID),this.view=e;const n=e.getState();this.livePreview=!n.source&&"source"===n.mode}cleanupView(){if(this.leafID){null===this.app.workspace.getLeafById(this.leafID)&&(this.view=null,this.diagram.state.cleanupData(this.leafID),this.leafID=void 0)}}showNotice(e,n){new t.Notice(e,n)}updateCssProperties(){document.documentElement.style.setProperty("--diagram-zoom-drag-diagram-container-expanded-width",`${this.settings.diagramExpandedWidth}px`),document.documentElement.style.setProperty("--diagram-zoom-drag-diagram-container-expanded-height",`${this.settings.diagramExpandedHeight}px`),document.documentElement.style.setProperty("--diagram-zoom-drag-diagram-container-collapsed-width",`${this.settings.diagramCollapsedWidth}px`),document.documentElement.style.setProperty("--diagram-zoom-drag-diagram-container-collapsed-height",`${this.settings.diagramCollapsedHeight}px`)}}module.exports=$o;

/* nosourcemap */