{"choices": [{"id": "f2529105-d119-4687-8da5-4d9a782ec633", "name": "mita", "type": "Macro", "command": true, "macroId": "dad82207-ae43-4813-b32a-390002895d0a"}], "macros": [{"name": "Context search", "id": "dad82207-ae43-4813-b32a-390002895d0a", "commands": [{"name": "Copy", "type": "EditorCommand", "id": "a7891dc7-4fd8-46b6-9c0f-71826e918d7d", "editorCommandType": "Copy"}, {"name": "Untitled Template Choice", "type": "NestedChoice", "id": "9acd30ae-db99-4a3b-bebe-779893a4f102", "choice": {"id": "6b1c97fe-5e42-478a-99e3-dc9945feebf5", "name": "Untitled Template Choice", "type": "Template", "command": false, "templatePath": "WSpace/template/new.md", "fileNameFormat": {"enabled": true, "format": "{{selected}}"}, "folder": {"enabled": false, "folders": [], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": true, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default", "fileExistsMode": "Nothing", "setFileExistsBehavior": true}}], "runOnStartup": true}], "inputPrompt": "single-line", "devMode": false, "templateFolderPath": "WSpace/template", "announceUpdates": true, "version": "1.13.2", "disableOnlineFeatures": true, "enableRibbonIcon": false, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "text-davinci-003", "maxTokens": 4096}, {"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "gpt-4-turbo", "maxTokens": 128000}, {"name": "gpt-4o", "maxTokens": 128000}, {"name": "gpt-4o-mini", "maxTokens": 128000}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}