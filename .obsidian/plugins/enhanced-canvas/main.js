/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => EnhancedCanvas
});
module.exports = __toCommonJS(main_exports);
var import_obsidian = require("obsidian");

// node_modules/monkey-around/dist/index.mjs
function around(obj, factories) {
  const removers = Object.keys(factories).map((key) => around1(obj, key, factories[key]));
  return removers.length === 1 ? removers[0] : function() {
    removers.forEach((r) => r());
  };
}
function around1(obj, method, createWrapper) {
  const inherited = obj[method], hadOwn = obj.hasOwnProperty(method), original = hadOwn ? inherited : function() {
    return Object.getPrototypeOf(obj)[method].apply(this, arguments);
  };
  let current = createWrapper(original);
  if (inherited)
    Object.setPrototypeOf(current, inherited);
  Object.setPrototypeOf(wrapper, current);
  obj[method] = wrapper;
  return remove;
  function wrapper(...args) {
    if (current === original && obj[method] === wrapper)
      remove();
    return current.apply(this, args);
  }
  function remove() {
    if (obj[method] === wrapper) {
      if (hadOwn)
        obj[method] = original;
      else
        delete obj[method];
    }
    if (current === original)
      return;
    current = original;
    Object.setPrototypeOf(wrapper, inherited || Function);
  }
}

// main.ts
var EnhancedCanvas = class extends import_obsidian.Plugin {
  constructor() {
    super(...arguments);
    // flag to check if edge is patched
    this.isMetadataClicked = false;
    // update the items in the "propertyName" array in the frontmatter of the file.
    this.updateFrontmatter = async (file, link, action, propertyName) => {
      await this.app.fileManager.processFrontMatter(file, (frontmatter) => {
        if (!frontmatter)
          return;
        if (!frontmatter.canvas) {
          frontmatter.canvas = [];
        }
        if (!frontmatter[propertyName]) {
          Reflect.set(frontmatter, propertyName, []);
        } else if (!Array.isArray(frontmatter[propertyName])) {
          Reflect.set(frontmatter, propertyName, [frontmatter[propertyName]]);
        }
        if (action === "add" && !frontmatter[propertyName].includes(link)) {
          frontmatter[propertyName].push(link);
        } else if (action === "remove") {
          frontmatter[propertyName] = frontmatter[propertyName].filter((l) => l !== link);
        }
      });
    };
    this.ifActiveViewIsCanvas = (commandFn) => (checking) => {
      const activeView = this.app.workspace.getActiveViewOfType(import_obsidian.ItemView);
      if ((activeView == null ? void 0 : activeView.getViewType()) !== "canvas") {
        return checking ? false : void 0;
      }
      if (checking)
        return true;
      const canvas = activeView.canvas;
      const canvasData = canvas == null ? void 0 : canvas.getData();
      if (!canvas || !canvasData)
        return;
      return commandFn(canvas, canvasData);
    };
  }
  addLinkAndOptimizeEdge(canvas, addNewEdges = true) {
    const selectedNodes = Array.from(canvas.selection);
    const fileNodes = selectedNodes.filter((node) => node == null ? void 0 : node.filePath);
    const resolvedLinks = this.app.metadataCache.resolvedLinks;
    const currentData = canvas.getData();
    const existingEdgesMap = /* @__PURE__ */ new Map();
    currentData.edges.forEach((edge) => {
      existingEdgesMap.set(`${edge.fromNode}->${edge.toNode}`, edge);
    });
    const filePathToNodeMap = /* @__PURE__ */ new Map();
    fileNodes.forEach((node) => {
      if (node.filePath) {
        filePathToNodeMap.set(node.filePath, node);
      }
    });
    const newEdges = [];
    fileNodes.forEach((sourceNode) => {
      const links = resolvedLinks[sourceNode.filePath];
      if (!links)
        return;
      Object.keys(links).forEach((targetPath) => {
        const targetNode = filePathToNodeMap.get(targetPath);
        if (targetNode && targetNode !== sourceNode) {
          const edgeKey = `${sourceNode.id}->${targetNode.id}`;
          if (!existingEdgesMap.has(edgeKey)) {
            const newEdge = this.createEdge(sourceNode, targetNode);
            newEdges.push(newEdge);
            existingEdgesMap.set(edgeKey, newEdge);
          }
        }
      });
    });
    if (newEdges.length > 0 && addNewEdges) {
      currentData.edges.push(...newEdges);
    }
    const selectedNodeIds = new Set(selectedNodes.map((node) => node.id));
    currentData.edges.forEach((edge) => {
      if (selectedNodeIds.has(edge.fromNode) && selectedNodeIds.has(edge.toNode)) {
        const fromNode = currentData.nodes.find((node) => node.id === edge.fromNode);
        const toNode = currentData.nodes.find((node) => node.id === edge.toNode);
        if (fromNode && toNode) {
          const updatedEdge = this.createEdge(fromNode, toNode);
          if (edge.fromSide !== updatedEdge.fromSide || edge.toSide !== updatedEdge.toSide) {
            edge.fromSide = updatedEdge.fromSide;
            edge.toSide = updatedEdge.toSide;
          }
        }
      }
    });
    canvas.setData(currentData);
    canvas.requestSave();
  }
  deleteEdges(canvas) {
    const selectedNodes = Array.from(canvas.selection);
    const selectedNodeIds = new Set(selectedNodes.map((node) => node.id));
    const currentData = canvas.getData();
    currentData.edges = currentData.edges.filter((edge) => {
      return !(selectedNodeIds.has(edge.fromNode) && selectedNodeIds.has(edge.toNode));
    });
    canvas.setData(currentData);
    canvas.requestSave();
  }
  // add 'canvas' and canvas basename properties to the node frontmatter.
  addProperty(node, propertyName, basename) {
    const file = this.app.vault.getFileByPath(node.file);
    if (!file)
      return;
    this.app.fileManager.processFrontMatter(file, (frontmatter) => {
      if (!frontmatter)
        return;
      if (!frontmatter.canvas) {
        frontmatter.canvas = [];
      }
      const canvasLink = `[[${propertyName}]]`;
      if (!frontmatter.canvas.includes(canvasLink)) {
        frontmatter.canvas.push(canvasLink);
      }
      if (!frontmatter[basename]) {
        frontmatter[basename] = [];
      }
    });
  }
  // For JSON nodes only, which are stored in the canvas file, not the canvas node in Obsidian.
  removeProperty(node, propertyName, basename) {
    const file = this.app.vault.getFileByPath(node.file);
    if (!file)
      return;
    this.app.fileManager.processFrontMatter(file, (frontmatter) => {
      if (!frontmatter)
        return;
      if (frontmatter[basename]) {
        delete frontmatter[basename];
      }
      if (frontmatter.canvas) {
        const canvasLink = `[[${propertyName}]]`;
        frontmatter.canvas = frontmatter.canvas.filter((link) => link !== canvasLink);
        if (frontmatter.canvas.length === 0) {
          delete frontmatter.canvas;
        }
      }
    });
  }
  // For JSON nodes only, which are stored in the canvas file, not the canvas node in Obsidian.
  renameProperty(node, oldName, newName) {
    const file = this.app.vault.getFileByPath(node.file);
    if (!file)
      return;
    const getBaseName = (name) => name.substring(name.lastIndexOf("/") + 1);
    newName = getBaseName(newName);
    const oldBaseName = oldName.replace(".canvas", "");
    const newBaseName = newName.replace(".canvas", "");
    this.app.fileManager.processFrontMatter(file, (frontmatter) => {
      if (!frontmatter)
        return;
      const newFrontmatter = Object.fromEntries(
        Object.entries(frontmatter).map(([key, value]) => [
          key === oldBaseName ? newBaseName : key,
          value
        ])
      );
      Object.keys(frontmatter).forEach((key) => {
        delete frontmatter[key];
      });
      Object.assign(frontmatter, newFrontmatter);
    });
  }
  // For the command to remove all properties named after the current canvas file.
  removeAllProperty(canvas, canvasData) {
    const nodes = canvasData.nodes;
    nodes.forEach((node) => {
      if (!(node == null ? void 0 : node.file))
        return;
      this.removeProperty(node, canvas.view.file.name, canvas.view.file.basename);
    });
    canvas.setData(canvasData);
    canvas.requestSave();
  }
  async processEdgeUpdate(e) {
    var _a, _b;
    const fromNode = (_a = e == null ? void 0 : e.from) == null ? void 0 : _a.node;
    const toNode = (_b = e == null ? void 0 : e.to) == null ? void 0 : _b.node;
    if (!fromNode || !toNode)
      return;
    if (!(fromNode == null ? void 0 : fromNode.filePath) && !(fromNode == null ? void 0 : fromNode.file))
      return;
    const fromFilePath = fromNode.filePath || fromNode.file;
    const toFilePath = toNode.filePath || toNode.file;
    const fromFile = this.app.vault.getFileByPath(fromFilePath);
    const toFile = this.app.vault.getFileByPath(toFilePath);
    if (!fromFile || !toFile)
      return;
    const canvasName = e.canvas.view.file.basename;
    let link = this.app.fileManager.generateMarkdownLink(toFile, fromFilePath).replace(/^!(\[\[.*\]\])$/, "$1");
    await this.updateFrontmatter(fromFile, link, "add", canvasName);
  }
  async processEdgesInCanvas(canvasData, canvasFile) {
    if (!canvasData)
      return;
    const tempCanvas = {
      view: {
        file: canvasFile
      },
      getData: () => canvasData
    };
    const nodeIdToNodeMap = /* @__PURE__ */ new Map();
    if (canvasData.nodes && Array.isArray(canvasData.nodes)) {
      for (const node of canvasData.nodes) {
        nodeIdToNodeMap.set(node.id, node);
      }
    }
    if (canvasData.edges && Array.isArray(canvasData.edges)) {
      for (const edgeData of canvasData.edges) {
        const fromNode = nodeIdToNodeMap.get(edgeData.fromNode);
        const toNode = nodeIdToNodeMap.get(edgeData.toNode);
        if (!fromNode || !toNode)
          continue;
        const e = {
          from: { node: fromNode },
          to: { node: toNode },
          canvas: tempCanvas
        };
        await this.processEdgeUpdate(e);
      }
    }
  }
  async onload() {
    this.registerCustomCommands();
    this.registerCanvasAutoLink();
    this.registerCanvasFileDeletion();
    this.registerFocusCanvas();
    try {
      const canvasFiles = this.app.vault.getFiles().filter((file) => file.extension === "canvas");
      await Promise.all(canvasFiles.map(async (canvasFile) => {
        try {
          const content = await this.app.vault.read(canvasFile);
          if (!content || content.trim() === "")
            return;
          try {
            const canvasData = JSON.parse(content);
            if (!canvasData)
              return;
            if (canvasData.nodes && Array.isArray(canvasData.nodes)) {
              for (const node of canvasData.nodes) {
                if (!(node == null ? void 0 : node.file))
                  continue;
                this.addProperty(node, canvasFile.name, canvasFile.basename);
              }
            }
            await this.processEdgesInCanvas(canvasData, canvasFile);
          } catch (parseError) {
            return;
          }
        } catch (fileError) {
          return;
        }
      }));
    } catch (error) {
      return;
    }
  }
  registerCanvasFileDeletion() {
    const plugin = this;
    const deleteFile = async (file) => {
      if (file.deleted === true)
        return;
      const backLinks = plugin.app.metadataCache.getBacklinksForFile(file);
      if (!backLinks || !backLinks.data)
        return;
      const linkRegexBasename = new RegExp(`\\[\\[${file.basename}(\\|.*)?\\]\\]`);
      const linkRegexFullName = new RegExp(`\\[\\[${file.name}(\\|.*)?\\]\\]`);
      for (const [sourcePath, references] of backLinks.data.entries()) {
        const sourceFile = plugin.app.vault.getFileByPath(sourcePath);
        if (!sourceFile || sourceFile.extension !== "md")
          continue;
        await plugin.app.fileManager.processFrontMatter(sourceFile, (frontmatter) => {
          if (!frontmatter)
            return;
          Object.keys(frontmatter).forEach((key) => {
            if (Array.isArray(frontmatter[key])) {
              frontmatter[key] = frontmatter[key].filter((item) => {
                if (typeof item !== "string")
                  return true;
                return !(linkRegexBasename.test(item) || linkRegexFullName.test(item));
              });
            }
          });
        });
      }
    };
    const deleteCanvasFile = async (file) => {
      if (file.extension !== "canvas")
        return;
      if (file.deleted === true)
        return;
      const content = await plugin.app.vault.read(file);
      if (!content)
        return;
      const canvasData = JSON.parse(content);
      if (!canvasData)
        return;
      canvasData.nodes.forEach((node) => {
        if (node.type !== "file")
          return;
        plugin.removeProperty(node, file.name, file.basename);
      });
    };
    const renameCanvasFile = async (file, newPath) => {
      if (file.extension !== "canvas")
        return;
      if (file.deleted === true)
        return;
      const content = await plugin.app.vault.read(file);
      if (!content)
        return;
      const canvasData = JSON.parse(content);
      if (!canvasData)
        return;
      canvasData.nodes.forEach((node) => {
        if (node.type !== "file")
          return;
        plugin.renameProperty(node, file.name, newPath);
      });
    };
    const uninstaller = around(this.app.fileManager.constructor.prototype, {
      trashFile(old) {
        return function(file) {
          deleteCanvasFile(file);
          deleteFile(file);
          return old.call(this, file);
        };
      },
      renameFile(old) {
        return function(file, newPath) {
          renameCanvasFile(file, newPath);
          return old.call(this, file, newPath);
        };
      }
    });
    this.register(uninstaller);
  }
  registerCustomCommands() {
    this.addCommand({
      id: "optimize-edges",
      name: "Adjust edges with shortest path",
      checkCallback: this.ifActiveViewIsCanvas((canvas, canvasData) => {
        this.addLinkAndOptimizeEdge(canvas, false);
      })
    });
    this.addCommand({
      id: "delete-edges",
      name: "Delete edges between selected nodes",
      checkCallback: this.ifActiveViewIsCanvas((canvas, canvasData) => {
        this.deleteEdges(canvas);
      })
    });
    this.addCommand({
      id: "add-link-and-optimize-edge",
      name: "Add edges according the links in notes",
      checkCallback: this.ifActiveViewIsCanvas((canvas, canvasData) => {
        this.addLinkAndOptimizeEdge(canvas);
      })
    });
    this.addCommand({
      id: "remove-canvas-property",
      name: "Remove the property of all nodes in current Canvas",
      checkCallback: this.ifActiveViewIsCanvas((canvas, canvasData) => {
        this.removeAllProperty(canvas, canvasData);
      })
    });
  }
  registerFocusCanvas() {
    this.registerDomEvent(document, "click", (evt) => {
      const target = evt.target;
      if (target.closest(".metadata-container")) {
        this.isMetadataClicked = true;
        setTimeout(() => {
          this.isMetadataClicked = false;
        }, 200);
      }
    });
    this.registerEvent(
      // Implement the feature to zoom to the last opened file when switching to the canvas view.
      this.app.workspace.on("active-leaf-change", () => {
        Promise.resolve().then(async () => {
          if (this.isMetadataClicked == false)
            return;
          const activeLeaf = this.app.workspace.getActiveViewOfType(import_obsidian.ItemView);
          if (!activeLeaf || activeLeaf.getViewType() !== "canvas")
            return;
          const prevFile = this.app.workspace.getLastOpenFiles()[0];
          if (!prevFile)
            return;
          const canvas = await activeLeaf.canvas;
          if (!canvas)
            return;
          for (const [key, value] of canvas.nodes) {
            if ((value == null ? void 0 : value.filePath) === prevFile) {
              canvas.select(value);
            }
          }
          setTimeout(() => {
            canvas.zoomToSelection();
          }, 100);
        });
      })
    );
  }
  registerCanvasAutoLink() {
    const plugin = this;
    const processNodeUpdate = async (e) => {
      var _a, _b;
      const fromNode = (_a = e == null ? void 0 : e.from) == null ? void 0 : _a.node;
      const toNode = (_b = e == null ? void 0 : e.to) == null ? void 0 : _b.node;
      if (!fromNode || !toNode)
        return;
      if (!(fromNode == null ? void 0 : fromNode.filePath))
        return;
      const fromFile = this.app.vault.getFileByPath(fromNode.filePath);
      if (!fromFile)
        return;
      const canvasName = await e.canvas.view.file.basename;
      const resolvedLinks = this.app.metadataCache.resolvedLinks[fromNode.filePath] || {};
      const fromNodeLinks = Object.keys(resolvedLinks);
      const { edges, nodes } = await e.canvas.getData();
      const sameFileNodes = nodes.filter((node) => node.file === fromNode.filePath);
      const allRelevantEdges = edges.filter(
        (edge) => sameFileNodes.some((node) => edge.fromNode === node.id)
      );
      const edgeToNodesFilePathSet = new Set(
        allRelevantEdges.map((edge) => nodes.find((node) => node.id === edge.toNode)).filter((node) => node && node.file).map((node) => node.file)
      );
      const updatePromises = [];
      const getFilePath = (path) => this.app.vault.getFileByPath(path);
      fromNodeLinks.forEach((filePath) => {
        if (!edgeToNodesFilePathSet.has(filePath)) {
          if (filePath === e.canvas.view.file.path)
            return;
          const targetFile = getFilePath(filePath);
          if (!targetFile)
            return;
          let link = this.app.fileManager.generateMarkdownLink(targetFile, filePath).replace(/^!(\[\[.*\]\])$/, "$1");
          updatePromises.push(this.updateFrontmatter(fromFile, link, "remove", canvasName));
        }
      });
      if (toNode == null ? void 0 : toNode.filePath) {
        const targetFile = getFilePath(toNode.filePath);
        if (!targetFile)
          return;
        let link = this.app.fileManager.generateMarkdownLink(targetFile, toNode.filePath).replace(/^!(\[\[.*\]\])$/, "$1");
        updatePromises.push(this.updateFrontmatter(fromFile, link, "add", canvasName));
      }
      await Promise.all(updatePromises);
    };
    const updateTargetNode = (0, import_obsidian.debounce)(async (e) => {
      await processNodeUpdate(e);
    }, 500, true);
    const updateTargetNodeImmediate = async (e) => {
      await processNodeUpdate(e);
    };
    const updateOriginalNode = async (edge) => {
      var _a, _b;
      if (!((_a = edge.to.node) == null ? void 0 : _a.filePath) || !((_b = edge.from.node) == null ? void 0 : _b.filePath))
        return;
      const canvasName = edge.canvas.view.file.basename;
      const toNode = edge.to.node;
      const fromNode = edge.from.node;
      const file = this.app.vault.getFileByPath(toNode.filePath);
      if (!file)
        return;
      let link = this.app.fileManager.generateMarkdownLink(file, toNode.filePath);
      link = link.replace(/^!(\[\[.*\]\])$/, "$1");
      if (fromNode == null ? void 0 : fromNode.filePath) {
        const fromFile = this.app.vault.getFileByPath(fromNode.filePath);
        if (!fromFile)
          return;
        const { edges, nodes } = await edge.canvas.getData();
        const sameFileNodes = nodes.filter((node) => node.file === fromNode.filePath);
        const stillHasConnection = edges.some(
          (e) => sameFileNodes.some((node) => e.fromNode === node.id) && e.toNode === toNode.id && !(e.fromNode === fromNode.id && e.toNode === toNode.id)
        );
        if (!stillHasConnection) {
          this.updateFrontmatter(fromFile, link, "remove", canvasName);
        }
      }
    };
    const removeNodeUpdate = async (node) => {
      var _a, _b, _c;
      const resolvedNode = await node;
      if (((_a = resolvedNode == null ? void 0 : resolvedNode.file) == null ? void 0 : _a.extension) !== "md")
        return;
      const canvasFile = (_c = (_b = resolvedNode == null ? void 0 : resolvedNode.canvas) == null ? void 0 : _b.view) == null ? void 0 : _c.file;
      if (!canvasFile || !canvasFile.name)
        return;
      if (resolvedNode == null ? void 0 : resolvedNode.filePath) {
        const canvasData = await resolvedNode.canvas.getData();
        const otherNodes = canvasData.nodes.filter(
          (n) => {
            return n.file === resolvedNode.filePath;
          }
        );
        if (otherNodes.length === 0) {
          let tmpNode = {};
          tmpNode.file = resolvedNode.filePath;
          this.removeProperty(tmpNode, canvasFile.name, canvasFile.basename);
        }
      }
    };
    const addNodeUpdate = async (node) => {
      var _a;
      const resolvedNode = await node;
      if (((_a = resolvedNode == null ? void 0 : resolvedNode.file) == null ? void 0 : _a.extension) !== "md")
        return;
      const canvasFile = resolvedNode.canvas.view.file;
      if (!canvasFile || !canvasFile.name)
        return;
      if (resolvedNode.filePath) {
        let tmpNode = {};
        tmpNode.file = resolvedNode.filePath;
        this.addProperty(tmpNode, canvasFile.name, canvasFile.basename);
      }
    };
    const selfPatched = (edge) => {
      this.patchedEdge = true;
      const uninstaller = around(edge.constructor.prototype, {
        update: (next) => {
          return function(...args) {
            const result = next.call(this, ...args);
            updateTargetNode(this);
            return result;
          };
        }
      });
      plugin.register(uninstaller);
    };
    const patchCanvas = () => {
      var _a;
      const canvasView = (_a = plugin.app.workspace.getLeavesOfType("canvas")[0]) == null ? void 0 : _a.view;
      if (!(canvasView == null ? void 0 : canvasView.canvas))
        return false;
      const uninstaller = around(canvasView.canvas.constructor.prototype, {
        removeNode(old) {
          return function(node) {
            const result = old.call(this, node);
            if (this.isClearing !== true) {
              removeNodeUpdate(node);
            }
            return result;
          };
        },
        addNode(old) {
          return function(node) {
            const result = old.call(this, node);
            addNodeUpdate(node);
            return result;
          };
        },
        removeEdge(old) {
          return function(edge) {
            const result = old.call(this, edge);
            if (this.isClearing !== true) {
              updateOriginalNode(edge);
            }
            return result;
          };
        },
        addEdge(old) {
          return function(edge) {
            const result = old.call(this, edge);
            if (!plugin.patchedEdge) {
              plugin.patchedEdge = true;
              selfPatched(edge);
            }
            updateTargetNodeImmediate(edge);
            return result;
          };
        },
        clear(old) {
          return function() {
            this.isClearing = true;
            const result = old.call(this);
            this.isClearing = false;
            return result;
          };
        }
      });
      plugin.register(uninstaller);
      return true;
    };
    const layoutChangeHandler = () => {
      if (patchCanvas()) {
        plugin.app.workspace.off("active-leaf-change", layoutChangeHandler);
        plugin.app.workspace.off("layout-change", layoutChangeHandler);
      }
    };
    plugin.app.workspace.on("active-leaf-change", layoutChangeHandler);
    plugin.app.workspace.on("layout-change", layoutChangeHandler);
  }
  createEdge(node1, node2) {
    const random = (e) => {
      let t = [];
      for (let n = 0; n < e; n++) {
        t.push((16 * Math.random() | 0).toString(16));
      }
      return t.join("");
    };
    const node1CenterX = node1.x + node1.width / 2;
    const node1CenterY = node1.y + node1.height / 2;
    const node2CenterX = node2.x + node2.width / 2;
    const node2CenterY = node2.y + node2.height / 2;
    const angle = Math.atan2(node2CenterY - node1CenterY, node2CenterX - node1CenterX) * 180 / Math.PI;
    const normalizedAngle = angle < 0 ? angle + 360 : angle;
    let fromSide;
    let toSide;
    if (normalizedAngle >= 315 || normalizedAngle < 45) {
      fromSide = "right";
      toSide = "left";
    } else if (normalizedAngle >= 45 && normalizedAngle < 135) {
      fromSide = "bottom";
      toSide = "top";
    } else if (normalizedAngle >= 135 && normalizedAngle < 225) {
      fromSide = "left";
      toSide = "right";
    } else {
      fromSide = "top";
      toSide = "bottom";
    }
    const edgeData = {
      id: random(16),
      fromSide,
      fromNode: node1.id,
      toSide,
      toNode: node2.id
    };
    return edgeData;
  }
  async onunload() {
    try {
      const canvasFiles = this.app.vault.getFiles().filter((file) => file.extension === "canvas");
      await Promise.all(canvasFiles.map(async (canvasFile) => {
        try {
          const content = await this.app.vault.read(canvasFile);
          const canvasData = JSON.parse(content);
          const tempCanvas = {
            view: {
              file: canvasFile
            },
            setData: () => {
            },
            requestSave: () => {
            }
          };
          this.removeAllProperty(tempCanvas, canvasData);
        } catch (error) {
          return;
        }
      }));
    } catch (error) {
      return;
    }
  }
};


/* nosourcemap */