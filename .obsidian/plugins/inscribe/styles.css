/* Editor Styles */
.inscribe-inline-prediction {
    opacity: 0.5;
}

.inscribe-prompt-textarea {
    width: 100%;
    resize: vertical;
    position: relative;
}

.inscribe-section {
    margin-top: 1.75em;
}

/* Path Mapping Styles */
.inscribe-mapping-table {
    width: 100%;
    border-collapse: collapse;
}

.inscribe-mapping-table th {
    text-align: left;
    padding: 8px 0px;
    border-bottom: 1px solid var(--background-modifier-border);
}

.inscribe-mapping-table td {
    text-align: left;
    padding: 8px 0px;
    border-bottom: 1px solid var(--background-modifier-border);
}

.inscribe-mapping-table .mapping-toggle-wrapper {
    display: flex;
}

/* Style provider modal */
.inscribe-provider-settings-modal {
    width: 500px;
}

/* Status Bar Styles */

/* Writing animation with rotation */
@keyframes write {
    0% {
        transform: rotate(0deg) translateX(0);
    }

    15% {
        transform: rotate(720deg) translateX(0);
    }

    30% {
        transform: rotate(720deg) translateX(3px);
    }

    45% {
        transform: rotate(720deg) translateX(-3px);
    }

    60% {
        transform: rotate(720deg) translateX(3px);
    }

    75% {
        transform: rotate(720deg) translateX(-3px);
    }

    90% {
        transform: rotate(720deg) translateX(1px);
    }

    100% {
        transform: rotate(720deg) translateX(0);
    }
}


.status-bar-item-icon.active {
    animation: write 2.5s cubic-bezier(0.65, 0, 0.35, 1) infinite;
}

/* Strikethrough effect for disabled completion */
.status-bar-item-icon.completion-disabled {
    position: relative;
}

.status-bar-item-icon.completion-disabled::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--text-muted);
    transform: translateY(-50%);
}