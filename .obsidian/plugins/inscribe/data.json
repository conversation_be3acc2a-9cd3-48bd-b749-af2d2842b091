{"enabled": true, "acceptanceHotkey": "Tab", "providers": {"openai": {"integration": "openai", "name": "Open AI", "description": "Use OpenAI APIs to generate text.", "apiKey": "api-key", "models": ["gpt-4o", "gpt-4o-mini"], "configured": false, "temperature_range": {"min": 0, "max": 1}}, "ollama": {"integration": "ollama", "name": "Ollama", "description": "Use your own Ollama instance to generate text.", "host": "http://localhost:11434", "models": ["llama3.2:latest", "mistral-nemo"], "configured": false, "temperature_range": {"min": 0, "max": 1}}, "openai_compatible": {"integration": "openai_compatible", "name": "OpenAI compatible", "description": "Use OpenAI compatible APIs to generate completions.", "apiKey": "api-key", "baseUrl": "https://api.openai.com/v1", "models": ["gpt-4o", "gpt-4o-mini"], "configured": false, "temperature_range": {"min": 0, "max": 1}}, "gemini": {"integration": "gemini", "name": "Gemini", "description": "Use Gemini APIs to generate text.", "apiKey": "AIzaSyA3Dmo0C3wm5ARAI0nIJOyGj-KqnJDTKgk", "models": ["gemini-2.0-flash", "gemini-2.5-flash-preview-04-17"], "configured": true, "temperature_range": {"min": 0, "max": 2}}}, "profiles": {"default": {"name": "Default profile", "provider": "gemini", "delayMs": 100, "splitStrategy": "sentence", "completionOptions": {"model": "gemini-2.5-flash-preview-04-17", "userPrompt": "If the last sentence is incomplete, only complete the sentence and nothing else. If the last sentence is complete, generate a new sentence that follows logically:\n---\n{{pre_cursor}}", "systemPrompt": "You are a writing assistant that predicts and completes sentences in a natural, context-aware manner. Your goal is to continue the user’s text smoothly, maintaining coherence, fluency, and style. Adapt to the user’s writing tone, whether formal, informal, creative, or technical. Ensure that completions feel intuitive, useful, and free of unnecessary repetition. Do not generate completion that includes the prompt itself.", "temperature": 0.7}}}, "path_configs": {"/": {"profile": "default", "enabled": true}}}