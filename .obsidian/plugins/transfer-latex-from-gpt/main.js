/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => TransferLatexFromGPTPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian = require("obsidian");
var TransferLatexFromGPTPlugin = class extends import_obsidian.Plugin {
  async onload() {
    console.log("Loading Transfer LaTeX from GPT Plugin");
    this.addCommand({
      id: "convert-latex-to-mathjax",
      name: "Convert LaTeX to MathJax",
      callback: () => this.convertLatexToMathJax()
    });
    const ribbonIconEl = this.addRibbonIcon("sigma", "Convert LaTeX to MathJax", () => this.convertLatexToMathJax());
    if (ribbonIconEl) {
      console.log("Ribbon icon added successfully.");
      const ribbonContainer = document.querySelector(".workspace-ribbon");
      if (ribbonContainer) {
        ribbonContainer.appendChild(ribbonIconEl);
        console.log("Ribbon icon moved to the end.");
      }
    } else {
      console.log("Failed to add ribbon icon.");
    }
  }
  // 卸载插件时的清理
  onunload() {
    console.log("Unloading Transfer LaTeX from GPT Plugin");
  }
  // 读取当前打开的文档内容并进行 LaTeX 到 MathJax 的替换
  async convertLatexToMathJax() {
    const activeFile = this.app.workspace.getActiveFile();
    if (!activeFile) {
      console.log("No active file");
      return;
    }
    const fileContent = await this.app.vault.read(activeFile);
    const convertedContent = this.convertLatexSyntax(fileContent);
    await this.app.vault.modify(activeFile, convertedContent);
    console.log("LaTeX to MathJax conversion completed.");
  }
  // 替换函数：将 \( ... \) 替换为 $ ... $，将 \[ ... \] 替换为 $$ ... $$
  convertLatexSyntax(content) {
    const inlineRegex = /\\\(\s*(.*?)\s*\\\)/gs;
    let updatedContent = content.replace(inlineRegex, (match, formula) => `$${formula.trim()}$`);
    const blockRegex = /\\\[\s*(.*?)\s*\\\]/gs;
    updatedContent = updatedContent.replace(blockRegex, (match, formula) => `$$${formula.trim()}$$`);
    return updatedContent;
  }
};


/* nosourcemap */