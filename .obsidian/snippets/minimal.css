.cm-inline-code {
  border-radius: 0.2em;
  padding: 0.15em 0.3em;
  color: #eb928a;
  background-color: #3d3c3b52;
}

.markdown-rendered code {
  color: #eb928a;
  background-color: #3d3c3b52;
  font-family: var(--font-monospace);
  border-radius: 0.2em;
  font-size: var(--code-size);
  padding: 0.15em 0.3em;
  border: var(--code-border-width) solid var(--code-border-color);
  -webkit-box-decoration-break: clone;
}

.is-flashing {
  transition: color 0.25s ease, background-color 0.25s ease;
  background-color: #999 !important;
  color: var(--text-normal);
  mix-blend-mode: var(--highlight-mix-blend-mode);
  border-radius: var(--radius-s);
}

.search-result-file-matched-text {
  color: #000000 !important;
  background-color: #999 !important;
}

body {
  font-weight: 200 !important;
  --list-indent: 1.5em !important;
}

.flowchart {
  /* width: 100%; */
  /* height: auto; */
  max-width: 100% !important;
  max-height: 1500px !important;
}

/* rect {
  fill: #dddddd !important;
  stroke: #ffffff !important;
} */

line {
  stroke: #916a00 !important;
}

.loopLine {
  stroke: #ed7811 !important;
  fill: none !important;
  stroke-dasharray: 10 5 !important;
}

.cm-s-obsidian span.cm-hmd-internal-link a {
  margin: 0 -0.05em;
  padding: 0.05em 0.15em;
  border-radius: 0.2em;
  -webkit-box-decoration-break: clone;
  background: #ffb86ca6;
  color: #000000 !important;
}

.cm-indent-spacing {
  margin-left: 1em !important;
}

.cm-indent {
  margin-left: 1em !important;
  /* margin-right: 1em !important; */
}

.excalidraw-svg {
  max-width: 100% !important;
}

.mermaid {
  margin: 0 auto !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* canvas */
.canvas-node {
  height: 200px;
}
* {
  font-family: Open Sans, sans-ui-sans-serif, system-ui, -apple-system;
  font-weight: 200;
}

.cm-preview-code-block {
  width: 100% !important;
}

.excalidraw__embeddable-container {
  /* scale: 0.450941; */
}
