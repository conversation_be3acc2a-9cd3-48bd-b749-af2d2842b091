Figma Channel
- **Figma 官方频道 (@Figma，≈ 68 万订阅)**
    
    - 由 Figma 团队直管，所有新功能（如 Figma Draw、AI 辅助、Figma Sites 等）都会在第一时间发布演示，保持内容“最新鲜”([YouTube](https://www.youtube.com/c/FigmaDesign/videos?utm_source=chatgpt.com "Figma - YouTube"))
        
    - 拥有系统化播放列表（从入门到 Auto-layout、变量、原型、协作流程），方便按主题循序渐进学习([YouTube](https://www.youtube.com/%40Figma/playlists?utm_source=chatgpt.com "Figma - YouTube"))
        
    - 还会直播 Config 大会和产品发布会，帮助你了解官方设计理念与行业趋势([YouTube](https://www.youtube.com/channel/UCQsVmhSa4X-G3lHlUtejzLA?utm_source=chatgpt.com "Figma - YouTube"))
        
- **AJ&Smart (@AJSmart，≈ 39 万订阅)**
    
    - 以设计冲刺（Design Sprint）和产品设计流程闻名，实战性强，能学到如何把 Figma 技能应用到真实团队协作与用户研究中([YouTube](https://www.youtube.com/channel/UCeB_OpLspKJGiKv1CYkWFFw?utm_source=chatgpt.com "AJ&Smart - YouTube"))
        
    - 视频更新稳定，每周围绕 UX 流程、工作坊技巧、设计思维等主题推出新内容，适合理解“工具背后的方法论”([YouTube](https://www.youtube.com/%40AJSmart/videos?utm_source=chatgpt.com "AJ&Smart - YouTube"))
        
    - 多次与《Design Sprint》作者合作，并讨论 2025 年设计冲刺的最新价值，对想提升整体产品思维的设计师帮助巨大([YouTube](https://www.youtube.com/watch?v=8-Syxs3SQ7s&utm_source=chatgpt.com "Is the DESIGN SPRINT still RELEVANT in 2025? ft. Jake Knapp ..."))
        
- **Flux Academy (@FluxAcademy，≈ 45 万订阅)**
    
    - 由资深 Web/UI 设计师 Ran Segall 主理，主打零基础到进阶的完整 Figma + Webflow/UI 设计课程，尤其适合想做网页与自由职业设计师的人([YouTube](https://www.youtube.com/%40FluxAcademy/videos?utm_source=chatgpt.com "Flux Academy - YouTube"))
        
    - 提供高质量 Crash Course（如 2025 版 90 分钟 Auto-layout 速成、Figma Sites 全流程示范等），让你快速掌握核心技能并立即上手实战([YouTube](https://www.youtube.com/watch?v=m9uXR4xt95w&utm_source=chatgpt.com "Figma Crash Course 2025: Responsive Website Design - YouTube"))
        
    - 频道强调设计原则、排版与商业思维的结合，帮助你把 Figma 技能转化为可收费的项目能力([thecodeaccelerator.com](https://thecodeaccelerator.com/blog/top-youtube-channels-for-learning-figma-and-ui/ux-design "Top YouTube Channels for Learning Figma and UI/UX Design"))
        

> **使用建议**
> 
> - 刚入门可先看 Figma 官方的“Design Basics”→补齐工具认知
>     
> - 想把工具用于团队/客户项目，用 AJ&Smart 学习流程与工作坊技巧
>     
> - 想系统成长为能独立接单的 UI/Web 设计师，跟着 Flux Academy 做完整案例
>     
> - 三个频道互补观看，能同时获得“功能细节 + 设计方法 + 商业落地”的立体学习体验。



Shift-G 显示参考线
Shift CMD K import image
拖动大小 option 是 居中拖动
option  快速复制

todo https://www.youtube.com/watch?v=clSHs94hNNc&list=PLXC_gcsKLD6lovcamt6i27q0hbXq6fmwi&index=2

cmd + \[ 提高图层
option + cmd + k  生成compnnet
shift+2 切换到对应图层视图
i 颜色pick
k scale tool

给大家准备了逐行解读代码的书籍，将机器学习和深度学习流程讲解的非常透策，几乎能解决任何代码问题，以及深度学习领域经典热门方向的论文代码一起分享给大家大家需要的话可以去我的公众号【阿远学长】回复【555】无偿获取相关资源！

项目地址：https://www.deep-ml.com/?page=1&difficulty=&category=&solved=


