---
excalidraw-plugin: parsed
tags:
  - excalidraw
---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBGeISaOiCEfQQOKGZuAG1wMFAwYogSbggAcXwATTYARzqoABE4UgBBAEkAeQ4AK0l9TGU65xTiyFhEcsJ9aKR+EsxueIAO

AAZtAHYeHgBWADZdtd2eTYBOXYXIGG4AFjX9hM2AZl3blbOV+Nv959uriAUEjqbj7B4AyQIQjKaTcI5rAHWZTBbgIgoCKCkNgAawQAGE2Pg2KRypjrMw4LhAlkxiVNLhsNjlFihBxiASiSSJGSOBSqZkoLTIAAzQj4fAAZVgKIkgg8QogzExOIQAHVgZJuHx0YrlbipTAZeg5WUASyYRxwjk0PEAWxKdg1DcbWs0eMIMzhHAOsRrahcgBdAHC8gZ

H3cDhCcUAwhsrDlXDPBUstmW5h+yPRnVhBDEZbxHhnW73HhrZ4AxgsdhcF37CtMVicABynDEdx++1uBeedZ1hGYTTSUFz3ExQgQAM0wjZAFFghksn7AwChHBiLhh3mbZtbptzoW3u9LjqiBxsRGo/gAUTGSO0MKCGEAUqN0I/RBEGzY8oFaLguGJGFfYi2IfZ1gQbB4jOB9iDeeJsGFBALmFeJnmFHhcGOHgEFuYhbmFFYViTZ93HEf10TAW0KPi

dEgx1bAsTgC9xQKABfBYihKMoJAANR4AAxJplH2cUAFVcCaJoAC01glfj6ETGBNAVSYyNKWZlHmHUljQL4Vm0HYiyOeIXjOTZ9l7d1nVQe5HhM15bjOVCu2AtZ/h1IFiBBNAwUskpIWhWE0HhREOGRMi3RKJUsVxDliVJcheUpalBUnBkmRTdlCXi7lEr5FLfzFSVpTUk082fPU1Q1LUKpihADSNRVCVNHVzUkNM/So917QZJ1lldAFPVXH0lzo9

0Q1wMMt1QTMrz7OMdPQXBbmTadiA65i5vdHNpoLMDPgs/ZNnrKtODuY6dUrRsOBbDg2xtHhnjWcyHhWdz3X7Qdgk3UdSHHSc1rndIBVGlc1w3O9UBM3d9wuYsVmPd1T3PNBZuvNhb2mh98CfbMoigN9yk/RwwsK/9pogW5dmIHgVnXXAcOFXZtl+R7nhWHhHPZ3AOZQp7NGg25cGFTYVgVZhSLyairko2iAQY+1NrYji+wpwgABVVQAKSMZxdnST

BegZihRKbCo8WqLo2BU+A1JmOYFUW75nmebRPipo74geeJfkRkprNsp4HMIlm3vekpPO81BfIhKEYUFYKBp1JEjUijE6rirl0B5fKBQVelGSGtlM4S8lkrz4Mioa0rmvKvG6vVLzNTQbVtsq6vyjK1a/Haq1ljtB0+pdNOPRZb1fTyMaSgmqbNpjBaE12bvUz71HL2fBBIbBIinLWKCTuu0ELvdK7q1u+7eG9l2zjWM5W64gch0hscJx1KdWWIIG

F2ySewfXH7twwzvnDI815YwoxmuvE8GNcRY0fK/Nur53zE2/GTBAAF0DAXOM8TYwpsA313LseIBxcCJhQtgYgpkECbAZrgsCroeAoTrttSWaB8jjEojLGi4wp6QAVkxNeLFijsQKJxSA3F0D4BgLcAAQrgKoygeAAGlRKbFILsAAskRPEWslHChtlMCQ9tNKO2WO8R47sDibC9jsBG99rjcBvnZF4bwb4FjWHsLqEdqqJxHgFeOdxUKhXCqiWqKo

S65TLvyGkaVC6ZQidnPK5cYk6j/MVQ0Nd5RhNxI3KO9jdR1Q7rKWu3cLSryhgPXqsB+ojyGuPUGqTQzoOmmjeaxB4wSFwPsZe61ymtO2pvaaODth72eI9A+1ZHEj1Ps2VsZFUJOTOL7JOH1H7fWfn9BBdJAbzhBr/HUq5/6Q2hnuYBh4EZgLPHPaBmNuDY1xoggmyDMgkx/JXcm5QeAIQQLsJZxALhnAFrcZ4mg1i5l2K4l46FngUN2NgTQpwVgW

SAuLVh5EOFeOKNw4ovCID8KVsIlWH0KZSQAAoACVrC4AlGwNo+xZgAAkAD66stZnFVBKZwuADF2w0lpd0i1SwvG0I5MyJx9iPXMvk6yTig5vBWKLLB7MASR2bqgEKOp/FBRsrfYJqdsn4mylnaASTompTfulIuWVOSlySmasm6TGpdwNbktV+TooqiKcaEpZphBlPTP3HUPVHTVOHoNMeI19njSaRg/pXEF6dLFr6j+G1BFbSioMxxXwb6vGZhMs

6aBnhnHzTdOZNSTKFgVX5cRayEAANQC/AGH8v57LYbiw5ENdo7lOQeeGftIDI2uUjGBkN7lbIxEgomLzUHvOaeUYgmhqYrHQpvammxNDxETL8ZaKxSGfGwKLFYOFiwM1dMQTdqKCBkXYeMTFsseHy0YgSsAIjihiNKBTAAGs4JomxSXjh4nUTAIQeJNk2AgKSkhdgVE2Dy6YfLTEtwshsPeXMEY9nuMWnUMq1j6Xsq4n2Xxb4rBVT49VKz/Jx21Z

hvVEUDUJJNVEgqsSMprQYznZJ5rxpVxKp3H19cVSupqgJ/UvHilZNan63uAabSVJDdZL2tSI0TzbcGGNLSoEfQTUtM4PTU2QKzAM451jgVfD+Fhk+DZJloAuCW8+8zzI2LBLTGMtb62Nrfjs4Gi4o0lA7fWk5sNzn9ogIOtN6Nbn3ngRVSdEgUGk1nRgiAiQiGbHPc8YhRazIM2MiLQ9nM1jClGasNYNNcAbv0SRK9UsMVcLlvRJ9ablaiNVuUJs

hBNhsBkWwKoZwugVCkk2GRTRSV4k2Gy+gcGjEIYBItZw1jNjaH2IkCyTlHISpeACBTmwjgJHWGM94wLcPVsBGRjV7otUJx1RZkoKc6MicNTayJdrmMWriWxo1trc4pO4+KL1TUJNtwbmR917cxPesByUNq+nMUQGDUPKGFHIB1Mjapxpk053hbaR0pabQ9N9M0xm4zrM9wfBu5AGZNZUDHxKJT+zdwixHSIQcVzX060bP+p55tuyfNo/dP54zQDe

2gJPOAodJQbywLudFvGsX0DxbeaksUmP0DYAPSexI8QkILoBYcXCZZNCaEIkb/mGEaYIHiMQFFVWjQ3qxXVh9DXFZNcJS14l5R8D7CaJ+0gygOA8H0CsfA9BnCqg0QAGQlJITATZukAlUvBh2s2zFIrdm9A4q2Ms8ALFtsxEqtilm+LhnbZwnokY8mdpHUgqNXfuOWZOYV9UPfY6a177oC6sY/i3pjFcld/fBwDlqQPBMg4Nf951kme4w7kwjxT4

avSo/9LimeKu43iO0xAXAMj8cyYM+mgQmabTAR2B2UvJa4T18s6dUtd0yK/OGRlzsrOn7TQ8+3rz38Gn8/BgF7tQW+2XIQJr6hYjpwI4zjq6hy4fjToJZ94q6b6gQ7hlj/LxCaA7ZIqQT7TrAoROQfB4K3Cwr7DEB7xliXq27SzUT1buj4ou4vpEpcQUzYgUAyJrD4CsFKJTbZxYBcaLDLBvRnBuyPQnCli7AfCey542joYGSF5dhPTmQXA06QCq

rLClixyBRXZ7C0ahLN6fYSAADEWuhh/KdIlq8SuhiSPeP208PGGSfGkO6cI+Tcwmw+omth4mQ+UOUm0+Qag8oaiOSmC+KmS+amGOsahO6+7Si0m+eIO+GY4Rioh+UMVMpwRwpwihDAVmBaiOIWdOZa24z0vwYyPYz+6yr+myTas4POP8fOfmP+guPaICFyouVyWOw6kWqAY68e3B5QgAG/GAD0ZoAM56gA/2aACwcoAGFyyYlA6s3REg/Rwx4xv4

nAUAEohARgZEqhqSSx/Ek0Yo1kIWw4mAUAbQRAfuHuCAwoPBFOTAUA5gBAxx0IVO0Ais8sSxuAsYTAYRhmJQxI0IsYBA0xhxvRgxoxExiIQgUAbA5K4QqxZEb+Eu7xDKNeywBkuwzWb6rWnSGimgHQ8QHAbQpAGiHAFQ5KwkvQEemgpKGiTYnB6kSe2k3AhEGwwEhwREjke0O2EhSRBwBeqGqEj0HM6RyhviahASaANGDeISaAI8HqsU5hjGL2ve

7ephH2T2FhCpVhIoNhTq/GLhVUThLcY+A+E+7o0O5SsO8Ofhc+OoKOQRy46Os8rR8akRCYTQsR4uB+xyLsuwrw6G5OGR1+3A2wdmeRUMzObinMJR7OZRnO7+3O3m1RwRBydRXaQujRIWYWe+EWUuUW4BMWTyU6X4sBv28BRaCA+wCA2EVu0EHwxAB656SKmgeEYy3pTk2APwnMSE5wO4ZB16FBGKVBJQNBe+bE4AY0m+cAcAUoEM3AnE0AkIGQ86

VI54CwDAhACAzBypXecpehm8m8tIeKIgKUHQw4+gUoGc25RhWu+52Ah5Aox56Qci72W5qp8p32PBB5pAR5J5/EWpmSHhH5X56QZ5jheSK5N5n5d5J5wFrh2pkOAFkF6Q5KXhZpYFt5WQ95+gXQvhCmFG8F6F35WxOxUicIqFEF+F6Q/ESxKxaxWokUeFUAGFAJRxJxjxwQFx15aFDFUFUQpARxn5bAFAkIuAGmXx9FGFM4bI+JWIglIQFMVI0lHF

ZFXF6QUlAl6sts5QmU+5EsWI4on6DJUES2bwy2hEEqy27wK5OlhINQ3AzgFk2gRanMJkd86wuGTRJQRgbABgM5l0BA44qIWwz0wKaJfCnFGFSFKa5SEAWlK5zIJA1F6xdFcVxAUoCAAi6qsVpAJAGibA7SEl5WwQYBDyyOWV1qOUqA763W+AFMvu9IAAFKWAiJfE1Y1dQKgBsLsAAJQKhQnKBRhUiaXKD1XZ58C8A55jWjUdXdUhX0XQUIBYU3Gc

BxGiUr5QlxhZWkxoDvqZAFUc4QHYBEDpVwmQAcAY6/QxnfHgmniwmbIzV2C9AQTZASinVwA5V5WnWaCFXnVbKb7YA3GMDqxeX4A+XugJ6yhpB/WTLyxvgQn6DqWGKZk3LZkdEy7RoGASgQ2LVU6dEnihBHGQ0A1A3PqsTgCvoQB/jhAzkk2sRAA=
```
%%