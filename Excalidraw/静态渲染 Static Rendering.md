静态渲染（Static Rendering）是一种简单而高效的渲染模式，它在构建时就将页面的 HTML 预先生成，然后部署到 CDN 或边缘网络上，从而让用户在请求页面时能够获得极快的响应。下面结合不同场景详细讲解静态渲染的几种实现方式及其优缺点：

静态渲染在 Next.js 中提供了多种实现方式，可以根据具体场景灵活选择：

- **纯静态渲染**：适合内容完全静态、无需动态数据的页面，性能极佳。
- **客户端获取数据的静态渲染**：适用于部分数据需要动态更新的页面，但需要权衡 LCP 与布局稳定性。
- **getStaticProps 静态渲染**：构建时嵌入数据，适合数据更新不频繁且追求极致性能的场景。[[getStaticProps]]
- **ISR 及按需 ISR**：在保证高性能的同时，能够灵活应对大规模页面和数据更新问题，进一步优化了构建时间和实时数据的平衡。[[ISR]]

通过合理选择这些静态渲染模式，开发者可以在提升用户体验的同时，降低服务器成本和运维复杂度，使网站在性能和数据实时性之间达到最佳平衡。

---

### 1. 基础 / 纯静态渲染

- **原理**  
   页面在构建（build）时生成 HTML 文件，后续不会再变化，直到下一次构建。所有用户看到的都是相同的静态内容。
- **优势**
  - **超快响应**：CDN 可以直接缓存并快速返回预生成的 HTML，从而获得极低的 TTFB（首字节时间）。
  - **优秀的性能指标**：由于页面已经预先渲染，浏览器的首次内容绘制（FCP）和最大内容绘制（LCP）时间都能保持较短，而且不存在渲染时的布局偏移问题。
  - **简单部署**：适用于内容不经常更新的页面，例如一些展示型页面或演示页面。
- **缺点**
  - 静态内容一旦生成后无法动态更新，适用于变化不频繁或完全相同内容展示的页面（例如房地产网站的演示页面）。

```js
// pages/index.js
export default function Home() {
  return (
    <div>
      <h1>欢迎访问静态首页</h1>
      <p>这个页面完全在构建时生成，内容静态不变。</p>
    </div>
  );
}
```

## ![[Pasted image 20250314135717.png]]

### 2. 客户端获取数据的静态渲染

- **原理**  
   页面初始 HTML 仍然是静态生成的，但在页面中预留出数据展示的位置（通常是骨架 UI）。页面加载后，客户端通过如 SWR 之类的工具，从 API 路由获取最新数据，再进行数据填充。
- **优势**
  - **较好的首屏加载**：静态页面能快速返回，获得良好的 TTFB 和 FCP 表现。
  - **数据实时性**：数据通过 API 动态获取，能够展示较新的内容。
- **缺点**
  - **LCP 可能延迟**：最大内容绘制（LCP）需要等数据加载完成后才能渲染。
  - **布局偏移风险**：如果骨架 UI 与最终内容尺寸不匹配，可能会引起布局偏移（CLS）。
  - **服务器负载增加**：每个页面请求都要额外调用 API，可能会带来较高的服务器成本。

```js
// pages/client-fetch.js
import { useEffect, useState } from "react";

export default function ClientFetch() {
  const [data, setData] = useState(null);

  useEffect(() => {
    // 假设 pages/api/data.js 提供数据接口
    fetch("/api/data")
      .then((res) => res.json())
      .then((data) => setData(data));
  }, []);

  return (
    <div>
      <h1>客户端数据获取示例</h1>
      {data ? <p>数据：{JSON.stringify(data)}</p> : <p>加载中...</p>}
    </div>
  );
}
```

![[Pasted image 20250314135837.png]]

---

### 3. 使用 getStaticProps 的静态渲染

- **原理**  
   通过 Next.js 提供的 `getStaticProps` 方法，在构建时直接获取数据并生成包含数据的 HTML 页面。这种方法将数据提前嵌入到静态页面中，无需在客户端额外调用 API。
- **优势**
  - **一致性与高性能**：页面直接包含所需数据，用户请求时直接获得完整内容，提升了 TTFB、FCP 和 LCP 的表现。
  - **避免骨架 UI**：用户无需等待数据填充，页面内容直接展现，减少了闪烁或布局变化的风险。
- **缺点**
  - **构建时间长**：当网站有大量页面时（例如博客、商品列表等），每个页面都需要调用数据接口，构建时间可能显著增加。
  - **数据更新频率**：适合数据不频繁变化的场景，若数据更新较快，则需要频繁重建部署。

```js
// pages/static-props.js
export default function StaticPropsPage({ data }) {
  return (
    <div>
      <h1>使用 getStaticProps 静态渲染</h1>
      <p>数据：{data}</p>
    </div>
  );
}

export async function getStaticProps() {
  // 模拟构建时获取数据
  const data = "构建时获取的数据";
  return {
    props: { data },
  };
}
```

![[Pasted image 20250314140001.png]]

---

### 4. 增量静态再生（ISR）

- **原理**  
   ISR 允许部分页面在构建时预先生成，而对于未预生成的页面，则在用户首次请求时按需生成并缓存。通过设置 `revalidate` 字段，页面可在指定时间间隔后自动失效并重新生成。
- **优势**
  - **降低构建压力**：只预生成部分页面，其他页面在首次请求时生成，极大缩短了整体构建时间。
  - **平衡动态数据需求**：既能享受静态渲染的高性能，又能支持定时更新数据，使缓存内容不会过时。
- **缺点**
  - **首次请求体验较差**：对于未预生成的页面，首个用户可能需要等待生成过程，体验会稍差。
  - **频繁重新生成问题**：如果设置的 revalidate 间隔不合适，可能会导致页面不必要地频繁重新生成，增加无服务器函数调用的成本。

```js
// pages/isr.js
export default function ISRPage({ data }) {
  return (
    <div>
      <h1>增量静态再生 (ISR)</h1>
      <p>数据：{data}</p>
    </div>
  );
}

export async function getStaticProps() {
  // 使用当前时间作为示例数据
  const data = "当前时间：" + new Date().toISOString();
  return {
    props: { data },
    // 每 10 秒重新生成一次页面
    revalidate: 10,
  };
}
```

---

### 5. 按需增量静态再生（On-demand ISR）

[[On-demand ISR]]
- **原理**  
   在 ISR 的基础上，按需增量静态再生通过监听特定事件（例如 webhook），当数据源更新时主动调用 `revalidate` 方法，让指定路径的页面立即重新生成，而不是依赖固定时间间隔。
- **优势**
  - **实时性与节省成本**：只在数据真正更新时才触发页面再生，避免不必要的更新，提升用户始终看到最新内容的同时降低服务器资源消耗。
  - **全球一致性**：重新生成后的页面会在整个边缘网络中更新，确保所有用户都能获得最新内容。
- **缺点**
  - **实现复杂度**：需要配置 webhook 或其他触发机制，对于部分项目来说可能增加开发与运维的复杂性。

    ### API 路由：触发页面重新生成

```jsx
// pages/api/revalidate.js

export default async function handler(req, res) {
  // 校验 secret token，确保请求安全

  if (req.query.secret !== process.env.MY_SECRET_TOKEN) {
    return res.status(401).json({ message: "无效的 token" });
  }

  try {
    // 触发 /ondemand 页面重新生成

    await res.revalidate("/ondemand");

    return res.json({ revalidated: true });
  } catch (err) {
    return res.status(500).json({ message: "重新生成失败" });
  }
}
```

### 被按需重新生成的页面

```jsx
// pages/ondemand.js

export default function OnDemandPage({ data }) {
  return (
    <div>
      <h1>按需增量静态再生</h1>

      <p>数据：{data}</p>
    </div>
  );
}

export async function getStaticProps() {
  // 使用当前时间作为示例数据

  const data = "当前时间：" + new Date().toISOString();

  return {
    props: { data },
  };
}
```
