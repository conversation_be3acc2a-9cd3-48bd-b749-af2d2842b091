---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
## Element Links
Nq4nlLfd: [[AI API托管平台]]

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBObR4aOiCEfQQOKGZuAG1wMFAwYogSbggAOQBHABYOfAAZADNiFOLIWERy9M0EYmJcTWC2ksxuZx4ANgAGfhKYcZrp2YLI

ChJ1bgBWZbnISQRCZWlt3dWIa2VhtBX2iGYoUjYAawQAYTZ8NlJygGImgGAkaQTS4bDPZRPIQcYgfL4/CSPazMOC4QJZYEQJqEfD4ADKsGu6EkYI0gUxDyerwA6htJNwAIx7e6PF4IAkwIn3T5lZlQ44ccI5NBM85sVHYNQLEVnO5QmGC5jC1AcIS45lhPrcADsAA4ZnxzowWOwuCLtczjaxOBVOGJGQzJjwGfEtvrdcyhHABlAteaajx4tNdVsA

MzxUMMi3nQjMAAiaV9rTQTQIYWZkOEcAAksRlbkALrMzTCGEAUWCGSy+aL5yIHGe5VyuQAgtnUC2AArZwAaRoBC70AznqAB+UCwXMV9wX7UKn8Onzsx3OJUPl2mBRauGata3dsEIHgY47gotwind9MRO085CfViU9wgAPL2EhOBOptXZbgzuerkFgiHysQACyR7YJIbzWPQoRfmmCB7CUoLgpm0LAaBkgVukmRQNwjxCHBt5/khgFwt8fxAvBhEA

aWxAPhKUrcDMFEQCWpDEEwIFQGBGFVthaC4fhv7Md8bGkCRCLoP8ALAghwlMLRYL0WgNRbEx2LBBwuAZAAarGhBDEu34ILeAC+qzGcyhAwlg5TQQU5kFKekBlBILAACoMpIABa0wANKYp0S7QFg2HMmMaATLqtzzOMWyMec6zEJsIqTAyCTxOlWwBjwoaTKGPAeucBxHCcaA8Cp5yXESUUCKyrxiWRgJScW/7ITC9WIuQHAomiWGYmp+KEoFggeB

StUILSiX0qVGpjRyXLDby5z8pIirKuuJTigpsCMrKJTITmeZ5NuJRNOQGS5twqrqjGVlhegaKYoBq2XWq+AaggU4MjU2qTNqX1LDUlpMNaZqoKGBV3Fapq2hw9poHq0xlQyuoMoadyxgmwRJjhpB4cW1HcVhypXW95xej6n3agGQa6rq31BoDdaWY2EjNm2Hbdqg/bDqO45sJOybTrBzK+pgvHoAA7ezXbttzQ6PZQrnBU2rbtjLXODiOY7Mk0nB

QHihBGEuPDVViesAGKaTi0qoOVdyi1ALZEMooMQMETQhUaTBQOYBBO0crtQOKmJ6FkuCWUwF1oCTzLfEclkEErYvlFLauc3LmK4EIQcAErhIbS78cy9YIAAEocxzi6lZV2eA24XHAcAEkeS6ntABwZOUzsnHMDCEAgFAAEItcRnykRIvxTFPkwjBAu6kOiUDZr6+gElS7xj+JECSY1s/z4vy/pMPRHUe16BIl1qKL3vIgHyv5s4gNnJDTyrS9/vW

GH6vY0TUlvDv7fT+K815sjmi/EaACF5APSDnYQAohSMkgXfdI8lJTbRlFFOegCshf3NhbK2+AbZ20gB/HB989YGyNtwE2SDoH6CTo7Z2rt3aexKKQpewCoikEdgvNgFADi4EFjHAoWCoFkPSGWGELZeH8JCILC4Mib5iI4ekaRTwKCuXgIFQCs8FxPFxAADQYsGbQWwGTTBqJMSYZieA1F1DlXuejPj4AAJrcBRtqbQ0wozLHsZMXU+V4jRhKEYN

gBgbyQwIHhbg0xtA/V+m6OytDxH6FgShZ6LlqKz0hCQShxtME5OIASBAcBti90KUBNgbFJGDGCILIuIjCln0chAQenx5GkGUKCAAFD9agqA+kDMmP02JWwACUmI87KDVA9FyXTcDdMmIDVASz+mrNQKMiZSSRHsJAa8WiPtODE1er3U6mkEB5ysqQSyygIklEyLUqcDSdxEFKXxXGAkSgaU7u8vGYps4lxxnhbZG1NAACsEDYGyHiDScBKnVI0vp

QWhle5gh9owVyYT8B3I6Fo8oYRghQtNCHPcQd6F4ujicus/NXjIuFnWUIjsiUYqxS9XEtcwD2UgGpcIJ5TLGSAA=
```
%%