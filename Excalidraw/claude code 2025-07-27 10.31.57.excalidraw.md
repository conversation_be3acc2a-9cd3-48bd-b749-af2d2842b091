---
excalidraw-plugin: parsed
tags:
  - excalidraw
---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
有一个非常有趣的 CloudCode 功能，叫 CloudAgent，或者说 CloudCode Sub-Agent。 ^wcFsw8Af

一个擅长创建 API 的后端开发专家，包括数据模型、身份验证、可维护的代码、服务和实用程序。遵循Next.js框架的最佳实践，以获得最佳代码实践 (全程使用中文） ^KKuozap6

你是一位资深跨平台浏览器插件工程师，精通 Chrome、Firefox、Safari 及 Manifest V3 规范。
项目背景
我将使用 WXT 框架 + React (TypeScript) 开发浏览器插件，需充分利用 WXT 的数据获取、消息通信、后台脚本与开发规范等能力 
 (全程使用中文） ^asDGRYtz

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBGABZtAAYaOiCEfQQOKGZuAG1wMFAwMogSbggKbAAxZgoADgBBADN0sshYRCqoLCgO8sxuOMbEnniAdhSATkSAZhTEpZT4

hGck/nKYbmdlrcgKEnVuScbJ1IBWGcnL+cSpmYA2RsaDqQRCZWkRy/frZTBbgpd7MKCkNgAawQAGE2Pg2KQqgBiNZopDvTS4bCQ5QQoQcYhwhFIiTg6zMOC4QK5QaQVqEfD4ADKsCBEkEHjpEDBEOhAHVjpIRqDwVCEKyYOz0JzKu98d8OOF8mh4u82FTsGodqqUiDipA8cI4ABJYgq1AFAC671a5GyZu4HCETPehEJWCquB43PxhKVzAtztdBp5

CAQxG48XiPEmiUujSeTz4ocYLHYXDQb1TTFYnAAcpwxFHJkmnksZom3cwACKZPqRtCtAhhTHCQkAUWC2VyQZd+HeQjgxFwDZLdwT80uycu03eRA4kKd/fnbBxEe4zfwrdDfUwAwkgEhzQAAcoAqOUAeumADj1D4Bi2MAIW6oGEIoREtjEBCoQD5SoBfgMAMP+Aa+VH2fYhmmUHIoF/QA0I0AUADABfooDhFfd9UGZLRnFA8DACAGX1KAAFX6KoTw

va97wQl84WQn8ALIkCwNyKC4JoiiP1QzR0LoqBsNtTgoGZQgjHEXh9U6CBWh42pcH0RkdVQP5d36ZoiGUTN0GCVoBneNMoHMAhFK+FToA1bk9FyXB3SYR00GDAdQ0RL53QIfD90Is8r1vB8n0Q5ivz/QDPJfDD6Jg+D/KQli0MCzjuVwIQoDYAAlcJ+ME8EhAQedzIACU+b4D1QeJtB4S5igAXy2UpykqCQAGlqqENgjFwOAnm5bpBOgAj3mGNAN

j1VI9QGwaBvmd4ZPiRphPKI5iBOVVEgmhJrhmGN4lmeZbneSQcp+NAiv+DhAUEyaBDFaFiURFF0TWbksRxI0CVfElenIDhKWpcDuQZJlJWlHl4TlUNeXFQUZuFXbRT5CU2Xa2VI3lYRFWVKN1U1bUoz1d57tNc1ChtUM7UkhBLNQay3Q9br0FweZfXbYgAz7EMRLCDc5pSRpLgWRJNNzDMowmbn0wLItBNW2cYyeaNsxEwha3rFnUC3HcRM0Wmuy

ycCGZskShxHMdVVuKdGnWqYeEWDLF2XRnygRddGwVlt0vk5yJDPQBRk0Af1TAGwlQAvvVQZoAAUTVQO9ADgVQB7r0AAH1AEXlQBkOUANu1f0AUGVABujQAHU0AO2NAEIrQBo9UAQAZAGq4wBfuUAGKzAEHo3PAHvlQAXv0AEqM70AY7lAEAPXPAFhzQBCpUAGJVADztQAKV0AaC9AHi9TDAFcEwAq/XzfptAAK2YQAxC0ANvM70AAHNAGd5LvAFf

Y39AFO5QB24MAdP1V6bjfUAACkAC0U+8Af3ke8AWjlAHDTQBIf5wignLyiA3a932A6D0PI9jhOU4ZxzgXEu5dq51ybq3TuvdB4j3HpPGeC9l5r03rvA+K8j7r1Phfa+98n7cVyHxASIxjqiXEpJaS3A5IiT3FAPSykqhqQ0jmUg2l3D0IMnFOAxkeJmSVKQYmpNbKkHshwRyBEXang9j7P2gdg7h2jvHJOacs55yLmXSutcG7N3bt3fuQ8x4T33N

POei9V4b23vvQ+jdj7nyvrfR+0VYoJSSsQtAqVHYiQXAgbKXwdr5UKsVMoZVigVUgFVSmtYADi8UACaUAjCtXgO1Wh3IKbOAWkNLJeonijW4IsUh01Zr5XiHMbQiRJg3HmE8eYPBGgxmoeULafi8qrVIQCaUpCgZnXhBdCQqIEDohutiXEfpHp9PQOSV6VIaTMJEl9Fk0Mqiw25N0hAINikpiZqdKGUoYb/ThqGBUkh6bI1sqjWA6NSFYzNBaa0t

p7REztkI6W5MvSJBpg9U5VkVyA3DHbepHNoxLFySwvMKlxoC3BYWDgxZVRG3uJcHgDxSEyzrMEPW9ttyePKCrB6asex5EtlrcoOtRzyymBOI2MwEyJHLObJcPyraQBttCO2iscVdAkegQABvKAHozY8gBZeUACKxgBH20ABexgBnPUAA/KgB5W0ABORgALNUAEvGgA3uUAKe6fdAAQer+QAfT6ACwEx8kgITZFzrUQggQxKYFzsyXAzYRGoEAFPK

qAACy1hCCtHCFAVAAA1eYqBAAjkYAYGDMIAB0OCAE8MwAd26ABmAwA9mbhsAIhGgAwHWvqgfkAANXCqAF6oAANSoEStib1J9cLJOZNgERcAoAAEpUDRwVSq1Vv5AAA6YAUUVABgSoASiUe7pqzcHDOO9ABryrnQAEbaAHsDfVgBD+VziHaVgAsQMADTmgA4OWjsGwAkt7fkANlKqBw3YPsXg5+r8qj8qFWKqVcqlVqs1Tqg1RqTUIDNRahAVqbV2

upIQJ1rr3WerBL6/1waw2RtjQmjgKa02ZuzbmgtRbsAlrLYgCtVba31qjo2tVrbO09r7dmu8g6R0TunbOhdK611Bs3TuvddjcGOIIbxZKJC6MSSkvgGSjSuX7g4Ywl9czyhaR0vgLjZIjLvBMlEcyAjnm/JEnZfw4jna8oFSKiVMr0Mau1Xqw1MJjUGEfeay1bBrW2vtZ+51bqOAeq9f+wNIbw3RvjUm1NvbIM5vnvmwtIQ4OnwQxKSthBq11obV

e5t7bu3Of7Xh9Ow6x2TpnXOpdq6o4bu3bujg+6aP4NDDFOKiVWBuNQB482PjtqtMCaVcAeNIBNTgKycl3AKrQC2tkKoI5SBLi2AwQgCAKAACERn3UJOdUk6BkThnDIMCA2ARCzJNH0fQrJIZDcuoMlbE2pukBm3Nvrd0xlLbJC9N6sy1vTfArNrI5rvpLI5Ac47G3TtzYW8DIUIpiiTZO7kM782dk/X2VyDr63NtZHigjE5SNVT/fe1AT7AB5C5Y

0MavYB/d875CWNsYh3dj7c3ag8SIYJHgk03uY6h3N1+QnVI8du4Dr71I6EbbYBQLauApNWyJ9TjshJmj08ZyEO2EBqQQioBj6nXPBc+aqGMibzBK3wnwBm/JZxtASzOGzRMlYkhbIEDLpksSqHzEaAkZYKQio8CKnSyYHWjBsAMPV1MBA0rAm0OceIlwUhPFKsL5H+hgdfLB+gSXHW8QkDx4x17QfiCsgQNwtAjSIDh5dW+BAHPcCaGCHbQrYeRH

jOG2EiAPX4R89IMoLEJ8CcgiEuXsv1BUApG0PMGt3JErKBdNSCXxfcCl5jNXiYfBeBd5r3XhvHvEeQ8e9CWH2lOCaw6wTbIiUPQiIOrbkSOQU9p+4BnkS2AiDR4K6QNK7wxHNfcfvzlEBhBQG8Rv0/w/yh2CnggODzBmRiLgAn98yfU/yw5R14thBGC4TW74DL7lBtTLKZBwa8yiZCBggGDi5MokosprhsqbgOwPIGDMgQGT4qQ/6hj4ChB0KQEA

FAHErlZgAhL0iMjhD1YlQgAlRAA=
```
%%