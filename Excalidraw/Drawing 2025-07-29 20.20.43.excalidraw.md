---
excalidraw-plugin: parsed
tags:
  - excalidraw
---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
梳理 Lucid-bd
Steer 实现
 ^NKJ7PHf6

梳理 Lucid-bd
Steer 实现
 ^fGINB8xV

ghoosty 的 快捷键处理 ^v0uIlkLF

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBGABZtAAYaOiCEfQQOKGZuAG1wMFAwMogSbggAOQBpACkAdgAFAAkAMwA2dLLIWEQqqCwoHvLMbnj4gA44lJ4pzoBmSZ4A

ViWF/nKYCYBOHjjF9cbE1Z5ExN34ztWtyAoSdQn57R5d98nL3bnFi7upBCEZTSbiNW7FSDWZTBbgpf7MKCkNgAawQAGE2Pg2KQqgBieIIAkE0aQTS4bDI5RIoQcYgYrE4iSI6zMOC4QK5EkQdqEfD4ADKsBhEkEHi5CKRqIA6o9JNw+BCIBKUQhBTBhehRZV/tTgRxwvk0PF/mw2dg1DsjSk4YqqcI4ABJYiG1AFAC6/3a5GyTu4HCEfP+hFpWCq

uDSOuEtP1zBd/sDirCCGIE0SnUanX2qymiX+jBY7C4aE6eaYrE41U4Ym4u3T6bOUyDzAAIpkhim0O0CGF/poo8QAKLBbK5OMB/D/IRwYi4dsTMGLRpXTqda6JRr/IgcZF+8ebtgU5PcLv4HuKoaYEYSQDNFoAwF1QABkhObiM5NMQADocQUIJioQB52oADc5fly5AUAAKsMVR3o+z4kG+n7fkMf5ASBnqcFA/KEEY4i8DavTchhABiuD6LylqoOC

BEXlAACCRDKEW6DBO0IylqQUDmAQ9FAkx0Cmlyei5LgwZML6aDxhOirYkCwYEJBl7QfeT4vghX4/ihwFcP8uBCFAbAAErhNhuGIkICCbqJrSAsCV6oPEryrMUAC+WylOUlQSO0ADiDrVAAQlMmAAGpcv0uHQFB/zjGgjSLNojTxO83xLLsixTKsuaKhRywObsiVTCk6UnIsPDxKsVHlA8xBPGgPCLCk2hpe86wrqsRUpf8kg2SCRpHDpHDQrh+Hl

MqqL0tieJEoSSC9uSlLUrSE2MugzIcKy7I5Gxio8nyaoakqmLaomiIqjKNVynV8Knai+0RVqKaRn4kgxi6xrSWaFoTNa/x2lOTouu6nregg4moJJQYhjF6C4PEoH9q9u4JgRSYdqg6WJAsaY/Yq+blkxG642WhaVhw1ZoOlizpem8QKgRhAtm2R6dt2FmKn2NKDsOW1jsj5RTjOc5GguS7XIlSzvQRW47hJe6Klih5oyeZ7UVBN7KXBr7vupyGkA

BWmgZQCl2RAMEqfB2tIb+euodpO0YVhOHcIsJb27kJFkfgFGVX0ww8YxVQsdtBH5px7j+3x+lwIJGEifqpBgxD0mkLJHDyWr6Bm5ralW5paGKrp+lGawTtoGZbNS1ZPV2Q5awuW5iqeeg9ApEIDr4MiD5EWF8ARTRXLQ/lrzvFMjQ5nM8SlWV/zezm2gzGCKSZrTKRj/81W1ejCxNTwosnIVaajz7AJAr1qCnANQ2wtdkropik0SPiM3EnNFJ/Ut

98rdA5DrWyHLB+UXaAohT3SOo9E6t9zqbzpqNG6qoQFVAevDZ6iMjQmk+rAb6I1IB/UdM6QoHodog0TnLemUMww8GQdGA0SMpIo1/GjaYk8VyY3WOxfG3BOgwMgHjEmVZcI8BSJjcqZxzhNlbMEIWqBlYV3KBzWkQ4sg81oZOacs5mb2RFlcMEnQFiE0rtuFR8sDyoiVqzf4/cJDAjYGwBEMBUCABC3VAgBr/UAO7GgA6VMACCat5DYQQzhAaxtj

YCOJcR47xXJ2gOxMs7V2BFInu1IuRbgx8aIR0DggViXJQ5cXwGkpkAl/hCSiKJBOaMk4ERkv4dOikrGSBsXYkJbivE+J0npQyxlS6oHLpZfU1lT410ci5cAhDIRwDgIKdR3B3LQG6tkKoM5SA7i2AwQgCAKD+Xmu/Okn88S/l/KMCA2ARD/wdEMfQgpb7LSms/WaxRDnHK2qcrIGy36LW2QyQYP8Nr/wOUc0gJyzlEV5MA9UoCxTLL+QCrIFyzqy

nlBCh5uQnnnLgXdRBYDfmIqgMigywg9Q0LQXcyFjyzkAHkMEUXiDjcoxKkWAuIokr2ySEX/JJVkIiUTOkuxZVC/Qxs8nMQyQA+5rK6XQqiBxWi/y2AUG6rgMppDIC0uxWcgctIpVIllSENGEB2SasxaKlVWQNUyvAr3KobyDnMGwEiPkAANZJSxtDpl2DmbRY9V4wKVDazE+AACa3BnA8HTM6pKJUFwpDSllcoRgbH6CmbjAg5lYTaESEvXYiRFj

1yJVinFCMCXoEtcsqkJBHYCJGgElOxAfzRzQJVStJAACybBiAIDVbgTQwQzGnlkQ295D9UDuUgP5TEOrSDKDJAACkEXCPCs6Z3UFQI1RYABKLkRllABnZBaiduBp200XWVPgvAD1Lu0KuiA2aaVYphaiclnFOC8zoZAL0pEEBGRDCnQaCaCI5A7V27g3TFTYCILWrppBzL/DTnMsuEHZGQGEFALcpk4NXoQ5oAAVggbAeR+RpzgM21t7bO0aKA+U

cknFGDgRsfgH95RwqIMyDhwsgkhAIgMGagYss+aQAVqY485iiEGH5Exh9TEZGblCHRZjVGaO0KGWAVydBdrhCmc5EAzkgA==
```
%%