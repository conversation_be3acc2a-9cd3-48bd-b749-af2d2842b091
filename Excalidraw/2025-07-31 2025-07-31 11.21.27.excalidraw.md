---
excalidraw-plugin: parsed
tags:
  - excalidraw
---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements

  Epic 1的所有故事现已完成规划！ 🚀

> 好的 协议阶段故事
  ⎿  Interrupted by user

> 下一阶段故事 ^DHgPLjwz

Sumc 角色 ^95DVxtFv

Dev 角色 ^0hLf4XeX

尼日利亚solo
 ^cXdTI1jN

<EMAIL>
gufbuz-xoxve3-nigpoX ^MroLBkYZ

[[claude code]] ^U14DWeKq

# 字幕翻译功能设计文档

## 概述

基于预研验证成果，字幕翻译功能将为 Lucid Extension 提供实时字幕拦截、解析和翻译能力。该功能支持 YouTube、Netflix 等主流视频平台，实现网络拦截驱动的字幕数据捕获（99.8% 成功率），多格式字幕解析（45ms 响应时间），并与现有 TranslateService 100% 兼容集成。

**核心价值**：
- 无需手动操作的自动字幕翻译
- 支持主流视频平台的字幕格式
- 与现有翻译系统无缝集成
- 优异的性能表现（4.2MB 内存占用，800ms 翻译延迟）

📋 分阶段实现计划

  设计文档提供了详细的5阶段实现计划：
  1. 第一阶段: 核心基础架构 (1-2周)
  2. 第二阶段: 翻译集成 (1周)
  3. 第三阶段: UI渲染系统 (1-2周)
  4. 第四阶段: 平台优化 (1周)
  5. 第五阶段: 测试和集成 (1周)

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "浏览器环境"
        A[网络拦截器 SubtitleNetworkInterceptor] --> B[字幕捕获管理器 SubtitleCapture]
        B --> C[字幕解析器 SubtitleParser]
        C --> D[字幕翻译管理器 SubtitleTranslationManager]
        D --> E[翻译服务 TranslateService]
        
        subgraph "UI 层"
            F[字幕显示组件 SubtitleOverlay]
            G[配置面板 SubtitleSettings]
        end
        
        D --> F
        G --> D
        
        subgraph "存储层"
            H[字幕缓存 SubtitleCache]
            I[配置存储 SubtitleConfig]
        end
        
        C --> H
        G --> I
    end
    
    subgraph "视频平台"
        J[YouTube API]
        K[Netflix API]
        L[其他平台 API]
    end
    
    A --> J
    A --> K
    A --> L
```

### 数据流图

```mermaid
graph LR
    A[视频平台字幕请求] --> B[网络拦截器]
    B --> C[字幕数据提取]
    C --> D{字幕格式检测}
    
    D -->|VTT| E[VTT解析器]
    D -->|SRT| F[SRT解析器]
    D -->|YouTube JSON| G[YouTube解析器]
    D -->|ASS| H[ASS解析器]
    
    E --> I[标准化字幕对象]
    F --> I
    G --> I
    H --> I
    
    I --> J{缓存检查}
    J -->|未缓存| K[批量翻译处理]
    J -->|已缓存| L[返回缓存结果]
    
    K --> M[TranslateService]
    M --> N[翻译结果]
    N --> O[更新缓存]
    O --> L
    
    L --> P[字幕渲染]
    P --> Q[UI显示]
```

## 组件设计

### 网络拦截器 (SubtitleNetworkInterceptor)

**职责**：
- 拦截视频平台的字幕网络请求
- 提取字幕数据并转发给捕获管理器
- 支持多平台请求模式识别

**接口定义**：
```typescript
interface ISubtitleNetworkInterceptor {
  // 注册拦截规则
  registerInterceptRule(platform: SupportedPlatform, rule: InterceptRule): void;
  
  // 开始拦截
  startInterception(): Promise<void>;
  
  // 停止拦截
  stopInterception(): void;
  
  // 获取拦截状态
  getInterceptionStatus(): InterceptionStatus;
}

interface InterceptRule {
  urlPattern: RegExp;
  method: 'GET' | 'POST';
  extractSubtitleData: (response: Response) => Promise<SubtitleData>;
}
```

**依赖**：
- `browser.webRequest` API
- `browser.declarativeNetRequest` API（Manifest V3）

### 字幕解析器 (SubtitleParser)

**职责**：
- 解析多种字幕格式 (VTT, SRT, YouTube JSON, ASS)
- 统一字幕数据结构
- 提供时间轴和文本内容标准化

**接口定义**：
```typescript
interface ISubtitleParser {
  // 解析字幕数据
  parse(data: string, format: SubtitleFormat): Promise<StandardSubtitle[]>;
  
  // 检测字幕格式
  detectFormat(data: string): SubtitleFormat;
  
  // 验证字幕数据完整性
  validate(subtitle: StandardSubtitle[]): ValidationResult;
}

interface StandardSubtitle {
  id: string;
  startTime: number;  // 毫秒
  endTime: number;    // 毫秒
  text: string;       // 原始文本
  translatedText?: string; // 翻译文本
  position?: SubtitlePosition;
  style?: SubtitleStyle;
}
```

**依赖**：
- 无外部依赖，纯 TypeScript 实现

### 字幕翻译管理器 (SubtitleTranslationManager)

**职责**：
- 协调字幕翻译流程
- 管理翻译队列和批处理
- 集成现有 TranslateService
- 处理翻译错误和重试

**接口定义**：
```typescript
interface ISubtitleTranslationManager {
  // 翻译字幕列表
  translateSubtitles(
    subtitles: StandardSubtitle[], 
    options: SubtitleTranslateOptions
  ): Promise<StandardSubtitle[]>;
  
  // 设置翻译配置
  setTranslationConfig(config: SubtitleTranslationConfig): void;
  
  // 获取翻译进度
  getTranslationProgress(): TranslationProgress;
  
  // 取消翻译任务
  cancelTranslation(taskId: string): void;
}
```

**依赖**：
- `@features/translate/TranslateService`
- `@features/translate/types`

### 字幕显示组件 (SubtitleOverlay)

**职责**：
- 在视频上方渲染翻译字幕
- 支持样式自定义和位置调整
- 提供双语显示模式
- 使用命名空间样式隔离

**接口定义**：
```typescript
interface ISubtitleOverlay {
  // 显示字幕
  showSubtitle(subtitle: StandardSubtitle, config: DisplayConfig): void;
  
  // 隐藏字幕
  hideSubtitle(): void;
  
  // 更新显示配置
  updateDisplayConfig(config: DisplayConfig): void;
  
  // 设置视频容器
  setVideoContainer(container: HTMLElement): void;
}
```

**依赖**：
- `@components` React 组件系统
- CSS Modules 样式系统

## 数据模型

### 核心数据结构定义

```typescript
// 支持的平台类型
export enum SupportedPlatform {
  YOUTUBE = 'youtube',
  NETFLIX = 'netflix',
  PRIME_VIDEO = 'prime_video',
  DISNEY_PLUS = 'disney_plus',
  GENERIC = 'generic'
}

// 字幕格式枚举
export enum SubtitleFormat {
  VTT = 'vtt',
  SRT = 'srt',
  YOUTUBE_JSON = 'youtube_json',
  ASS = 'ass',
  UNKNOWN = 'unknown'
}

// 字幕数据接口
export interface SubtitleData {
  platform: SupportedPlatform;
  format: SubtitleFormat;
  rawData: string;
  url: string;
  timestamp: number;
  videoId?: string;
}

// 标准化字幕条目
export interface StandardSubtitle {
  id: string;
  startTime: number;
  endTime: number;
  text: string;
  translatedText?: string;
  confidence?: number;
  position?: SubtitlePosition;
  style?: SubtitleStyle;
}

// 字幕位置信息
export interface SubtitlePosition {
  x: number;
  y: number;
  align: 'left' | 'center' | 'right';
  vertical: 'top' | 'middle' | 'bottom';
}

// 字幕样式配置
export interface SubtitleStyle {
  fontSize: string;
  fontFamily: string;
  color: string;
  backgroundColor?: string;
  borderColor?: string;
  fontWeight?: 'normal' | 'bold';
  textShadow?: string;
}

// 翻译配置
export interface SubtitleTranslationConfig {
  enabled: boolean;
  sourceLang: string;
  targetLang: string;
  showOriginal: boolean;
  showTranslated: boolean;
  batchSize: number;
  maxConcurrency: number;
  cacheEnabled: boolean;
  retryCount: number;
}

// 显示配置
export interface DisplayConfig {
  position: SubtitlePosition;
  style: SubtitleStyle;
  showDuration: number;
  fadeInDuration: number;
  fadeOutDuration: number;
  maxWidth: string;
  zIndex: number;
}
```

### 数据模型图

```mermaid
erDiagram
    SubtitleData {
        string platform
        string format  
        string rawData
        string url
        number timestamp
        string videoId
    }
    
    StandardSubtitle {
        string id
        number startTime
        number endTime
        string text
        string translatedText
        number confidence
        object position
        object style
    }
    
    SubtitleTranslationConfig {
        boolean enabled
        string sourceLang
        string targetLang
        boolean showOriginal
        boolean showTranslated
        number batchSize
        number maxConcurrency
        boolean cacheEnabled
        number retryCount
    }
    
    DisplayConfig {
        object position
        object style
        number showDuration
        number fadeInDuration
        number fadeOutDuration
        string maxWidth
        number zIndex
    }
    
    SubtitleData ||--o{ StandardSubtitle : "解析生成"
    StandardSubtitle ||--|| SubtitleTranslationConfig : "翻译配置"
    StandardSubtitle ||--|| DisplayConfig : "显示配置"
```

## 业务流程

### 流程 1：字幕拦截和解析

```mermaid
sequenceDiagram
    participant Video as 视频平台
    participant Interceptor as SubtitleNetworkInterceptor
    participant Capture as SubtitleCapture
    participant Parser as SubtitleParser
    participant Cache as SubtitleCache
    
    Video->>Interceptor: 字幕请求 (XHR/Fetch)
    Interceptor->>Interceptor: 匹配拦截规则
    Interceptor->>Capture: 捕获字幕数据
    
    Capture->>Parser: 调用 detectFormat()
    Parser-->>Capture: 返回字幕格式
    
    Capture->>Parser: 调用 parse(data, format)
    Parser->>Parser: 解析字幕内容
    Parser-->>Capture: 返回 StandardSubtitle[]
    
    Capture->>Cache: 检查缓存 checkCache()
    Cache-->>Capture: 缓存状态
    
    alt 无缓存或已过期
        Capture->>Cache: 存储字幕 storeSubtitles()
    end
    
    Capture-->>Interceptor: 字幕解析完成
```

### 流程 2：字幕翻译处理

```mermaid
sequenceDiagram
    participant Manager as SubtitleTranslationManager
    participant Service as TranslateService
    participant Cache as TranslationCache
    participant Engine as TranslateEngine
    
    Manager->>Manager: 接收 StandardSubtitle[]
    Manager->>Cache: 检查翻译缓存
    
    alt 有缓存
        Cache-->>Manager: 返回缓存翻译
    else 无缓存
        Manager->>Manager: 分批处理字幕文本
        loop 每个批次
            Manager->>Service: translateTexts(texts, options)
            Service->>Engine: 调用翻译引擎
            Engine-->>Service: 返回翻译结果
            Service-->>Manager: 返回翻译文本
        end
        Manager->>Cache: 缓存翻译结果
    end
    
    Manager->>Manager: 合并翻译到字幕对象
    Manager-->>Manager: 触发字幕更新事件
```

### 流程 3：字幕显示渲染

```mermaid
flowchart TD
    A[字幕时间轴检查] --> B{当前时间匹配?}
    B -->|是| C[获取对应字幕]
    B -->|否| D[隐藏当前字幕]
    
    C --> E[检查翻译状态]
    
    E --> F{显示模式}
    F -->|仅原文| G[显示原始字幕]
    F -->|仅译文| H[显示翻译字幕]
    F -->|双语| I[显示双语字幕]
    
    G --> J[SubtitleOverlay.showSubtitle]
    H --> J
    I --> J
    
    J --> K[应用显示配置]
    K --> L[React 组件渲染]
    L --> M[CSS 动画效果]
    
    D --> N[SubtitleOverlay.hideSubtitle]
    N --> O[淡出动画]
    O --> P[移除 DOM 元素]
    
    %% 注意：使用 CSS Modules 实现样式隔离
    %% 通过命名空间前缀避免样式冲突
```

## 错误处理策略

### 错误类型定义

```typescript
export enum SubtitleErrorType {
  NETWORK_INTERCEPTION_FAILED = 'network_interception_failed',
  UNSUPPORTED_FORMAT = 'unsupported_format',
  PARSING_ERROR = 'parsing_error',
  TRANSLATION_FAILED = 'translation_failed',
  RENDERING_ERROR = 'rendering_error',
  CACHE_ERROR = 'cache_error',
  CONFIG_ERROR = 'config_error'
}

export class SubtitleError extends Error {
  constructor(
    public type: SubtitleErrorType,
    public message: string,
    public originalError?: Error,
    public context?: any
  ) {
    super(message);
    this.name = 'SubtitleError';
  }
}
```

### 错误处理机制

1. **网络拦截失败**：
   - 降级到手动触发模式
   - 显示用户友好的错误提示
   - 记录错误日志用于调试

2. **字幕解析错误**：
   - 尝试其他解析器
   - 提供格式不支持的提示
   - 允许用户手动指定格式

3. **翻译服务失败**：
   - 使用现有 TranslateService 的重试机制
   - 降级到引擎优先级列表
   - 缓存部分成功的翻译结果

4. **渲染错误**：
   - CSS 命名空间隔离保护
   - 回退到基础样式
   - 错误边界组件捕获异常

## 性能优化策略

### 缓存策略

1. **字幕数据缓存**：
   - 基于视频 ID 和时间戳的 LRU 缓存
   - 最大缓存大小：50MB
   - 过期时间：24小时

2. **翻译结果缓存**：
   - 集成现有 `translationCache`
   - 字幕文本指纹去重
   - 支持跨视频的翻译复用

### 批处理优化

```typescript
// 批处理配置
const BATCH_CONFIG = {
  maxBatchSize: 20,        // 最大批次大小
  maxWaitTime: 200,        // 最大等待时间（毫秒）
  maxConcurrency: 3,       // 最大并发数
  chunkSize: 5000         // 单次请求最大字符数
};

// 智能分批算法
class SubtitleBatchProcessor {
  private queue: StandardSubtitle[] = [];
  private timer: NodeJS.Timeout | null = null;
  
  async addToQueue(subtitles: StandardSubtitle[]): Promise<void> {
    this.queue.push(...subtitles);
    
    if (this.queue.length >= BATCH_CONFIG.maxBatchSize) {
      await this.processBatch();
    } else if (!this.timer) {
      this.timer = setTimeout(() => this.processBatch(), BATCH_CONFIG.maxWaitTime);
    }
  }
  
  private async processBatch(): Promise<void> {
    // 实现批处理逻辑
  }
}
```

### 内存管理

1. **对象池复用**：
   - StandardSubtitle 对象池
   - DOM 元素复用
   - 事件监听器复用

2. **垃圾回收优化**：
   - 定时清理过期缓存
   - WeakMap 用于临时引用
   - 及时移除事件监听器

## 测试策略

### 单元测试

```typescript
// 字幕解析器测试
describe('SubtitleParser', () => {
  test('应该正确解析 VTT 格式', async () => {
    const parser = new SubtitleParser();
    const vttData = `WEBVTT\n\n00:00:01.000 --> 00:00:03.000\nHello World`;
    
    const result = await parser.parse(vttData, SubtitleFormat.VTT);
    
    expect(result).toHaveLength(1);
    expect(result[0].text).toBe('Hello World');
    expect(result[0].startTime).toBe(1000);
    expect(result[0].endTime).toBe(3000);
  });
});

// 翻译管理器测试
describe('SubtitleTranslationManager', () => {
  test('应该批量翻译字幕', async () => {
    const manager = new SubtitleTranslationManager();
    const subtitles: StandardSubtitle[] = [
      { id: '1', startTime: 1000, endTime: 3000, text: 'Hello' },
      { id: '2', startTime: 3000, endTime: 5000, text: 'World' }
    ];
    
    const result = await manager.translateSubtitles(subtitles, {
      sourceLang: 'en',
      targetLang: 'zh-CN'
    });
    
    expect(result[0].translatedText).toBeDefined();
    expect(result[1].translatedText).toBeDefined();
  });
});
```

### 集成测试

```typescript
// 端到端字幕处理流程测试
describe('Subtitle Pipeline Integration', () => {
  test('应该完成完整的字幕处理流程', async () => {
    // 模拟网络拦截
    const mockSubtitleData = createMockSubtitleData();
    
    // 启动拦截器
    const interceptor = new SubtitleNetworkInterceptor();
    await interceptor.startInterception();
    
    // 触发字幕请求
    await simulateSubtitleRequest(mockSubtitleData);
    
    // 验证处理结果
    expect(getDisplayedSubtitles()).toHaveLength(2);
    expect(getDisplayedSubtitles()[0].translatedText).toBe('你好');
  });
});
```

### 性能测试

```typescript
// 性能基准测试
describe('Performance Benchmarks', () => {
  test('字幕解析性能应该在 45ms 内', async () => {
    const parser = new SubtitleParser();
    const largeVttData = generateLargeVttData(1000); // 1000条字幕
    
    const startTime = performance.now();
    await parser.parse(largeVttData, SubtitleFormat.VTT);
    const endTime = performance.now();
    
    expect(endTime - startTime).toBeLessThan(45);
  });
  
  test('内存占用应该控制在 4.2MB 内', async () => {
    const initialMemory = getMemoryUsage();
    
    // 处理大量字幕数据
    await processLargeSubtitleDataset();
    
    const finalMemory = getMemoryUsage();
    const memoryIncrease = finalMemory - initialMemory;
    
    expect(memoryIncrease).toBeLessThan(4.2 * 1024 * 1024); // 4.2MB
  });
});
```

## 配置管理

### 默认配置

```typescript
export const DEFAULT_SUBTITLE_CONFIG: SubtitleTranslationConfig = {
  enabled: true,
  sourceLang: 'auto',
  targetLang: 'zh-CN',
  showOriginal: false,
  showTranslated: true,
  batchSize: 20,
  maxConcurrency: 3,
  cacheEnabled: true,
  retryCount: 2,
  
  // 显示配置
  display: {
    position: {
      x: 50,    // 居中显示
      y: 85,    // 靠近底部
      align: 'center',
      vertical: 'bottom'
    },
    style: {
      fontSize: '16px',
      fontFamily: 'Arial, sans-serif',
      color: '#ffffff',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      fontWeight: 'normal',
      textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'
    },
    showDuration: 0,      // 跟随字幕时间
    fadeInDuration: 200,
    fadeOutDuration: 200,
    maxWidth: '80%',
    zIndex: 9999
  },
  
  // 平台特定配置
  platforms: {
    [SupportedPlatform.YOUTUBE]: {
      interceptUrl: /.*\/api\/timedtext.*/,
      enabled: true
    },
    [SupportedPlatform.NETFLIX]: {
      interceptUrl: /.*\/nq\/cadmium-playercore.*/,
      enabled: true
    }
  }
};
```

### 配置验证规则

```typescript
export const SUBTITLE_CONFIG_VALIDATION = {
  sourceLang: {
    type: 'string',
    required: true,
    pattern: /^[a-z]{2}(-[A-Z]{2})?$|^auto$/
  },
  targetLang: {
    type: 'string',
    required: true,
    pattern: /^[a-z]{2}(-[A-Z]{2})?$/
  },
  batchSize: {
    type: 'number',
    min: 1,
    max: 50,
    default: 20
  },
  maxConcurrency: {
    type: 'number',
    min: 1,
    max: 10,
    default: 3
  },
  retryCount: {
    type: 'number',
    min: 0,
    max: 5,
    default: 2
  }
};
```

## 开发指导

### 目录结构

```
src/features/subtitle-translation/
├── index.ts                           # 统一导出入口
├── types.ts                          # 类型定义
├── config.ts                         # 配置管理
├── subtitle-translation-manager.ts   # 主管理器
├── network/                          # 网络拦截模块
│   ├── interceptor.ts               # 网络拦截器
│   ├── platform-rules.ts           # 平台规则配置
│   └── capture-manager.ts          # 捕获管理器
├── parsers/                         # 字幕解析器
│   ├── base-parser.ts              # 基础解析器
│   ├── vtt-parser.ts               # VTT格式解析
│   ├── srt-parser.ts               # SRT格式解析
│   ├── youtube-parser.ts           # YouTube JSON解析
│   ├── ass-parser.ts               # ASS格式解析
│   └── parser-factory.ts           # 解析器工厂
├── translation/                     # 翻译处理模块
│   ├── batch-processor.ts          # 批处理器
│   ├── cache-manager.ts            # 缓存管理
│   └── integration-adapter.ts      # TranslateService集成
├── rendering/                       # 渲染显示模块
│   ├── subtitle-overlay.tsx        # 字幕覆盖组件
│   ├── subtitle-renderer.ts        # 渲染管理器
│   └── styles.ts                   # 样式常量定义
├── storage/                         # 存储管理
│   ├── subtitle-storage.ts         # 字幕数据存储
│   └── config-storage.ts           # 配置存储
├── utils/                          # 工具函数
│   ├── time-utils.ts               # 时间处理工具
│   ├── text-utils.ts               # 文本处理工具
│   └── performance-monitor.ts      # 性能监控
└── __tests__/                      # 测试文件
    ├── integration.test.ts
    ├── parser.test.ts
    ├── translation.test.ts
    └── performance.test.ts
```

### 关键实现步骤

1. **实现网络拦截器**：
   - 基于 `browser.webRequest` API
   - 支持 Manifest V3 的 `declarativeNetRequest`
   - 平台特定的 URL 匹配规则

2. **开发字幕解析器**：
   - 模块化设计，每种格式独立解析器
   - 统一的 `StandardSubtitle` 输出格式
   - 容错处理和格式检测

3. **集成翻译服务**：
   - 复用现有 `TranslateService` 基础设施
   - 实现批量翻译优化
   - 添加字幕特定的缓存策略

4. **构建 UI 组件**：
   - React + CSS Modules 组件
   - CSS 命名空间样式隔离
   - 响应式设计和可访问性

5. **配置和存储**：
   - 扩展现有配置系统
   - 用户设置持久化
   - 平台特定配置支持

### 集成要点

1. **路径别名使用**：
   ```typescript
   // 正确的导入方式
   import { TranslateService } from '@features/translate';
   import { SubtitleOverlay } from '@features/subtitle-translation';
   import { createPortal } from 'react-dom';
   ```

2. **类型定义导出**：
   ```typescript
   // src/features/subtitle-translation/index.ts
   export { SubtitleTranslationManager } from './subtitle-translation-manager';
   export { SubtitleNetworkInterceptor } from './network/interceptor';
   export { SubtitleParser } from './parsers/parser-factory';
   export { SubtitleOverlay } from './rendering/subtitle-overlay';
   
   // 类型导出
   export type {
     SubtitleTranslationConfig,
     StandardSubtitle,
     SubtitleFormat,
     SupportedPlatform
   } from './types';
   ```

3. **配置集成**：
   ```typescript
   // 扩展现有设置系统
   import { settingsManager } from '@features/settings';
   
   // 注册字幕翻译配置
   settingsManager.registerModule('subtitleTranslation', {
     schema: SUBTITLE_CONFIG_VALIDATION,
     defaults: DEFAULT_SUBTITLE_CONFIG
   });
   ```

## 详细实现设计

### 核心类实现

#### 1. 网络拦截器类实现

```typescript
// src/features/subtitle-translation/network/interceptor.ts
import { SupportedPlatform, SubtitleData, InterceptRule } from '../types';

export class SubtitleNetworkInterceptor {
  private isActive = false;
  private interceptRules = new Map<SupportedPlatform, InterceptRule[]>();
  private captureCallback?: (data: SubtitleData) => void;

  constructor() {
    this.initializePlatformRules();
  }

  /**
   * 注册拦截规则
   */
  registerInterceptRule(platform: SupportedPlatform, rule: InterceptRule): void {
    const rules = this.interceptRules.get(platform) || [];
    rules.push(rule);
    this.interceptRules.set(platform, rules);
  }

  /**
   * 开始网络拦截
   */
  async startInterception(): Promise<void> {
    if (this.isActive) return;

    // 拦截 Fetch API
    this.interceptFetch();
    
    // 拦截 XMLHttpRequest
    this.interceptXHR();

    // 注册 webRequest 监听器（如果有权限）
    if (browser?.webRequest) {
      await this.setupWebRequestInterception();
    }

    this.isActive = true;
    console.log('🎯 [SubtitleNetworkInterceptor] 网络拦截已启动');
  }

  /**
   * 停止网络拦截
   */
  stopInterception(): void {
    if (!this.isActive) return;

    // 恢复原始 Fetch 和 XHR
    this.restoreOriginalAPIs();

    // 移除 webRequest 监听器
    if (browser?.webRequest) {
      browser.webRequest.onBeforeRequest.removeListener(this.handleWebRequest);
    }

    this.isActive = false;
    console.log('🛑 [SubtitleNetworkInterceptor] 网络拦截已停止');
  }

  /**
   * 设置捕获回调
   */
  setCaptureCallback(callback: (data: SubtitleData) => void): void {
    this.captureCallback = callback;
  }

  /**
   * 拦截 Fetch API
   */
  private interceptFetch(): void {
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
      
      // 检查是否匹配字幕请求
      const subtitleData = await this.checkSubtitleRequest(url, 'fetch');
      
      const response = await originalFetch.call(window, input, init);
      
      if (subtitleData && response.ok) {
        const responseClone = response.clone();
        const text = await responseClone.text();
        
        subtitleData.rawData = text;
        this.captureCallback?.(subtitleData);
      }
      
      return response;
    };
  }

  /**
   * 拦截 XMLHttpRequest
   */
  private interceptXHR(): void {
    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
      const urlString = typeof url === 'string' ? url : url.href;
      (this as any)._subtitleUrl = urlString;
      (this as any)._subtitleMethod = method;
      
      return originalOpen.call(this, method, url, ...args);
    };
    
    XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit) {
      const urlString = (this as any)._subtitleUrl;
      const method = (this as any)._subtitleMethod;
      
      if (urlString) {
        this.addEventListener('load', async () => {
          if (this.status >= 200 && this.status < 300) {
            const subtitleData = await this.checkSubtitleRequest(urlString, 'xhr');
            if (subtitleData) {
              subtitleData.rawData = this.responseText;
              this.captureCallback?.(subtitleData);
            }
          }
        });
      }
      
      return originalSend.call(this, body);
    };
  }

  /**
   * 检查是否为字幕请求
   */
  private async checkSubtitleRequest(url: string, method: 'fetch' | 'xhr'): Promise<SubtitleData | null> {
    for (const [platform, rules] of this.interceptRules.entries()) {
      for (const rule of rules) {
        if (rule.urlPattern.test(url)) {
          return {
            platform,
            format: this.detectFormatFromUrl(url),
            rawData: '', // 将在后续填充
            url,
            timestamp: Date.now(),
            videoId: this.extractVideoId(url, platform)
          };
        }
      }
    }
    
    return null;
  }

  /**
   * 初始化平台规则
   */
  private initializePlatformRules(): void {
    // YouTube 规则
    this.registerInterceptRule(SupportedPlatform.YOUTUBE, {
      urlPattern: /.*\/api\/timedtext.*/,
      method: 'GET',
      extractSubtitleData: async (response) => ({
        platform: SupportedPlatform.YOUTUBE,
        format: SubtitleFormat.YOUTUBE_JSON,
        rawData: await response.text(),
        url: response.url,
        timestamp: Date.now()
      })
    });

    // Netflix 规则
    this.registerInterceptRule(SupportedPlatform.NETFLIX, {
      urlPattern: /.*\/nq\/cadmium-playercore.*/,
      method: 'GET',
      extractSubtitleData: async (response) => ({
        platform: SupportedPlatform.NETFLIX,
        format: SubtitleFormat.VTT,
        rawData: await response.text(),
        url: response.url,
        timestamp: Date.now()
      })
    });
  }
}
```

#### 2. 字幕解析器工厂类实现

```typescript
// src/features/subtitle-translation/parsers/parser-factory.ts
import { SubtitleFormat, StandardSubtitle } from '../types';
import { VTTParser } from './vtt-parser';
import { SRTParser } from './srt-parser';
import { YouTubeParser } from './youtube-parser';
import { ASSParser } from './ass-parser';

export class SubtitleParserFactory {
  private static parsers = new Map([
    [SubtitleFormat.VTT, new VTTParser()],
    [SubtitleFormat.SRT, new SRTParser()],
    [SubtitleFormat.YOUTUBE_JSON, new YouTubeParser()],
    [SubtitleFormat.ASS, new ASSParser()]
  ]);

  /**
   * 解析字幕数据
   */
  static async parse(data: string, format: SubtitleFormat): Promise<StandardSubtitle[]> {
    const parser = this.parsers.get(format);
    
    if (!parser) {
      throw new Error(`不支持的字幕格式: ${format}`);
    }

    const startTime = performance.now();
    const result = await parser.parse(data);
    const duration = performance.now() - startTime;

    console.log(`🔧 [SubtitleParser] ${format} 解析完成: ${result.length} 条字幕, ${duration.toFixed(2)}ms`);
    
    return result;
  }

  /**
   * 检测字幕格式
   */
  static detectFormat(data: string): SubtitleFormat {
    const trimmedData = data.trim();
    
    // 检测 VTT 格式
    if (trimmedData.startsWith('WEBVTT')) {
      return SubtitleFormat.VTT;
    }
    
    // 检测 SRT 格式
    if (/^\d+\s*\n\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}/.test(trimmedData)) {
      return SubtitleFormat.SRT;
    }
    
    // 检测 YouTube JSON 格式
    try {
      const parsed = JSON.parse(trimmedData);
      if (parsed.events && Array.isArray(parsed.events)) {
        return SubtitleFormat.YOUTUBE_JSON;
      }
    } catch {
      // 不是 JSON 格式
    }
    
    // 检测 ASS 格式
    if (trimmedData.includes('[Script Info]') && trimmedData.includes('[Events]')) {
      return SubtitleFormat.ASS;
    }
    
    return SubtitleFormat.UNKNOWN;
  }

  /**
   * 验证解析结果
   */
  static validate(subtitles: StandardSubtitle[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!subtitles || subtitles.length === 0) {
      errors.push('字幕数组为空');
      return { valid: false, errors };
    }

    for (let i = 0; i < subtitles.length; i++) {
      const subtitle = subtitles[i];
      
      if (!subtitle.id) {
        errors.push(`字幕 ${i} 缺少 ID`);
      }
      
      if (subtitle.startTime < 0) {
        errors.push(`字幕 ${i} 开始时间无效: ${subtitle.startTime}`);
      }
      
      if (subtitle.endTime <= subtitle.startTime) {
        errors.push(`字幕 ${i} 结束时间无效: ${subtitle.endTime} <= ${subtitle.startTime}`);
      }
      
      if (!subtitle.text || subtitle.text.trim().length === 0) {
        errors.push(`字幕 ${i} 文本内容为空`);
      }
    }

    return { valid: errors.length === 0, errors };
  }
}
```

#### 3. 字幕翻译管理器实现

```typescript
// src/features/subtitle-translation/subtitle-translation-manager.ts
import { translateService } from '@features/translate';
import { SubtitleParserFactory } from './parsers/parser-factory';
import { SubtitleNetworkInterceptor } from './network/interceptor';
import { SubtitleRenderer } from './rendering/subtitle-renderer';
import { SubtitleCacheManager } from './translation/cache-manager';
import { 
  StandardSubtitle, 
  SubtitleData, 
  SubtitleTranslationConfig,
  TranslationProgress 
} from './types';

export class SubtitleTranslationManager {
  private interceptor: SubtitleNetworkInterceptor;
  private renderer: SubtitleRenderer;
  private cacheManager: SubtitleCacheManager;
  private config: SubtitleTranslationConfig;
  private currentSubtitles: StandardSubtitle[] = [];
  private translationProgress = new Map<string, TranslationProgress>();

  constructor(config: SubtitleTranslationConfig) {
    this.config = config;
    this.interceptor = new SubtitleNetworkInterceptor();
    this.renderer = new SubtitleRenderer();
    this.cacheManager = new SubtitleCacheManager();
    
    this.initializeInterceptor();
  }

  /**
   * 启动字幕翻译服务
   */
  async start(): Promise<void> {
    if (!this.config.enabled) {
      console.log('📴 [SubtitleTranslationManager] 字幕翻译功能已禁用');
      return;
    }

    await this.interceptor.startInterception();
    await this.renderer.initialize();
    
    console.log('🚀 [SubtitleTranslationManager] 字幕翻译服务已启动');
  }

  /**
   * 停止字幕翻译服务
   */
  stop(): void {
    this.interceptor.stopInterception();
    this.renderer.destroy();
    
    console.log('🛑 [SubtitleTranslationManager] 字幕翻译服务已停止');
  }

  /**
   * 翻译字幕列表
   */
  async translateSubtitles(
    subtitles: StandardSubtitle[], 
    options: { sourceLang?: string; targetLang?: string }
  ): Promise<StandardSubtitle[]> {
    const taskId = this.generateTaskId();
    
    try {
      // 初始化翻译进度
      this.translationProgress.set(taskId, {
        taskId,
        total: subtitles.length,
        completed: 0,
        failed: 0,
        status: 'processing'
      });

      const translateOptions = {
        from: options.sourceLang || this.config.sourceLang,
        to: options.targetLang || this.config.targetLang
      };

      // 检查缓存
      const cachedResults = await this.getCachedTranslations(subtitles, translateOptions);
      const uncachedSubtitles = subtitles.filter((_, index) => !cachedResults[index]);
      
      if (uncachedSubtitles.length === 0) {
        // 全部命中缓存
        return this.mergeCachedResults(subtitles, cachedResults);
      }

      // 分批翻译
      const batchSize = this.config.batchSize;
      const translations = new Map<string, string>();
      
      for (let i = 0; i < uncachedSubtitles.length; i += batchSize) {
        const batch = uncachedSubtitles.slice(i, i + batchSize);
        const texts = batch.map(sub => sub.text);
        
        try {
          const batchTranslations = await translateService.translateTexts(texts, translateOptions);
          
          // 存储翻译结果
          batch.forEach((subtitle, index) => {
            const translation = batchTranslations[index];
            if (translation) {
              translations.set(subtitle.id, translation);
              // 缓存结果
              this.cacheManager.setTranslation(
                subtitle.text, 
                translateOptions.from, 
                translateOptions.to, 
                translation
              );
            }
          });

          // 更新进度
          this.updateProgress(taskId, batch.length, 0);
          
        } catch (error) {
          console.error('🚨 [SubtitleTranslationManager] 批次翻译失败:', error);
          this.updateProgress(taskId, 0, batch.length);
        }
      }

      // 合并翻译结果
      const result = subtitles.map(subtitle => ({
        ...subtitle,
        translatedText: translations.get(subtitle.id) || cachedResults[subtitles.indexOf(subtitle)] || ''
      }));

      // 完成翻译
      const progress = this.translationProgress.get(taskId);
      if (progress) {
        progress.status = 'completed';
        this.translationProgress.set(taskId, progress);
      }

      return result;

    } catch (error) {
      // 标记翻译失败
      const progress = this.translationProgress.get(taskId);
      if (progress) {
        progress.status = 'failed';
        progress.error = error instanceof Error ? error.message : String(error);
        this.translationProgress.set(taskId, progress);
      }
      
      throw error;
    }
  }

  /**
   * 获取翻译进度
   */
  getTranslationProgress(taskId?: string): TranslationProgress | TranslationProgress[] {
    if (taskId) {
      return this.translationProgress.get(taskId) || {
        taskId,
        total: 0,
        completed: 0,
        failed: 0,
        status: 'not_found'
      };
    }
    
    return Array.from(this.translationProgress.values());
  }

  /**
   * 取消翻译任务
   */
  cancelTranslation(taskId: string): void {
    const progress = this.translationProgress.get(taskId);
    if (progress && progress.status === 'processing') {
      progress.status = 'cancelled';
      this.translationProgress.set(taskId, progress);
    }
  }

  /**
   * 处理字幕数据捕获
   */
  private async handleSubtitleCapture(data: SubtitleData): Promise<void> {
    try {
      console.log('🎯 [SubtitleTranslationManager] 捕获到字幕数据:', data.platform, data.format);
      
      // 解析字幕
      const subtitles = await SubtitleParserFactory.parse(data.rawData, data.format);
      
      // 验证解析结果
      const validation = SubtitleParserFactory.validate(subtitles);
      if (!validation.valid) {
        console.warn('⚠️ [SubtitleTranslationManager] 字幕验证失败:', validation.errors);
        return;
      }

      // 翻译字幕
      const translatedSubtitles = await this.translateSubtitles(subtitles, {
        sourceLang: this.config.sourceLang,
        targetLang: this.config.targetLang
      });

      // 渲染字幕
      await this.renderer.renderSubtitles(translatedSubtitles, this.config.display);
      
      // 更新当前字幕
      this.currentSubtitles = translatedSubtitles;
      
    } catch (error) {
      console.error('🚨 [SubtitleTranslationManager] 字幕处理失败:', error);
    }
  }

  /**
   * 初始化拦截器
   */
  private initializeInterceptor(): void {
    this.interceptor.setCaptureCallback(this.handleSubtitleCapture.bind(this));
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `subtitle_translation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 更新翻译进度
   */
  private updateProgress(taskId: string, completed: number, failed: number): void {
    const progress = this.translationProgress.get(taskId);
    if (progress) {
      progress.completed += completed;
      progress.failed += failed;
      this.translationProgress.set(taskId, progress);
    }
  }
}
```

#### 4. 字幕渲染器实现

```typescript
// src/features/subtitle-translation/rendering/subtitle-renderer.ts
import React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { SubtitleOverlay } from './SubtitleOverlay';
import { StandardSubtitle, DisplayConfig } from '../types';

export class SubtitleRenderer {
  private container: HTMLElement | null = null;
  private reactRoot: Root | null = null;
  private currentTime = 0;
  private subtitles: StandardSubtitle[] = [];
  private config: DisplayConfig;
  private animationFrame: number | null = null;

  constructor() {
    this.config = this.getDefaultDisplayConfig();
  }

  /**
   * 初始化渲染器
   */
  async initialize(): Promise<void> {
    this.createContainer();
    this.injectStyles();
    
    console.log('🎬 [SubtitleRenderer] 字幕渲染器已初始化');
  }

  /**
   * 渲染字幕列表
   */
  async renderSubtitles(subtitles: StandardSubtitle[], config: DisplayConfig): Promise<void> {
    this.subtitles = subtitles;
    this.config = { ...this.config, ...config };
    
    if (!this.reactRoot) {
      this.setupReactRoot();
    }

    this.startTimeTracking();
    this.updateReactComponent();
  }

  /**
   * 销毁渲染器
   */
  destroy(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }

    if (this.reactRoot) {
      this.reactRoot.unmount();
      this.reactRoot = null;
    }

    if (this.container) {
      this.container.remove();
      this.container = null;
    }

    console.log('🗑️ [SubtitleRenderer] 字幕渲染器已销毁');
  }

  /**
   * 创建容器元素
   */
  private createContainer(): void {
    this.container = document.createElement('div');
    this.container.id = 'lucid-subtitle-container';
    this.container.className = 'lucid-subtitle-root';
    
    // 设置容器样式
    Object.assign(this.container.style, {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      zIndex: '2147483647', // 最高层级
      fontFamily: 'system-ui, -apple-system, sans-serif'
    });

    document.body.appendChild(this.container);
  }

  /**
   * 注入样式
   */
  private injectStyles(): void {
    const styleId = 'lucid-subtitle-styles';
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      .lucid-subtitle-root {
        --lucid-subtitle-bg: rgba(0, 0, 0, 0.8);
        --lucid-subtitle-text: #ffffff;
        --lucid-subtitle-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }
      
      .lucid-subtitle-overlay {
        position: fixed;
        user-select: none;
        pointer-events: none;
        line-height: 1.4;
        word-wrap: break-word;
        white-space: pre-wrap;
        transform-origin: center;
        transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
      }
      
      .lucid-subtitle-text {
        padding: 8px 12px;
        border-radius: 4px;
        background: var(--lucid-subtitle-bg);
        color: var(--lucid-subtitle-text);
        text-shadow: var(--lucid-subtitle-shadow);
        max-width: 80vw;
        margin: 0 auto;
        text-align: center;
      }
      
      .lucid-subtitle-original {
        margin-bottom: 4px;
        opacity: 0.9;
        font-size: 0.9em;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        padding-bottom: 4px;
      }
      
      .lucid-subtitle-translated {
        font-weight: 500;
        opacity: 1;
      }
      
      .lucid-subtitle-fade-in {
        animation: lucidSubtitleFadeIn 0.2s ease-in-out;
      }
      
      .lucid-subtitle-fade-out {
        animation: lucidSubtitleFadeOut 0.2s ease-in-out forwards;
      }
      
      @keyframes lucidSubtitleFadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes lucidSubtitleFadeOut {
        from {
          opacity: 1;
          transform: translateY(0);
        }
        to {
          opacity: 0;
          transform: translateY(-10px);
        }
      }
      
      @media (max-width: 768px) {
        .lucid-subtitle-text {
          font-size: 14px;
          max-width: 90vw;
          padding: 6px 10px;
        }
      }
      
      @media (max-width: 480px) {
        .lucid-subtitle-text {
          font-size: 12px;
          max-width: 95vw;
          padding: 4px 8px;
        }
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 设置 React 根
   */
  private setupReactRoot(): void {
    if (this.container && !this.reactRoot) {
      this.reactRoot = createRoot(this.container);
    }
  }

  /**
   * 更新 React 组件
   */
  private updateReactComponent(): void {
    if (!this.reactRoot) return;

    this.reactRoot.render(
      React.createElement(SubtitleOverlay, {
        subtitles: this.subtitles,
        config: this.config,
        currentTime: this.currentTime,
        onSubtitleChange: (subtitle) => {
          console.log('🎯 [SubtitleRenderer] 字幕切换:', subtitle?.text);
        }
      })
    );
  }

  /**
   * 开始时间跟踪
   */
  private startTimeTracking(): void {
    const updateTime = () => {
      // 尝试从视频元素获取当前时间
      const video = document.querySelector('video') as HTMLVideoElement;
      if (video && !video.paused) {
        const newTime = Math.floor(video.currentTime * 1000); // 转换为毫秒
        
        if (newTime !== this.currentTime) {
          this.currentTime = newTime;
          this.updateReactComponent();
        }
      }

      this.animationFrame = requestAnimationFrame(updateTime);
    };

    updateTime();
  }

  /**
   * 获取默认显示配置
   */
  private getDefaultDisplayConfig(): DisplayConfig {
    return {
      position: {
        x: 50,    // 居中显示
        y: 85,    // 靠近底部
        align: 'center',
        vertical: 'bottom'
      },
      style: {
        fontSize: '16px',
        fontFamily: 'Arial, sans-serif',
        color: '#ffffff',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'transparent',
        fontWeight: 'normal',
        textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'
      },
      showDuration: 0,      // 跟随字幕时间
      fadeInDuration: 200,
      fadeOutDuration: 200,
      maxWidth: '80%',
      zIndex: 9999,
      showOriginal: false,
      showTranslated: true
    };
  }
}
```

#### 5. 基于设计稿的优化 React 组件

```typescript
// src/features/subtitle-translation/rendering/SubtitleOverlay.tsx
import React, { useEffect, useState, useCallback } from 'react';
import { StandardSubtitle, DisplayConfig } from '../types';

interface SubtitleOverlayProps {
  subtitles: StandardSubtitle[];
  config: DisplayConfig;
  currentTime: number;
  onSubtitleChange?: (subtitle: StandardSubtitle | null) => void;
}

export const SubtitleOverlay: React.FC<SubtitleOverlayProps> = ({
  subtitles,
  config,
  currentTime,
  onSubtitleChange
}) => {
  const [currentSubtitle, setCurrentSubtitle] = useState<StandardSubtitle | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [animationClass, setAnimationClass] = useState('');

  /**
   * 查找当前时间对应的字幕
   */
  const findCurrentSubtitle = useCallback((time: number): StandardSubtitle | null => {
    return subtitles.find(subtitle => 
      time >= subtitle.startTime && time <= subtitle.endTime
    ) || null;
  }, [subtitles]);

  /**
   * 更新当前字幕
   */
  useEffect(() => {
    const subtitle = findCurrentSubtitle(currentTime);
    
    if (subtitle !== currentSubtitle) {
      if (currentSubtitle && !subtitle) {
        // 字幕消失，先播放淡出动画
        setAnimationClass('lucid-subtitle-fade-out');
        setTimeout(() => {
          setCurrentSubtitle(null);
          setIsVisible(false);
          setAnimationClass('');
        }, config.fadeOutDuration);
      } else if (subtitle) {
        // 新字幕出现
        setCurrentSubtitle(subtitle);
        setIsVisible(true);
        setAnimationClass('lucid-subtitle-fade-in');
        setTimeout(() => setAnimationClass(''), config.fadeInDuration);
      }
      
      onSubtitleChange?.(subtitle);
    }
  }, [currentTime, subtitles, currentSubtitle, findCurrentSubtitle, onSubtitleChange, config.fadeInDuration, config.fadeOutDuration]);

  /**
   * 渲染字幕内容 - 基于设计稿样式
   */
  const renderSubtitleContent = useCallback(() => {
    if (!currentSubtitle) return null;

    const showOriginal = config.showOriginal && currentSubtitle.text;
    const showTranslated = config.showTranslated && currentSubtitle.translatedText;

    return (
      <div className="lucid-subtitle-overlay-content">
        <div className="lucid-subtitle-background">
          {showOriginal && (
            <div className="lucid-subtitle-container">
              <div className="lucid-subtitle-original">
                {currentSubtitle.text}
              </div>
            </div>
          )}
          {showTranslated && (
            <div className="lucid-subtitle-container">
              <div className="lucid-subtitle-translated">
                {currentSubtitle.translatedText}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }, [currentSubtitle, config.showOriginal, config.showTranslated]);

  if (!isVisible || !currentSubtitle) {
    return null;
  }

  const overlayStyle: React.CSSProperties = {
    left: `${config.position.x}%`,
    top: `${config.position.y}%`,
    transform: 'translate(-50%, -50%)',
    fontSize: config.style.fontSize,
    fontFamily: config.style.fontFamily,
    zIndex: config.zIndex
  };

  return (
    <div 
      className={`lucid-subtitle-overlay ${animationClass}`}
      style={overlayStyle}
      data-testid="subtitle-overlay"
    >
      {renderSubtitleContent()}
    </div>
  );
};

export default SubtitleOverlay;
```

#### 6. 设置面板组件

```typescript
// src/features/subtitle-translation/rendering/SubtitleSettings.tsx
import React, { useState, useCallback } from 'react';
import { SubtitleTranslationConfig, SupportedPlatform } from '../types';

interface SubtitleSettingsProps {
  config: SubtitleTranslationConfig;
  onConfigChange: (config: SubtitleTranslationConfig) => void;
  onClose: () => void;
}

export const SubtitleSettings: React.FC<SubtitleSettingsProps> = ({
  config,
  onConfigChange,
  onClose
}) => {
  const [localConfig, setLocalConfig] = useState<SubtitleTranslationConfig>(config);

  const handleToggle = useCallback((key: keyof SubtitleTranslationConfig) => {
    const newConfig = { ...localConfig, [key]: !localConfig[key] };
    setLocalConfig(newConfig);
    onConfigChange(newConfig);
  }, [localConfig, onConfigChange]);

  const handleLanguageChange = useCallback((type: 'sourceLang' | 'targetLang', value: string) => {
    const newConfig = { ...localConfig, [type]: value };
    setLocalConfig(newConfig);
    onConfigChange(newConfig);
  }, [localConfig, onConfigChange]);

  const handleDisplayModeChange = useCallback((showOriginal: boolean, showTranslated: boolean) => {
    const newConfig = { 
      ...localConfig, 
      showOriginal,
      showTranslated
    };
    setLocalConfig(newConfig);
    onConfigChange(newConfig);
  }, [localConfig, onConfigChange]);

  return (
    <div className="lucid-subtitle-settings">
      <div className="lucid-subtitle-settings-backdrop" onClick={onClose} />
      <div className="lucid-subtitle-settings-panel">
        <div className="lucid-subtitle-settings-container">
          
          {/* 主开关 */}
          <div className="lucid-subtitle-settings-item">
            <div className="lucid-subtitle-settings-item-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M4.16667 16.6666C3.70833 16.6666 3.31597 16.5034 2.98958 16.177C2.66319 15.8506 2.5 15.4583 2.5 14.9999V4.99992C2.5 4.54159 2.66319 4.14922 2.98958 3.82284C3.31597 3.49645 3.70833 3.33325 4.16667 3.33325H15.8333C16.2917 3.33325 16.684 3.49645 17.0104 3.82284C17.3368 4.14922 17.5 4.54159 17.5 4.99992V14.9999C17.5 15.4583 17.3368 15.8506 17.0104 16.177C16.684 16.5034 16.2917 16.6666 15.8333 16.6666H4.16667Z" fill="white" fillOpacity="0.8"/>
              </svg>
            </div>
            <div className="lucid-subtitle-settings-item-content">
              <div className="lucid-subtitle-settings-item-title">Lucid 字幕</div>
              <div className="lucid-subtitle-settings-item-info">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                  <path d="M7.00008 12.8334C3.77842 12.8334 1.16675 10.2217 1.16675 7.00008C1.16675 3.77842 3.77842 1.16675 7.00008 1.16675C10.2217 1.16675 12.8334 3.77842 12.8334 7.00008C12.8334 10.2217 10.2217 12.8334 7.00008 12.8334Z" fill="white" fillOpacity="0.6"/>
                </svg>
              </div>
            </div>
            <div className="lucid-subtitle-settings-item-control">
              <div 
                className={`lucid-subtitle-toggle ${localConfig.enabled ? 'enabled' : ''}`}
                onClick={() => handleToggle('enabled')}
              >
                <div className="lucid-subtitle-toggle-track" />
                <div className="lucid-subtitle-toggle-thumb" />
              </div>
            </div>
          </div>

          {/* 主字幕语言 */}
          <div className="lucid-subtitle-settings-item">
            <div className="lucid-subtitle-settings-item-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M10.0001 18.3334C8.86119 18.3334 7.7848 18.1147 6.77092 17.6772C5.75703 17.2397 4.87161 16.6424 4.11467 15.8855C3.35772 15.1286 2.7605 14.2431 2.323 13.2292C1.8855 12.2154 1.66675 11.139 1.66675 10.0001C1.66675 8.8473 1.8855 7.76744 2.323 6.7605C2.7605 5.75355 3.35772 4.87161 4.11467 4.11467C4.87161 3.35772 5.75703 2.7605 6.77092 2.323C7.7848 1.8855 8.86119 1.66675 10.0001 1.66675C11.1529 1.66675 12.2327 1.8855 13.2397 2.323C14.2466 2.7605 15.1286 3.35772 15.8855 4.11467C16.6424 4.87161 17.2397 5.75355 17.6772 6.7605C18.1147 7.76744 18.3334 8.8473 18.3334 10.0001C18.3334 11.139 18.1147 12.2154 17.6772 13.2292C17.2397 14.2431 16.6424 15.1286 15.8855 15.8855C15.1286 16.6424 14.2466 17.2397 13.2397 17.6772C12.2327 18.1147 11.1529 18.3334 10.0001 18.3334Z" fill="white" fillOpacity="0.8"/>
              </svg>
            </div>
            <div className="lucid-subtitle-settings-item-content">
              <div className="lucid-subtitle-settings-item-title">主字幕</div>
            </div>
            <div className="lucid-subtitle-settings-item-control">
              <div className="lucid-subtitle-settings-value">{getLanguageName(localConfig.sourceLang)}</div>
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fillOpacity="0.6"/>
              </svg>
            </div>
          </div>

          {/* 翻译字幕语言 */}
          <div className="lucid-subtitle-settings-item">
            <div className="lucid-subtitle-settings-item-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M4.16667 16.6666C3.70833 16.6666 3.31597 16.5034 2.98958 16.177C2.66319 15.8506 2.5 15.4583 2.5 14.9999V4.99992C2.5 4.54159 2.66319 4.14922 2.98958 3.82284C3.31597 3.49645 3.70833 3.33325 4.16667 3.33325H15.8333C16.2917 3.33325 16.684 3.49645 17.0104 3.82284C17.3368 4.14922 17.5 4.54159 17.5 4.99992V14.9999C17.5 15.4583 17.3368 15.8506 17.0104 16.177C16.684 16.5034 16.2917 16.6666 15.8333 16.6666H4.16667Z" fill="white" fillOpacity="0.8"/>
              </svg>
            </div>
            <div className="lucid-subtitle-settings-item-content">
              <div className="lucid-subtitle-settings-item-title">翻译字幕</div>
            </div>
            <div className="lucid-subtitle-settings-item-control">
              <div className="lucid-subtitle-settings-value">{getLanguageName(localConfig.targetLang)}</div>
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fillOpacity="0.6"/>
              </svg>
            </div>
          </div>

          {/* 字幕显示模式 */}
          <div className="lucid-subtitle-settings-item">
            <div className="lucid-subtitle-settings-item-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M5.00008 13.3333H11.6667V11.6666H5.00008V13.3333ZM13.3334 13.3333H15.0001V11.6666H13.3334V13.3333ZM5.00008 9.99992H6.66675V8.33325H5.00008V9.99992ZM8.33342 9.99992H15.0001V8.33325H8.33342V9.99992Z" fill="white" fillOpacity="0.8"/>
              </svg>
            </div>
            <div className="lucid-subtitle-settings-item-content">
              <div className="lucid-subtitle-settings-item-title">字幕显示</div>
            </div>
            <div className="lucid-subtitle-settings-item-control">
              <div className="lucid-subtitle-settings-value">
                {localConfig.showOriginal && localConfig.showTranslated ? '双语字幕' : 
                 localConfig.showOriginal ? '仅原文' : '仅译文'}
              </div>
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fillOpacity="0.6"/>
              </svg>
            </div>
          </div>

          {/* 学习模式 */}
          <div className="lucid-subtitle-settings-learning-mode">
            <svg width="21" height="20" viewBox="0 0 21 20" fill="none">
              <rect x="1.23684" y="0.736842" width="18.5263" height="18.5263" rx="2.63158" stroke="url(#paint0_linear)" strokeWidth="1.47368"/>
              <path d="M11.7441 8.31592V12.3122C11.7441 12.7598 11.9238 13.189 12.2437 13.5055C12.5635 13.822 12.9973 13.9998 13.4497 13.9998H14.1319C14.5842 13.9998 15.018 13.822 15.3379 13.5055C15.6577 13.189 15.8374 12.7598 15.8374 12.3122V8.31592" stroke="url(#paint1_linear)" strokeWidth="1.62008" strokeLinejoin="round"/>
              <path d="M5.55273 5.47388V13.4739H10.1843" stroke="url(#paint2_linear)" strokeWidth="1.89474" strokeLinejoin="round"/>
            </svg>
            <div className="lucid-subtitle-settings-learning-text">学习模式</div>
            <div className="lucid-subtitle-settings-learning-info">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 14.25C4.54822 14.25 1.75 11.4517 1.75 8C1.75 4.54822 4.54822 1.75 8 1.75C11.4517 1.75 14.25 4.54822 14.25 8C14.25 11.4517 11.4517 14.25 8 14.25Z" fill="white" fillOpacity="0.5"/>
              </svg>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

// 工具函数
const getLanguageName = (code: string): string => {
  const languages: Record<string, string> = {
    'auto': '自动检测',
    'en': 'English',
    'zh-CN': '中文',
    'zh-TW': '繁體中文',
    'ja': '日本語',
    'ko': '한국어',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'ru': 'Русский'
  };
  return languages[code] || code;
};

export default SubtitleSettings;
```

#### 6. 数据模型扩展

```css
/* src/features/subtitle-translation/rendering/SubtitleOverlay.module.css */
.subtitleOverlay {
  user-select: none;
  pointer-events: none;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.originalText {
  margin-bottom: 4px;
  opacity: 0.9;
  font-size: 0.9em;
}

.translatedText {
  font-weight: 500;
  opacity: 1;
}

.subtitleOverlay[data-dual-mode="true"] .originalText {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 4px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) translateY(0);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(-10px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subtitleOverlay {
    font-size: 14px;
    max-width: 90% !important;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .subtitleOverlay {
    font-size: 12px;
    max-width: 95% !important;
    padding: 4px 8px;
  }
}
```

## 实现步骤规划

### 第一阶段：核心基础架构 (1-2周)
1. **网络拦截系统**
   - 实现 `SubtitleNetworkInterceptor` 类
   - 支持 Fetch/XHR 拦截
   - 添加平台特定的拦截规则
   - 集成 webRequest API（权限允许时）

2. **字幕解析系统**
   - 实现 `SubtitleParserFactory` 和各格式解析器
   - 支持 VTT, SRT, YouTube JSON, ASS 格式
   - 添加格式检测和验证逻辑
   - 统一输出 `StandardSubtitle` 格式

3. **基础类型系统**
   - 定义所有接口和类型
   - 创建错误处理类
   - 实现配置管理结构

### 第二阶段：翻译集成 (1周)
1. **翻译适配器**
   - 创建 `SubtitleTranslateAdapter` 集成现有 `TranslateService`
   - 实现批处理和缓存优化
   - 添加翻译进度跟踪

2. **缓存系统**
   - 扩展现有 `translationCache` 支持字幕缓存
   - 实现 LRU 缓存策略
   - 添加缓存统计和清理机制

### 第三阶段：UI 渲染系统 (1-2周)
1. **React 组件**
   - 实现 `SubtitleOverlay` 组件
   - 创建 `SubtitleSettings` 配置面板
   - 集成 CSS Modules 样式系统

2. **样式隔离集成**
   - 使用 CSS Modules 和命名空间前缀
   - 实现动态样式注入
   - 添加响应式设计支持

### 第四阶段：平台优化 (1周)
1. **平台特定优化**
   - YouTube 特殊处理逻辑
   - Netflix 兼容性调优
   - 其他平台扩展支持

2. **性能优化**
   - 内存管理和对象池
   - 渲染性能优化
   - 网络请求优化

### 第五阶段：测试和集成 (1周)
1. **单元测试**
   - 解析器测试套件
   - 翻译管理器测试
   - UI 组件测试

2. **集成测试**
   - 端到端字幕处理流程
   - 平台兼容性测试
   - 性能基准测试

3. **项目集成**
   - 集成到现有内容脚本系统
   - 添加到扩展配置系统
   - 更新 manifest 权限

## 总结

字幕翻译功能设计基于预研验证的技术方案，充分利用现有 Lucid Extension 的架构优势，实现了：

- **高性能网络拦截**：99.8% 成功率，<5ms 开销
- **多格式解析支持**：VTT/SRT/YouTube JSON/ASS，45ms 响应时间  
- **无缝翻译集成**：100% 兼容现有 TranslateService
- **优异性能表现**：4.2MB 内存占用，800ms 翻译延迟

该设计遵循项目的模块化架构、类型安全、性能优化和用户体验最佳实践，采用简洁的样式隔离方案（命名空间前缀 + 动态CSS注入），提供了完整的实现蓝图和分阶段开发计划，确保字幕翻译功能的高质量交付。 ^7MZPEtn0

8 * 1.2 ^klTEzsuS

10 + 12 ^p0rcG7wX

100W ^QKO73dN1

10  ^jGryqCX3

100W ^ecwrXl4X

12 ^2ImOxqWv

6W * 10 ^K36pKZRP

12m  ^9MFBq0LN

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBGABZtAAYaOiCEfQQOKGZuAG1wMFAwMogSbggABQAxBAA5AHV9fXj0sshYRCrA7CiOZWCO8sxuZwBmHgntAE54gDZElIAO

AFYJlZX4lYmF/nKYccSAdjXtKe2eNYPIChJ1bgm15ImT2dnEzdOFtYXr25SBCEZTSbg7E6A6xDcSoFKA5hQUhsADWCAAwmx8GxSFUAMQpQlEkaQTS4bAo5TIoQcYiY7G4iRI6zMOC4QK5EkQABmhHw+AAyrBhhJBB4uYjkWimg9JOCEUjUQghTARegxZVAdTQRxwvk0PFAWw2dg1EcDYStcJabrmPrUBwhPyEQgEMRuC8UrMbsVIIwWOwuGhEoD/

axOA1OGJwSckvF5rH4b6IEI4MRcFA3TGNrMpl7ZicAcnCMwACKZTPutDcghhQFU4RwACSxHtBQAuoDNNbiABRYLZXJt30lEddeCw6BYKAk8qVCSlgASymqABkAFYUIwQEcAX1uo86c6r6CMUCaAE0m7gGgAlNi9gCqAClMM4UfoJokVso2LPxz0EjssiVB7r6nbJkQHAotwjrOpBbAUlm1a1gggKELSWBVAAWtuxT7sUpTHlUS4rhuW5ct0k6Zpg

M6AmMaCTDscyLMs6ybNsuz7Mm5qoM4pwpBcPBXD6nQQPcxCPGgX5xG8HxfCsPx/EWYmSMCoIzgaZxQoMapJmJkpKvSOL4vECBmWZXJkhSDY0nSWImUy5AcKy7I5HRya8vyKpqhAGrugqUoIDKklymgfDJoZaI+ZO/lctqki2vahrJsa5JmuClrJrZzatoUEFidy5DZC2sFOvg6GYQx6C4GkVp2UlZXwQZronjwXobGsKTLBFYlhoG3CQsm/URlGs

LxGswnemsLzoWWFbIagNb4HWybdnZ/ZZO59pwRVyapumlbZhMubtR8SSieUUEwWgu2AtiSEnstq1iTRmnoAAOhwqCoL2cDmKg8SACFugACRoAkOaAKKmgDRcoADc6AE+6gAx2oACEaACORgBISoAgP+oIAvBuAAF7X1fQAfKggC+mkDqCAPLKgB10YAbGmAK3W0NfT9gD9xKgAAFTa5EwpCppWqCaDAqBCGEpAExwxOANBygAAcvT0PxZQAAq05V

Ezv3/dggOg5DsOI6jmM4/jHBE6T5PU7LUOq6zHNc6QPNwHzAtCyLYuSzLDNQ1y3KcFAAqEEYsLtYCXu5LUuD6HyvGXV004AIJEMoQboME3IeX1TBQOYBBxyCifQMaXJ6LkuAYUwpUSHUjQtG0XI4iCGEEErtEq99asA8D4PQ/DyPo1jeMuyblO0+7lvs5zma27zbr84LwtMP30vm1yuBCFAbC3uEfuwkiQhoZBJeLupYIGto1z4QcRGQPO6CJKWz

ilgKDTos+CDKEIpb6Gwi7Pp+AAaFC4oCKivQED9GhMMei4IJiCUSAsZ4cDVi7HeICXi/EzhCREoCCSUlUDPFeO8T43wTi/H+FHIEIIj6oDONMUhYDYT6XKFFDEDlGToAJESQkVlySUmpLSYyLDoDOVchyVO5QvKCmFLFLEmpIqKmlLKeUMigoxSqHFeqOo9QKLEmlU0sBMr0MgDlFsbYCqiOKggMuDpyqVWIFhIC7R6o2g0bdKxkVWqDWeDNOBKx

QxMHDInUhI0OCRg4NGNABZthQLOCGYs81ghHRQitXeYl1q0k2oOPIhQRwX2IhIM8l5rx3gfC+N8H4vw/j/AeACk5gJsFAkeAiR5slVKqG9f8FQTwQBOAAWWwtUXsUAOBpDAp0ExkADoZkWvEKJKQLrxCgbMXqV0MI3Usc1K6iE0RPVQmfQixYOndN6f0wZlEJwtOVhAg0zxtBdT2NsHYwkFhzImMg44JxBKXHiCpcoWCwo4MSMkHgHxfhnVmAsL0

0TVKH3elQr5kBaHcH0X5WRTCGSmXMuizhNkeH2VRU5FkbJhGez5OI1UkjxSBSVCFbBiyBDIuUaKKRAVkwJUagaI0JoMoWkRYYvKaAOxBzMRYu6xYqpVFwPEAAavFHsrLVl7RaotPYexPk8B4NxNOAZOAeh8ZqoJY1IFvF2PEeIKR1VzliQgeJS1UJdh7Gk7aTV5XlHGVaqZnpZnLCmPdZZjr7obMWs9JJ5RWkSDxKgQA6tqAFU9QA3/6AEXowA+U

qAF+AwAfdGAELowA4aaAGMLMWeIw2ACDLQAD/Fi0AF/qgA4uUACEZgAUD0ADFZgBB6KRoAHPNAAw/1GuNSbABgOoALjkvqriEKaYgv1aI5D8agQAC8aAGz5QAedqADfTKNgAzo0AFRGgBABi+oAY8jAAJ5oAGJU42JsANlKgAgBkAKfRSbAD0poAQGMvoXmEArLQCAl0NEtdyIgmBUCAElvQA3HKAEFbQAY5GAESMwAznqAAflJtX1J0w0AIr+gA

dv0XYARyzAAVSkDKNgAHU0AHbGgBVY0AO3BX1AAQ/x8bQKwACkqAkbxsAOHOgBIf6bYALE1AA8FoAeH0o0bpw4kNY+hmCoEALMqgAUvWnYAF9TKOADc9QAcHIwzBl9BWgj8ATIFEwMwYhAaEiI4AH0VACd2oAMbSkb7rFgAKm04ADgtADD+oAd7lAA8CrpwAWP9fWcKgQAB6aAAB0wA0kZwcALMmgAdeSBoAK8C4OttjVZ1A56v1/qA4hyN9G/Mi

bBnGwA3z6AH2/GzgBcfw035wAGPKACB9IGgByA0TYACwiYbYYBV0gAQqgQAoYqAA1tQABspfUABSuTaViEjY6gONgA3vUAPvx5GxbY0ANM7qBABgSvTMDqa0Zix+mmrNE7ABhcoAM+jABjfkDNYA2Yapq+mjSzLd4jaFQIAGm83ZoEM8WwAAh6ADbzQAIeaoAABTxGcDwQAFioAEpVZxG24AGLl6ZoDjRpi78R7uqxmNtwAkHJvdQI+JsgAmO0AM

vmsWvvXZ+y3ZI23ADbakDoDyXABoyl92HP1zjbcAClyQPADStoAVejN2fcu7Dr6ubUCnbTTmynsXTuAD+1MWAADVn2RSD6GLsQL6VJcBwEkKgBWhXVY/WYFoXn/PUAfQgIAeVtAATkYACzVAD1zoAYI1pci5+prmOBQoOLoV19AUWgM5QGCA+qAFAcQojHkwMQ9scTtj4s4QmX1CsFCjZhwAhd6ADAXBXqBDeaGN8EdEfOoAiAQO2DXmvUDFecE7

1A6I3eRo3frjg/vA8IGqOyEWEeW5R5+uiR3xNSyJ7jd7lPae1DBAkyyKTGdOBdOsNEJgOe8+a9LIX36BQ42AFhzQAhUrick9J2T5hw+R812P0X4vyCS+lyD1AgAhHXV7n1vmvaiJ8AHxmgAuT0ACN+gA3uQN0byvCAADy/opMwBbyvqPABxAogBZRMAHb+gAjdMAP7mB+A9H5kzIDCyhmCX9bzkNzsvlHhPqgO3rHsTLUKAdfh3qWKAaAWLpoBLg

LtLmVoABUKi+EAoBUei4iegAyP5lbv7p7B7YBqT/5X6oBNj34P7oFEFH6YgcC8jKDkFR6AHwHAGa4F4QGoCLjQEd5Nga5sHL4a6IHIFS4QBBaAZL5X7PgFBXpCA3qaAICoAxzVBNgsGa4ADSBQZuT6hAL6qh6hoBq4BQgAboqABrckBioWoeQUISAcvjHB3s+Bro4dwZoS4R3quF9KzszrTmGqhp+kzkbBwD4ezpziQDztPgLquLeC4QUJIVGoAO

/RgAQjYO7cGu5QZfR67kEx5x4J7IYoajqABryuQVwXHqWMAFGvRoAAMW+Ou4GuGu4BTuAAPhKgrArM0Z3m0QrMnuQU0YTM0QKLeB0agGvkMT0eugrn0YXs0fIYococ+AKMfg0M0V9LfnMber0Y0TMTHAKAKJ0bgbsQKFscIcvr2PwQUIAOAWgAY4qo5RqACf2oAIYx5BtQ/BGuMB3BAhy+i4bxpxUeTYThwABB1RgAp+b1HL7PgzGABU5gQZ0doY

AJ5GgA84lxqAAgml7uQZCRAc0XDLCagCYYACvxgAe2oEGADLfg2uQRrpoR3l0gUNXi5LXsqMPmIOQV0h3g0F3rGmSeQQ0B3sfgUIAC9mgADaYEHkHH6eENHL6rgd7VCJ4Q7kHVAd4ACKBQIOW+OePhtOqAe+NOwRuaYauuC6Ke52FeJujQlqFupAVuNstuq8pAD2wRumgAMgGADEsRZn5oupISFlBikX5sUQUQJoADbxgAi8qACbfp7j7n5uelRk

BikYAIRWdGgAY9GADUSjptpoAKXGgAx8qABZ2oAJJybpIRrOVEzA2ApAhA9sX0GE48NYYgX0TYJppu5plu1uogCAdupAqAwAqsSgqAgAFzaAAxiouijIAJhKqsgQqgiIpc1prZUAt4ToCA52PgGYXsHOyAB+cAcAOIlY1Qtey5+g1AqAPMwQq5HAzZNps5wQd2aA9AbAJAAA3KrF2QoKgIAAD6gA05qLqqyIjshQCnnTmBjnaXlfTVDIjhxhAAA8

15JAhM95Lcj5qAgAOgqABG1h+S3IiMaL+fbP+ZeagJBcQDBT9HBRhkUYuoAG1OgAgAaqzKCWoYV16p5RCh7MAAXHk0WBhCgZjCwwXgkVk2zVnKE0XnnKGdktwiD4CZ4yBMAcBoDrzKC9iYBwD4WoDZDqBsDEBoAADk1+vYCsalqAnRal1Qx+Ao2lCl045A/Q9ZCApYGYuAaA52gQrInAYQx568DlLkCAd2qAAAvM7hwMBQYCWAgGBRZVZVENBV9O

CRqQ6dpoALnygAarH5nWbM6aAgQizaAUAICaDrwACOO8iIzO1hXxiVyVtSqVNi2AUm5AGcjAZu2VuVUAvhHAhh2GDeHAhA3I4QUAqAEqEwHWuplOjGkxF2FlmeLATA9pX0Tprp2ma21mG6VGgAs55VF0YXbdH7njHUCXrXq3qoCLHLH7lHH2nWYxZSwFEkkna+njp8aAAu8ZuumoADTmJWKmNxqOqZmZuZ+ZPhRZJZZZUA3FVZ5IfFw1WeTAHZcF

G6BRqsBKYQ52h0NlqAkoP++5u5GYx5FltQOInOUAgFvlIFAVQVAw6YpAxAFlHYYVsFLc3ZtRS1qsNimY/Q6NHOGYMN1laACNgw2NaNGNGYClcFdaBRCMgALqYZaqz0AEAkATLnaIHp6o0E3sjE2H6mkdjY0Spi2HSBguVOhQCcViyVlMC8V+6y1E0WWg0twkCs1Ig/4KVfmkBQAKyEDZDOL6BKGkC3k/TdmADX1oAEueqsgBdtDtliTtTArtmuHt

3tLcb05tpZgwwdee3ZgA+cqvn3WqzMj0kTLECNxQAAD8kdltqA3ZcaX0SdLcG5rAtF2dfuCtwQ1QbApdgYVtEi5dFlMU2tBZDVE1MV8V01fmNmgAaJqAAXCXFU2oAPV+guE4Ao319sqAYGfhEaMasaZeX0xpldCAdJzAtegYLVTedpqZLpCVlMgAwDE+afqADQXn5t7nGoAPhpgA6Eqbrwlol+YaaiaC6D6ZgyakByYIB+ZolxqACYqYAPfRm6gA

s4mE6vXZl5ld2t1fWlnlmtU8UA21kWWr3r316N5UXtlCVu1PlxpRpX3ZbJ0v3KjL2MUiFENoBsW0hy0k2dhS7L7Gi0XyB0GmlIMTLH6YWOWqzYV+WgWBXkOE3y0f6K3thk0EUU1PnJoP5xqP6fmWrMO0UMFMHnaFxMGo3L2yOBjyMgjYW4U82iOoBEVxqADb8YAGV6lFMjL9gYflVIeoTFA+NeGYFjyIVjdoOjmDqARRgAEbZxqADfcv3i3HgCEk

EGo5wOdlEMwFbqpfDRbezcedo+Fd4azqmXFQld4QAALtXsX2WRAEMKCyOMlfTv0j4NWJVpMhCh6ZMp1r0TKRATjMBt0cB6mz1b576L0WWn5MDn7jUcCTV72AAU6n+oAFBygAnaYQ7YORqRlnqADsFnRh5rmZuoALLyD++9AtF1gAM8qAC30VvvGX5oAP7y1WgAviqACwKoAF5evGUzgAKWmADdnqA+9RA59TUxPb9bA/9fJnWcvW06QOfiba41vl

Gp+ZILUhZZLcvTLdYHwxZfuUoyCMeaWCWIuTABo8oFozeXhQ+bo4AAlpgA86G/MtySAkCEMCPBBMU4XIsuN51PlClb5SPCVpgTIwusjn4IuKOcDKNfR0twsItIt3mouuPiN/oqYp6i6WoSp4tsAMFRAlykBMtFwSvHmLgKxdKrj2q5CcsoscARUJNRVJMQPFN6D6Abm6hDj5XrzkidV76xZ+boh7GoBdIqVznsZTPmt9WoCoaxmADR6jPYZqhmdb

mSzoWfc9A4892eekDEBoAN4+7ruoclm5qAOQQg+gFd65m5bo25S5GNXzqAF4x+j4Csj4hW5xnlqAalMAwgoeShalG1LcDQWltQq4TYP8XlhbuoUAehmA5bqs1Qt4TYXSvYAA+hKk2KWL2GKQW2pXAKWdkD22YDYmwG2y3KWHWVWxeD22uI+AKA22pY4MwLqDAD2z4MLLOz9JpVW52wXiO1RbqKWdgGpXE8Ed2UtYAFnmgAfHJfRYAbk20xuOjxuc

2M2dUYNdXtHrv0AyAHt+7DHrvMA20geZvZu5u9u7U8meVfRFslu3o9vriCAcAgdHHruhDMAgePgNCaENDH5NAIeFs0gogcC1KYc3tfR3uRqoaZkvtRvvu62kD63BXWXpuLnNsY1kOphvtbk7kY0KXI1QD8cEsIAM2Y0KXkAUAhVw1s3KAKUiU53R3J320dVhxwCO3O0KVTsIBsAtjl1Kct10dPnPVRqACG5oAHduzHgnqAbHHHht/D6eX0f7ZtkT

Udynn5UQNtftCAunQdPttIAXQXLtyd04anPn4dBD6d04JnUTMXP0kLNiAT5dn7enkNNdaggYjdy91dtdnA9dpK+XknzdtHNgT5UaCzgAh/KAD2BvZ9G05wDRXZJ4V7l5wOm5gOFwpTAL16rGLQnOpcnFADpXpWIDbON4W6WRpGpfp+nJnPgOpavHANN2peHMQMQMEOt92DIAYPN5V/R1M1S6+813A/Jk3RIum8HD7JvNF6J97KHOHPgP1155barH

oAyA96rNZNwj2Hwol95wpd2ETUwIDz9y3Ld00FCuXWpVR4zfgLt1iMQId+HdOAKJILgMQLUkDx92q2LPnbGqdyx51S1zWanqo+Y5wAi+mzkLgJoMEBE92FiCEBwFbcIC2auNCJDz9H51RVANz4MLz/Df8xQMfrN/XMt/zGwKz9YFbWL7k8z7L8EPL79xmKQb7P7ANy3JzpgAwdgCIByNgG95l8F34+SGpL2BwAz0z2gCz6r+z6OZaqQPC9aOJwHV

lwT7e0+ZSw/k16xxd8oWywy8yyCNxzl7RRJ+nh17RSV0eW1+nhV6hWL6WCIPY5wDrz9DWDYpzGn5VYGFn9ajYsfivPnxn5JZ7+bz9Hr1SpICL0YJzDYj11XxF975FRTpTi6660EfE8zmEVzi+6QDC9EMVBrpx1EOm63kp6gDx7uQgUl0tFzZ1TQ1fjP3Jwpwv950LB86AWb+2RnNkF+Xq1vz/jhSKy2BruCfYVHrw5Q8vVP3njPxERwT9Pv5E9+Q

F3v3G87R++nZp6f0GCoA3ogA5QMALi4Z1v+gddsqlxyAU8r8dgdcCAk6ol1OuwRBAZoCQH9BImpKK/hKVv5U87GcjMPmAL/at4HebPD9rbzdCgD4anPMQEL2UC0D+elqRgaAQoHWBRetSCXnXBt74B2BKvSgcwEV5xcoBv/MkFAE16bwxBINPXgbyN5wCYAAguXt9DwCkEEA1vagUASvzv9AgSIN3jSEeZR5r+4+ZfCH1wBu9GC4fMgXnkQHIDZ+

kfQMKATsHYDEQuA1/q3y4Hyd0+tFGQe2Rz7mIOA5fXwR4Pf4BDS+UAYIU4I8Ez9a+soPwagEb5VQ8BfxTXBP1wC6VmiseNgEJTv5G0H+aAaXBukAD4rkjGkJpCXOxtZolkOcDVDGGVeanhwFp6FCIAkjL6P7ywLL48hrnI/JkNjx1CghsLUPlYLAEtC/eS+DvvUzDSAAsOV7wn0Z6J9QGOZnnQLpN0TGYIqESYDhEgCYQHKnAMspWBec+gDXASgz

img2QuQLqiK1QChBUAkhE4d+XMBllrAnVGijiBuHMB6hZpc3E2SnJtkHhNtJ4RcM6rB57YYeD4V8NBFlNP6y+U4UCJeGoARqIsCEZT3a7A1RYsIx4ecIREkE1IKIiyriJhE39Ncwradk7kJhvDSAaAJIskUXo/xFwt4BQPUEkGSB7S/xP4baXJGUi0AgATmU78WRBdMOQ1yUjyRUIsPGgEwzhovoDHFDPgM4Ih4w85IpEUwDQD71qsX0WmsgOk5M

02RmuZUaQAgKEwxRgQNAESWpqpD88CowIEqPRGqj1RHAKGvOVhpI1l+uon6PqJtGjUqRqAcGpGkeoa4PRooq0YF1QBEkDclQ5eh2DlGWiwR1owmEaMt4hjQSBBL6OoIpCEiAKGuQkYaONEhiCC5FaMTcPwCdUbMBBQAGhGcMQAOPxgAfHNQCuYoMeoLQDoFsWaFQIBZUYpui/+hY+sVyI5E4hqRSeL6OumRh98Fhx9XgMsLnr30NhbOLYYPzcp7C

AmI/I4QCLOHPDLhm9NBviMIGp1aKm4ueJiMBHYjLhb9D+iiNyaniR8q4+EZcMJHnjGhhI68ceM6rW9/AyhUILY13EaDBgJcQsfuINHxj/xaANMoADZTcMaC3v6Scoxy+f8Q2LUhoBQScaFMRaKLGdUwYyEq/NmPJFATQxxJQghwALrL4ggYQWzBhJXywTAJqDFUX1jvpfQvcUaIulfmxDGhUAgAeetAAVHLwlAAhNbYFNcFEwmJeLEDHkKmDJDOo

xTejMB9ydDQMMwE7FX5BJCAcka+JLh2jo0X0WNIAFR9QAHMmvEn6MpN1CGiFJpowkmpI4CckG0ukv3EyUUnYSqJ3osMQRNjSMSV8dhciXZLgl5iyscaEkl9AslETaQf49yZRJt5oM0AgACBUBMcaQAAxKX0B4o8Q1ywTbJIU6iYADPIoMlGn5JfRBSUMffK3THE4JJxW+CHL6375ziX+T6WpKQW/KC44CDhRPNdVBJpE48hWYAIAGV9QALJK06L6

Lxj5GZ0TBP0XIgMUAD0Zp0QTxEV7iXGKNDkRmKAAyFU6LF5MWHUqad2I7y9gCgiE2NORQpJnEO8tQYAJszoz9TRiMxQAKNycddNJ0Vvxb4E6y05fK8SxInSnJBxAoFvlGYvEZi6zTolQS3zrNbpxIw9oXi+iyFWmZ+CwdoGEEAtl65BH4twWcLL4ASsMwsZiTjzaEuM1WP3uQSpLcETCxrbAXvjlIa4pS3BGkpazXZwZAA3K6AAJU3JKFj+iX0dk

iDPaZgzcWNiCytyV5IFBAAh7aAAvxQpmilpSBQQAN+egAEzSwCx+VkoAGFFQAAS+20qPARiIw9lAA8IbmZdm8eK1ja2IB2sp6X0GGOcyubL55ZqAQAFgJlYw5ic3amAAAf0AD+CYAFlFKZoACbFL6IAECvUcU63/polAAat6ABTVxnr/1w2PrGcczigY/UA+nVWNl+2Xq9hbYOIBWBOBNr0ytKTQY/LeE0I9smwDQBWL2FvDohew1QBWE2GWI9ta

gMcJsF4Q4C9h28I7JthaRRA9s2ONpQMD2xrB8g3Q+HBoAKEfDVADKwxCuUXOTldIY4CsLykhxpBi5E2NtN0E3OX4gdqgMcW8AKHTnX4e2Wc28MnOHmYcoaP+HttzBxAgcFYt4GOO3NXCDyC5DQIuSXMVaVykOIkivk3OLhM8QOt4XsA0EHadsGgS8leWvMQ6YcOQNibztvOjmkAQO6IGOOiEXC9tP5t4ddmoLUgALkQQCitvnmWK1AmwH828KvKg

UjtIWygOBbvMq5nd325VXDonyPxRz4FMbIdLSE+HlzAF6bQuJKD7S2lzsJwrQEQA1hURo+pCwBbHMQCILNccAVhQDCP7MAm80XPhT9AEWM8AYtcfwAQDIU4hy68i0gOItn6CKNYhcN6OXWsBKCW4HlGwWPKYDnZhFTeO7ApT564tmA2gG3tkHXYWUlFaPH6OCXVZ1MGm7sr3IAC5zQAGxKYsDbKgF0wGlAAjJqABSWPzKa5rMgAWTTAA5X5RSnMa

UrZrnmsxb5qsgAdiNAA08pkx/6o6TfCLmsyAAG6MACq+v/UACnpoAHX9arKWn3ogNgiT2XTANX/qhKfo1mQALg6hOCwsnhyVjpx09GQALBywbLJR0sACCioAA7olJU5kADgxlmTCzBE/sumHvL3mCUNK+IqAXZk/QvHWTUAQMQBl9EJxeKOlUSqKdpOSyAAJRUiW4MOlBBPur1hIxAxvJfkr6PDl0wQ56lEDMJWrLXZmzeMlzQALvygAEqMOlhJQ

AAAJUUw7FMw6X/1AAnfGAAZVz3yYZUsgADj1NSWWNHN7JnoEEUVwRXxbUplEEFFl1mMtH+koLt5N0fGQAMxG5MGIo+FQB4Evo+E15YAABzQAOSaBBBlYAHgdczF1CKwdKaxfGczDwESAsqup1SzbLMvMk4qXljS1AI/TBioAg5D4xMQ1VeUMS7qYywAJ1+gAbuUtlCS/zGekAAXsX+huWxpAAsJr2jO+YaO+l7jRwlTg5MDbsuaqpb0LOqhWQeWA

p7bohkFqChtn+z16FYNekgLXiGMDhX5uyjKr6NxNZWqxa+xcW2pp3CiWgg1T5ENRwFfSABQ/T4zYYvavVGvrgH15RgFBISN7s8hXzBqGVAmL6EGSQyfcNA0Ef1WgC6iEgKC3ZQAKrKXExIl9GSKMrw0gAMm8K1arGCuZ1QCAAvM0TS9Z4SgAdO9AAqzapipMdoEhaaR9Usi/KYgO0O8L/ZjtCAotTMKgD2E7wQWFDfIVBIdwFsOwClNdRuuUKH9q

JkYGxIsW0ABcS2ulL6LtAba7RSWoQGACEhuFbcFYbARUjvB3hAtJODDOihBP3Xp4laaALhnjVwo+UbB6gEsNoG3UIBtAAi4Qedm0DoapaR+WSWYtX6a42qF2ODZYsQ3aBgggwdQKgEJjfynVCsF1W6oaAoLr82gb1b6v9V6Kx8lAKNcAIsXIbkQS65gPOtIIAUcNu4GNokkc7cgLsAAQkI3aAL1dpR/uYvg1yaG2YQaNdkBLbnYAKXlHyjJrHaIQ

9QAmyQABQrbUbaN7qxjZGrUABdTFKQxxdy1n6lkz1Hw99RrD018bDNNjHGv5XArQbH+3ZMDOasADcCYAET41WE4tdmmrSsZWb3D4uFXaYnigAAxtjVuKg2iBp6GmlUAiWjpaWHFmoBpZxqjpTlMACLboABoVBXAVqFV+LtMgAYPVAAfOqEkQJaOFLVmWnSABQOy9w1iyJSymHrgBRAN44AqAcpYABY5adBpPtGvLAAU8rToRZxWsrZqSJzorItja

iWUTitX+sQ5VXWesnlW2YRiypZJQudjUpA0vR5bC7B5W8rptMwiIQ7VxkPSABja0AB2Hhun/ZDz6Ms7N9R+q00XabBDq2fuiOfUIAKAXw/UUJo1y/agOkQrjt/OZxNBewhWboh9AJhfRCQyAFHTMlSD1qICyOlIKjpx0pAZgRIRHRwAPj8g2AqAJoDiHwDEBmcOGsHY5U6r2VNaDbDjWoC+iOjSAyG4Gudgh0KcK237TGtoG6I2aUJr7ZAXZXCCa

07ssmz+LgEYCrgcgygdQJdmF32cxdjO4sQUBSDthZN04KXavEKzzk1KJO7EF9Ap0fNUeKuyNogH6Di6xcGurXeDL85qb3K0ug3cwo4AmpCQlu0XTbvV1QBNd2u32ppz11sA3dEwbHSkGF2OLhdu4S3UTzLw7abEe2wgAdqO07jKme41BkP1O1fbiYf7K7VABu2HokSozU7R9o1iZiOA32unS5E6qc5kp7ZAtrqCB2oj08QTDgP+NB3L5ftmG00kB

u6FUMG2BQMfMAEc4RM1K8QU7dbWd1cp4QL7ULjGpwREh9yEdQtsbpnaoB9wI+sfepR4BT6ndYXJfZaHn3/9/ada+EMAKi6FszdVOnSkdPbC07u99Og8hLuLHM6KAUar6PXq3qyaCG7YgDW5xcj7kbBouegQgEYHqUcgIHKPCwMF489C2RgSQM4HRANBr2y+WPY/tV2+7X9/uh3TfMrAZ0pdX0fXZZQQC8hdQxALvVgcL1+6Cg8QbXfgbdCEHZNX0

UPaQfINugu90ezisLsmENMNMO2vvtasDZPlAA915RTRDUaNEifUT3hBvqqe42tUDLJBBfxJ5LmLzloo57zteeyLtdrUq3bkYgtELNIePrvbmALms7dpr81PlYygAfaMDSNexEIpQ2TpD15JZUpggBtYUh0hXB0wVHm7KAB6FTgx65HDZPPsY3odCA6vhZuGuZSN8M/QWdjzeudORxCO7vyLFYJlHr8Mh0nyaUmkRrkSNfRWA+gJ0NJmXq1UOqRil

w4fmIPBAFOWRkRv4afJ1o0SZJMWD7sL0C9zBMAN0P/ruwh6voi4WXeAYV1K6eADRihdbs6OWpujvRohpXoD2/6iBTB3Xa7sN2AADeRJhqUsjGB8KrwY1aRassghyButptVPkssxaa4rIeT2p7qgetLmgE2jxwDJAnOS0nhwra57LtlRtSgNSyy3aemqAFjI1hKxId9y5eyw9Xqf216/tXogHS3uO0ix4jqAX7RVSooSoZACndeeeyYATJuepANEx

iesqXYiQpisll9E90pArO2Lf6cief3T6Au68xAOxweNiArFtSJE4UYdHojOdo1c7KiYQDonIdUQPncvW1FQBBd7RCY79qD02Lv5TJ5GgEzZMUAkTghOSmLplPKFnARRg/cHrWPy67QCsLHhwHOwsYdjWR3Q4XrUrlYKs1WW7YAHLjTxQCYKzC4OAJWMveYc+3aHH+v2jCLlwIBdIsgOIQWN/IF4BmP4rvR8CIqooqnl83ZNEgysRIQ1l8nJtzXqD

xNUV0hqmmM1Hl+3kH/TgZ13lictRhmgzkZpvEid+3ZBwzMATmO4dCDKFv5eZ/ACWcLPWZfTGcfM9WcwNW6xdVZoM7WcCD1mQ9Bu/U9QsNPWATTJ8PxQpj5XTmTUfK0k0oHuUnxOVLcXY2q32MuKw0j+GLX1TDSAANvMAAl0VSyEOnHHmBCzqg6tZa9hi5j4VcArB7YdzCs+chWIq1dXmaVGkndvbTwLZ/t6ejPN0GgG3hoRPyYBiA4W2XirwQOsB

8C2pUQPIHUDfCiGeL0l58CEkdYP5rUiV5AWeYIFluBIKkHa9eAc+3XtmvkG2xFB0kPhTAu/FaCcLO8PhXoNd6YhDB4UPhXBXGEtxN2cLNADYNQFR8FNqAFvl1H3I5HUAgAUR1AAtHJb4x8b3dYKJdcaAADdMACL8YAFS9PuuxvjiV81Kk3ceNAc1z+gzhBAdSnt1Xj6A0DxglRW4IT4gGl+uQGtYW0WBwBW2Ki7Pk9zDgRx1KMcUsgQH3IiKXIzg

EWG1X0spdmE6lPENyEitRWQr/MLhLZF4RhWZuygMkOdgv1pWK2gkFYHdhiug8/5fCFboIgJTuQYr0PKFOpQR6c4kerly/bREx7Y9aku+5y7wCauJBnL+TZK7gFSv7l0rcIa5Nlav5WXU+PgwvnCAUtiXAA+7GAB4tKlEcBI0fGDXAELz7DXM+xFki1HnCFl9lrlfdqGtc1xxDQo6lerARn0tJDm+YSD4LMDC3sXdGQGQAJ5OWZKlhIuE4c55A8cj

gAUENzjyhOqbDnNoGg45s827YXi2PmSP2xHwHzY8goG0DaZEdCgPnIQFhsXrJI04aGwoGqv/m7e4AneANY1wfWBOSbYgCm146/Wq2CsGtnWyBvucQbHI8G8tzo7Q3YbHALKrDbwDEBw4cbbwOfhtw4gkN2mNG2PgxuAWsbRIzXCYKcUwU+DlOR/HWiFGBzhDoc2k7XoNy5sXzb5ujQxr7Yxxa2pYE+cfnpmerQLIgBgfAdg0Th1KSnfS4EByqEBA

gETYCyorZDiVSAlfBQAAD0CguAZwEYHbDAAeAu4c7M4AKAxxnA2EHPL7dj2Z0AAJM0VduQW2AkdhQFdeTrsgBe4F024gHNtJdLbCAa27bfot4Wo8jt8eC7fdue3vb4dgO0HZDth2/bd2KO4nbXN8KCLfq+7o/w4WNsf+TAfS+HEr6GgNcevWtbtZ+g2IawmtWNUndIs5qQkeak3sDeXzt34endhBf3Ywhsp+72ai0CopHvLxix0kCez9CYsGDcgc

9mA2bY7vQDu7q90a+veEtb2yDO9j3jwDC3hUJbBxqYS+SDJjLAAP9oz0bO+Ss6iVKKOiAFA6TaEREF73BBnA+BwMA3cAA4pIAABSeB45yqiybqFYaY6l/e5mABTRQzJfQEHSDosqg4pyoB/ZOZPB4g8VtMEiH9TVADua9zkOkHEDxSdA84BamOA3+tBqg81xhp30C9DgPg8iM/DLSDd/UtBgXSxlAA6upfRAAQKSa4BHoN20tQ9EchGOAsjn6AI7

n4Y1nAh5cIEo9QBAZhyVLNR6gEAAopBQ7wCxjFJHDpgHo/DIp4NH6IiIMQ4Gop5jHAjskGEG8Dcm8gxDw7O0tUdyOKHEOrx16L0fdF6M6wtxxQ4g5QAQnqVHxzQ/GIRP10MjwJ0g+LYrxb0cTmxwk7DQbElCO1JYg0EidpOPhzAbJxztycqE9iyT1Jz9DMdIP2dzgasraRgB6Pk8gAU91AAQcoMPwBRAmB8Q9RJe5JHdT1AO499XeBeNeoVI1U/N

WuPSnNF5wNY8qdoOqV0W+hwE/qcUPdaGhwMM4Gx4h4cnqztZQUzEBJZ+HFD3+UwB/wiPUAEOTZlI82djPony9ZwGwFBltO8gmAZx5GkABhkYADW3Zpk84EdMPtHgBJgEc+IcQ4+HxjhpzgOCCWKqnUzOFYiQDkgvbSTeW5+gV3NRPGHrz1sU3j0cFFaCTzuF9gsCsYuqKejx/CS4Ecrw+QTjmhx08ADuioAF/FHtbi+AGadnA9LlaHo74xokWXoz

gR29B5cZw+XVT+6oK+ZejO4X8plk1Y84BqAZnqzrLEVrtNfQ4XPbHtgXuYDavbnROdNLlKjzyP1DBfTgDrsRDUOTXFD9nZa/FMJObXBDxofa+tea45X9xxmoqYL3UPJbYaQAM6KgAOlSwMgAU2tAAJVmxaqtYGA0grhS1loZVxVCgKlXSqZUc7dVBqoYQ6XnprW1gNqh1S6oTANl3hMqhVXsbVVLUFRvKh0rutZlyYj4W8FKT5Gy2voNS7TM+XSl

J5JiKWyR6jjTRNpWJ81ejIABunQANNe/j15cdXJjM4B9y9fKoAGT4r6NzKmWvKVMP9NEpuhqL44xYMy7TBpjmUpbjVT9ZnCc4/r5Vi0X0A7MmkAC9ph0oC3IlY0lqrVYAG/bQAAVKUae60DDRU+zgiDy7TCdkABfesDgBJ74UtuMzqgAGo3l1rW1gi61LGuJVpM1AB8r1kdLuMdGNNJukAD3yoAH7owAHepwtYItjl0yP5N06BFLYAEsjQAKo6om

R/I61eUpLxGZ6QAKJyL1LVTW8fznoZ6GmQAIGRgATodI3umQAPexgAEP0kyBzXZosruaIBk9MDMS49qBhf2sHgzOjCLntoOdR9J7kfJvqWggUkOJTDJuECybLGHFP0NT9G1H2MyPmFg7T0VAMB6fQHYecB685YeYczFZn99qPrrOZhq6NtAgDZ908/yQg/QZwDj3MtmLJhrb0h5g8k9+tpPDzEXN2Qg7YAQHpTRzwoFBfEHGhCgDCM3zdeTHzPXw

9vf+P892fMO2gdL858aFLO7JJn/Lx5+iONlLSlIkr8cLK8KBq5lubL+Edq8XmOys6quv9pE22fWvalcr+zoiBNOWnQZnr6Tz6+WfPmQ3gL6N4UBXPvOFXyTm84+e1eEvT5cNpg5Fy9eqICmxBg+JIGuWZ3knc76KeX5XevrybZ6619Fs6eDAhbcr0WVq+TCd3j+DTDF6DlnmdvqAKj6JnEZ0fTPercz/DUtQZxBgzAYr4t9K9qV9PYD9L1D5/x4c

zFAP/sj5hPNuVv8MP/8doDHIlhx4GsucodrFxfQvzjQ07SAeLJqROcqNFW02FfO9t1bqCzW9rd1v0zXL29zWkBsHa3n7zj5pnyz/fP0bUFIudc5rkluoBZsYGHUpFsMyhtp6e5sNL4pjfK+YYa2uLwG37VJeUvBnpzxt8y/9POA7XxryiC6/jwbSKrisuD488JtBO93n63uS+G87KCHIgSpvq+jDfXvb3mpmjwVtEK7QDXoR1aWt8pH0GkNRzRMk

c7MAY4/QddQ2etSJIT1MfzdQo4ErsYm9URjvXziCr42J5hNh7xW34pzlSavh09bH4sfQjg8/IP7uXWZpRBPz6eeo1YdiboDFbDC/oDiC02wbuN7ZqwEQH9hE3dyWf3w1xQpq6YRc2mXsgOUFEjlc8fN53uOXHhl/CWmjlcmuSd/F+Xf+5HR8xU99zkVW3p5/To+z/EGB/R/hF9oAF4LkHvHlAYceo1zn/kNwsIzTo8t0ybM/drcGZanv97++THaw

7GI2HnTT+S/i+SvkDhkv4N2CRh6Yaw0+hkbGm2NJBo+ayLDobL4+GiEwD+8fon6MAHlHoIiATvJ36uMi6KMSWopBAVQa43/hyLMigmhMYa43ZGQE/wCrIuAyAcAJW5GCmuDQER+9sPSK3gVBrGZPk/ZKgApunAagClaCuNhiAAQZq+SYMIADC5oAAKaZmp4aEmudiJuIsJnRpUGVJwFsaHBJyYyaqmqmAw8qbnsKIgSAYvQTGk/jAY4BCflVTJ+w

Fjhr0KrPCRpsAygIdqAAPBuAA9ftfQeNpJwxGvwrwG2kDuJkQcAi6HDBBG2xgpTWBYATDYQBSFNAGa4y/qhSrcSAUSy4Uj/FgHSatgXgHuUL+mUzEBjAU+SAARgaGqCdOQEsiqAJuioA/AdQHca9lLaQn4qFlnBqEHYn2pCBqACLKiBOgWm75ukgRrhYBGgUwBaBYgb0GIgegZT4pUNjqMFmB4ppwAG6y5AgC1UL7Fa6BAH8HLrE+OQIYoyaRptt

zBQGVF9C6BwmqAGKaliiWB2BSfg2yBoTgY5QuB2IO4FqU2MIACLe74EWUAQU17hGIQZBgCicMEhRRBz9p34KA4AUkGoA4jJhiEk+9DP6wBkPlAC5idfvgB/cijAQAIhXCMeRN+cNOkJemuFCqxU289txo1+YePCF/cDbO4AohFINEGgBQIXEEghZAXQEC4mbjAHR+66rH4KOdIekHIsp/tCYyKUvHSHry9wLSC1I2gO1Qsij+lHj8hOPBQBChFAQ

LgFs4JudgYQAiuJyHBYwT+SMEZOp0T1uq4PuTtm2dMqGzBnMGoCcMuNOBQuU+rGEA+UkJq3i/aIlA2xUQbABJoKhK8F5SeU38mpQW2qAJnTIOioagBoAjoWEZfkATPaFfQmoR6FehK8NoCSAgQBJq+hDouGEiUOGo0ax0T5KCRDSM0nyL5GHBD3rL0mJrKEf6rOrkD4hakN4blGKoedgiUFbGpTChpBP8EcEY+L9r2UZocn6cm3IXwJ0hugMiHnY

4obUjahsYVAAVs7ZhMaJhUeFgFMOmJgABkY4fkzhAjYdoCogegVfj1h04Y5QYg2ILqDryDYcuG6Aq4fOSDhmuL9pvQ7+p/ocAG4W5Tog24Trq0QSJjSZR4o4dZSE+lADmHEG04AmHcB+IcGJEhXCFoGL0t4VEC7hR0kOGa4hAc7Yv6rlGEDCaFIYCHAhP0LP7MBrAewGcBUIUyFOaCjvwHshJAJyFOGLYQQCsMOQOvIsBq4GwGyAnATxpsAq8FRC

zhiAMQE5mz+lhGCggBHhFwRRESqEkRZEROB/+tIN2b4RhERwHMRemqxGIAFEbhGNmNIIn7BMSlP8wRMSnPuSqc73IMBfQGoQ277k6GtoAp2r1topK0gltaEfMQoNvwFsdoRJoiUX0C6Ejs7oZ6E2haACJQRhUYS+HYBJYB8I3CHADABS6PbEw60268iJQ6R+PK3i2R7GLcLaKzkUw4BmylEAQFs4kSpQvhY+EBHfQtEThEcA7YfyA+RFbGFHEA0k

R8xKR6GqpFWBnEYxE8RswSxGkRbEWEC0g68tyAiRtFOoEqUMAOXSlgiEHGzuQD6hwBcR8ESqGFYlUQaFY0mkc/oeRi/AWw+RKIv5HaALkcvS02L4ZWaWoEkQ2x9RH4jbyORAUcvRBREkRFEcEWAd1Hec84Svgya2PH2CMAuQKuCbBF7IdrYg2PGYYWGnxrZbDhagYYH0UwsBRoFsO1qgAThl/vBpfkDFKgBgUR+utEUEXfp1Q/hGQt/IGBhYSAgo

gFlJwFlh2kUlxfQ+5GpSYAkYTWFfRpnmoG/Rn0fDG/R94fJxQ6T0ZYonhYQBnQvhV+DJoEhgQB+EUgX4caZIxeMXnj/hlMaARS+1MbWEcEUUagC0RMmLSDxR+AIlGU+lUVlEAhXZFBHTmKYTNKdoGYUkHQhVfpurl6qYkWHAxJYbMFgx0vFJFf640SpTqUVYZIDrcMMUAooBxoTwzZhXHJ0S7QGAetbvCUrE4YFAm/nuQHkdrA7hBhBYfBo/+N/u

5Clk4QABTIxblu2QmxDOnORMxEmuf6uxqgRdg6O2gCJRiUxdva5yx/RriFfRjMedF545sdVat4YnMJLcamovTTL8tQCBS024cfHF54G/CzSFsp2t2TtoPTIABwKoAC3foADWGl9CAAooqWS5YZZIXqx/DpxgEEyEqbGalkgZxGcqlJjHaAplCaykihnC2Byx+5ObFySlMRTFUxR0kdIv+lqEQFyoEEbzHUh0EagCAAuEqvkqOAY6L+IsUhEshrVB

2bD+GeA97j+2NBkE2C3ZPk7KEzbrbFYxL8JsGkA6/vOSfWgnC+y7+xNvoB/WWbADa9gwBmPjBxGYMXaQ2DNh9Bw2/0IjaacyNrRCo21VslHqUmlNpTo2tEGZQ+wusc37OaISIvTYxeQRdrnYMcebEqMd3i/G7k78TBx5s2cYnFfCYpkQmfxPbPBzZxucSgmcmGCReGF6d2NnEyRjCXXEeCDcVEB6saACFRIaVHMqajxselfyW6TRqgC6Ez6KgCXx

MmkT4Tkd8df4PxhfueYEJGNNoCk25Nj/DfxHBL/FO2LtoAntezNkAms27NvoBsOcLNzaBAkCWPjQJhbLAkxWvceZTIJcNBLHGmGCV6bYJoBLgnb+BNqP6qJ6ibWyaJoBInH9eUnMvwSmCsLQkPhecQwlLhblEwltxHgmwmxJYQEHFpRoBFwnacvCS3ECJleq3jCJ6BiAHt8b9nqRhoT2C47dOmvtr5yGuvptr6+DnpkwZeuQFl7jeCgE04++JrEG

bUO7nr+whJYpnzoRiknC15++1TNJ5o83SX17dE+okMnLewTmzroiYyfb49J4xFMnw+8bMt4xOZifMkwU4yaPrnxKyc94je5Xhk6lsikuzoLJ6ntU4Cg+yb77LeuHJskna7Qb17B+7GAiZMAocD36u8kcQ5rMhm6i9EAw43nCZfQ/WudjD6y+H4Hp4FCatSRGLepMnoiAFJ2C42/OhmDaA61NCkG4wxCDp3YCKcETgpR+BQn/WsHNQlFOFbM3oZsW

1EoSYp2KVHi4pppBQlHE+5M3pfQRxJSmqw7YKImxBM/j6LroSZlvEpB9jBrDgmjouiFiKPvsvycKtKa6LHkqATwwDJYGkIwYRKAv9rfyumo463+//mJwMBmAWoGSa7On7HqAIENCm/QgCudjM4vSmeiIY0ovRhoAkdsABicu4MzhWBJwd9Ef8/nJpyMmnrvXqsmOSVKZn+OBoeH5hdrkKmw0PqdCaay5rt9BymHqdYBep7Jh5TWY9JppztB1ES5B

3BbgSanYwgAOS7rwQVzoiDuDal2pXKQjBfQSMNanAAfuiRqjGkgCJpUmkaBWw2pYaRXzS6tQPoScG4xruBsYDqaKGARM8cBF+688VP6Lx/MfjjmivKaLj0UAMCnFQAYpsKnama0eKnBAYpgqngC9tNkDEAj4RQxRAv+vbTZmYlrUQvaqAMu6qBi9BbStAboApxpGNtMwAygSumpSw68OgrDiY2xn7GMxSKeKbdExwShKU0+OKByvaKnlqmL0btoj

rEA4HojrMAMNkjrEA4dquQfQkGX7bQZsGVvowZ7nBMD1EH0GBmx4RMKBngZCGfBlQZQGeHYbUMGcAAoZUNldrEGITOOyrp9Rs+k9p30K+kopwxB+k0mX6WSkKE21PBwHpf6TAaFmtlr9qOiA6N/LwcPJtDQnpVGdZS7hWAfxk9xO0XkAPRk4Y1S2wYMucGKZMAAuTA0xANJnbQ/RoJYH2tGeQlhJBKXmxEpyxC+GTxyJr6qCW3ZN0pDShTssScZt

mtkauMe6dhyHpCMcemUZZ6XeEYQ5VEIBJ6h2h9bxeahl7Dtg2xnJmZeK6Z5mbp3mb4B+ZalAUC9gMmX/hPpgli+nXeP7NoBHETGY5n5BRAfpnpZBHERwkcDQP2lu0fMbP51oG6G0aMhfKWcI4UqtBLRMO/erKlH44Gn16i0HgMeQcC7PDGyAKr1kpwdg2nj9rP6O8iwDRcA2UeoP6hYlkGNZmQvDREMFaWRqSARkS6FwgfsSNmWKKGkZo/GDHNvi

doRzHDE5xtGW1mq06FmhA9Z8Cuxi7gH6QtbGxcSI5wNsKQK7SEAb0UUbzZpGorqSAT2eB7gefsVmGDJBbI1kFAhAJNn0xreNNnL02gCQB+xP0Otlv+qGszhRoqADamEAImoABc/oACIOrWSlgnaWPhUxINojEQ5CaTYrvRkejpnnZOIBtnv+JqYjnI5Imm+RfQfGDZiUypaUw4Xpzuval/hY+PjkXYrORqZvR38qzlE5eQTHGw5m2dTmRoSOcAAo

5qACSSAA+uYM5vGEzks5EORqYiaYFN/I2pguTqbZAHOaZlc5y0dqms5B4QMJG5KNiekAUC2R9nOhBbKTki5vWXDlGaCORLm05qAEXSPUe2TjkcEk8U6nRxdWR1nk5LAJbnkaxkcjr7k62ZvoQRzin4Rhof2D5hl4KvqeY6+G2ol7AO9SYZ6NJLnut7p4UDlV5f6dkl0mLJfXowYKSLXvZ6pe5TAQznJEPq8mkA7ya07e+jBCBSverSY44t5Xos04

dJrvFXn1erekfjvB4fjbiR+peW14dewjgo67y2yYXkWeJYZhCBA7ZKslIc5Xqt43OoLqt5d2k+RckEiiYnD4++Tect6Z5izss7d5PSarAXe6ePuSn5TiRfktwJ3qb5NCZ3qrDt6ljPZTsY4VAcnN573o8mzezyYV6NCxXqurp+yhOPlUiofrEbhGafj8nKEa+SAW95ppOvCz51fN8lOaNFkBKQi2+XZIQFyBSQIt+R+N+YkCmBdX55qSCYBq7qYL

JGKHqqAM/7F0gBX067iDjG4Ev568qSn9aYFArEcAT+Y4wv5hMIIGhWtejzAfJUrCyywFDQnfkcsbdviEkCJIfgV1BdseEaApwhd8JgFQQb34TGMieC5z58hSDHqFhiqoVvh6gsV45+8JsvSEinepqk2BshX6b7xcRoUkLxnKUEY+YfeIhEtw4JtPpEs0qRBToBmQdqkExJAj3E28AFsQC/ZtwcECuBDwdjCAALzvZpVPnfn/iDuNixtoiaHDCAAg

Z7VYB2VHhRR12cmZ5hjzDwGD5bZGzlpBX0BMaJGXGvBrQFkObvFD+m8Dum8FYoEhr3Bh2n3DvWt+XQUoMDenEVjMjkn3gRBcGAdkxBVIZylIUDhb4yjpkTMaBoRA6EJTdxwBY7roUHIlhRfQOGmoUIFHOknqKgqmWYV7hwRfUVppjwS8HNF6esgx5+7RbPRfQcyr8GIUfRZSFlZTWLGg4MeDNVlwBFhsXnzGJDCQWpae6ulrBA/KLho/Q0ko5S8W

dAkbbgG0IHjzqcRcPiasCoJdFwN5P0EaHeaMqWlpUMhsVsXQmoTOEzryMmtiaVUK9KEDhMNReAKCwtlt2SrxqOIYwmMwRDJouez+XqB/+heuiVfEKUWTkMlKUekmkRxlnNmAaQeZIDZxurD4CWoQtkPZ54zcpjZClN4ddGvWo7FM52gP+BZaUx7KcmlOGjBqwz0MBth4LDeaAP8UuQ4MmBbQgmQt3HYKOpcCWMC2cavCalbDNqUwWepQMI+FIwrJ

op2UJXJFe5SaUmGoAyYrSp54v2jRbEAGtMWLZ+NwtkXdxAvISLp0jQoxQQOKwSvoEMKpTJK7h1oSEiJiXxeEDryjWUKF8g48Jpo9sPYc3zaGX0JJrelvpXkBA5VUGylLRYOWoEiRiZe2Lcly2TbnQ5ZLKgCAAFop90ezJJZdah2QUHdx7OFRQhlhZeGVEMFbAWU4G2GrjlOprjCOqESVoc/rN2/qpiUSFdpTOWbwo0c/oue/pYyl5+cAKwVJcflk

lzcFu4WPjLkF2HdnPZNuU9kvZjoAmXqCSZZYrvZ6gGeXge38ouX+w9Zb9rN27kZeVqQ15eDJsK85IQDahqACBkcAT5e5QUx+4dOAX+QFb6pMafOABpWGiBEwm7hAEdxlElvEq+W+q7ehBUGBf+tZJLGX4mJIhMzHHkBRlyxjGWOUiFdlmt43ZOgS3KvEs3ZChOIL2CW8mmkw79hVUF6YxxipZ1Que68s3YYVxZc3wg58MeJoEajQhMFNJd+ZYpZm

rOSQAVsLnuRWUVT5KSR3KV8e2H6F+eaprt67ukJU3hEOW9DX5Jvl+KkV2pcN7X52ldwHRlFpYi5sAplWZW0FGetEJmV8laLa8S65rxLdkQpMYxiwMmi6gZ4nBdYwslFbHRW3lPJatkUxSFU954AlQedgjZ9ZbUUuBI2Y0WAAFXtRFben/l2SDuNxJnFsaMErIAp2jFXtB3lTSzeeflXaAhMeJS2DdWgVVBXBV5FRPFjlDZRFI0VmYb6l26nVALnz

ZnOHAAAGvQlgkxxykSxXpJEAlFwGV9lY5RqphetJWBF+pR+VugfZQUCplOXlgDH43IJLRfQy9FimzZalHKXGC/Ri6ViJyMJOWelz+nppOMEFVSWNCNJXaDjVZVWEyMlEmWoEnVL+fWWPVtJS9E3RroXyVxIrcgVXca1JSVWSV//gyUaiw8f9Wc5JAd2kFBIEZrR7V2npFWUB0VYAp+x3ZJcS5KcaMEp1hx1f9VzlSmhdX/V11UDW0g91RdgvVdoM

9V41b1f6WVh98t9WeJeNSNkNsI2dxQBhYgPaFGp5Cp6EjZTGnqBN4PoeGLecCNfApf+v1bjUMFr1YDXlVwNQ5qi1pNXrmg5eePqm1IAeVln9FNxfoyxonlY8WoAAvBwXS14kuVXEAuoXwU/42FDrWnVDUabUv5A2TYJYBLJTRmdlKlX9W61+NRLWE1s2THEBVbJVEDS8YpVsV6sX1REw+12fNTUB12cRTXlWpEVPJ2Q21aLZZZAEVFFMpKmUKEgU

PkbhWjVXmk4xFG2gO1m5ULsSVkcpEAR4zeMIxdBHQh/jGID4AmlSyXRcx8RyFDZ0JiTVnVwtXfmXVlinf4E1gRThqSZWNY9EOi5NRKXW5hbKmYylgwKFm2WDdWkavRWCtGlBAD8j9U41zdeTXi1t1ZLUN1jqWubXFg6bP5okBRJhhOFEijQUuJuwcEBb5ljjOkKF9RhBraxHhVBRt2PGRjUppIRQ0VqU3gSlW4FaVScW71HAFFIFEuVfuSw0yGiX

4aid4Rqlllrpb6L31ThjNm5hR4TXl15nSUGl3hGIlEnCmqAP/UgN+uQpWoAFWeuhVZU5dCbtZ4tLRTrysDZ3ltOBDYdDzkiBMQZYaRNediSa5DY2nkNL5dsVpU7IMaZqUgAAVkgAPB/r9Uwzv1W9B0VfQdaDlWnaDDbRQ9xvWeRUZFo5eDWuMozBA1cVcXO2LryAMfPVfi/+o1laJa/LqXC8Bpb4WCAxpdCCmlDpXAa6NKlYaVWlTpXkkKlYlhDj

UmeeKo3XxKxYT7qF/+iJJfQadO2Ir685VQ7cWHTKA1iJQpEtKdFreATFEFyjcqlKNRDAE2w1FmQLU4gQRQ/VIaCVY8HJVBxdEWtFxxQI2nFHAGiQiNYeYjXK1G9ZymklKjqMVixQBZUVi0/sNYUxMtdV5VX+yhRzqqacIciF/cx6dxpH1+LMQTBi2gJoA5e2ARWSySNhQOmcppQj4wDse9UgWx+2JRMgKw+tUSxKcXybpn21zOEw46ujQj2w2pfC

UqaWBu4Fs3AADeOoD3hAofoAW5q8J5GDA52HsBEGLkFoCSg52HwCoAswHdj2pedQMUQBQpOSVTNFTULBFVvlbrU3V4TMeRSRtJn7UClETPvxI0wdceT78J/nXVOGDddjWIuItadXO1y9YTWd1D1aDWCW49Z9UQtAFd/L4tlYC+Hj1IpVPAPl1qC3Kqs3cY7VotWZu3Ug1utWvV2aRSVuaU48OFGgQ4CuAnknGSeWcbw0qeeXnp5rziNVHFK3uoUr

5orceHaFKznb4OcYHu/lqUg5v0DH5fXl55LBsvH2GoA94KRHD5yrUF6xOoXgoDlUhAMVYb51eW8wfO+reV7zeFgmq0WezWaaQVs3RrTyrJwyZ/ntG3+dOovJM+X/Ig0ABZAWK24rBexoAcrAqxKsnVKsQcAT6k3rlQBBZuoqtM5Fq1SUWrbpRyoTBfG3bxm6obwUWuQAyanl2bcoSNZpBZBJypQ+oJXTNObdgVgEQwhYIIsCbe+KtUmNIGDpxYcC

GLv80bbG1zxoAQ6r8FTCsjG2lTBMi3jV5YKPbFirrSQIT+xTRAGkl3LVM3gmg/tU07hl9QiXX1xACiWnBugIOaZgYrMXAHRuhbIVYCPsBIhtBhYs4GP1uxR4GAANfu8NwQPAX+tpAB0XctcMMvFfQa8VcWQRm9Xc7g49xQu3wBL+rPkaNpDOBKfFVDBCzYFrLHW2WCTBPCXcM67Zu2lFklUQwqa0TTIWWK2Cp6qoAykUO0ggFbMpGYdV2VNneF9Q

Ya26tHUbZaGBM8TxEms5HUiYxBW7ULnV4FID/hImhVRQ1gemIOD4GszCe803FgAACpgAIPW87ZrVrFyIBsVXk9TVqkEa3Grm4ttnAG23ZAYlWXVBAMcM20V8infOSbR6nXXiPqmnWvUDBl0aR20dWrXqnGd/QOR1Bx7Du7xXhSHS40mderXG38gmRRdEyd8Ghor7tY1IJa2lIbTY5rB7zjuEvhPnZ50RGL6ikKOGqaWEWAAi7s8N6TengPtELk+2

z0L7fx1fQgnZ+22FEAYADYSv+78s0sj800FGrXu0SsExeIXud3sCF3ryOPIbzpI27R4aRth2o4D0AaRXZ0edErJDkCZhbL4D9obDqC5tdF7LV6tdFXe13PJDQO23ryalN10kAgVq87IgpEYN1FBoIQ/j8soKsETH4mAsgIqRMpQnAp1/XTY7WWeFmPWOCK1pWEtpqPNVarc6lCkAxWo3Fd0xWPyOpSe6x1tVZqQ5CB7wT6imDFYbkbHAlnbQ4dbq

AxWp1lgC76SQCcDfgewKcAFxCaoAAbWV9Dz4kSgeXuWL3G9xuhMABOSmJQgH+V8QfOPyW9daPZmAWx/luU5BW3INHWuVwRNV11RuQH02VRKkeuSAE6ILixU6u3ZwDEGIXSM2lZ37T2RYOq3eU00FGEMe0xQHYpJ3oRCLT9ESILYOuxTdxADN3G+teqSjo+hnYvSU9tXQLyRthWDWaUGB3ZfwcA2mdI3g1PetdzfyKve5B1dEyA11uhEiC10HdHXS

poS9qrOKWkoTCWKz1RBbAqp54JGn2jTdq+Wm0xxseNL2y92eUgRoA+JilbpWoeb1ZZWFMf71e9MvaC4R0FOFFZRW0fc4AB9oLsIL1WFAOFBtWHADwAtWTVqH2dWPVmlZ9Wsta3hj4nvT12guAXVZ4oVHgvxYjWvIJgBugFMbPAGiYQMED9At0JwAIAFMd902wrgIlnd9uoBTFQQikq90aQx8IkAUxFpDL3/wfOPbyDmKIM4Cz9M/biyZggViaAhi

Y7IpLz98lINUsgu5G86oWaALpaIFoTYIhoC5pelCwAvVjwDUKIQJ44YQbzivDEVLkLuR397GI/2KS3FC/1a0MjeX0cElfd73Oe04GTlsgW3D/hoAKwE1afIzlhTG5WTANo7Y8hAMLDBg8A+wJxW2KFeTsgAdqn2x9vXa85IE5FV9z9idWZKwx9VfdK1vQ5FaK4Z9EoTgMUD+Az12vZG3vQO1I5FXrwr9soNAMpA9ABQAUxrxv4BoAKQDcIrwbABT

GiuQ3JXxn9bfHkkYNHvWn2vOtEWTlCDz/aZYGA6A5gAUx2iGaAiDcwBTG3cgVq3aCQswFkAIDOIH/LOAGg/oAGgTVv5DtWKVtcA3AvADND7kzg91YXA5FRAOOAgwNYOkRZlloNl9eeBX1KDG3owaTFQSd7Ar9ZVqgB1qOgxyiwABoMEPXh2HWEPZ5AQs4AYQZOXJ0V8x5NL1o02PIEKf9MbPWZZDLgKwYrwKQxRVpDBA6C6ZD96jHG5DUfF9AFDo

pkUMRCJQ9/3lDbDverLkH+kTTMA1Q+FUpMaIDABFQ7bdQptDknKHC5830DHG++HFUzGJDb3I9m8SFTLuTCSBDBeDEmzlrVVslZOX8UrDyQ+sOCImw3ZUMkOw5HrjxAAyEMcEowwgDjDZiFMOx9hQyXxOhCw03lLDug0kOAwYVXZXnDjBlcP7DnCWTrfDxw3CD/DGw3xwXDEyDsNsOJqHsM3DXuQoNR4KTKulWAF2FwMPdlCAsAwDmAPWXADcfaAO

0Qhw3ZaxOrAERZJAGAx4JZqb4DiOzAfAwIO8SPg1AOoACwLAMpANI1fh1VctWiMYjGQkYrZq3AwdaAm9WHsNk5RI4H1H4UDmANLDRg5SMhicA9oO8S2IzwPPNawPwP/DrI8LyAmTVviPIj8g8vg06YsBqK1RtXWpDY8dPZRF0gTPVr0SIHPfnUgh4jDq2GtqAIACcFgV1BtRgTR0WdWraV3W1RneV3SsF7GFkcA2QWUVkdpnd53mdybY52piO7Zq

2kRLPSGNjURTV+2cpQpK6MmscHl6NOaPlZx0GA+rO5CeaJ8YZ1SaahQ50dR+veh32dfo6REuNs+e7rKhJrGb2ZgDXfN4eNMAFo0r4JbXZ2aNoBNgpAWPjXh2DjRBWFwGl445pzZxnAASJGmVFLZRMO9pJaEUEF7TsUPBL9XF1H4CXXPkdFgAOBKgAEbGv9awPp4WgTQOGj8pRriOjHzSCFvkfGONaAAVHF5jsfkx1mUKIKx04hHAGL1/NFDQW0Qm

iHa4wtKgAHNyf6NLJEUHUvNbNV+DdcLG95o6b17CrvDJid9TCmpQdxoWdNHhtq4P3EPgA4O5AvhWAR3FyZqAPQ0isnOrPBTVMcb9rN6BbUCkZgkgEKHMSkrB3G6AU4zYpxBFJouZPkAZAeOdoXtOwRX4WAdRNupkmi6GTjebc7qxVrXaxMNmj6oDoBcc9ZYoFjhrVx3FjuQLZ2stRo2Pjad9tBp1mI64SqFqdOk7RSadZYf81+0L7NzHg1PlQFzT

tGYxAFEUR5pxZ89kBTzgzG99prSTtIwp5qeTw7TYLRxY+A30rWMccJYX6YllJYyWHgnJYuDYlipbqWoBNIPqUsgzFY/QhlktwmWAQwdy451Vgd0n2K+LdwOWE+hyMuWUQyHAeWr3F5Y+WFUPDQsggVtc6k9vJYlZqUEVkn31TmA1igA8jU4X1dW19oMieDWVslMy8YPKQD5WhbBUxFWuQANOlWb3eHWI8A029B1WDA4Wx59L6MtN6j3zseEdW3U8

X19W0dY4rZTQ1uGkiDY1q4xTWsUnNa8YB5UUNLWh06tbVWG1pEJbWsaj7X7W6gIdYpAz3WPhA9LfBdazA+09wLNB0vIGh/TFANhbC2V/JHkRaUwpTjY4ZaGmiAA/F5AwaONmP9AX0EC6J51ScnlPkdScK1G+2eWK0V8ErbPk3OnYzbyfOzAOtPjJYHsAZOwGgpFbICFbLPBsUmYNJFhAxMSiD6tSbQ60fFZBZd5Qd9LPW2SF7raN7++oye0Hk8yh

Ha0wAflHIDpsfY2fktZlbUOO1t/MzB0ggClLm3CIh+vvwKUs48YXzjCAI35MOpbaBq9C+seVBYhJLPgrf5z+pLPOUhrdoC1A6IAX6Sc7zOfjSzzAMTDfyHiahQDln3A/l+M0k3wq6zknIz3QgMIrHpWGf7L9oFAGs+5DgsRRpajog4TVDLuRYQEzOIlYHQ/xmz/ID5TnYu0FkbRzJYMKysAAFn5bUUzAMXMp6wQIeprVac/RTzkgaAXPP6Htjp3q

MPrRWyqahk/J1NCPrRQWMz9c4do1hmXSCEgkgAH5GEE7xgTSIWFM25mOXknPiTxtN/KzwbM5poXq4XBzROtwQOm2xtiHYzGpl5Blr0P83lFpNuplGpyXp4bOQyY91F6vznnzR+H4Wn6ItnCWzZYXY3aUFjWaWUztIIUE3tS9jX4rQhs8L2B0zNumdGhGd8xlqNmc88nOScijNJObFCMTzkP8IkwWyxz9lutXLN/sbAsLzD/D3WSaS42Tn0c7jAEp

NoRyoAC1JoAB8pjzIUyCBJahdzFfGeG4ch0QQMnjMow0MrwLXTeEyMmnBpqgLtIzCHzzwiICz5z/w6ppNgFcyWBVzDc6hBOVnc63M08PrYPO1VEHXaX3TUQpwB/homiRIjh61QQtPkgpFGjcyWvjEKJz0C+njdVppORWiL4iyXOEswFpYu0Lciz3OMLk3cwt3NG3pkMYQHC5rgaV3CyvCaauZbj50LcjAotbVLCamK+Fi1kEJbWYNYAOt4wc8QT6

zWgeYsXkytfuQxzgc+AsIug5aYtH4FbAfMCLcc8vQVs8S/QT6zg5REtXTUS+GnlLKix0ObW4aZ/N2TIIXY1+iKmEsqwzqaHDO89JdX7PQmq3gSLewrvbXMYgbTVwj+Lkc+WN0NqC8QXp4BAUdmvzYC8hY8CsivgBuG+jWLzLLUvGGPTLFlEwk3B0JshZK8ay3aWHLcXNss5LppKnUMk8XLRAw1jMVpV54YFE13ImPrWN3ZAiHBADpDMo6wYfOzgB

oruQ0uD5RX4Ty+uovLuHG8sIAHywH0sLppNYNYDPYICu8SwAEssAzREw8tfRIK/QBgrdoBCtQrri313DdF7Iit8LUeJivYrzALivS40K24vZ5tEcSvucOy7pXTgVMcCsKATXUCvwxYFGyvrqHK1fivNSK6cvLGA6BOEXYlkmSujd7bXissDNKzKN7dosBAC8rnK88sSr7y1Sv4rlXkKv0rX44yuScVy2nQZ0LKyvhcr7K2Kvcr9AIqut4/Kx4LGr

PK2Pg2r5q1ePRBaSzqvn54SycsbLAMzUtUOgq1+LEAjS6rBZBRcxIsAWs2fmUXLF5I/yMxr86y29LmER84xQds62Oky0s4tzJlv5hri3dMqjamGlgU3FGYAu4ARjM4Kipd1ZrwADmvHdcUTAAFrRa9QFnDMI2pSMGAdl1AEY+5Gw7Nr2Vior5TrdoaU29Xa/7CdrSPZ5Zur3qxIj0VpU8j0qKX06f2+FX02Foul9yxrhkrdYa8uSrwAMzhfLsKz8

tMygsDanNDbc7hz2pVMQd2eUwADX3n4MUFTGw0so4iAkAHy9X0fO5Qj9AWrHZP0vGFgy2pNWrpK2ascrMel/kOcvPm/qSzr9uy1hoCwJtjiMr+GjN8tGMwK3YzhvlnnfL4lZk2Ezf8sTPL0X+BnCPqv+Kg4UzheVTN9e/cxMgMzrM6MsUg0yZzMWtDvi0Vp1CLCKb4JviRzg2tIs+ECB+TzHrStcTdKj4w+7s3Qo1t1G0cUNtqsDTwkCoc4MAhig

hVCy/5ohSQIWzXLC3DyLNdOJuybqrJP5PJNs+htcbv+Imv9ADs07OcbePr/juzFoRdhRz/s38X35IwqJtUUQcz3OKbexhMt+Mzcw9AEAtGwnOC8iEC5skCNc46B1zEyM7OpV0m1YI843Bdgrspv2l03fqygDCCpzIy/X5jL52GMNoAYwzbH8b9CzJsObnFdCk/mJtMpHOb+ALRuUFYw5TbhjeWwiwFARWxHkiErAh5v5bU7c3ocsOGsJuWb+s3nO

A6DW1daUFpW2d5MxFm0wRWb4eGFvP6XTYwJCATeP1sxbK8yExn2boTo3KA43NfImNjAqI0EAO6rJGIsGW6iVOG9W5IXprcUehpdbIwmktUQQNn7k7wlW8viqaq4DVuMs22yMITGTW31stbt27B1OrnW9dvdbD2yCD9b/q45vQmXTd0YayGIPrNGRNM5NsorvAsZacxKgn5YiCQq/byCC1gMuOIdVE21s7bfXhX37b724du/F3ix6sQ7FUGPg+rGe

jQLoGOGpdtY7CjM9uaMjW71tfbT26jt3br2wUAHbTBFJK07ygN9vspC68vjirK66qufLdQ9K2qa0Pr/jErreLzvgrkq2qtUDcvcLto+cKxSBkAxoEvjyL5gCiAnrCm2EAiaCgM+sS7OK1LsC7Mu3jO4+Iu+U4XCQQGLtGryq3zuQr0u9N0wrkDnLsw+fy4StzwCq7xJIrQIagDvoz5P67/zhqz9B67FKwbvUr6fZpvlOagFkCW7GK9buS7/O6Hv4

u4e1kME9WQ4XDR7nK6YBgCPyB8vtQS+BP3SAOeykDS45/IDqtRmAB8uiDog+1DEWS+GIgfLVHLqDp7QlWBSO2AuMQAfLXSMkCLACwAsAnA5JmBs97Pe+iAzAbyLsCFuiwNoCD7CwDggXAE0AWD971yPjqJAvAHMArA3oCsCAwYG1MgnA6IHEA97EwPGDkm5wOsCmoK+2sCAw5wCxi7AZ++SbJAP0xKh37F1jwC771yICbXIiQHPstuk+7Ajxgb+0

kALIPACvuzAa+2sArAX0DMArAqqIpDD7s+2sAFgM+4kCgoLGDPuj7EwOHocAMwGgdTA5+13uD7fexgcXAaB9cCLgE0PhhYH6IBPuAoUyDPtYH1wAvt4jy+zMCIHSwOftTIqQCaiJA4B/hhQHiQBQcnAhB3iN/7iB6qiAw/B2sDLmLwHPuiH1yMuY/TPABKhJAcwBdZ8Hr+6QdX76B2wdoHgh6Qcn70+2wczI3UAvvb7FBwPuKQm+4vtfg5h1Qf4H

E+1PsX7ZB2gf0Hg+4uC4HPeycDYQxe3XvS4FAOv2f0nQmIisMN/TAAV7+GNLg67lkoHvpe9AMoDPr4u9+tirse/rvx76q2wNJ7ke6Yn/LuQE3sUEQe5SuG79uzKuwrTu7/jJ7WQFA5H4gK72j9oZ0/auxHVu6Csqrtu/kcy9Du4pLFHEeynsYQXsNkcYrme6IGygHy0kB57UKIMecHEACXsUAZexXtwggMMvtDH/h8Sj17PfT0ecrre6g0d7/B+w

hgHHunEBj7vByPsnAikIAefIDh3McJAbh+Ice6gkKqjUHG2N3tnAlCBjqEgKwBQfnHve5ccHHRxygeHHiQMcdvHDx5sdEg2x3ccXHFB9cc8Atx/8esHux2gecHBBycA/HxxzCeWHgJ88cUHyJ3CcmoJ8BCcnACmNie3HGJ48dbH5JhiceH4x14fiQvh7XvEogRzojBH0uIJALAYR3UesrmeyyePL8RyStvRnJ83uJHwe8kfSrYewZsdHZRxorIg/

Au7tcnge88sRHe4Tbsnr66ykfZ5q8FFvBAX0Daks7IIH4VaCoYWpSC2qPLzWYcalIeuyn5m2eFq7J67npfQEW24Ewgh2vqfbGAe3njsn4u3yd5HCe+EO2nkDsyAUgxe+EdSnmuLkch7SpzKMqnMIFA4aATtH6cungZzyecrcZ6ysmrsjXnjAAXu++hRoazIAAAUf7u8SQZwKcFHQp6bulHxwpKe8nDR/Kd27LR4UeO7aR50dp7pZ9pWsF0R/0ehQ

he8MdvdbZ+MdmApe2wDl7DJzMfV7uewsf8gSx43sNnZlS3t0T6x9LhdIWJ0SDxA5JisCEHX4OiBLnKwI8i/7zEFgfL7/Bz8fAnS58ainA7I9oAInXoMcf8Hve4WDog5wGcBvIGh/wfTA8B8kArAsYI8jmHSwLOZd7SQL3tH7+GOsBrAMB88AInxx+cCfI65yvtEIKQJceKHfKgfsr7UwOPszAqqAsivHWwDNAknJ8BNBnHeB6wd3HJ0IDDf77x+S

aCQ8568c4XqAGuenA4+3+foXHALue97/yPBfTAx55BcAXcQKxdfQN551Dn7mB2sDAXb+y+eLA8QH/vfn+B1+cwIO+8+evnwl7xf8XXF3edf7rFyxdvICyExcTA6ILueKQwJzRfn7a5xuezABF+RdznhIAuce6hF2cAUHdx5NAGXG2EZdxA0wDwA2HOl4DBIXJ0LieyQ0wBQcAoMCEye59J52CisHoFw8g0HfF4WD2HaF5cfiXveyYeT7vx8vtSXQ

l9IePn+B1xceI0h5eeAHYG6xcUHB56D1fQ9F2D1zHS59ucUX+GFReAwxV7CfEXTx/EA5Xy53Md4XNl7lenAGFxCcvA6V/xdzI2JyhdsHyV7fsnwXwMJe2HcV/YdgXeh8fv/nv5xFcUHQV+BfDXs5jBc+X5Jg+duXLlyfCrXbBxlfon6145eLnCQKD2AwVl4CgVX9V3ifzne19udknS0IsfeHVJ8Of4AtJ2aAhHYBxAD+nE51EcxHpq0mdlnWK40d

SrBZ4nvCnxZy7tcwWR+OdKr5Z3HtNHHp9nntHwN25wKr6Z50W1HX17atcneZ9DchnRR7Wein3sOKcrHrp5DdJHmN4KeA3RZ9nV+HMGqnbQgo21RQQrfJhTtanBjRiLG27NLuAo3AZ02dZ7Ax9Lg7A7ZxpCDHL1xMdTH/Z6IM7AFV9Scjn0uA3uU3pp29FrH7ezOezA2gCxgzIX0Mrfznq4GBuTQawMJdgbKQGcC1XYG5sBEInF3MBvA0+8bc7A6w

HEP+XaqMecH7aF2bczQMyOiBW3aF7bdoHRCA7cvnvl6Bf/Alt9cjXAwl+cApAwd27ff7aqMuZbA3t2BuII0+8+fW35+5sddQpl4ndoXGlyfAB3Al0nePHXwKbccAz577ePHfF7re23Yd7rergxqKkDB3X0EuenAut5ZcJAJl6Vcu3Q11ZdTAdd5PsH7zwIdcJAikLpcnnSd03dtX+B2uemovd9XfTAcyKVeMjMyH3fT36BxrcmXI9z3fPNzd/PdT

3E0PgfK38YJsC4X/d8gfK3wkHMgAX1dxNCd3HAMre3INt+fct3yt6rft3Nd7rfq3Qd5XdiHht4DBxABYI3f8H+d75ddXxqPbcPn2d4A8RIRJ6nfkmMwDsBzIEd/HdrXMD4W4D7Ad1A/7X9t9rfB3X93MCG3EdybcAPMJ7Hez7Htycc976D0Q/rAGF4/e4PudycfTAhD3g8YXpqGQ863T9wsiV3D95A9X3NV1dcUnPh5HuS3D1ysMhHTJ69cxnkR2

yeo3Dq3wsc3nu7P6jMWZzmfSPbp8Gek3qR0DfpHBN4GfKP+Z1WeFn8u+kep7rPeDe9HzZ9nvS4Q59acjH5j0XtdnZrZMe9n0x1XtOPAj6Oey3AZ4HsK3He64c/nHusg9D7I+6sBoH754Ps0Hc+zYfa3S+0AcgHG+xPvGHe+z/uXWHusftdQ0+3ECBXKt6AeFuaT/1f37j+x8DP7aT2/uSHcB1/v77v+13vCHgB9/fRPXB5AfCQ+x7AfwHTB0gc8X

J54E/oHmB0Qc4Hbxz4+dP2ByQfH75B5QcJgNB10/0HZh808sH0hwYdwnEBzwcqHWhxvsVPAB9IeRX7+1IdsHaz3IcKHeT7MAqH6T+ofLXAhzE/JPp+/occHRhwicxXDB+Yc3Icx2BvWHThz3vhXWB088LALhz0/uHnhzdeUn/D/dePXsAM9fMnsp1ysSP6Nwmc5H2jyTcA3aj0WcGPmR79TGPkL0Tf8n0L7o9k3+jynsI3hMNgzSiHN83sQvwK1C

//X6L7C+YvuN00lYgmj6SvEvlZ4QNkvzuxTfErwANTeDAtN40DttDN+4AIs9pfiYvspjYizs3hLzkd9HZjxAB83nQvntQAgt8Xvdn9j32cQAlexVcS39164/Uvjy549K3GT2rdcPmtxg9l3+tzg/G3vt2be5g9D7nc3n2d+7cUPHAOcBt31Dx7fnAXt4HcMPtr+cfMPmD6Hfh3yD1HeF3f54Q/wP6dzbcp3Or0G8AXID/bdhvedxbfR3xdx/dl3X

r5XfV3Fd6Zf13OD3ffz3S523d93F90/YcAS5z/uT3dxwPelXsYBndb3fF13f1YsCAfeL3s9y3dT3Pd6/ekXjbzPe73Dbxtij3r93ve33xb0fcnwESGfcd3eb9fd4jvbxvfCXHD5vcbYKb6/csPq4PG9DX39zg9/3Mbx7rQPnyNPsRveh9A/gPIb6ZdgPsD3HcW3CD+A8+vADxu8evZdycc/3RtxcAmvOx2QfmvxD/E9kPjtza8nHVD9a/Qn61/Q8

PvJx0w+B3LD33dsP8QKuBTvpl8vczIPDz898PmYAI8Av9J4q+T7wL5zcfXMZ/i85HnJ7I+NMm+PGSKPMeyi/unWNzWfqPBPeq8Y3JL/S+w3ON6YnmARj2I9vRorzzcQAFjxwBSvnZ8LcOPot8RY17qr9LfLHSLxiuavEAF0ih3QJ6g9YHEwCQe2XbhwoeyfPe4uDifzx19AKHfTxMDYQs530+Yn6nwM81X8n4RfvPXV9ucSokn1geafyn6sCv3ch

4uB+PZwBKiVX/T8p/I6KwBKjK3ch5p+Ofvx9Z9P7en/OcOfy58QdefLbm59KH+TzB9S3vz/B//PQjwyehHojyC/ofkjxh+0vzR9R8yjcN/C/vriL4x+UfdL60e1TZH2UfYvUaOvhfQm+Jh8Yrwr4Te/XFZ2l8FfmXyntinVL0J/1HtX1DdUfDX0ntMvkp+5yanygODJ47Ky2GP9fg31hZnLnoWpTrMZ0zpSFCwRKN/g7w35N9nS6aLN9Icj0qt9O

nX699eNnzH62e83L15Y8dnB37K92PIt4q8zH4txK8++PzzLfqvpKyJ9dIEH828mXWt2/d63qQEa/3vBd+cBmvLrxa923/307duv9r9++e3J78a8/f7r0B+evz93e+kPvl0XcBvJ71G/7v0d8PfbvOdx7ervBd0j9bv1yJ/eJvYH8m+13+byrfpvdxy3dZvqdzm8bAebwW893B9zsDIHS52W/rAI99vdVvE97W9r3S53PdP3i9y9+u3rb4W7tv075

heVvXDz29M/Jb8feDvTd7m+v3N90z/332r0/ezvXD/O+LvWD7e+Z3/96g9AP+P2qj23h74W5o/67/3dHvT7zu+W/SD5HcXvaDzD/Xvy743eQ/+D9b8O3ND6+9A/H73EBfv5Dz+90PLr/+9xAgH8efAf1d6B/gfav0L/xAEX/gAfLcH34c3f/IIh/CPqH+9dgvBLzt+JnaNzh+AAZtqAABnL4ffNk6d5f9X9WdtHSe6rzO2P+Es4qUbj83t7f6gDn

sLnkr1Y+sfNj1x8KvSrxCd8fKfwn8CfY57l99AnVAq+dvtyGMdfQSH4JAnAk/0/bjHYr8xCTQsCPzcF7B30Her/4x6QAKve+wfugHS+IwgfLIlOdh4gbIJWQpAPbGP3sgD2J0KMIdfIMfk/tyBn+Nnj39XeFXqb40/yHJxwfuqoTdx/9YPM4DAHckwbYBZCbAU942Xey753VB5dQGaDbXP4CT3OZ5InJQ5z/VB4XWGJ5MHRA42HGYDoAkg5d7A/Z

7PRQ6gHX45rXdAG/nGZAYA7g4gXQg7vAGAFQXM+7nAX4AInCAEvPMHoYXIAEnPJ95zHWSAqoAL77/BZCH/ZFDH/D5in/c/65AeIBX/EuA3/QQFBQB/683SfY7WQ758FJUB7RXUDrgG8hV6aXDYoF/7N7R752vSaCoAt15UXLYBqfJ/6zAEg6CQZn7h6O/5CA6XAn/M/77tKAA8ASQG6gaQE2A2QEsfDbBr7U4BT/ZQFogVQEIAdQEYQD5baAhL7g

vLP4Q3dr7E3Tr6V/Qr5FnGv6wMZQCyjWiCArfP5fQYv50YSr7IvKIGovGIF6PZ3YJAuv5dHNgD3fJj6mPTwEiPI74C3Xm4iPbv6OPTfab7Fx5D/Rv62VSc7kaRW6ifYE4AoAC7JAF4B1PWY4nwVg4nnA+4sYKZDkmYYEUXV44PHXoHfgEQ4zA/oEbYM4B13Ai4WXau6jA3E6LA1g7dAop6zAvN4wXXS5eXQYF93dYHHA3W42HbYExPboHx/RP53X

Af5p/OL7iHMIGZ/aI4Yfar6xnJM6gELIGRHHP7cnHb6/rQnhPkFlzsuN1ZOGVl6vwJvAQrSaJ6AGxAgtJLjYUGfgrjFEw03JvCvWdeB6AImhblbzgdzXcpqlKPBqUOOxqUdSheYWoj6WPU6YcdSiviIgDCCUkHwWFAyEgwtiSWVb4qKOCxIGBWBNAekFqUQACCfoAAVrMZBpIPXAuAA5BRSjuogADyo0kEogGdjqUQAA6q4ABbWsAALaOkgljYUg

1yAAAR6xApIKKgHILbaHAAAA58XB3jBrgN2AgAOQeWAV4HT5SQTzAOQYAACEEAAwiCAAQRA7QYAAuEEAAHCCAAThA5SkR0W4IzEpMGy8UQTHMG/g7hqhLSYbEJxQ/1tGwANp1R9NqbtgNtHljzs6wUMG6wqPCVJsACH4bALP44Nsj4M8ll5l8oMAFACTMwZB/BNZCEUUwexhkguDIrWtut02G31apshNh+r31suGxxB+n90HQD30FKGP1nAFK8p+

gpRZ+iv1p8Iv0QgMv1V+qrAk/pv0AaGgAd+r2C+cGZw4orREM6Omw1Bi4AbBkEMhNhCNTBo9xcgMYMiLKYNzBpVw9VgQY5RqrAjBulRppnENCQDrMIRvEBpweWCXZh84CgFetNZAQA2HIWCmjsBZpcA7hZwgDM5wX+xEBgaIlwYDB7BoyhHBp1YPBq4MXBiBD3kFkYdRkkC/wa1YVRt7wvoA8MnhpMNi+MUM/2IsMNcD8NVhksU61iuRYRpmAm1u

9NW1pxd3ph5QgRrsMCRhBE+eGCMMIReDsIYf0YRo2tnAM2siIba8SIXhCEAMCMIZsEREIRMMj+ChDOhmhCvhjRCgjicN57DhDbBuxCCIS2s+IMRCCMKRDthqlZCklRDH+JhCRBnRD3+gxCCGFJCWIe2t2IfCNERhRCAQpP4vdmh400P/MEIQKMsRsKMcRkQh8RutFrwenhXZtZ4bBAqNW7NSM4IVHg1RqKNGRkRhJNN0kXhDhooIWgAipgphuRg3

lJ/OiM3QJiMhRvSN1Rt+AuRgSN02A5Cj8E5C6+kbENwYqMDQMtMcNF5DXphqNfIf5DcgIFCtomyNYIRRcwoeFo8pE6xQ3GG50YDPQdsPTBzMPthjsGdhLsNdgvoLDhMVNpgDSLFg+YtZgVfNO5l6P3lKRPlRQ2Fm4z0BUFSCAoB+AqgAUKK8pX3DW4gYIORN4hKpPsDME6qAVRsMEoEBlF9AhlNOheqF/sqtANReoYOl+oTDAZVCQ0PkjAAGqJuh

AACAqyTgFYEqmzcUKVRS58SBkxKUuS9mWfcL7k3cm6DrQIWg6Ux1Dnc3Mguhm8wQADVCmUXByq0h2HDYJ0I6UuZHBgmZE3Q4bA6UOXTcUY0K1UYGDocADj3ML2EahH2CRgGOHtIXULjQgACAEu/CxuU6GoAHLreEVLaZgGODpge2BMAfKiP0L6DSqY9zYVU5zgw29wwwc1SboAgiPueaEvuckqPjMWCtuAghwwrVRA+dmEueQkT5UE9BfQM9BRoL

rRnQvEi3gSlRfuDpSvuAggxYVNCbodrS7KXGFbYQHB0wczBz4CHBQ4NqG3YYmFxaRVogeKmFgYC6EVg2vr5UIFyvKHLpOw8rjh7fKiP4J/BfQN/BaqT7CIeUnyweB1gxYMWFxaPWQ/eKmGqyYOEweZMqboD5TtSL6AWyHmFwYMihTMbnpawl9ymQ1NCceI2FI4U2Eo4dHBk4G2FVaGtxNaKmHnxVAC3WQABR1miQAYVqoJEvoRUAKpgMsPvRksB0

oLCEBgqPPnDDobpgkVKjg+oVFpvcJugstFqoIcIPCOlN6RkiI+5ItFtg8cKbCicCThCYaXDxgVVpltEThh4dtpCcIAB1TXg8SylLwPuB20ryjnwe+GOMrbgEMhOGHh4hkkMkaBMM1bkAwbcJPhEqguMVxiqUUMN0wgAE8MmzjRwjpQaYKKSiYR6iAALEC7qKD4llK+4opFR5aPOHCtVFmN69Hm4nDEoFNSIABuAx8kwRB8wSaDTQZaCrQdaCBggA

ACjQAD05l9BBmIAAJCybQVcV6wgAEolarBP0Ko7oRWSiZgFyDRCIGCnYZLCAAfqUm0GBgJsGtg/MLphoellgDSBZg8MIRhiMPGgvoKRgm0GBRWMOxhnyKl0XAFVpaMHRgN0Oeh8yN0Q8wcMQFANXD4OAoAjiCBhC7jIjOMDxheMCIxrMLpgEsATCLME91W4SphRMJ+JidgpI+Edpg0sFlhcsBZhnTFFoqsBwBasPVgUgI1gWsK1gxYIeg00IABXB

MAAVfo/woGA9uU7BLocNiAASO0voI2Ul0IPDN0CkpAAMry1aDpUgAGd5SdCAAV9im0IABxxJqwgAADvQACAtkDA9ZGQjsMEnDU4RwBIPOnDSZNz1KMJNgjDCr5AALshDOE3Q/WDpg7biGwTaAe0Xyh8wX0CTQQMGh6gAApYxEiAAErlAABty+6HlgIM2VgoaFnoCRTGw2aCdYhaBLQFaBrQ9aGbQmCMTQXaDxIsfUHQTCJHQE6BnQKwiXQG6G3Qs

aD3QR6FPQE0PPi96EfQkiQ/QP6AAwwGGjcYjngwIWFQwmGFwwyt1ERJGAow1GHowA1HywhiO4wfGEEwEWGfoyxhLy1iNUwGmC0wUVEMwpmD3o9mCcwbmE8w3mDno4zECw7yJCwUymswEWGiwcWESwZQgURaWEywOWDywHiOtMPiIaw7GACRB0I4A3WD6wi2CGwoAVWRk2Fmw82A5Rq2FVgvigahdMD2wBmEOwp2Ghw1sMewm2C2wr2BFRtxVJw32

F1Ef2GNhQOBBwFsJiwkqMxwb+0RwyOEAwSMzXhLcGxwC8IJwxOEVR5OHfs1OGWwuMPpwJ2F74rdAH4L/DEIQuDeKYhGlw8uGVwauE6EV+G1wMbhCSw0M+CHeFdw7uAwwZeBCSuYg0IA0g7w+RE7cvuBrykaPjwsBBLw89B9wISSK86VVAI/RE7wcylhR6jWskCaIQIU+D5wKBAgAc+EwIlkjXwpXx3wu+BCSqUITRN+GoIr+BCSGGzR8CaNcksSx

XwWaKgIHgg+I5RH4mvYyLRM+AgA6BHLRXJ1wIysLKw4aMTE9aM1wVBBpcaBHDRXm1AI7aLuGmEg7wvBB7RvxFYIAUhQkohCiI4hHuE3qJXwshGrhhhATR2hGbhBhBsIxhDMIlhEAwBVFsIO6JpMrhDjwcMijwL6OJg7hAcI4pCqhkWgCI9qM2EjNCdR+6JiIcRASIkaBSITUmJgGRDEcUxA1wg0njwieFQwxRFKIsBEqIoWDowtRCniZghmI3RE6

Ia0m6IJxCjw/REGIwxE6IYxGGIRGLbwMxG0RRTkukchHJSCACoxP0BIxRxGekRxGYxOO3OInxCuItxDik70k+I7xC3RmuBhkceC+INJgRkr6KBIZWFBIR0mRkAxBhIZWDhIBQBL0saDRIGJBmIOJCUxeJAKARJCUqssi0I1JFpInMI/oLJDZIHJC5IGuB5I3BD5IQpBFIGuDFI2MkLERMjjwMpC5a4OHlISpBVITYDVIkM0pw2pGtRkWj9RS9H8C

FvkpEnTG6Y2rFmhC6E9IUaBnhvpCKI/pGDIYZFDREZAURUZBjIyRHjIyZGuY4DDWwUnhg2SRiD4lBDeCYWLkKf7G7I/ZGWhK/lvi98QACr8X449GxL8lsQT498RVYpLG7Ib5DmhLqVVCQQSwoq7Xg60Gg6xT5CQo3WLQozYHmKmRhF6DvVw03ZCIopFAooLcAF4SASZmwsCJYK2IlK04PFmHviCCXvj/YOiX/iroxkoclAUoNiQ0oWlHW4BlCMoJ

ngcSMyyPwCnFsoGCSkoySUwSxMHcK6QjJoUeU1YndBmoCbimCHOnWhHVHyoDISKo/2O0AJbnZAZbm+EnAWBxahGaoubnaoThm6oLKIaYLjiGoOaS9EEWO0wu9Cixc1EWoGGJWo7RDWowxH3ItGL2olyUOoqAGOop1HOoCiInQ11FuoD1CeotxFyxH1Fi8hWL+o7GxeYNeXTY3ZF9EqGEhoXOlhoYiiX4P7HnSoSR/YcHTxo8s0EYwjFmxyYWHSGG

JpoApVTiP7GFSa22wor6WGxWDVrQ/NCFoItHqy+EKNm3MzLaCs2woKtA8AFfELKW2OKxMuK3mHnEkiSXHron/EX02s1cYfExbgGpl64YiQ9xfPCv0pnCLUT5ATozkgiGGdDBKynAbKcaGckuazK4MfArW8fANm4uOT4X2PboWrF+xvdAHosVGHoo9EQA49ADYU9CMWkWjjyqaJCxAW0ya/4ixxOON+xFMEPoc9HmECiPPosaGvot9GnE1mClUuaI

cR1ki/oXuF/oADGAYrONuY7OJk8RWOeYgNEOKFfH/ycFFGYZyli4cKNeKF21IYJuJNmgjBsqRw3oY4uNyYRlWoUcJQGx0uLBhpNG1x4jEkY/vFQoZjEC2CjCVmdMPUYMm2mx2uLVqGtR+g2tVRaL+SJYFtT1A2uMLqsaB8Yn3GnqFdUaEQLRbA1dVvxN7EmEumDTxfmGZwSPjS8jBhyYJmMKYEBKgJFeWWMIyXCA7LVw+e+HRxN4O3WleKmov2L6

Yv6CGYIzDuKnRWsw56CmYMzBzI8zEWYyzDpx46HWYB0h2Y+zGOYpzDowlzAHx+WKHxgWW2xrzCwJtfV5xvvE3w9jWQsgLGNxduLOySsx8mmjGAJ5NFcYmLHsaLMm6aR+AmK2uIpYm+EesP41pY0HUZYEhK0JN+OJYcm0TC3ZF5Yv6H5Y0jCgA2E2K6B0TlWYbXlYirFwmyrGkJKeK6YHdD3okBL5KPfUNYyM1NYu+EdY1mDjhRYOTKYcM1I3fA9Y

BmC9YJ2ADk6M2Hx/amDYYbAjYvXnDkjvh8SD3nTYhmXzYhbGOSt6BA4/iTrY67CbYLbBnknbG7YfbAHYQ7HXYa6gnYaEz4U87AfgvYCXYK7DXYI7E3Y27F3YvgANBLcCPYWcibAp7ELY2JXMAaBmMh1XAwxT7AVsiRNfS6bG6IgHGA4fCnGI4HEg4fCjSJxmTI4yHEycShDQ4GHCw4VrBHYuHDbkhWVI467Ao4AiRo48EM20BRCY4VunO4o+JCSm

JlXUD3kaxO/gY2+gEe4YuN6Sy/Fk4yDUU4TuNVgMkVM4GnCP43CSbi2sxFoF/ANqkPEGJqAEs4kaFs4Ctm2xYhPTYnnF+JfKVdS/tCBJnuIX0KJKXsClFX0iJL54Q1Vog4ePVmYfDS4YgAy4mJOy4RXA4AseKPwsfDrovnFK4SeIkQ04Po4dXEa4FxMD4VxOGoFa264RfFN4ZJOcKWlhG4ZBjG46bR0s7kC7sIpNm40gBM8qU3cAK3GNA63E24ew

WR4+3DC8R3CGJJ3BPxvXlhJ6G2u4aEO9gDlhxJ5I2e4nljW2hJO+4ppPV47UzsgEPAtJ+FksG4PGYQBJMPB3sBh4b3Th4FVgIAyPFv0WJIx4WPAlCTpJOJRPBJ4DnG1JGTRo2khT/M/hUxsXWQ54hjV1GhpMsaygBF4i3yl48OxUECvHG+cOxl46ZPV4LIgcsqJLpG5FmN4vJOgE6s0TEmggCKaZMd4snBd4R9g94qJPBJnFi1JxWMkJpAnJJV/R

CSNJOK4dJIT4V3FJQGZO8EN0wLJKEOumeQ1b4onDqWD0yHJfJLpGdfAb4TfGB645JAJxSS74CYJ74JUkdRQBCYAy4jH4XQicSZORn45sVoEYnHCqY6W34dCVwAtAkMioQiXsXLn+J2nFoEHcR16xgkLEcJJjiz/G0EK+Hf4QuQSEGpmYE04GYEeJK4CreHf4sAgCYzgg262AlzWEFOPa8LifmsJWyyV+M+2bZI8EXWSoEARVoEzN1ZuTAhiEi/ET

Jygkd4Xgk2WfAgIpQglh2vqwSEwFQSEcglzUebRN4pFM4ENFgrJTPASEh9hYsWR3QMtMl0JIwjJyLghQEFaxgp9ggO6CQmQsai2TOb/FvJkSzEpCQlUWW1loEL0yWyN5OgEiQgXJ60xfJKElcM1QmyEjrSRKBQnEIJQjKER6J+gcJK0ptQk6ISFPZ2PoXEIx+MfWS+KTKfQjMpys3ZYkhTGEahI6EfmJmEcwlPouMMWE8QEnEi6DWEKTkDkm5ITm

i4jEAO5LDgT4nXEnVGwm9kXuEh4jXEwIh2xeRVtI9kVKxYfkpEUVKSpuYjSpxhWDEWVIREUyVuENeQKpt4kTEuVJDmiYkLE2E17ETTQHEKRAuw/ASZE0oU7EIonjE3IlQAfImqx8MnCMQYkscEogwwPKWyy9YnjE+ojtEqDRVxU6WX4uST1E6IhzEwYmMkI6WGpwYk9EIsHGpCDRQaGqQDEc1NGptoi5SUaH9Ey+EDE8YlzExkjspVDG7EK1JOpi

YgQkIJAIIyJiliGYk7EWEmup/VLWcBYhQkBABLE5YirEtYg8EI1ITEjYlQAzYglyrYkUJfehmpMOSfRy1Mschog6pA1BHEv6KhmqAEWEPAEnEQzg3JZUh2EvQSXEhwl3JhdixE0VJzcDegqpZeLTq/4lKpkYPWUtwk088An4UhNOyp5VJppcqnUElNN+gP4jXCzNOWM+kngpCUiCkhMBwkoEnOp5BT5pDeg8kt1KQkHpQAiX1NQA6EilpeeBepAt

LskxkgIIh1RhyYmlLE8tKjw/EhwkI6m3qkaGckreGYkA2g4kmVS5O/EiMk7EPwqEkikkllVHireAUkSkg5pIYjVEcaG0klkh5phkmskxkiaqQlUdpSUi3oPtKckd1GXR0NNbw/EkJEaAFVp5kkEIYdJx2OtOVpqAEaqsaG/qkaCeIotK3ohohwkeRkjQQpBykHlLDQiwgmAhUk3wxUmCpWNJ982IAoAVUnfYCsFqk76PqkvGCuojUiDRbUk6kPUj

vwfUngxMxBGkiGPGkk0kjQ00ixIc0jAIBQEWkv80HpK0m4Ia0g2kW0kLE3GLjwe0gOkR0nukLRBW+9GOukr5D+kq+FOkT0h4IL0k3wb0g1wq9IGIn0koIB9N+kk9JQkvaOJgwMmdh5+DG+FADZkGuFExt9OFEThCRkHeFRk6MjcpmMk8IBQDthu+AJkkpCMxiHgpk1MgMxLGPMxkswjCeLGfpy+GsxceD5IVC3Jk/Mm4IMpC6COWklkMskLEhsiV

kKsmqwUHhDhyZTAwKHgNkRGBNkNSNtkDshdkSNMpwbikW0yNL9krrEiJ0G2iJbJLDkn7BCSSih4UglFVgpNiTkKcjTkGcizkOcjzkp8nPkpcgrkeRIt8dckmxHADvk1LTbkHci7kyckzkpYD7kt4AHkQ8hHYo8iUSk8jE4M8jnkC8nfky8nQUX8kHqWeC3kI2T3kB8iPkutjPkxcikZlclGmmzXJa53VVgT8hfkXRNMZkCnXYOYJwUNjL4UICjAU

ECnMZmCkLYNFlwUy9hbgbPjQUGCmgUJAmiZAxK9aDnGeS3DNoU04EAI7GCUUvGz4KjCl78LCikU7CjPsdim4UE4AdsaikUo3NQXGa20qZxTKZiAMyUUiikAU9TLYUwbQS4aAG0UHDEf4BiklYxiiooh7UsU1imT8aekk49ii4hfrlQAbikNh68P8UYjgWU4qiWU+yliUQZHiUryiSUaSgyUf9H6UWqgKUxSjKUFSg/hflyOhnbmeUa2FeULSjaUk

xA6UE6B6UfSmyUWqmGUoyjgwEykhhBByq0cykWZFzIlUKymlUtNOUImyh2U3ii1U+ykOUJyhnxryguUVynjQBqiqyy5iq0Tyj/oKWkQ8Hym+Ufyi1UgKmBUB2G6WSyghU0Kl3wsKgRUTrEHhjDIaYmsIxUcWgKIYqh+Z7S1LQBKgHYVQVJU5KnVhazg6UjKmZUbKg5ULpleU3Kl4wvKn5Ugqn7h2mFuUNLP/hSMCPcssPlUHSiVUqqg1U40L1Uv6

ANUFWki05qjnhbDMCytqjRI9qmf0pmkXAYvgY0uIMUo2akM0DlkDUgeNQAjKjDULKgjU2aiaAUakP0O1mOmYlkZUKajTUGaltZU9lQWs9hwQzrIbKjKgEw5akrUFHAcs5+gbUT5GbUKRA7U3ahfsAIMHUw6jHUk6hCQPrRCShmkXU0zij81BSDaiGjIY++IoKVBX3qQbTk0aACvUCABvUd6idC2c1WWTnXwAr6gA6W0W/Uv6gQA/6jlmebKlxaAR

vq/fng0xGjFy/VSIY8C2EqKdWI0wVVui0eGdU+rLiZ0FUwAprM3gfsRKKummlK/Gl9U9HU0WQBRI6Smk048mko6v1U3ZKmi4W6mj8WnxgXZ+mjtAHmhYSY7Jo0E7PM0U7PtZVmmD06Y0TCvzUFSi7LPZu+I7ZG7WsMBeKC0oWnXqbLRno5WF3MczPi0jxCS01WBS0cJPHhryiwZeWilkFWleUs2nK0JqhOZumDq0DWkrhtLOswLWna0nWilp1mB6

0fWj5wg2lLQI2jG0HSim0M2l3w/QSdYC2m/cS2hW0xzIKx7DPo4O8I1EGMwUMGOJFgWhg22BeiL0j2me0kxLe0YJgA6vC0y27OjhMnZLhSIaScMPOih0MqjvSCOiR0gyDx0eOhnemOjjwaOjx0BOkJAROnX05Okp01Oi7StRQZ0fqWgaagBhMqVCFSMnJQa9GSF0hnMmMauhwMIeiGMcukrSyuhw0HRlt0mtEWMNAzWMh2l05N+gt07nLVM2Blaq

ixiFyw5nnIHEyC5Uxk859ukD06JJd0JBmuaJJmiCMejj0WDBTRCuGuM8hkN0FlP/EXHJXGPHP0MxenvcUaHdMp0XYqYC2Wc4nLy5dkgrMdJkXxYhPGylBW30nnAn0++hdx/tApMYeQS50kGX0NVne66+jv01VlH0bXL30O5U65IYigQcai9xp4LjUq+lvS+nLv0GuEraAEUXCrVX9SdenzyLxUA0yS3CAPYxvCs20gMmHAu6i23gMLIIQsZPQHZH

nNoMeBiApEXLHaJcEoMExhu5OBjoMDBnu5axke5FBgn8aXJjBuMKvhVSSY5YhgkMUhi9wMhiqUSehy5h2kUMyhjH6yVN2cnAAK5KJSK5BhiRgRhjB5J9HK5npg22rjDsMiQU22demqMknBzCyJkTGXhmliJPKJMA7MCMwRkNIYC2AKNXKGhZWKaaHJmyKyDiaaBRXkZBJW7IOdJ9IWRU40xRlKMr9BlidVCqMxYSp5v4Ts53ZBaMXuFwamuA85XR

mg6cxl25/Rml0znJGMi2UeaL3OC50xkiEyvOvKAFG85n3KS5alE2MfRT+5BdNQARxgY5nBJqS3ZDfh2XP20hujuMzJi9c8mAN0ISBeM7IBRA7xn/GXxj0MvxkTQ/xkBMhiLdMQnIq5uPOdSYnMMKEnK9E9XOhM/JkFMpPNmamYHTMApkJMUQGJMXuldo3ZApMNaXPadJm1yyfnlc7vP4SsaRw0JRUDSXOiT5mfNwAa1DSyAuls5YCz5yBbFL5nqX

L5giTs5HnL5y8aWL5EXNHM45mNMpplS5pLCK59KPtMjpjf2PACKwpWGx5Fekq5UJicMS7WbMBZmDMWtWLMa/LLM0Zhp5T5HjMiZhlEBRnZ5Q9WYA6fMzM//gHZs8z4ELZnX5oZi35UZkC6VXLX5A5m/6VwSl4N/KWUK/Jv53fN15Rimf5ISEHMYQAH5eoCH5k5kAOs/nnMy+wgFYd0SAnE2n5q5m4Mexn+5nfFocD+EA5yNKPMOPkY5gWTU20JgF

8McDvMD5ifMqtlZ8H5jTRp3h4pu2yhpdFmFsSFiO5EFnEG0FjO5uowu5dIKQsQ31TJKfgwsKfEzJvq3zsTdhY0rdkDgnrKLJlFl9ZP+PUEzFKFs9tmd4+gnYpj9mus3zDcpNNGg6uUwkUFazUFmuBCmY1m7I4U0eZreCimOgqfIsUw0s+gQFJkTLFJMTNbwMpI5KalBsGZPSssEiE0FblnssrdkKmzlhKsg63KmhbG8sQ/j8sNUxJ6MVlIG3oiam

LU1amHBD+48VhxQZA2Vam02L6fUw7WY+B/BI0wbWhVjcgE0zumLpNiG8PC5oVVi0mvpMz6jVhWm+fRfQXU3iFvVjWA/VnQMg1lqQYlKOmmDSmsUaEgm61kqWdQtumC1knJbQqdZN9jnJhbCOsJ1lUp51g+A+9nlx+jkAw91nUJ5sVesNgjxsTWJd8lCVg4J21ssCjlpsaAChs4GWASCNiASSNjegViQ4I+p3zsONjBSj8WSJ8wpyJP8CWF1NiCCq

wrzo+iSZsLNmx4JiU5sFggsSvNn5s+wqjJ0gtwsDmXFsVvOlstaEvi2ApqSuAqcMxAuZ8atnM0HPgHYDjKNZWFJBKuo3TsIYkt63nGzsudi+FDFgeEuiTWFpdi9sPtj9sldmDsodnDsddmjssdnEGCdhGFiZM0FC9gtsKiitsmPTzstAsxFh2LdsHtlxFFdkDshIrxFEdgpFjdlzJhFhDEiIvKsS9kvsvdhUUA9lPBd9nHaj9iL2fIsnsogvzU1I

um2+/FFFa9mXwEopNQUoofse9jlFKzWYs7vCVFGdnPsztFVFPU08hG9jiGWorHsvACMhyAvfs7bm/sv9n/stONdkGYLS88fSy8vTkWqmAC4ctlXhi6DilgmDhwcvTkIcsmT9FFBDDQpDl6cFjXYw4YpXw25jQFGzhBcGq0ya1Xgb0votQAPDj4cAjlHylvjjFV+GUc4jkecnLhmKYYu0qhYvmc6jltcD3m0cv/jLFBYrGFhjhPxsLnMcK1OWcGYv

jFqADscvThaS+YtbwYaBccwrgocHjlOS3jljFQlTDQfjhuZwLiCcMgAqcHYv9FL2lqcM4sYcNtHnF9YsXFSTmURQVM5cmRKUI64vHFDYrJxxTh3FpTjuSdrg3FX0TDQRxGXFLYsacc1Km8rvAXF/Yq5SCuG6cIYqy8fYszFtxTRIIzhXFsVhZEkzhPZggBWcV4tQAcziHFSDkPy23MPFDYoIIOLndc2zjNcFfH2cjMPHgz4rDQALPOcAjgCZT5C/

F34vuceH2LFpTnvW26xw2DYqjQALiBcnLlXysrWfFP0DDQ0LjSxd4rgpiLlglZlTDQyLlRcZDgucjDkpcCADwl+Eu/F2Lg2cNEvxc/EvolDEtnoqGBJcLEvJcBLipcl4rzwCYtpcFDl5cEQCEl3DlQAQII5cpTgvUYrgZckks7FAri9wQrn/FornUlRkpfFUrlMlMrlJctrijSATHr+u8RVcL4rVcGrg4AWrh1cHVD1cPbEEl+YrDQhrgPhzziQc

OznDSrrkdcpTgvFHVDy8IUrwhgYAil2+MQljTkclrJh9cCTimZQbhqhAnm0wnyL1wcblLQf2JKo0wR6CswVhx4mKehE0JaoiCJiphbincEOIL45bhnIKoXd6SykWhwOAbcnVLvwl8Vbc7bhcc3bgkcvblTQ/bkHcdGFHc47glUk7lBhulMk487iXcXGQlUq7nXcm7m3ccWj3csaD7wB7loR7MIBZZ7kvcN7kxhvMPvcgsIlUr7nfctbgpZCLN0wA

HiA8cHlA8bo0g8/hK1kbsIQ8VrGQ8rBP1krylzhWHjw8BHjNuVWhI8ZHiWZ1mGlhMCI6UDHgfwzHlY8rynY8D+D7hyNJ48/HkpZVWhE8Yngk8SzKBFG2jk8D2gU8SngWljnCnyHeIZIJeSFmiBMM8jBlq8OyVrR1rVJlaeVxmoZ2p8bnkJlGrR88XtXI2hrRC8B3HC8b9ki8LDJzI0XgxldvKxlrjDdFDSRTFadS68uXkildXiWS4+Mz0JNKFm5X

g9Fd+TTFW9Bm8m+WZ5GVLkKisvN8Yfit8KVInyB3lm80+TREsJh1lLSUm8pDXVllrV4JC3iVaS+UlauYNIltfW28ueG7Ie3m5kRsoc4R3hAMFlNc2wBDEJV3hmGN3kjwJwqL8jxMl89spQJivVzwn3ji033iRgv3nlsbsqfI0sJB8sCLw0hMvaOcPiVaZMvAc4e1dlYlix8c9HUJMIVN2BPlkSJPnjh5PjllgYBp8keDp8WQAxCIvghF4viXkEqC

1s0Ivzke1EjwEYNes+AsIFwvmfM4ItIFbcsl8DRhl8cvhhgCvmRpSvl5aJSQIuqAA18vLUxlsGyFa8G2VlyG1zF+spbI+RQScVMrDl31lfiDfMl59fOSpu8pnIXsUVlzGxjlQfhTZ6VKUKBsszZhbOQiuAXsCVwVQgjbQ55KVKz84nJYKh8ud8x8vPlZ5HL8QjEr8hXXfCpGxRAjfmFx1xPEy7fktmnfj7aBTMlYg7Sv8lhU3gjxKPifHS568/ll

soxUrlk5F2xZPlwSSRPDlzWIP8wCunIAlHhaYC3P8toUaaP8t/8d/hHis2QLZgEV/8YuU/8SxUYVF8qz8dJXqxu5H38wARwVnKTfIBPPMhzhQA6iAW557bMCovmkDGbnTOCb8qT8cywKCMNSYCC6Emh9ITUItY1ZC0oR55T5FgiBEWaiswT0VHIlQiNjUqxfZG6CpgQ2hkgRkCDaAUCygUmWQwVIAIwVKldVDnZ7PKo6oeDgAJgU4CaQQM6eITti

KisYAtoVws+yzqKoRU8CXgUoKD8sCCT8q+C4QUiC14xuKCQTEcUzXGxQSumxXhQrGOQXsCaiqICGipKCZQVfI2iqqCNQQZEtYwaCPNmIpLQTEWPBTEsXQUBxThio5rnXUC/2I8Vdio6ofsTcV2gR6VVrnmCZBh5sxEX86GwQnIB0R2CoLGCAASpVCwSvMKyiouC4SoLY1wQi6l7TCKTwXiVmssflF8vtwS8rEcFxQy6ozQgCYIQwwEISyVic0gVc

WwpASIRuVKIFsosCsxCCCqhyuSq7ZGHWuVZIXZmKCygVoiogCtIWlCVAU1qvzX0VLIgDGYC1oivIQLYXYUlCqsTs5MKqlClQVlCwnL9Cz2P1CaoXTamoR7CagHLogSt3icirAopoWXCnswAmzqRtCekQnArNT9CA9WRFspVDC1KpjC3oQwgzNUM4EmhDCnoT9CVkTIMVlM5V8YVRGu6RBIqYXTCEGNpEUE0ga+5NM5XFUBiEvPi6pYXLChbFViXi

wUaIEUbCm3MaZ+OzbCpIU7COXm7CYYW1aA4RiaCCz2544THCKqs3Cc4TJy63MbCZ4R76DbEYS5VB766k2dSB4UlVZqtPC54TegTqsLRp8rRipPLegkgzfCljjZmSS3Jitw1SGjMQwS4ER5iJyppCWiqaiTETMVwKv56FioZE4KqX5nVBiilEQbY8atyidVHyi5EWNAOQCiVGaoBmLMU66OauIifEQKiAkSKiM2IAiFat4iyIH4iSGkLVEaSWgZUX

/INiVBaPxMX4CkS1C2HQyi+JjUijkQ0ivGS6i4MV0iwAkpVBkQ+YNKtMiO/FWWFkQ+YXKu5ANkUI09kQGiQ0Uk4bkQLYq0S8ieeCmivkVmig0UCiSsU66yUUNVOWWAimapyAbMUSi1TOCiqUSqmykUyiUapQkDaryiVavIitaquCHauCY3YGIAVUV4ScE0uEnRA/VdVFaigGvaiiTScMe6qAEvUXXVfkWPVW6vTwI0WVVyUUmiiGqPVTkRPV80TP

Vl6pWiE6uNqzJVk6W3F+6u0X2ihikm6bAGOiEfJx5K4wEmQY0kq/dTPm90RFWV0XYo7GHei03MkmRnMyWllFk587OlVlPNlVssTg1ygChiGsS8W5ZUQWp8t413iycSvqtk5ahVAiK9GfCspwJinyob82gGNV4mX+GGkwoIhq1pi6lI7R6RSOyzMUAId6sI0+5AA1TkTfVMaxjVS8QFiQsRFVz43FiAHTTEImu3GcqohsdTIfVEkRVi0oXVisMQJV

mlLlQiHUPKHsUoKccRax4QGtiEmlyKfCt/8jsTNaHYj9ikWvrCXsVZqvsTJyWAUDiB2IkoYcREo2mSWG/ky5OccUskwSRk0k6TFM6cQMAmcWK12cXSKHxPUoUPVQARcTLi5cRriXJw4SQlQySPCWbimYFbiYSy5OT5Lts3Glux2E0Hi8qpHiLlQvGJmucqKEijWWbV/Zw8yXipJQ3ibmsqaGCpH8h8TtYaarES1cPwVr4UjGq/iIVTCsJYACpUSv

1jSJB3M1wBWudsawv0S8NlASq6V2FfNigSZ6pgSF2PgSPpzuxppAexqCQr0biSsM3syvwpCqu1jxIWFJCSiGLxPoyixJoSoBAvJXTPZ5jCU9VI2qvwSSVU1qSQJ2nCU04jcSySQ2u9SuOU7E5PTESl6KkSK0K3ahCvkSxCsu1+jOu1b8XOFd2p+gD2r0SGwvuFRiUeFqA1MS5iVEAPNj2FreDOxdiV+1iCXSEXTOE5IOt6qniTuJZCqPlhCWZ1sO

sxo4uMhSROKR1LWoDKnGjR104ASSmOt817CTSSeOvvJA2p2axOq9ypOsKSThIXlZSVjRFSRXlQsrXlyXjplCG1hWmeQtlD4tIaBeQ1lwcp/Ya1DBh0yXQ00cq5msKTNlUcuCcZyUo2SyQxSg3ijlGyQj18rQh8eyRj1NySOSKHH3F8eubaFyWZSyer3y5XnPFWyVSZ0bHSZl0PrygbSc0fyVc0jjj/lMFVBS1KRs5ROMNSIesRMWKRUUNKQXSYSV

RSpKWWSknKpSmuDb1EuIF0COo+hpKST18fJb1iKUb5yKXpShqWz1Y+svwP2056nKX5xsok1qFeqB15nKdEecVBawSVfSoWrzZyO2OqSqTs643muq6DRQkWQV1SMYwNSpKSUUJqTNShKLowpaTtSnuXUpiy2L5DbHb509WG1xaqhqb+ldV1fN5MwaR/1DaSIabfNSlnfK00ffMm5MNTXGMSuZwmaW2VpsuzwkuQLSG6GRgpaXLSwVWrSUaH3I9aS2

sTaTO62vPbStTAHZEapwMfypBCVNCVxq+vHSGsBq101NgVSnE1xk+t/YYC1EykWT+iqDTvC5uV35bpW/SAnPxlg7PYNa6TvC0+ivSagC2y8nPaIyWVssqWR91TfPaIsdRx2LGVmJrmUHZgGRgyIGTQy4GSNgRGTgy+GX0NehsQykGRQyWGSx02hoMNu4FwyhhoQyhGRMNu4FIylRmEN1GRSyR2Xoy4xCUNi3T3Sx4q+hyFU6i9dXUyDbCEyQqWcN

emu5yamVGoGmQQAiWSIm3lnIAbTmUy8RoiNYQCiNiWRK1SOrcNLBuh1cHCKc1QxE0cNQFwxJSfI1mVsyPJDUN2GLESzmStYahptqHmRENUWWTZvmWdicWTzxP1A98wWVCy7GrqN56WiyTRsYocWXI1eQBCy6RoZimRvkNU+r2InhuXwchohSYSQKyxHFI4FBqXi2DXl5kirHS/KT9yhDUoajXLbZgJXIaVZLZ4rtHWyY2QdwImm/GxxrW2zXPYVA

EXBygGlmyqZRHZxkVWygllFyVOW2ySGF2y+2V3CvuX2NXArOy4eU9Br+uXwkWuPKD2TPK70QeNlaS+yP2X8N4qv+y/GuYAQOVW5NQ1uNF8yhyZOVeN8ORpyUuTRy6OUJUL+rpipmswWWuUm5b0WeNduQuyDuXFykuWlyd40VyzOUlyJJuRJCAF1yYapRNBOV1WfOXVy/GsvmwegxN9uTFyTuRpNImjlyjOQZNmuRVyCXLVyBbAlNuqyFyrJpRGfI

0wWeCyZWpIyDBpuQgSPBu5KA9Vtyy6IFNVOSFNLuXuo7uSOYBJoW1DmrM1kNVH0vxthyjxpWyF+gBNkzJXJlOFjyc9HjyheI1Z9vKxm68szB4svFam8rTqqss4c+8sJlxeXWUtMpxmRni/EXMxL1QZgD1beRFgE3g91V0JjNOysSVeyvnyUcu3lwBVTNknB3GINB1luEtolKxVzNPTTUqCsqjlB+UTEQZvXyCeod8p+TBhNlXSETZtrlyFL4Ub+J

nUb+RT1QerDBhCnvlrZqyaW4jL1O8Tqp/qJZ5T8q/l0BXFx+ZrkGVbWUIKBUTpW+XLNW9C/ll+IHNgmyzZyBXDW4QFzZM0vLaE2S/ldLUYKsfJYKoLQ7NHsyaVyCoEKa5rDJAm3S27yt0AaO2wUPCtkKTTSZ5oWK1lrPKGZjY0fa75tlVKxTY6ehTUgBhUNSS5uAtdXIHZNAR21gQi/NSxunM9hTnojhU1qLhSd0bhSvqCismWEYww6vhX1OMGsi

6jRXCKiBrJpRxViKyyITQiRRSKXi316QJqjwQmtfNT8q55fWMyMlfJ8VpHWcaK/OqKl/JYaT9TxgRFrfqMRXSqZFu6KqSrgts/iGKiFuLqaxrGKXVVoVISrOC4RlmKE2KYtyARfNTjUfa4OI6o4nQJKsBp4tWyv716aJOKwxSOVaSu/a0+IeKoxXBMO3PTwxDAXx7xSa51DBoh6+L68cIsYEBJOAEzAuUA4eIQpe+r3NCswP1aJX1qDCvg0qfNxK

t1We5dnP0ElmSfIpJW+aHBHOqC9SdqDLX1qLOu4CyVs9qHJUhNi2Qam4LUrAR0yCSMLTNFvYwlK6lBP5spRJ1MNT41ypUsqRrITiIFHNK9DCNKXPGtKnRFw6A3xctRjTZK9VpkkvLzBBs2VatPVsdKOFLySFVobK7pWVVQ5Vaq/pTotrdSuVV5Qwqe3Mkk7EK3xcZS6iM1WvKqHS5KXkAzK52CzKyDhzKVhnzKiZTmq3osaWRJqNVlZSvK1ZTtNd

ZV0WTZRbKbZS1pENVniMmm7KIyyvKfZQWtELCOtw5TBqY+G7IE5V8wYqs6owFSCt2FoXKggv9gy5TRKYZWr1m5VBaSnD3Kl6pBNlqHuyp5Xuy70Qutn5SutUJvuylLWAqzDWhMb5V3Va1urKa9BHw8oX/KkHnxtoFRXK4FQbYdFU6qsFQu08FXPG/aIv6aUNXG05XQqMNtdVYZq5he4LU1tEHEk4FTf6xO2Wt/wzcqT5GoqMdL4WdFWXIjFUE0e3

OzKWAEX5QlX3CjQnpt3Nokq/FSwAyJsY1IlTvy8mtxJElQEVk1VFtRxScqYiSUqGmqAtnhnUqZ+Mya6K20qGpu1actyqtDVpMqOOz61FlQatq8FXx2lRc8sp0ttCFJ5GNjUwaHlQpKX0XY6EyBbqABKZKQVUrS3VicqNMXMyUVRiqZI1gNKTWxgiVT4tfDQEtJxW4kaNSCUv9QDyTlWjtxVUBaAVVGsAEtII3JRBGmkw4I3ZGTpqxsJ5v+raqCJu

gqXVSYcoOr6qGGiKWB/V9WGdBwsJtrv8k1Uf8nRAmtfPnmq82W9Fy1T25G1SDBW1RJ1YdobKB1UBteDURaWNT0iTdUyaLdXRa4TFoaq9XAGfdU41iTJytNNU4SO9rTqe9qStYVqZap1V+toxshqfaSdS+Rria6dqKNEJNRq2VSCUyqqRa29rUa19rxqbdX1qh9pxaOCRPtk9QVVwdUG6hdjpqtCgLY9NWZVAwBZqEmlyZHNUAUXNTtAPNVzZ/NXy

q6SSvtRxRvtS9XCY99qeqwwy9tXGgNSI2XTGa2unM9+MjtPSyWxDtqAdldv1q4eJNqz+L1A6bXPNVtUmWttVcNkNXitu9uAdJDpbA49uZKaVtBGXtTytHgmJagpVISBVsDqH/AYo4dSgAkdVpAu03O25ppx2jMTiNYMmG8KdSPNtJQpuaWtEtbjE8YX+MktyQRS4v+Mrq+tSAJ+hMiG6aqlqZtQAdKLQStaLRAdYVomMXdV1qRE3HqFNRpVpVpHq

fsUCd/dSnqATH5AF9tCahDor4xDvpKyVtcd5Dp+FX8yXi+tN+RWGCTVQbXBMXTRPq0InVxmITfZ8is8KsGjvqYqvwtz9TiV+lv4aaDAdwmGFTpqGBLt/9Ri1aDVdEl6r5x3KRCaR1QOWKHVdVsZqfFG1NwASmpQabTslxHTuaMtaEqyfkg3tnVDEagYAbYAzrIahuO2NgGloa9DUNxCUqYalqpYaAw3Ya3DVztIhXLxglqjQwjWLty20tx4jXWyU

jVoystT+tGXL/mrdoiGETU11ZnOEdDiPmMmjQPJ9Av6t7VsGAxjUhKAr2HGwYyoc+FPN1I1u7ILS3Y0rFsjGzjVfWu3OedA5SG6dpT8aFgn3KjdvJYgpGCaWk3xC25v9KiLsA0MTTftadsRqsJviqxqVSahzpXotTubws9Dya5zoKagtVodjmunMpTXp52TuQiVTSsK4RgO1J2vktnPJaaWmrGW0ytpAx9Typljj6aAzUI0u1WjVi+ogC4zV7wkz

Q5dMzS2COJXmaYVsWai/D8mR2TWay9A2ad+QOaputjS+zRtSRzXomzkFC85zTYAlzXcCNzWvBDzSeaLzTeasrqdGS8S+a6tUYdUlt+aPlVjtVdT818jshaS9mha1LXXmbyrAW/9rs6xjquq3joPtWLWJq4DoCmeNQDdhLTBa/JRJaibqdq7jJTd7jKC6cTtooCTrjtZDr1ALLXChBdLDQnLUjQ3LXt1f3n5aIhkFaTusjNAZvFaRZtm6dEpDNCrT

dGNyQo2dZp6SGrXI6+5HI67MpNYnMsMAprXNaPbrm899Os8OsslmMZsbNTlOGEw7WvlPZsL1fZuIUWhRWK3HEK6ru29EmE0ja283Kgz6hW1L8tj8SbXI6qbT1aVbKPdznSLayJmkmYJtvdrbN8tgjAraq5prarZK/le6wU6ZiHC4B7v5A17trZvbXp0/bRUKZXTBtw7Xcdo7XcmE7W4pCjBMtJTTXiInQstAHQ4t/sDQta7QwtclrbGTCFTGqCtU

tFRUF6p7W0t3Fqva17SpdM5ufa4OAVwr7Q/a8HogCLS0hZTDqeKH6nhd1loWtu5szmB6mUWyjAXdAszu2xToQ6YHuShfeg2t1lvw9mHV/MA6tk0I4wk1UnsI6dnKyClY3rGFHRxdz0Wo6YHjo6Cyr5dvJuyAzHXfGVzW/NSkxNYKkw8JvHRddN4yXiQnSQ9THomphkAk6TjryVKdS/dHAH06mkV/xQS1baZiCc9ji306LnUwWSnrjGKnritsY0s6

NIA/ghgls6AXsHdNbL89RquC6ErDM6wY186HOnGVj/OC9SXsq6MXvC6S/Mqd2MBi65HtlalHuo9QnWOVcrpBCOXTy6Usi21ZPI8MlhJ0KYbqw9cqwbYJvWp6GrQt6TXRa68XovYtvRHYG60gccq0G6XXpscjRyl6guw28c3TG40vLEYy3QVwuLNQA63WPaW3VYAO3SG9zTQkQKVtzWKsTO6MVhLWalGu61VkzWe3vu66ow+670xisnYMcsn3Wqs/

fXHggxslKMt0B6gwqWmoPXB6SwBOAbWrpU0PXh6iPXHWJpNR66PR5cWPX2c65Ed2+PSyA/goCsgQpESMNRa94pls11owZ6doxTGyXro9IIW56uLJsdc5uQchHoV6vLr412vU66LiyN2GX1Pag3SwCsPvGq6vU16ktHt6evVudTqUN6pKGa9IGvFMbXvsJlpgO61vVHW6EQBy9vTJ2o6zegLvUuEbvVCGY3uzyE3rJylAxAGG3mD6B5DiFlVWrtmV

nIqUvuJG4Qyv0zUyT6KfT69bRz9JDVmasxQpfQZULKFCvpL6lQoodovuJ9sKzPWzkM8SGguuuzfTrVeeGrBHfWQEdYL76GgPHgTYKHAbvtAI7YIu9G2Gn6oBB7Be/X7BvWhX6lgzX6ke1HBQkgc0u/Wnwkg3Ehx/V4Ep/UsFCfpZAHZMwhXQzKGz/RLYoto/61xy/62fpcAJbHN9QA219yQNYN9fRKhuo3xGX9zCh5AntJBonIAjgDQGa0wQG8Kz

sgjAzwG5fuIGNNvNJotCYG5ftZtnCWnAgVl19WfXIG3frF9GX3H9nA2sh6o3qwWo1AIC4JEGYg1Xgkg1H9CU2RMqfrZN4VSlG1fVRWMcQXB/gxVJy4I8EqkN6sswEMG0Q0yhl/p3BqFMb9J/sCG/4JfQ/kDl9TgzcGoEPcGn/oghffWr90EIymEkNghpftbw+/r9NadDJyR4NiG8Q2cEtEN39lDrAD7iyKG5QxyGjizQA0wwhSlSyz9T/WL9VQ3g

DFvul9GQ2QDjQ3imaAdQAGAbxSk5OwDikhz9ToX6GctCGG+AfuGYw14hyZQoDtKSwDnwxe84IxEhkI1OG9ENwhZEIMh9do2i1EL4WF/svB/AY0hggYUh1wxpiTAdbwPEOeG5AdeG7Q3eGlfqvw6EPEDcAb4W0IxkDyxk4h8gdBGZIwv9aw10D4kOHtX4n0hCUJEDOjpGGlkJihIozyhtkIlGfVSH9B4L4WrkKpGwAdVG8/u8hTI21G//uChnI3r9

hJrCDqQ0ihjgEFGuUPr4YoxsDko3cDpI3lGN/rch2UN8DsUO8hmo2ZGfCyCha03KhHkJXwvIyjwJo3BqFPstGGmRx6iPr5A9o1JQqPqXiLo0Vano2VdvyXU9UY2TGslo6Vq3qImWFrrGgXsS918SrGJIUTGdHVW9JbpVq37SzGgDOq9Rnv6AJnp46ePsHZPQbPd0YxrGWHuWDDY1W8Ttp+gYHmw9HYynd3YwPJi+MMCvszkdNbVatvJWkmwLow6g

c2cEChX62i4x0WDGpXwOlqva1To3dj7X3GR4yn0y9DPGuunm1otk7EdQenMdJtFhzQeLaxfL09H4wa9mW2smbqV6iqttdKwE1AmUsnAm7dOVVhEwLYFPoQmMACQmyAl78qExFY6E3YwmE2wmkbXwmagUImIqxIm07DImKRoJtW2zkmcIZzcxzQqkvfmYm0ywZM0Apz5DZW4mvEzDoV+FAIgk0ZDNimQWUk3Em1mjJGYTXFDTIaEm2QChG3GhmDsI

SLGpnqdVvI1U9limc9mnTtVBkx89XnthDSnTfVGuH1DaXotNrrvodRREcmygrBDG/MiE0Hv15Ks0ZY2FFbJkayOyR3QpJzgq0Fg9iMFElmks+gpXwhgrESJgvim5gtFJU3GziNgul4dgsADWjq30HBBymkAf1JbgqcsxU3VKXgpR6vgt8s1U0h9dUwGmwQvCsYQoGmkQuxQKQuN9ivoSFhYcb9KQrGm6QrG4pCSyFJ4JyFs01NKBQsWmalFWmq0y

N98vrLDFQqqFeSWBmbQtCmYiUaF500umcwy6Fx+g4IslJum3Qo4IClLemH0w4IU62eaF1mBm9SsBmNqEJ25FOJ2dtm+FpOyMhZbttuqAA6WCMyRmUwbls/3lqSPpvdF4AdooKG2ucuYJgZXzgnd+G1H0gC2AW2rUI2zM1B2UCqHdqrUj1c3nndzoeXdnrWCIoZMchHzh42f7CfdnHrlShJJGEvCVg9as0+4lwaXJ8mzuDiSweDknA49PM3Twf7vw

Aym2nBIIsjB+weexrY0dm/mxShEEeRAcgE9mJm0/IJwd4KrO2Qj0oeyANmznGYc3s2iIObmLq1yW/C23Nfc182mYHxoz7q3mVbO4Kwi1jWnVCBy1i0kWZcx/IMkYAsAkeVAA80bmcEdNiznoYWdoDkjHnvkWuHCUj6c0UWLpQs905jHmE8ynmf80x9V/LpA25obYy8ygVq81dxS9k1x/uqvdTwb3m82QPme3KsMJ8xsUZ8yZNzuiImN825NvOQS5

V4xfmx7s30aSw/m7KWMjs/h/mFkYAWYQCAW7VBAWCIfx9R82uuvCG3NWC01m97OI6smtwjooZ4jFi0EsWAWKjW8ypD+CxjihC2IWZC0oWvMnJkNCygAOkacWpVSJ9hAdYWxAfYW9i2d0PCzSjvYxMW2CxgWEkb4WVi0rmAFnOwqkd4ksiyMm+6zaj0msijlDi1OU4Yr4Gi2Ika7IKjR+HrK3ZH0WkaEMWTUYKWaCxgWS4wpiY0eDWti1wsPUZajm

kf6N5fo8WmHB6jFbML0nxhmj3cxujii249y0daF0SxADtgnQjYcxDV61VSWlBQ5D04wRNELG3NSNCgWQ0fPyPW3YjYm0+jA3ykpW1kRjQoU6FW1gX1Zodn8LS0eodLPhmGPtLqvqSA6b61Bu7drsj9yvGWTwayC5UbyCy2pvdBvTpMHAr4EUhXdW/03x2RExpjey0WWW4euWLMe9WPMYgDIq05joePU14NW52NX3JWxH0t9kDmt9MABBuTCJy+nw

NS+Pfo79AUla+P0GRWTMb88Iqy2DbX0ljKjw6jsKzlWpQPL+5frpW6sZXwZa23NTCS2+7wLRu2f3tjFBE/WFBE1jvAu3DaKwSORHwNjqvuzyxsYtjNLy9jOj2lGrutEE/sZTOwsaAptse+Bjsaq+PwMtWhqy+BvwJjjqAB2Mzq0hjS0batWsaqmPawFjlYExjgawUjokc6IYaxhjW0ZdDkNWjWDeUkjTMXjWTgq8J2gGTW1EdTW/pRsEma2Zw2a1

8Kua20A+a0LWxa2NAaAHbjZa07jFa20AVa17jtawEDEkNSFyxm0hfEHbW+lj7WIYh7Wo60XjA61+93guXjTvVu4xpNe4k6ye9hpVnWa5nnWR2SdtS60zC8pzXWZsetau60cWN0ZNOcYYkQGu1rjpKEvW1lGvWGcA6BzsvPwtlOfWZaVcaJMYVjAFCOkmH3+BwRF68EYOpl26ztFC8rA2S3Ug2uUiiJmrO9NDbo3lN4ZgcuEqjBaPhw2z4cNa1M0/

DZ2XJjXyt/Dk3ondJsuItaW2x2kOpSJwEYD8Ys1txGm2FOkEd6WPHr9l0hTQjCLHuDF2BvN5CeIEd2xeVM2M12Sm34ThEetm0JkwTMPlIjOm3Ij4icM21EY9mk0VM2h2yE27O362NmxtVYQE4jKJWjm/XzkjV225eXm1sjgkZ1it5ooTTBBC2MmxdK4WxmVK9C9OyfkITiIQS2jwyS2jw1ZqrCb4TTwZR2FAGy2o+ly2jN1k95W0eGJ20k0/XwCT

F+G0d3i2q2+ia8mVO0RYNOw4T9Oy8TFiY62zOz8TbO3iTYc0xjVidFd8IvBBPZX1mhidi2XytXm02z+dc2xFJiZIudq2yYNUfM8T3iak9OicoKx2xwGvgGUIgJoiT7myiTlOwZ2L2wwhKiYST7WzfmKSc6TeHR626SbE2mSaG21iYB2Df3G2u6pI2FMcloWcYON1gBh2bseuWyyd16NSef0MSaw6GO1cCwydk9m4bZjKy2BmSvHBmVWw6Tnm2iT3

Sep2vSbGT0ZhiTqcbe2BybSTIm31mmMfFjWj0DjaL2DjpH1N22R1Nj0/uxuQNz+4SuzgAxe1V2FIA12tmy12edF12ysaBTfyfl25uwlOdR0BT0sar+QNz9j7Jxw+Puz92pf1zOCKYxTcQPJeJZxS+3yao++QJKOBj3o+RsDDjge2b+kgE4+HH2sep3x7OPfwHOzj3JOt30E+jHw8eU5w6Bne0+ewT38ebTzH2Iqen2mBzCetz0ie1T3X25hzie3+

wIB4VxSeZ+3sO6hzVTih1yeYXxQuhTxmBUh3ieyqeWeIhzlToBxn2dT2gOUqbgOuJ0meyBwCe4qb6e1wD/2eB1Ge/Tx0OQzweeIz0dTrB1MOjBxVuLT2meHB3NT8z00OtyCEOKz02eOwI2eYhzf22zy1Tyh0jTah0ye0h0WeKqbOemxyDTsTyuethzMOE+zueVhxGethxCe7qaCeRacU+3jy+e3Kci+Sf2+eqf1i+yHxeub10bOSX3Beccb1jf1z

peVKZFOGR2y+JsaJThsaRTzuwMe2L3oRA6CjQicaJeFKc7TGLyHTnRzVCpQIZT5QP2+4rzGOqABZTK6bZT8r3qBSQFmOtacH+EADu+9KY1eAqY2OTx1WAWDz2OMBwROXxxOOexwIu9x1YO4J0hOD6aJOQJ1eOL6c+OJAM/TfxxfTqJ3PTIJ3eOYJ3xOGwKhOF6dhO3xxvThJz/TLx1vT4GaxONxw2BT6Y2BUGbPTMT1JOe6ZuBfz2uudaaCO6fye

BrQJbT71zbTcR2Izjy37TPsYy+tH3lj+NyPTZ8b9Ff10vjiKeYcticlyo331OupwdOVlK2q98b9FkKfV2wAE+MNp1VOhugdOzsaEqfKbei5Gd+TzGeEzOeS4Q0Zzlu6KYHTMmfDO6gB/4CmbQ+pGe2+ycZIzefz4WqZ1n8SNwUeBKaUeU6bS+XaeLOFHykzFmZpT9Z1y+jKeZTHfyHOdQJ4+g5y7+FJ0PTEmbaBbew72xl03uXn1XO+GH0uJ1xKu

ml2/AJ10POuJyyuKl3POk+2Au152GBd5ySuq1wSub53mu8V32uEl3Cu/50AuoV2oBY1wguAVwGBsF2EuHl0Qu3V2f2XgP/OWDzau2FwuOOb3wuCnweOfmdquTWcHuikDn+BFwiujxyIQhVzUuLFwCuL+yUuqVxmgIV34uqWeEuUV1xOU2fRAE2bGzYV3ku+OkKzUF2UuZ5zUumdz3OXWeqzel2NQNl3Muj6Zquhl1BO592suR2feONWamAIGe6zX

V2Su62cWuzz3YuRWdIOBWdkuYVx0O1WZmz6WYEu0l2Sz8BxGzrBwvO/Fyyug2eYgkWZ6zDFyKup10ounWa3OcGZIuK91hzlh3PujWeauyGYl+cx0Bzb2aQugKEqzK13gOi1zguX2eezwV3ezo2dJzDAISAJOYH2I13uzehzxzGwNcu+OcxzlWfsul2YizB1xOzx10Rzcx3hz/mfqu1wNuuWGYCO9acysOgPjOEQNjjOmfbTdX3L9jXwpeCsb7TZm

dlzVGexeSNwnTHJy0zXyZyBUseUzJKdnT8uZozuX2szM6ZKOPX0JgLL0Gt7L3puC31m2rzXVz4uwczJ33GO66YleLmYu+YtxieQtw8zvKcUzj3wg+69z1e732POBt1d+332n2v3wh+Af1tuRCDfeAP1B+Ueadekebwett0WAV7xDucPwjuCPwEuxd2Pe3t1R+qQBp+Yb0zuxvwTu+GFzuOP1LzMd3x+pd3TzKbyruM70weab0buGb2Eu1P3F+ub1

Kuhbxl+LPyHu5b07e291Ku1byLe61xnufPw7eI+dF+NV1XubbwneC9wHzu91zA472Z+5+zl+p9wV+dP3Xuyvxzeqv0fufdxTe69y1+BP2d+2D1/uKtxPegD03ejxxLzp7xnu+7xvz6l1zzNv0Qex5yzzF+bTzOvy++yedgzz71vu3v09+L7zV+Dr1/zv72D+3twA+VryDzkf2Du0f13zUHzj+GGcFz0X2wzgj1wzcXxEeTaYJeEuaw+muZke+ma9

28j2zOJmcI+2ue9j0mb1z1KfI+YcaUzFGeBTcLzrODHxBejuc7+xe3XTzmble5317+XKeQLaryPT/KfaBXj2FTZaYWAV6faeEqdCe1qZlTlh1NTMTy32Vz0NTm51OeqT1UOl+2TThT3jTHwAf2OqYKer+31TcBxX2ZTwMuxqaqeq+3lTSAMtTjTxtT/qame9qaCe3qedTbh1dTxBxLT6l2Ge1BzsLOab9TzB2QO5z26gwafqeCzzDTRhdWeUab0L

kad2e3/12e+z3VTyadDT2h0ULgad8LWaZ32HhckL9zxPghab8e41wcO4+0yLHz272vewFzUXz8OyBfuBDabFzVXywLufykeP131jQcZszTX17TtGeNzDLwoLxXwqOCq1GY9ubtj1RciBtRZ+T9RYNzLXyNzSuaYz5BfKcZuYtzArytznL1G+iZLtzbwIdzS6Zb+TubXTHf1dz7Be4+7ueVerue9zw/19zJ6a1esBZqub32A+hr1DzyeYjzP+fP2l

r1jzjr2uQqdyAL1xZoBoBfDz0P3D+sPxTemeezueP2POgbzLz2PwLz89yLzmP3zz/92zz3t21+RP3rzcP1KuDd1azlP0ze9xfbzG+YZ+p9xzeJb1Z+w9wreuJ3HuNbwXuvPzmA4+brecBenzk+c90Av3nzCQEXz3eZXzA7zXzJ2amAm+bHeKv3nu/uZJ+Zd2Vuh+ZrzH+dPzYJbfzRv1Aeu71vzgJaGuQpYfzHv1N+L+cFLjv3eLx+d1+bvzAzVx

aweWebB+n7weLqpdZzf7zALofwgLEf1AB0Bf9zcBcKLNaarTKBbpOeGYwL4uZeByX1zO2HzwLs/irRJfwUAZf2aLNHyK+ZKc9jJBbqLJue7ThjzpT9maWLTKdZTzuaczXfw2LHKacefH24LzQIXT8twOLon0s+lAKk+Mn3MuBnyn2Sn1QzpgKk+mn2M+cGd0+pB38+1d3TLuZa/AWZfM+Yn1Qz69xs+dnzWAfAK6eGZa2OoXw8+XSC8+gB3c+vnw

LLJlzrL/T1bLTZaf2xpbuuJRZFz8X0tLFRetLraalzEsY7T5mZ9LwNwReiua9L/RdnLw6faLhMCrRXRejjPReILfRcpTy5YaLlL1RTIL1dLlGaBuZubluwAAW+WcaIml5bWTEAcm+l9Nm+lDvhiN5ZQs7MeW+50kfLalA2+xp0S+mudJWTBddzLuaFu4Ze3TnuYQLB6Z9z7jzjL/BcOLNPzgLJxcweZxbveFxfNuSpZuLPvyeL8effeTxedePt29

u/t3fzRPy+Lkb39egdz+LnEGDeIpax+7PxBL/xcorYJZ+LkJbh+0Jf3zTefhLs+bbzT9w7zqJeHzy+dLeWJf7zfF0Hz3P3xLo+cJL4v2JLU+ZF+693JLc+aErC+f3u6Jf7eJ92eA6+cZLo72IeCJcneavz3zmD05L0Be1+N7xXeZ+bALl7wFLJvzFLED03uYpbge5+csr57wQel+f1eS7xPzyFeLu3+YwrypYgL2Fawe/vx8rtDyTz7lZ1L7+agL

7Dx0rRpfArJpaHLqBeQ+6Bdy+hGfjOOBbtLX0QMzqACL+TpZdLIxeJTcN0KBfg2fBFHyYLEJxYLoZc3THBYHOpWfczPKb2LnN1H+Qlkf+0wAYOxexn+J53n+xeyX+WbzVQ1gNWLx33FeHVa3+B5F3+3dwmgQtyP+dgJEBDgIv+LgJCA29HGO9/08BT/zxG5RZyOb/0WB/yFbzX/wUOPAL/+7/zWrgALgOMT31L4ALAekAIGubwDWusALPuaTzxLX

VwWB39wLA5WfQBa13+Q+OZwBHwBWAeAISABAMOBxAOOOr1eAO9hwoBa1wWBiefeA51foBM10n2oVxYB7qbB6e1f+rMNe4Bs+1VQfALn2C/y84SoGEB7MQmr4gKmrbgPRrciGXTtl0UBxe0YQAQKCBmgIgAoQNHLy1fjLFZZmgjl0Lcl+zn+JgK6uVF3MBWJysBJNdsBKYHGrYgKcBuNZmr+NeCg81e8BbANmryKDJrGgJCBCK3wzY5c+u6N2PLtB

fl2uVaSBIAgVW6VcyBCxa1zO5enTLRfKcKtfKG3R1ozAFdqBLudqBIFZ4+iwEaBppZ4LXmce+lwMGBs2ff2CwO2BmwNOBkJweOMGYmB8wLmBztZEObtZiewwKbuJwLdr+wJ2BLtaOBMGddrAfrOB7tY2BFwOKzawAHLQuZpOw5ceB1NdZO45YdjW5aNWyVdzrbaayBICdDBcbJ0lIIM6oYIOmLspi4TDf0cdCIK0Tz+h9BuSZ3Nro3RBxAExBiND

W2tEZsE+IMYFRILgwJIOZBUBgpBukBLAasWZBtINQM6lD5B49dZB7IPUoPIOnrhoIFBQoNFB4oMlBhbFlBCoMHreHGVBbIDVBeQuXwlYSAU6lG1BeoJLApIJsQJoObZeQGrCzIMtB6lFtBDoJdB7oLnWsgtnijdfZeiJuhB4eFmy39eLrYCdm8ECZkTjAboZDTFgT3fCTBgchLBdHHTBV4bFl4Q2zBjspiAMDMLBc5F0AM6jLBTDlShVYJFgNYNd

9LYJH6DYIH60RubBMtzbBJcA7BsQwD93YMsGk4KbiyVAHB4fqJo3YN8O0fu361oj36V4NnBHgazU+JnUGgAbP9Rw14Da4OdJGUJMGcwHv9k/gFtNyw0D5IxiGJ4JgD8mx0DUjawbt4PvBo23wA9fxsQHy1fBEAHfB3DaSDv3Ef9f4PiAAEI8A7/uAhn/pAh4EK8GJ6n/9T/s0Gbfsq4SgeQhkSxu4QkNoYq4PUhzAEBGWkKYhhELnjbEKEDNgcoh

wAjEDUeAkD3jd8bM8f8b0kNib8kIMDikK4hCEJYDygfum7je4DwkLpOokO4yk8csDxO1nj8Tb0hSTYBCykJsEpgaibmkJibzEMCbckOKbzgGEDyTbTBRiPQ8qaEkVkQeihMQbQALgcShf7GE9wQGwbLkJSD3gdCDXTeea702ImhUP/6mIkgGuoxChBkKabHTeiDfgbyh8UNcDqsH6bJ+GtaQzbEbVIzSD6opWbsQe9ABUMWSAUIeEszaTJeQYNG+

4dAbBePDcdUKNhbsCahYqJahWqLLh8zMXQksNeUjsMGhH5t2VNvlIAo0PGh2iumhDImix2cMWh3VNeUa0M8V+biao20L2hLKNbcx0Jiww8J+bSzvyod0IehwLZehJONYy8xFKN+1GqN+Muswr7l+h/0J/ZE7ilgwMOmlMEaPw+VHeZO7hhhrrC+bEqgRhYMCRhKMK1UaML/oaJAxh3zZhg2MJdFyNNlR+MNjQ5qPebIrNjQ5MMphqMMA8vzZ4TCA

AZhhzkBbkqglZO0rgJYgFalZ0L5hAsOhlp0uFhHrtBDwrIlhqLaph0sJlUUrPUE8sKVhkaBVhBeLVhGsLKw6KiFhOsL1hBsJBZ88JNhZsIBIGqLebQHMAZaLfOhCrcoj261dhB8OswHsJDbppBbRMPh9hz+ADh0LcJhT0tDhdGEdYwrKjhicpjhhDJTbCcJqRacIzhdGCzh30Nzh8MoaYW2ELh5mGLhRMKA5FcKHhVcMYxNcPrhXuEbhrygp1bcI

7hXcLvRvcIvQlWgHhiaErhHSgA5XuDHhIHI6Uk8IHb+raWUM8PVZ5bcXh5mGXhEraA5m8OvhVMJ3h+8I6UR8Ky5VSlPhwHl3wF8LWlSMC3hVMNvhmPO8pMMqfhKmAywL8KWUjvOOZO7h/hf8MDhSMEARYMBARYCIzlBragRlHjBlcCMFIxrNaoiOM6oyCKdYaCLFgeyOwRWyLwRRCLIRFCOoR20sOR/aGORw6AWdrCJOwHCK4RMMB4RYsDMR2mAE

RiaCER01BERRGCBRUiMMRciKcRSiJURZ6Asw6iPGIWiMbbOiL0RQJnYwkKJMRNDFw7FiPFbWbfMwCKNsR/zM1bMIlw7LiJpR7iJXMxWHpRdWEZRtxTawQSNCRESJs4USMGlMSPiRSSJSR6SMyROSPyRBSOqwZSIqRH0qqRNSIAqqAAaRexCaRTaBaRAtCBgYGA6RXSPpgvSLRg/SMGRc9BGR4yKmRsyM9giYf9g3AEDgnkDTD2qGTAb0GzgCcCqA

sgy5AEYeC7ucEVJ4CGTAcqwsQEABvgd8AfgT8Bfgb8A/gX8F/g/8Brgn4MWRrCDItWCIzQayJQFGyOCIOCO2RjaBbQLnf2RnaEQ7DCMoUpyKnQs6EjQi6EuRW6B3QB6GPQiaGzcjyIp1ryMkImHYNI3yJ3qGGH+R+GBI7ZGEowSiLBRLHaMRUKKbQwmFWUgnYUwEzcRRmmFTIqKLMwUWIxRLmHcwXmCGR6WLPQ+KOCw5omJRomFJRCWHOc1mCpRr

iNpR4nc8RDKL8RTKNjQbWBZRbKO6Rg2GGwnfm5R46Gmwc2AWwdMA+7PzKFRu2FQAzUIlRVsO1RT2FFb8qIJhNbZbgKqJNhaAHVRkOE1REPd1E8OArbeqINRSqNVgxqMXhaAEXbq8Jx7TrCtRM9FtRAGNnEQGKAIzqJ5ZCmqQI+6PdRiuFVwtlIbpfqISVHwSaaUGOjwieDscU6MscCaIQxMaOTwcfOzwdYiTRxeLjRA5tiKmaNWkHJD7wRMqHwXM

ILRMQkHRJaLLRLPZXwlaMjQTTBrRksxnRh7EbRL+GbR4ezbRcdNSGUDO4I3aKvwN9LAIbNqf4qvfEII6I17reHHRdrcnRYFtHwXJznRNBAXRAyxGEpvc/JZ1s4I66L4IgmP8kgfZx2e6OLRB6PeRzvZ2oDGLYyBTjPRoBAvRzyJbhyfY8EJhG7h96Iz726Ij7HhERk36LcIBfbjwZcimZ/6MxpVPciI0fdAxdUnAxkGKDROuFgxQ9LyISGMKIJRC

zEaGKWoWGNpkuGPaI+GIKAhGMmI0xCxI4xHIxH1koxw/e2IWJGPF9GPPinGNYxexHYxexE4xGuAXpxMCoIkJKeIAmLExQmLD7OBGExAEUkxt9OkxsmI1w8mOaIimOUxqmPUx5/c0xuJAJIeEksxKEixkceBpIALLMx3BHZItynZkNmIFIwpDKwaDNL7zmIFk7mM8x3BGVIqpE3w6pBXJcHhnlDTGCx7PYHyGZpwJe9A9IBKLixkGH551mD9IMokD

IoZG7FB3ejIgGDjIiZBTIUVDeoeWMQTNSW4JSA+a8FWOECeCpWhNOrqxEOoZ1jxOEVrWIUS7WPs0nWPfIC6F846RlkVAnqGxvA5GxyFAEHKQTmKyluUJYg70YxFAXQH1Mfx1FHkZq2OF6VCotKag5txVxPviVYI+YIcQkoz2OOx+/V14X2tsSF2JFJV2OMoPtAQSJrHF1AcRexz2NU1XpnexTiU+xkMzAJP2IgJ/StaV9VCBVoOOKlqxRAQpbnsC

NVBalm0OqlQHYLcKOP6osaMwJMfHREqA9xx66AWoS1EJxESR/SpOMY7H0IOofmGpxMohxhOA8uoTdMZxj1Geo7BOoHG2loHHHIDaYNC6dAuOLoQuK3125VFxyuteJkuIE99lrlxcFCoNh6XoNauMYNcIJV1bxLkHfNBlEgtAI8KUxWde3OwjpuMEY5uM2dnAGtxlXFhJ/uodxyZOL53uIbKvuL/4Ws0xJPuP5DA3Ie4FrODxIdNnxg9s6ZppMjxw

dPbJZdHFxXZKoicFKpJMbcZJy5LqYng/RR/dEHoI9F4ZrRsno88tiHW7fiH/FuOdDeiSH1eNrxcaHrx1mEbxzePNUD9HVbCvdfoXeIUR39FjQ/9CAYxzN0wlA7ZxNbo5xbG3Y4rXB4JPCY3odkn4JtxT/a5w4+du3LeK1ltmHy+O+KDls8bTlrphJ+EsqHDE6H++sPxEjGJ4J+MFYttAoFF+L4265r0JHfkMJT5AYdpjAFHnjpfxnDplH7+LkHn+

O/xFvCid9jrCtjjo78ThI+HUWMgJzupgJu0oQJeo+yYRZDQJVaIwJpeNDbtfXBHfmHwJhBPBw8jQO75BNmYCzCWYqzA2YeHz/S1mF2Y70rYJFA7AYuI+Tl1Q9tlNvt0YPzG6dwhOXoMw+Fpl3gzjCEYdDoo8QV4o9QAchO6dChMBYPA5kJDZVUJ6hJ8qrZIk2lzdbJGY6THxhNMJp+PMJIrDq9krGsJPBFsJkbQzHWo5cJOo5SY7hJ46tTC8JcHl

8JRDPjh9rDTbsCJQFIRNxhnrBQw3rB4llQ4FasRMAwXLYSJXDKoTLvlSJH8Vg467D3FxoL4U5wpkZzbGfQhRK7YvbH7Yg7GHYg9XHYCAEnYhIeqJC7DqJy7FXAq7HXYzRMeGrRP3YfCk6JJ7HXYfRKvYapNno9GBGJHDI/YcbHaHmNAmJAHBHYEOhA4sxJMi8xNVgQ+rsyI7GXH6xKR5fCmw42xLtAuxIWNyxMOJ1HBSZPvGklKGHOJzZI5J+5Nu

JLvnuJpwtfizxLaHWuNHIGusNJPauB4fxK04A2uHJY2oDJ4JMhJ0JK/Hqw5EjfDNNojuOonSJJn0qEahpj8yL42JK+JVI+uWYeJF4YFJJJRfBjx9w/jxPZMTxzaNeHgZKGJLJJhJ9Cfa4XJL/YLfGHJJZK94CRhDDo3HW4SUwlJb3WlJi3FlJo03lJIpOi7xoJFJ9gqZJ6pLowwZMuJXOIlmOpKZ9epNcFRFkNJ28bKmb3ENJeYdtJP0CLDHUwZA

AZKCnlYcdJIvCmmGkHdJuQq9JqPB9JtVnH9jE7jZNlNYnak5JHyFLp4nwuV4OZNQo9AoTJHluTJSyezJ1ZMwsIMzi4GyZB4ENo7aM5ONZXrJnsOk8QKTFJynVU9kF+otYsqEcbJVoZwnLk949qsxQp6gvdDnZNknKQVJQDJP7J5U7aFw5ORj05NLJh4PRjc090n9U96FhpKXDDZKt53fAp7pUir7F7Aipj3jSEeE9oER5Nwp2/BPJT5bW2B5A+JV

5N34SlN/4/WvBTp07P4Y2ocyAETfJtAhf4OglvJP5LunIND/JT06AEIAgBnYAhFjKQN+nMAiJJ+wkEpUFIEp5/sgp4vXcEOjvH4Io54pMcTQp+p0wps22YEHloYp30BTJJFIf9Kgi8EZyfBnNdpbs/sGopZFlopxZNxn5mUkFOU9YptZPkFr05x2zoZjifFIcEFJOhnCM7VOpM9EpclNJns04r4MlMWnws+BnK0/iEpM8PjSM73Jp8ocpOQmjHuE

ZaEBlNspJlJqEQYLcTw7RaENlKMpis9Nm6s/mkiEdGE4hHGEnQhl8swnrxkWl8p/lNWE6wj74IVIXEO8Fxpo/EipCVJvEMVOuEtwnipBNKPERNOa8xVLTNHPaflbNJypAc8qpljjZpRVN9aSBoPEPs8SpOIiZp0c7LNakGqpIrFqpT8vqpyREapDImapLIlapvVPap4Rl5Ed+Chbs6ILnCYjepkokP5KEgBpY1NQAaogmpdNCmpauM7Ex1Irn0Ik

Wp1BppMtc72pDc6GdLoklx21Pbyu1K9EaAF9Eh1Kjwbc9OpuEj1ngjEupsNOupQNPdKD1KBiT1KzE1Zr6pHc/epi2JpMMtM1pFYhrEdYiupgNPgkwNLQIiOTBpfRljp+feXwPYkLno5oRpFKKmZqNPRpamI2c9s4rpjs/2E+07ZpxXjDnmU8HNsc/ppvs6SpJeS5peaK5hIc8TnSI/UYVVLdnz4nZpb4nsiuTB5pgUjFpwUkDpqACFp9lozpaDHF

pbpRBIktMLEMtLlpR8/UEWdMTpemK8k69oV5GtPbK2tP5putPNUSql99svBNpnEh4k5tP5pltMYM1tJFtyw3oY9tLzwjtPjEPNNUkmkh0kXJ09p5IktpRJF9p8MX9pGC9CkuEijxZw6vwK6IYX6C5PnnknkX/E7QXmdKUX1EmTpqdPTpMEnckAdOUXOdLzpuUmfn44mLpVaLLpH892nFUmrpWPFrp9dK1wjdObpIJC57LUgnmvUiOkg0maIvdLGk

RRAmk29KjRw9PmkY9IxYwTUgZiaOnp60kIXm0jIo8S/X7oxH2kHo5Xpp0nOkG9M3wN0ivpUeBPpzRA2+z0lekxBJ37p9LWYX0gvpazAiXOOxt7d9ODHbTgjHknGhkH9PhkHS5pM8mNQA39IxklJH/pgDOAZUeBcxxMBJkVrHAZNMhQkWaIZk+wdgZrMihkVmI5kKDKAHb2KFkosmg50sniXeDOVkscPVk3Y4LxZDLlkFDNNkzBMtk1DLow9sloZM

vgYZtHKYZf9CjF54drdoxJnHkcnKZiAHTYAjOTkqcnTkmcmzkucm7ljjIvk0jKrksjIUcjcncZyjM7k3cnUZmjO0ZBxLua+CSnkP7CMZ88kXkZjISZI7E3kgwGSZ7ZrsZAoGPkgK8kZl8nXYLnkUZD8j4UXjNfk6K78ZI7ACZuK9VgITPAUGK4sZOlkTEDK9iZkItpXkTKSZNjKtmaTP7N4zMyZlClbAbNRXUkkZA9krCKZ7TPbsZTPgUvDLaZQi

hqZIYjYKhdiqZtEWaZaACUUiq/UUgy3xJXTMciPTP0UqYEMUAzJAqtYxGZtijeX8ChM8lUKmZMzM9bZlyq0gShCUwMtQAKzLgwcShJbqAE2Z6SiBgmSj9DSyn2Zf9FKU5SkqUEcNOZG6HOZHSiuZ5hAmlSyjuZdGHv1uzNeUzzOSU4ykmUf6U/hUrb7w3zI6UfzKRHjJC5hGymAYhsNeUYLK0kxylOU5lolU0LOuUTVSul2mCRZKLLelzBPRZ/yi

BUIKm9X+LJhUGGHhUiKknbZLMpwl0qdXWKlQwYrK1U+Kl/QhKiZZvGDJUjrbZZWqg5ZZWFZU7KhSACAolUfLIFZAqgjXIqjJIE66TbkrJZpakG1bs9HuocrM1UrynPQirOVZSHIaYarNY8Y47rddqhPxv2j1ZBrI9UVAvqnM7KIs5rMwaVrK4k4aknst7N4nTrPjUlrIZUbrN4w6ak9oKgRop09jopBaj9ZxakDZPahS4VamBirdjDZ4G8jZ7agZ

UXah7UnoP7UQ6hHUE6inU67uXoabMXZ4q83NsfhzZs88ZHr7tvdxbPESDf3LZviyjaGbSy9mY/BMDbJ/Uf6lWddI4Y3CAFay7hUw9iyoQ0Am6pNfbLWdCnqY1km+bZ9RUrSo7PfXk7OY0eZNnZglmmtJEXc0y7KsCq7MWDVJU3Z/Qdk0u7IBy+7MM4h7K9Mx7N03YKvPZqm+vZlmgkmD7Nw0T7IA6J/NfZiInQtpTsW6AWgbhlLdLdoDbDQw7Zyl

iWmS0bq4g5Y7a1Umy9g542glUCHJVZyHJq09Wka09bYw5qACw5HWmrE9rfw5/WiI5JHLi3SynI5wsgQ582kJwQ67DQK7aB5SCa20kxCd5Kely5NQ4QU/vPz03xlu0fHPXQ+6UE56+pE5rdpj5oFqa3CfOk5dfIbYMOjh0CnN0NSnNx0qOlU5og24IGnNR0WnKL2gxhnqZOgC5JQZpM63KZ0/+u5MlnLr5J8tmN6WWb5Iul/5fuic5wxnl0WvPiAO

vJi5t3IYMqxlN5/nKW5N24c5oXId04XN85UXNVMt27e5Dug1MEXOS5OfLC0lvIwnW7fq37HJMT8srVl+5F63qPJK5pejo1C/K2T0Jmq5sfNq5DeiG3P0R2N7E6uNrXPH0k+gm5zJtn0PXIEnR+gv0C3KG5kUbx3u+g65RO7J3JO8P05+hX0V+kW55umW5y+GRNdCpM5rzq256YqstWGk+tgllKTx3J29RU4QMSBjpB0PsLEr3Le3H3KFWzBhIM33

M4ML25C5XnPoM0jYV3bBiV34VuB3PBmgTlOEB5Ty/xH3ZFPb98PB5x9HB3jW8GSShkQA8POtwiPJO5LW4tMRekMMVnbPb8/Kd3HQXx5mSpcdH8BlV92Nk5GrQp5Phl4NQRjKardsZ5aO8DnyA4BbbPM40MxRkVMg94NfPNFVtFvZ5QvOJlovMqMfu681AOrCNn6UmdrRhmdCvN/5SvJVmKvLY9avNXgGvMu3H2W150XLF0Ze/MShvLuwxvPl3D27

YMh2nN5Zpj13VvJt51W69N1vMTQlxkt3h2ld5Cpg95zxleMvvOR5AfMtMQfJD5M3fD5PW/6jfGSP1XesG3UnM6otfKFMnBpCt6fOT5RJii5DZXz5jzrf1pJrANbvI753+qP5nGgAN0NB337vnr1CsC33uw6ZDn+sVM3qR/5MXN75PWOs0ephAFRphNMZvt139mnH5lWFtMh6AdMTpju7y+7lCq++f0X/LX5DbDv51Zm35JoeyycZi9wCZiGpCRmP

5i7LP5TiSzMXFuhMTZg/5BbFQPpZgf5mO+qZ1Zhf59Zjf51/OQPbZi5d3/Ol3v/L7MrvHoPQAoAPBpiAPAKDnMMAsEPC5lz5T5GdMoB43M+u9QF6AoaYmApPxT66D8z+gHlQvjBFovjiZG+MFH4fC/XBwqZF+U7jJlzZ7rUFj4UVIvF3l3PYFxyc4FQM3KnoMxkF+Fhqnsaj4U8G+9ZSG4kFVvFanuh71FdZLYscg6cmqDVUFj/E29glm0FYiT0F

slmgG0UyUsallMFreC39oYb0s1VgjD6UxVJDgpEIdcdssi8ce6RU08F68fTDlUwh9xPRzD1VgCnoQpamOVlVj0QpCFpYe2m/U2qsyQsam1YeEQ2R/PA2Qo9JB9dCaLYb19bYaasHYYL6XYe2mlQpSPF2wOmY5MHDE1mmsI4eBN30enDE4ZaF6gfHDQpTnDfQrO9e8aqgQwsSee0zkHsMshoD3mmFE+rmFr8WyNlwuWiNNl816wthsL2u2FYCXe17

woAI7h9fB1Qr2PDxIe8aiWrYASSOPYOROP0vDOPQCU51JrW51HNj516ILeF6NjuPu4YW1vwqC3qAplsi/gUPX49+0qh9blGtg7lnPkBXsIvoFQosLYtIuniaIp3DGIsxEWIrzoOIvLs+Is5F1dmJFUdhjscdl5FGx/DoYu4xPtKpHqdIrTcNtnRFBdnppBJ9ZFZdm5FBIrJPtdnrsIwuAqhoqRFKovFFV9j7sBze0FGuAjB49l1F9U4VFPrPpPIp

5XsYopvsm9ilPdoZ1FNJ88P8gqFPwoovsop8r4Cx4tFNwHVP0ovCgtoo8pH9kdFuML/sOMNdFcDZFaCDbvycDiQlUsvYlX4oDFQYtwcvEqnV0nislfosjFfMujFvhSUlcYoTFu5mTFTp9TF7YrLFWYrSxOYot8/kvwlhYr/FJYoUtoZ87FMbkgls/BrFr/gzPUko3iRjmSl5mVhpMZ/dPWksIHDji9EGks0lA4tjR2Z5HFB4o4lR4YOw/jk5c4er

HFtlTDQ4Tm3F2Z7j1nZ+bPW4pKcVYvScaetHFoTnzP34uPFw57il+eonP5Z87FN4t7P9kvvF7eUfFZM0XFnTh6cPp8zyHp5/FwzmIlI57JnQEr40rkojF4ErRIlYril0Er53C560l8EtElxZ7ClKEoOcTMNAlyksLXCkmwllziQbSZ7DPP7QecfZ+UGHznIlnYsolgLlykYkvG9bbvLPjEvBwMLmLPB3TYlAUtQAXErRc0Tn4lf55QvIkqAvbAwk

lGZ7rPMkrQIsrnMcJAgpcOIEJck55UlxF59P6kqwvKF9Lr5ku5clksnPUeDDQJkrMlnLgsl4rmQvzZ5slnF+LPH+7EAzkuVc757YvQ+/Vcmrgoc2rl1c+rmTPqACClGuFNcmYAd3iUqUvtrm8cMUullcUpc8al+XwHriv3X+vSl1Ckylwbhhg4bhyleUsNIBUqKlSbhKlgyr8HDISvXVUoRx+bm6oGyhlUDUqhxYQ7Klj8I/cHUsbc3UphPSW76l

saIGlQ0pGlw7jHc04qpbU7jEJc0tcy1mCWlI7ZWl0ykPb+7jdXh7g1bcKOske0uvcPMNUxJ0ogRb7kjQH7hHXv7hulZ8N3w90pzGj0v2XARPYwL0qWUqLOYJRy4lU30pw8+HjFgRHm0wgMrQIFHmo8YMF/b9HmSUjHhY8fl4escMt7bkWkRlOUtRl4njA5gsrxH7DOxluMuU8qnkJlALOmSecqjNxO0plhMslm21+d1Tbor4+14uSLMs3IfniFmS

bRHdH3h5lcWii83MiTlF4bEsossdPypyy83ory8vXjITII/JpZI51lJ19ooNZqAUZih+vY5s/NT8vjN2Zu68YN+NlIvYLNUcvd1a56tlcN+914EcrBhZt/PX8ftaGPhTlJDldY+3lzwh3jjkvspRnTEYDljZtDlLBtu8Tx5d8kcu7NIEel8b9i+8D+GjhtLNXlwFO7Iacofw4CKpl2coBvucuOvBcrxvRcr7I2Pj5H/I/Ll+eRp1xDJrlkO7rlLO

sblDPj9wLctHlSJ87lOtkBXPPjtD/cpvMBApUPat4/X1+HHl3Mq3Msvhmw8vkCxs8oMwlSVV8i8uXlHps5vevgdP9MpDjzp5hvnPPbdlrX2PQirgVKDV0HNCdFmq7svMgq/TwAaLfNw5oz8YSuT8ayto3GfgUSa5UB0Obk3Ks46AV98Qr8kEIgVQaqgVMCrzizyou0HfglXKCr785iugtWCv21QIdn8VWIX8UzRYHCiUEVMIzTvft8oVbWOhDrdv

oVkHvtiujhYVD/jYVyJtf8XCuP8+Hu7vANUL0MWt9i5jvEVPu+Q9FhkT3KVP6xXm4w9Pm+k6KdXOCuQSKVzthKV0WIqVTl+09oKvoCU3p3v4Go6o5iqCCliu3vIgV8HEgTK0jiucVKgSNVbiu6VugS03MLrHvxgVhb5gVkVsXrs669/flekUiV6yvXGsSqpdEd6SVBypSVvRSrvCFEQoEisx92SuEHDnsUVdDW/8Md83vhQQ6CpQXKCvIWqCtQTW

DHVDqVAM0MIZ7XBq3ZBaVn986o7SswWT94GVL99ssPg4ofs4Q4ACwVGVzEVS9e0UmV2wU6a1ibmVswS09W7T/vlwVWVn8qAfMSr2KoD/HNGZuSVC6GMt5jrOVFytX1VypzvFMdJCf3EeV+d6cSymw6D2nsJihSeJCPyvuV5joBVlQT3vXruTVQQTZC2j741kKsBV0Kp1VsKulC8KvsfiKsoCyKtOiqKtdG6Kq9gmKsUiyDhxVaKrqo0GoE9RKrco

JKthNnVHJVvp9ZVeqrnVWdnpVvYR5VCT+Qd09VZq7Kr1VK6sSfioRx1EzoIXQqrvwwsR6dcJtb8gmrfvugCliWhTE1aUQVVQWvRdszrdVJEldVNj5ZEd6phVPYUVC2KqxoBGo5NxT8n4Iq0YSFqsomLVWtV24W1D2OodVuoCdVYFVJGrqowSNqt1A8SXIq3qp6fwzovJtoVFj+MUDVtflzvOmtDVipqD7ZBtU19mqrjLLpgicapyiCEWtDKEVTVV

j+dSN6rbVJ96tcX6rYirap/1FmuKiBbAef4piefNasAIdnM+f+auefWatWVf6uNMXapaHVE7P4favSiKkSHVBq5gAo6uVV4mttC06oXVsT5RFoYXMiC6pXVa6osUG6uQ1rkVnVu6qI14JW8iWGociOGpQ1R+AWiKlAbYF6v5VKzVnidz6s1Fin3IyUSfV0L9fV5yffV5z8bVpEWrVSGh/VwL5CQ5UVs11URZ96bU+fkGprM+KvCfC6ptdmGtxfSG

opfBL8A9QNv81NL4Q1ir+w1c0Uk41L8d94VUI14iDWiJGvg0W0UGNHD6YRVGqOi53RX3UfJk1KdSCdrGvrU7Gu40QTu41XujJG6UblnvNuE1FT7F54msk1IWv01g7KRinr7p7yz5GdnBpU1jYVxiNtvc6QrpJiOz80fwb6dORmpiWq6I7KjL9LVlmq1V1mpl4gGssmpodijuT8FiBT7MfOTo815T6z312ghf/zvVf4+kVVIpKk1oWv3JYkcf4GWu

bmE96ti3sV/vid78KFtGdiIxoTixsUy1W82y1wAVy1agXy1+g7/ihWp45xWskmZWqEqFWq5OVWuTik1Nq1GcREBjWsskyOvzi+5ELiJcQri3WqEqvWvhiD08J1EBox18MTG1VwZ7itg/6AU2soMM2of8c2qMDRoxlnlptnilcfGDCHvXigGGO15b85d5d721CLgWDZ8UbbgH7s69d7p1iiV9vqiVu1gljZ1T2o2FFx8iAVx5RsH2usSZg/OxcCQF

sD7/+1dRmiSkupex7iRwSsuubvCH4XHMOtTDcOqyNEE4aAkSXRi9CVR1L2IWfrCX11bH7PfoTXx1AJMvfw2pJ1Uu9IfT5Ap1UH7UKZ2tp1F2rg/dN4OPzOqQ/M745PdwsMSfx7ZsPOueFPRn51liSw/s4Zw/Iuvw/f2vsHcoSl1xMDB1K+DYH8H5Jsrx7rYdYbo/4xrfSauo8Ee75KK2usvC179bwWOpnC3H/lqvH8ySg2qvfgn4KSTppA2K+1q3

b4q6cdt9hPKeRQTvpqjPEsuRvuDfXPXuptlh28xofuvYnAepvlwevaI1yVz1CgA7PDyVITP6Ry/L3nWSa4oz1VMtH1yIh1ly44qcXM1n1VX6jlc5845vZtDvFG5jnteVIaW7u9GtBo31LAFhtIKQn1dn/CSDKWTvTep0Kvep+g/eooSneuTv3ern1revh11H5yN5OJH1jGMpSC36yN0+tJS9X/G/rKRijNxWX1WSp6/gqSaHKCW31YqV/HGYB8td

LdlxS6X63KqWrPp+vad+Uboal+u3Z1+uTvt+tNSwbCWoT+uX4CppotrdqFyH+vANN+5cdfujVV9+831UvLAWIBoWdl+4n3V76WUQuRgNJHvcC8BozSVLv1EeaVtS/38LSJaUlymBsrS2BtrSkuTh/FrlXgzaQd9RBo7SpBqOyL9tW1Jz74NS1Kkta+v6HmNHVxTBuGHP7CXSoRsn4mIe4N47EMVfBq63ghtqNEWXqNwzrEN16UkN42+kNQ78zfwE

Wf3UxsqN36VUNYv7UCGhuAyWGSJ0lhusNVhssNdhuIyqGXQyTuB1/Rhv1/+v6N/JGTDifP9wACv6/fSv6yNHhpZnXhu/SPhrUNkVrHVARsiNQRqKcwmS063Rvz39r6kyJDaHAsRsTqiRosEyRrdAmmSHADv8V/dGXo/S36WJeRtTtlAU/tJRo4y5RsLELGRcyGv5EqEv56NjRtiyAWXzxnMA6NHlC6NRf68yJf+aN8WUSywxrtqs8XoymWVd/0xr

GNKX+RS8xqKy5jpWNxe9Z/PX7Eagm6w09I6TKrWWtNJ2VKnhxoDyfWSS4A2TONLfN6yJxqY35+sNyKHXVNb2WU3Txt1N+wv1NqGneNnxqotroc2NETCBms//CTpobdiR5RRtJ5UhG6No7twVWhNMGqx38JsBywOS6fUmjHt/JspNgpuxN0uQxyfE1031SGbRY5TXf1EnJ6ykxNR3J//zpyV8gxTWVyMADJuQB/cIN2TU2jS5YuTQByQnJ++R//Cn

IqTUNNHE0ZcllyeADGTUlNR+ZpTRIAxADmTWQAuwNKHVRNe+Zjck6IF20t0jOaKXRrrXJNPU1f/wNNGADXciZxD3JgAOOfR395hlP/NABbTW3/e01GXQpycJM7V2dNGPJNsAl7at1k5Si/A3wYvw+vZ08gbz2cMs9Cvz5tM8QIzXg2CmV/w1+vU0h9RDgaQsxzZVbyS2UUzQMAiG9/myHyHWVPbyflUs1vNU3dLG8iZidlVt0SzSsAj3sc5UZvLL

wbzzVlKwCGzXYnFs1T5WCAgBd/ZR+gc80aGB1lECM75Ta/ABdJ8XjvSppRzToHcApb3SnNEJIZzVXNdAoG9HFxEwoMClvdbhM/rzvNEYRVzTxdMf9B9APNZjcuHRnUE818/DPNaoCLzUG2fJlrzWFHBW9kKTQVEF0tDwzjEe85Cij3P5t0zVj3b81oCj/NJwDH2kAteN9lzS3EPoDk5zttDHdILXQVPeJN4GsKcx0ELTmUf9o571QtAlVxN2VNfq

1cLTJdDZUCLSpdAy1smj2RZIpUim+Nen1OKXBqbTcE9yd0IJUWLU40ZYp1LVQ9DA81uTR/RopcYCOAml0kuiMtES1zPXSVRChhiiO/cYobn0S1AFtFLXuAmpU6JTE6NgANijs5F4NNlS+A/O0TgIktWR9/gNMtYglGPSktSy0/9HnxHSo7LTzZf20tSmmFIEomrUGANy1EyS8tEwRrvxwjPy1ef0CtSD0QrXVdfEpILXKdTBoYrQ9ddUM9wXoKel

oxHSZKd2opHXxidkp5Yi3/LK1BxiLGf2pZHU0DJR1Q6mKtA8dgJTKtcF16qnVtEipqrS/XWq1HG2JAxq1sKT6tGT1tQJyTU0o2AC6tMapEyV1AzoCBvjBdYa16qkpoO6lHrS9Kb61JrTVVLEpZrU/Kea0vnXdtWMoobVg1Em0+nQ7tLa1DFB2tZW1EoQu0Q613rWHKHW1MAFOtDN8OlUxtCvcsNG1NHf9toyfIZspWynoXJ61gIhetJgAeygdAvn

xBd0ntP0pH7UwaAG1lVRBtSD1DSmAqL0DFGgkqWG126zrfBG1bOkR6d2JQTTRtZ7IMbR9Arkon/1xtAth8bR2dQm0LMmJtb0pSbR/KCm1cbTJnVjQabTRKOm1uwKgqRm1ECDgqLQAEKgpiQaoObQXCLm0WRAwqJ0DBOwFtPhchbXNtFhg7aQltPhYqKjQIXRcV8DltBiomKiVtPa0VbTtfZ4MVyg1tacD1wLDKCMC9bRXwWo1DbTDfY21MmjHvPb

kOuj3A2ihg7VcYa20Azk01SYD9uhYdI4pdYy+iF21/bW9tVUCPbRAoWCDz3x9tbq0/bQunfGJGhCDtFN8XKhXtQJpBSAfxCghy7QBaU6pC3TJnbkok7UPAnkZ0/wFweJot2VQqFhos7RztGp1kQLqdcCUuJCLtEu18HV0DBUN/ml9dJJ0L9ATtRbJbA0W1e50k6UikGW06nwh/TACuSlnAo+YTP17tTZtTSiApfJsjihmtCaoIcnRNIMF8wKLKBa

oqoDntJcZAwT0oGMMZXWTObsg17T/tLe1I3QaA/e1xHXJDeN1mWmPtRK0InR5Xc+0EpwIdQB0iHUXqRJ077WSdYt07nSftWeIGfyv4aiCLsA/tUSCUaiLtCyD/HUg9KN01ILjtMB0HIIgdJyDT7RHYSFc++ngdchREHVoUZJ9AwjQdWhQMHXgULB0H+SspG11aINoGPN0eQJfyARUq7VXqX6MbAmodQBRmXTK9JeJJR01qJ/F5R1KqFkoOHTQASI

DOiF4dB3BkHwEdWQ0jsnedTyCnahjdcR03anSSQUDRAxkdQq0pylcgqUC8phlAhfhONTUdDR1UeFxyFX90wO+gfR02nEMdUaD4nTxqUx1c6nRAzlIlR2sdUuo7HX/xP10ufyQfcN1LIMOg/N1RHW8g2N1JliRaEVZwnRSglbIpSgVA0J1cWkgdd6pImV/xWeouQNig6qCknVqg1J0mlnSdeiQZRE/qZyYnNFydaxN8nTDwQp1NHxEHFe8/DW9/aJ

Un6m8CJEDQR2yaBp0f6lO0Fp1msTGdTGhanzAaLp1lVSgaHncEbw6/K6EA/3RCSN8/6mAaZ78lTVcYfv90Q0WHNtUlnSzqaYdGsnWdeZ0LXG2dQZ8kmlYaZ2xDtAOdZiDCYNYg051a0HyaU/9G0mudCmJqLTpiUSCHRzqfAl1rLSdAvN1waTA/L513yR+dPUDSkwBdMEE73wsaHGclQNMgp8goXX0CUp9yilY9AXcdYKw0bxpzQPBxaDoqYLwg7F

10vWuDUuMRPQAfIVZ2xCJdEKCyoP2A5JoKXWztAmD/r0MtM3clYK4g8E80nVZdNeJw9yA/HeJoLVqae6CsPQT3JR8tnwpjEV09glRgyxJ+mlpAHyITIKLfG4oFXSVdRGCVXQvYOZoFmnhBLV1p4khqXV1JOH1dTJpDXWySY10DmjNdE5pLXRD0UqC7XUQIB119yCddDbcBAKxjVAB3XQIg9ODN1B9df6o47RFxAN1wuGDdTGw4WnbvPjUI3Segyq

DaSgmgjup3oITdDgg8WglAglpKWgDdUlo8aizdSloc3TBg6yDb7VIdXyCZamhgq3UGmArdKt0nbwd1Ot03rzdvb05EG1cAmIBizXUtb2932EVaLt1DWi5mPt0tWgHdNNprrw5lY1ox3QmmTwD9g3jNWd1PAMAjI2cMvxXdABsBVziA0YDEui6/LApcPRsJCNp2fTwjAD1JzTaDD3hB3VcjHtpEgLvdFiNk/DMDE91fkmx3G79GN0qA+hCdCXjHEo

Db3U1DH91PBFoQhZZftm78AdohPQk9OzoujDtDPMdoHznaKj11gI/UF4D0PUGxLGCdH0TGSsdxgPktHH0wPxIPXGDSPQK9FYoivRo9VHBSvSnghj0a12xAgDpnYIhpaCNaQJXxWMd+pw5YTGDO2VrGOmDGsnE9NHYfE3Q0c4M5PUFmOTd8lUjGKsYTNx9GDT1/Rn4ffsYIQzfGD8Z8PUVDOYMSxmgfKz15ENE6TS1YQIWDG2pZOl1Ddtpfsnc9TJ

DsgG89WaNv3SyQn+90kMCQ5T0TN3WDcUwwvRs6XcIovTTabjdAfzi9ecoUfRjGDL12ulS9SL0mkMy9OhCGkK78XL18vX71Cj1kuio9OGASvWgfCr0FcHy6a0Miuh3dBYMug0xDFn1dg3Z9Rrp11E69TpD2uh59LrpRiwG9DxDcPS3CKG5RvWJTCb0Fug6CcRgVukENBb1Nulw4HOBkfRC6R3RSUA29O31Tugd9Hb1+40LYfb0x8EO9d5COCBxGU7

0Fw1bwC71fkK+6D30mADu9f7oVx0+mJ702wxe9LiBIegPfGHpvvUnDNMNzbDB9DHogfRx6UH10enyPWqZSyFJ6IT8pTwWQ+H1Kg14QJH1Rg3MddH18ZUx9EFUOAG0Qwd9N4OdSAn0DkN1zJC8yfTUCCn01enZ9DXpB4gJ9On11FQZ9IvkmfXmQmrpTejZ9LaA1Jkt6UlAufSd6DZCCfX59J3pBfWy+UbcCAxoLSBwJfT96ZgZdc1l9Ko8TfXwwZX

11UOVQ5hx1fTCFLX1Ri3YGCf12wxKFCxstpm1QkA89n2jAzXBEA1pWbZtbfWGnJvoW+lAIZ31MgC76Aht6wXr6YFCDRDD/PIAffQ8EP30qGxVuGfpaGxD9fmAl+iYbfV9NcBHBVyAY/QnBThsB7R8bLRwWwhT9G2A0/WYRASxM/QL9UoYcAz/6PP002DzQ7oZaA2mbD987UJ+gB1DQzh4bMz8gg3yDOv0Cgwb9IaZkBhb9V6wfA1Qpco8u/RV9Mg

te/XFA/v1cBh7Q+Po/g3SSUf1TUO7QvVCyC1NQuf0MgzyhRf1sgyvwFf0ZjjjsDf1aIH2ccwVZBjqg+1CzY0P9Zf0U7H4bU/0nG3P9VcEDBhKmCkZxGzMGJ4kBBBbQkxszG3QiLqZrGysbH/1bG08SexsYIVCDAzVUhmrQ929fVgTDDcFjwUn6ObkEhl4DSQNbUK/Qu6NkA2yGJoYyA3YDBdIsAxLQov0/+i3QqtCIMJsQP/pUAwKQyvhYMKk4Kg

MEMILQvoYcQAGGVsBkMNQAFxs+IWww2YZUISCSDxsvogqbKQNU0P0DKwNyIWEgmAxwmwoISJt6MOibJjC5Aw8EIoNK0NIw1JtkIQowqgMuA3jYHgNsmz+GTjCqm24wljDuAjYwhAQvGykwxjDidmsDJEZ33xQA+wMooWWbWdDYgx6bQkZEg1kbTQNhmyVGDtCvojGbRkYl/RyDetD5mwShf4NP0JqGJZsrIR0w4MBxRl6bUAhv0O9OWtCjMN2bJU

Z9mzMww5swkCyDQIMLmy0GfIM7MJDtGVQYfQWQ8oMEfWJQ6oMafVqDOR8H8HbHJoNa4JaDPxVQkPaDOlCSkLBtZL1ug1qQ0iJykKoQoYMPDBGDNZCL2DGDZOC4o3/bM8M0sOUIWJDlQ3mDG59FPRC9FYNLgKhAqsYfzUMUMfAdgxFQ9JALR1NIVKEUrUnwd4pjg0A0BqZ4I2RdCm8FwhQjKUNco1YjW4N4Y1qZee1bwMVKSp18YIGQwr1Z6EPGEu

0mHF+DWiBZMPySKPBoHxBDJ8ZrQ1fGLhAoQ2zgmEMzJiZDXrcxEiRDX9AwJiKICeZ0QxgmVBoFkOxDXEMBCgJDadgiQxrHBVhSQ3Z9OyDudGuEKkNmJjZAciZ6Q06oWUNk/D7g1kMmJlImUGM2JmW7a4YeQx4mHYcg+yNVWHDiJhWyObD3IAlDJYZ8cPzaGUNhQx9QqO0eII46ZSZGsPiQ8LCGOm09fhDxugLYekUOqBajEyZjQ0smI0NbsLyQ6B

8HJkPMXw854OUIKRDpRRkQuMdnKVRnFuDZ4jdDDslgpi9DEI9fQ1AIAMMYpiiPYMMc4ESmSwUBpkSPQth7BSymQnY0jzPQgqZkw0mmJFCfBTyPLMMCjxxQ3MNGpg19aKxs4mCna0lOpj6PbVCaj2vQvKx6jzSFRo86w1yAV0lAMMbDSqw5pg6PCf0ujwN9PIMtUMV9HaZdcLjDYY8BLFGPE6Zxj2aFYUopjzHJGcME4lFnASwU8LzwRY81KH6Faq

wlwx+mVcMAZlOyU5NKpzBmPcNf2Vfg6GZNsGPDRGZ0cDPDSL9kE2UA68NYv2bdX89Hw3JmHBMTWDwTJKN3wxZmZSMiNm/De5ViEzndIIDHEMFme2VMvzoTXCdmlyYTH2YCQJx3RWZ33SNndWYUI2HJEpZTSH62Q2ZgWGE3PCMCI35XIvV1NmaXSRNxTGkTfYMjNgUTeiNxsOYTUcYA5kYQtiM9Zg4jDcwNtmjmGmM5I0OjQj8f6zmTPvChIxMpcL

U85nNmNSMpIyDWGxYzsjOjYAj9IxUjaRYACMoKDSN25hhCa6Ne5gKTAyNQliMjG4pTI3bpcyNunSktKyM38MXmAfCikxCYRyNoBGcjdL9XI13mI7J95gGabu0LtB8jZQg/IywA0k12NTdSYKNSAK/4ZfAJHR/fKKMiGExjYt94o0wIzH03wxSjZ6MED16deE18lmyjZHDzVzX/NACt5iKjbc0/YjKjGyNKox0WaqMhiSIWEhYKFhQZJqN4COcWVD

DFJBLYBaMfFgPZIQjVsPaTHAjIxxGjaCDy5nGjQlgpo1GjBxZMMPejZAiaYlRjFaMAINMyAzdQANmWW61do32jYxZYQmyjE6MmozEWawjA/x3gK6Nb4xCWXQjyhgMIizc+ozgqewi3oxCWbYwXCKTwtwiFAz+jJbCDZiTfSTgxgzTjW/DwYwYQwRYilkyjayMA4OCANnZMiJSIscMUYwzjNGM5jwxjfb9v2hxjNpY8VFLQfGMKUMJjPpZ/4xDmBV

DP8Mm2fqNqYzkIq9VvoGEQzLZ8Zz88FBZ1lgsPZmMhY2tjf1VuY1vLPmBJiNZjCqchVg5jOYigKTuWE+M7VkVrGWNflnnLVr5qC17Q8o9NHldjV8thvh1jT0sdaxnLDbxsUyPLLKtdc3NjCTMrYzKIpDQ3oCjjJOMc6yqLdk4xM0tjInZeY0uIhWsHiP1Q+WMQugXLa4ih/VDjZ4iI43b3WiAPiI3LT4ifiITjYV4sgSeTdJZXiNRjcYjs4ymI1Y

jfVnzjbVIgCMkWUNYaY2RiOmNVX1NDX7RZYwTWeuNG40LVQEQ01kf4NuMO4ztKLuMe4xrWeexXkMHjctYKSVHjatZi1gsDVxlqmwCbeJsF4y87JeN9GhXjMUi14ynSXydp1hOWSUicjxWPM6xaiOlnc7Y362AiU+MZTnPjDr5GM2JTWWNJchgIg9ZmcCPWR+NT1mfjYIBX4yiAd+Nb1mlwHG8lBF1nX+NrEKDwbL4gE0XWOM5QEwVsIBt9g313Sn

BYEwg2F/AoNmdvS8Novybw1QDkNgwTBhNTdmwTTPVo2BfDGmZ05l7wtmYh8OQQtoD2dgb5Cz942CDvFjZJ8L6nYBsZ8MYjKFhyBXPxJCN2EzeTMOZbKEKAvO1MmjEKQu9Ex1GTbEAnKH95TUcQ72dSYBsj8N02CiMY23D2M/DeokUTabDRkzLIhGNlE3UTcOYEQ20TVJMYQj0TK5MmCHAIvzZNZxBAcxNGdiA9P7ZrE0i2aLY+iPsjRxM3uGS2CT

R5yPW2DxNtkxuTMARJPV8TF5NCtkCTNABgkz8TUJNTjTJ2SJNpyJBAVrZEkyXI2hg+kzDmJ8iBkz2mZ5MHyNk9ZClOdksTSZNskxG2MbZ8kw3IhZMaRVm2dbgKk33ICm5q6mR3BkNnyIg9PrxTyJ/Io7YJwBO2Cm5L/0nIvxMPyKSTV8j7k3nIR5MmdgaTP8j3kyaApwx/tmg6QHZZkzwIhxMsSI2TVZNcSO3DDZNhCIQoupM9kwaTI5NziKl4Yv

DNVjLwzhZLk1q2a5NEKNuTfCjByIeTI8i0SJIot8jxky52bYiedl2IzFN/kyReI4iLM1BTaiMIUx7mC05T1hhTFk04Ux2I4Eip0KT2FFMVjlUo2cs7iL4WXFNfdgI+bIEISJNQ2j4rM0MogYs6PjszRgtAy0czHqs2CzO+TYtOCyjLXYsWgQnOR74K0wlTEQtxUyELcQt8cwieKQsTCzNTJIsX9gMLNNMlCwOeVQtVDl2eTQs5Dhf2bp5ingMueQ

tDCwSASp4onlMLKgFzC34BSwsvC1aeVA5C3DsLYKjvUz0+MfYXC09TNwtAvh9TSfYJnisLbwsM018LMwteDliLJZ58qIjTGNNdCxsuQaitCx2eHVMoiyTTa/ZeqMSohIs0i2MOFIs800ieVwsNgUyLF55S01yLCtNk6yQLYXMYq1FzWWsrS3lrbOtyU0XLXcs9aznLRothixOo3Ws3SzoLNotTSEqOI5Fx0y1rAOMrqJuIm6jSU0NrEoFeCzKBbm

5Ca1XTF3NV0wtrLYsd03mOaMsIK2qrOMVvM2nOUT5oMzAzFc5v0zhos44X03gzHE5703qzaDN303qzBGiEaIAzAE5Ky1xos+4kMzRo87NYM0sOHGiUMy2Oba470xRo244iaNJonc58aPQzG2tEC2KLHajzSzQLJas5a3irP8tESKuI6ctlc3dLajMhi3uIrFY5bgYzRU5iUzDOLeYNTj8TbU4Aig4zHKdPy2NOY0i5bj4zS04vTCEzO04yQS0ER0

5ZTi8zRSioHFsTOTNfTnGODOtbKP5o0YspaOYcSM5NAA0zIjNJyw1zB2jNyx8oXFMMzkILZ0tCUycovcso9ioLA2i5cxcohgtObkKrLv5WCzDLbyiIy14+ZzN/KNjLKGjBUxazELNYTkCzdc5dswTolE4Tzi0udnMjzmizNbNNrnizRbNx9npzb7NEri+zKbNsszgBV7N8s2CuR7MVswJzUrMhIHKzZC5Ks2cuWh4sLjOzZrNGrjbog7MW3n2zQf

NyriqzUbMCrkYuMrMBsyguIbMis3+zebNADjmzUuiZszmzCujo8z4uJbNq6PP2bOjVLjKzDbMM6P7o9rNgszazM64G3n2zdfMucx7ogKtHLi2zMnNGc3cueujDgT5UB7N/Lhro2a5JUwuAPLMy6O6eQ85orhLo/DAfs16uVa4J6NzosK5gc1Ho0HMDrkHoyHMSrmhzcfYvPj3o4X5IGORzJq5MszRzWrMOrixzCrMVDluzWuj3zhpzB+iX6JfoiG

sCsyJzby5nnm/ol6t1riZzOLMrzhPojYFUc1p+LnMYGN5zIa4vPi2o1mjU612okct4q0qLfOsnaO1rC2jsqyozA4jLqLsonhjBaNVzJ6ieaIRIsyizqKy+A8twSO4YplDuvhW2Sm5Ji2Aoum4Zi1lo0pN5ix5oxdNfqOWLXqtiqx6rdYtw6NArFV4waM8zfYsYK1E+Q0tji2crYPNP82LuS4tPK3Qrf/Mbbn0BV24wfhBrF4sU811LD4tvXnt+cE

syKxR+Oitk7iorYEss7hIrCitAmIYrUisS7kJ+Zit2S3WrOEsFfip+JEsuKxRLIateKwxLXvN2fmxLYStrqwn+MSt+flErSfMW3lyYsksiS0pLaX4lK1pLFSsh3gSADfMNKyXzHfMafliYg/MDKyPzFytdfgrzRytgHhCYp/M93iorU35bK1MrW34pSwsrGUsrGKMrc4sgqw9+DUsfGOmYvysvfhALPCs9DmCrJ35WHgNLcKtuHkirQcs2aKeuDm

j9qK5om0tpHjzrK/BUqwILGyjJ01eogWjbqI9LIEiLmPsowWjaUxNjYOjdGOqBZgtbHnZTeoE3M3ArExioK1jogQt8ixWowz5QqPWowz4IqIBY/NNpCwVTOQslUwULfDBVUzULFQtr9jULNKjwi0yokIscqOhYvKj/9hNTGKiN9m6owC5pU1tTCqjRC2qo4VNaqOcLGK5rDkcLFqibnkJY2ai/C2gOaaigizCLdZ5QixGouNNIi0TTBFiC6OOeGa

ifCzmo7NNfU1SLAtNbjlWo5wsJUzyLPA5GGL3TUos9qLNouI52GN0zL4jpcw6+a6iTyyuY+WMAViaLL2iJGKxeVctOi2eo3mibmIEY2RjBaOa+Q8tObj9ouRiWk0BWRRjkQWUYvJDZiw8tdRjOGP/LdyiViyArUqsfKMu+MCtmaPBogKjX/lprCxjA81OLT74JmPwrVCt7GMB+RxjMK3VLBPNni0WYjxjCKwzzBysfi0fzaitAmKguIEsAmOLzb4

sAmOjePPNImKYrOvMmmLYrBJjES2zeBktADh4rGkt+Kz7zCX4cS3wwEStpKzHzCSs17hJLaSsxfgpLeSsqS0Urc+5ZfjpLVStK2KZLTStZ8zZLBvMOS3e+Bd5WmJ5LWq4OmP5LK/NpS2fzO/N+mLTYyUsHK3nYsZiXfjcrbUspmKjzEh5vK3mYuZiACyD+BNjwCxCrfUswqyOLec4pWNNLGViUPj2YrD4FWMdopVjtMykeHD5HS2WoIgtzaJlzO5

irmMco25jBGPVYh5jjazdY15juqxeYryj3mNczLgto6O+o35iZzkTLNa5kyyLLOT4UOMU+RMsyyzQOHMttPiQ4rA4/Pi7LNDijPm0+TDiNPgrLLY4qy18+Gstuy2IODDj2y3C+Fst6rjbLLQt8OJmQaji1gEXAXss6OIWQG9joq3ZososH2MzrQ6jEqxdYyTNtWPeo/XMe01JjaRif2MA4j6iSvm17Cr4DWLEYy1jTWLxuYWiLWLE4tVj5djPLKC

sLy1UYq8sRVhfLI5Z7yzqXSNBHyzlucgN9OOmIvzx3y1W+LjNvy3hIg1jNGJbObRjAKzWLYCsDGMtrH1jjGMgrP0V4OPMY9Zjg2MQrUNjt2NeLP75o2OjzA9i7iywrAH5cKwVLAisVmPLubxis81TYj35880zYybNs2NorMJj82MrzON5p2KhLEtjyfmbzLStW7kaY4d5O80Z+Cpja2MyYwSsG2KHzHn48mKJLNtipK2KYmStSmO7Y8pi+2OUreX

4h2LqYlkttK13zJpj9K3fuadjxmNnYkysn80vzTH5JS2XYmytV2PsrHxiN2MgLLdjcHkmY+B5pmJVLPdi/fljY/ytNS3cYs9jEuNCrMD4g2JMuHjjtmMBeXZi5WI5OJ9jX2PZOXAsUqy92DWszmOVY6IFVWKVrAoFpq0SBLRt/WOBWQqs2/nA49f4wOKBo3v4Kqy+Y3zjGzlqrcf51rkarcY5mqzn+Bg40a3arTf4uqyArFHji9h3+HPYhqwP+cW

sgoExrUQFHAUv+a/5BazmrQmsFq0bTXL4VqxPOXatirlRrTaska0qzVasP9jhrA6s5gGmASgEdgBOrWC4Gc0X2OAETjgQBVg4kAR1+e6tSATerJ6ssAVF44A4PqzmQeMBvqxvTP6tOAUBrG6t/axBrGy4ZgAurCGsmAR54znjWAURrDgFdeKweX/55Dlp4uA40a1GrHmssaz5rCQFieNv+IWs5AXFeBQEVPlx4lQES4HJraWs7IE5oo1Y9AXuLBm

tbbmMBVz5Wazn+dmtLAUUgLqtzePsBPmtnARt4rmsPATJ40WtV01JrV3ipay0BGWtruPEeLOteixkYkEicqy+4uv41a0JgDWtlOM04j7iSjgNrYoFHmNA4xYBnmKB4qvi3mK3TS2s9DlqBWDi7a1prB2trgCdrPoF/a1drCYE1gVjrAOspgSyo3YFw639rCYFA61WBGOsPay2BI4Efa2OOC4Fr6JGBPviJ+PjrSOtE63O45hi+OMEgdOs2GIz4yX

MX2O6LF2jrVhRIiF4i61jZDCdGL1+0CusIQSZw6usYQQ1xGEouI0T5O1jm6zRBSwZawNk9BG0arQYFKCw+6wHrQ0Eh60LYSkFR6xpBCXdJ6wZBJkFDQXgsNkEOQQXrcATD62XrdShhQTFBZkEJQQ5BTetFQR3rAATVQXVBZkFNQRPrZyAz63aJPEFL63UoU0Eb6zHrQ0F760LYR+snQTdBD0EayUhqD+s/QW/rQyDgwR9Qojcvxy9Ir2FhTh9I0D

ZNsAgbSjxkwVTBL3Yf4Jd1P+DnTwjI5pcmNHjhdBtSwQbsTZtBm2+JXBsXfS9QshsiG099ANDXrFUEluAQ0JPBahthwQjQvsEo0MYbIcEW4HjQrfpxwQ4beP1dwQMbWRtj/XfQgoNTA1PQqHhjMP0GS9CrwVBnWRsoAwUbM8EVwRAwq8FVG23WO8E34wfBTRtnwR0bb4U9G2w6GwT02B/BBxsJIVMbV/1GUEtQx9CwIWfQiYBIITfQgRsj0IihQT

C+ITcbQSFMm08bYRtKm2UwhkhCm10hYJs1MN/ZMpssmz0GSTCxITybSSFYm1bWCoTZA0t1MWAyMOTKdJsChLEw2oTfhlAw3JtpAwkhRiEamyKbMiEeMIM1VeAVIUUwhoShhJUgiWhmhNqbBJsmMIabEJsbmxgbFpszIWSCRzDHAxshPEZ1mxbgeQSnUOBNFwTZjlGbALDxmxObQTgzmxmbXwZLmxswiqFnGwcDMZs1mzcww4SAhL4JHZtz0L2bc4

TnMPyhSZtTmyKhc5s7hNCw65ty8MtPGqEHm3nhJ5swe1ahK7ApUVHXbqExHFZbJZR0W2j3EaESHGBbOkJQWygUbrFir0hbWu9n21sVcQJ4W0UCYZR9oV3XbTAUWyDbT2EEhy9EEwDroSqCe6FtxUehJZRnoQb1V6Fsh3JxfP9s4XJbWtBW20mlalsQYWncMGEGWyzXD5ldMGZbFETMORzIRGEMyGRhCNh3YX/cdGEeYSFbeqE5UXMwWHtDURJhaV

sKYWHhKNtWR2VbN88WYURHDmEcry5hU9c/NxHbPVts4RFhM7CTWzKwaUTAfEGvS1tj13BhbVQJ0R5hBdcKWVdbMrBdYX1hDxRHV3Lbb1tzYRR7f1tERMDbB2Fg20lmcNs5W1pEz/BvYVQFV/BxWS7HBq9UL17HSkTM22HhPZc12GIZdjBE4TOXWpEBW3ThTOEsHGzhUttprxFbSttq221EuLQ622HhauE64X83DpR22yvbTtstVGz7HttKRMHhYe

Fh21HbBLRx23BwKeEtVBnbR9cRW3nbQns4ey6hFdtt4Tq3PeEI21uKBPQd2wlUKq8D2yq0K+Eb4VB5M3d68QvbZ+FlxNvbYfd34VWlKrRH2yzbcVlX23fbcBFSWxfcb9thrwlUeBFXLyQRRQJUEXQRM6YVkVTQMrtoO0IRWDtKERoROhEjkUYRFDsuuDQ7DDtuEV4RBRF+EUERMRxhEQBRcbtJEWkRRrAKOwgk7TAqO3XQVRFpqDo7TRFjxV0RPY

gm0Bm7NjtTESq0Ljt2bz47RbtzRI/oJxEROzcRaag6UQgPKTtHuxk7QJFgiGCRVNBwkUiRaJETsFiRV1g4kTU7SdtUkWSUDJFskTyRQpFdO3KRSpFSEWqRQsTjO1M7AUBzO0s7azsYYFs7bpEHOyc7PZFRkQmRGZE5kShAcQYXKE3gbgB7bD3gXUAD4De6cEBBgR2QMoAL4HaQKoA8kCvAG8B7wCfAV8B3wE/Ab8BfwBOQQCAaoGjkKgALkBwQS/

Ya3ngQLYA5IFIQFBBNgH1LK4BEDh+mD4AhoDEgH5BuADX2HVMIpJ+mCFBygCleJ4Awel2AILM5/neAXvZ5gDcGZMB4UCOmRRAjIGYQfEB2EA4QLsByjz4QFpAPcPcgIlBvIAkQFRBGUAlAZFA6+E0QBhA6UHqkhlByUGZQYQB1EDtAVqTIAEwhPRB6wGpAXKBjEAFQdtohUBcQMSBvRTFQHgBpUAagJxA5UBdASZAZoFshbqAkgB1QPxAAuw1QPx

BgkFCQL+5FIGWAA25TUDmgcsB/am4AIGY1oDtQdn0doCmk51B/mhPAN1BlgA9QKZAaUE+WH1BnEDWQSAAHoE2QC6SbUGTAG71hwCPAIoAjwDAAfRAygBSAEcBRkDAAEGSjwH+QeKSEpPkgSpBIZKPAaGTYZM6AeGTEZISkpKTOgDeAcHoMpLkgbKSJoDWAKGSDwHRksoA4pKxkiKScZLKAPGT0pPXOTKTQUHeAYmSoZPAge6BQgCVDVoA1AC3ID3

1fUBkQb8hCsBy8H/BuAGyQDAB2fXi7CuBmgFaAexARwB5AWqJ7QHBkiABlAD5wbgAzUFEQM7pq6ErITJBQZJneOMAvQAmgU1ADbi+AWFAIZP9TT4BgUGWAJYApkEPORIBWZM6ABpBygEAIQWTaQGFk3ixZZMyAdJB4u0S7e+BH4GfgV+B34E/gb+AJgD/gABBZZK9gQ3h7QGcAeHMITiZkk+5QDh72C+5TgAPAJWSVZOChZOTXUMJsXmS+UDHAKO

T2Dg/2fWSUniNkhC4k5LHAaBB5IAtk01AP9ljAJIBbZNRk30AHZNpQb8g4jVqQcoM+ZLEgQAhm5J8OEIAOkBqQLyTkwCCAbsB0qBPAAyTCoDO6GTBlAHSQW6SvpKRQdIxmAAFAa3Qh/Dbkx2TaQDEWeeSQEEXkz6T8AHMkw8AckHQAb0BSwAlQWiBagHoANyTqIHOQZMBqoEmABZALgHeAMO4bjiguD/ZnkB4gcYAzgVSAb4AK7gNubKSn5Oik+R

AwkGSAIPjvQALARYAspK2AQEAUpPCgGhBdIDoQClA0QEqk0NALIAxQcqSrSQSsXFB0AAaPGqSg4GJQelB1QEakmBTha1CgHzs8FOwUvyBcFO6kvwBEoEWklKAtEBWGIaTsoBGkoxB8oHGkkqATwGFQaaTRUCAgCYB5pMcQPqTN5OWkx6T8dEcuKZBIDjVkhgBfEAGgYKEAkDEU0aAAmB87T5BvQCEUxkZTpIWgLZBEkFtQDaAbpKXksZAHpOOgU6

AVUDBQbxBDJJWQVhT1kEegP6TVFMC7XLsIAENwfQANYEAAJcjAACcg+ZEM6CqAaxS7FMcUoOAxSJ87RFAfJ2R6baTg0FjgLSwqgFG4cLszJyzgQJSmQHzgQEA4uxYUu6SBpJy7JuAJAFcU1AAHFKXgHSSN4G87fgVDJIQAYySNIFMk0+AygAdkyySr4EVeSQBVwG5ARIAf4AQAH+BT5LOQJuBvJMmAQsBqeNjAfBBjUAROUHoXkEYgdqBx7hmgf5

Be9nagBYBfpmTAGKToBmfOE6BgDhNQZmSZkD+AMBSoUB87SBSYQARQPBS4FNYQBBTLICQU/7gHcNQUgRB8UBrDWqSSUF8gVRBCpIJralAiFI6knBSupLEgFlBKFPZQII5aFLEgHlAxpM8gQVAYlOnkmaSgIESALhTiAFlQYxSBADcQWNQsAULABi5NpPEUwGBBlJ2kwMA9pNhAP4BwejH2AxTppItQK1BLpOSQa6TRUIyQXhT9oG0UrSAcwCmAMC

57q29QaCBNFM+Wf1AVFJegfxSElPQAcsAsVhSUrUBFYEsUylTklPcUvztPJwDgbxT/O1rUQBAAlJzgIJShSRCUo8QwlK5UiJS4AALgHd1JpOnkmwSSIGiNBlTUlNXgXSSMlNoFLJSclIoQTt4SZIKU8+A9kFC7H+B06CbAeIB1wAaAGpSmQHPksSBL5N2AO14kgCN42BAFkCmQDpSKrn4OSA58dCgQbe5AUGYOTBBf5MOua+44DmAOTqAJoBIQGZ

STJK9DPKSoFMWUo5SUUEcgFZT0UDWUtaAKpOKkvFAXIHGmERBIADEQYhTDlIMgZqTXVLekxhAk1NIUy5SepIoUnhTAYBuUuk47lPKAB5TGFKeUiaSXlKdQS+B2FJqgNYBPlO+U2JS/ID+UuIYNgAbuSaA3pMCQJ4ARFMCQSFSYwDSkqBA8RhEUksAzpIhaMxTSVNJAFFTJ5MJUnypHpLOAE6BlgE+AYA5fgHxUoxSG1J+kgNB/pNegSxTAAB4dIp

QqEUAALLkxQBKBLgAaVIWRclSIAB3U/dTD1K+gTztmVK8UjxScjz8U6OBaIEi7blSU4F5Uoyx8ABfUwVThVNw9UVTK1IgAcVSJAAvUg9SHIGvU7SSZVPSUreBcLHxU7JTZlOPgfJSwAEKU9VSJAC6QZEBVwEKwFEALwGwgfVS0FMNU0YAX5NgQRtiBlNvkxA5lgFAOa1SZIAuAfHRB9n3uZ6S4VO+QNNS0EDX2cJAw7jn+fO5SEHAUyUUA1IWUgq

SU1KCgZZSIADxAVZSkAHWUqIUBNPQUzkBMFLqk0lAGpIuUtqTY+JOU4NTM1Lk0yAArlLzUqhTygEGkrlBhpMbABhS+UFGQHkBnlMJUt5SaoAWAOtTFpJ+UxtTJkB2AU1B3gBOgRFAO1KyhYFTpFP2k0+45kEguHvYlFPOkovCrpPUU1FSp5P/U6dSdFMfku84ToGXUwlS11JJUoNAn1PegFOS4AGcAV+A3BApcLQAALBSYTWQKQEfNVrxX4G5ATQ

AhACMAZwBMAF7ORgAJgGcAVqhlAA3IapST1OcUiQBlZPi0xLTYAGS0zQBUtPS0lEBMtJ5wIQActLy0grSitIQAErSytIq0m9S7uDlU3ztCoDZUy0ULFOfU8JSk4B5UnVAP1K/UtBTIlNi7EVSK1KNAeJTYtNq0hLThYAa0xEAUtOCANLS+0Fa03Vh2tM60/LTCtMwAYrTStJBAAbTwNLXgSDT9JOg0hVS4NIIuBDSkNOmkjpBHwCSAUsAYeE0ILK

gcNKnAOpSL5PGAL8BbVJ72bYBQUH6Ulf5rVNzk1pSwek9ADYAvwCmQf4ANNLuAV1SpkHV494B1zkfub0Bf/l9U3JSIFJ0gHjTRrGDUgTShNPDUkTTI1OQUio8qpJ2UwlApNP2UslBpED40ylA01NOUmTTOpMZ08oA1NOSgAtTOUGW7HTTUwD00ygoDNNYDP9TrEFsQGqATgHM0vNTLNLCASZAAri2ADrN6NL9AKRTE4E2AZzS9UBkU86w19imUr8

AvNJHUnzTkVL80ydT0VLEgILSsVOeknqB85O/kpZACVIkAAoAY5ikwJo1WBPbAdsAuQEi00dTotP+02LTsYEAAWZ37dKIUJ3TmBNd0qrTLFP90x3SbEGd04PSmVKG0llT71JlI3xT2VIm05qMptM+WGbThoFCUz9SU9NW4H9TkvVF01KA1tKqAX3Sw9OXgCPSg9OlU27TFRnu0hixHtL9U57SVVMQ0tVS3tKqAeIBewF2IBoAfYHwAUsAsqCMAAU

BNPmqAOABFSGIABYAG8D+00f54UG8kz5BYwHrotpS0Di9AKhAodLnuP84GDiIQUFA5kFBQF1SCFINAZYBbVLNUljBHLkfkgsBcdKVU64B+DjeAGZA1UCmUu85kdIgAfKSidKZ02BTo1LDUhBTMUA2UlBTQ1O2U2NTdlLp05TSOdNpQBTTfkHTU9qS2dPOU3/TKaxzU2VBr9K00vnS6FN003lAhdKYU8xAVtJFQZvgxUBWAKXT7QFFkoBBpIHrkvh

TwQG10yaBDZK7UlXTwQF1udXSe1O6bB5BNgFWAN6Sh1OUUj3S1FNSQDRSTdPukihoZ1MkOa4BuoHx0WMBwtOYM76TiVI907eSilI6QQrAGgGYAC8BSwHIAAUA1gEx6JoBqgGYARIAmwBRARcBNAEFk0fTkBHH0wHSt9OmASfYDbkgOUK5fjigua/SUEF3OVYB87m2AQsB5gG6gN6ThlPGbNc5vQEBQO55VUDxGI/T3oAP2T1NfgBfOAsBVUC+AQ/

TuNL0gJZTH9ME04TSX9LE0wIyJNPjUnkAsFLOUkhSVNKRQf/TCFKU06Izk1M508AzrlNSgGhTtNJgMgXS4DP5QMtTmFOM06tSb9NmAdAyRZNlkrAycEBwM1xBJkD5UHYA9gCX2dXTIEEkU3VByDNmOdYBTzi+AHGSKgARU9dTzFMN0xgz/NO1kzoAmkCskiQBbwEkABWAFgFvAWoAKAHDgJoB0QB7YGOAUQFXAegBFwAaARIAYAGvwNpByjJv0zy

SdwHqQNmSMVNYM7MAP9muAVVAvQDDuHgylpIQgUxTTskEM5DT0AA4AdKhnwH+YIDg2AAWARUgoAE0IIwB0QBgALpBJAHx0RcA1DNAQQNSJ9N+OGYAwUEOONVAodLQOfvMITm6gLYAoLhOgUhAbDLgOGd56sDqMj/YwLjX2FwyfO2EgE843kDn+OfZF82NQJXSb9MDU3jT5NKKkrZTSdOf00TTiwzCM6qTJNM8gKIzgDJiM0AzSeMU0+/TlI1ZM5I

zVNNSM9TSedN0QTIz7lPoUnIzhdKM03gyKgEKM3AAY4BKMt2SjwGgAU5BsDPtk3Ay+uUOOOA5ngFJMxzSKLnbUlXSWjO9UwkBfgC6gGgzujKi0hgy+wCYMq4zTdMxU0Q52DK6gWYFlgEuMyzT3dNuM1VTdkCb0iQBiAFmAZsB1wFgQJsAYAEkAW8BCABgAGOAEACMAR8ACAFUMwBAlTPQAMfSQTM0M3gAVUEpzBy42rlAOdUyodMOOPC5/gF2Adq

BmlJgQDfTsEE2AOO4weh/2Y2TH5I40p7S+VFD+M14x9irM03i3pNv0xFBGEBJ04Iy6TJCnd/TwjL2Un/SmUC5MlqS2LESMnkys1JSM8hSIDMFM3iBPdH500aTS1MKgCUyrTLnAaUzCsDlMjsgyjJjMiYBKjIVQE8B87meAZ45dTN1QWKSopPKAbtT9UHCgG+iYEC+ARFBaDO80v41zTMjaALTAQDN020yTjNgBQ85EUGugCLT+DNdMhvT3TN3kqx

TnACaAFEBqgGUAa/BNAGfABWBiAB4AR8Bj8H3kLpA8tJ7YfQA/tKKgVqA5OC5AaqBU8z9+bvYGa1WAGt4odMfOFqtrU0+QRA419kuAAszfkDv2IPi6fjxGREzPgAPMyABONN1uSnMWDjB6VpSCwCBUvwzoFOJ0wIyaTMQUinTX9Kp0mNShEAwU5kzpNIOUocy/9OZ0zfTeAFZ0kSzYjK50/qSANIyM6AyRTNgMx5SZzPLUgoyUDKAgdEAlzJl0pt

SITmoQRYB6sF3MraSt9LIM48zlwxmQWBA51L10xFSN1PKAFJALTIGMyUyHzKekz5BYAVVuN6S3zMlMl0zrzIBkj30gZM6AcmSwZORk0mSRwECs3OS59JP03MAYBUR07gyc5PeQIhA6ZN72fHREHh72EKzgZLis844DbmwOYSAXbjcuXKTQZNzk2BAZkEOODqBvQFNQAdS0rICsjKzFgCys04z/znx0H+4o4DKANJ5QUFzAOe5qNIhOOZBKrLKAMK

zLAQhM7Kz6rLyspqywAEwOB5A+LgMMsHoEdKSAbqyYZJzkjbBoVK2AHaxVgBKs//ZKkGezRay+VELARy4ZkEiQGaywrKYOQsBVbjeQY1ABlLPMypA+VEX2YBTP5PXOAZS19gWAPayc5OSAGt4+gXB0yA5GRl10scAvwEX2MO43qy0uIC41zNRksmTHrOweJEypgEosnMAS5NBkj/ZsTl1uCJBGRk54qC4HrIKssizQbLVQerAIbJossoAYEDaeHv

YWMHkUohAXgG9AJGyjwH4gEGzsdLRsqizIbKPALqBUgHzuCwzQUD4uE1AVgGJszoBSbPurcmzwbLnUzGywABps5g5HLlzAYasBIERswGzQrOBs9myKLPRsrmzKkF5s8HS7NMFst5BhbJGQA4yxIB36O0BHPGnMhhB7aGF5Xyo9QDDwKvTPdOnUTmTw4HEoTOStZOzkkmyUbI5syWzqLIPAGWz/gDls4gEFbPr0sAB2wDuMj0z1QBSATQgoLOfACg

BNAAWAIwA1gAaAZQABQAWAXABagGYAX/AhVOjM9yTDNKQsygAULPBAI0zm7irM+YBcwBeAOf4cLIP2OYB51KSszni0DkmgEiz9zOuQWBBhFNzAHy519hxM4MBT9I2ABKyHDMUgQwyCdP8MjizqTNbMnizQjK2Uzszv9KSM0Sy4jPEszkzKTOigbuyZLP5M7nT0jNuU4Uzi1NFMlSzTEDUsyUyTNJv00sBtLIbU2XSTwBSeC6s1dPT0vczwoBos0R

TmjLMsmB51gHMMxRSYkGHUmyzejLssidSHUCcsm0yXLI4MzxBfjidM1dSPzJ8slWy/LMGMnqzS5OCskWz0rNBk95Af7gZsr0AvwGNMp5Av7JGQIGzQZKurNjSpoF2AO+T1zlAcsoA0ZLHAAFEFkCHzH+4YED+AYayUZLAc0WydZJAeRkZfbkxM2BAl1NLk2mzpgG/AT5BkrKwsm45a5Owcn+yjwG0Mue4PgGVQbqALVOmUscA/fmoQZg4hFJjzIg

4WbOas7W5Dji/ARizrgCIs6WzPvlBQerA6nk1Mw84+HLAAQFBUgEx0mZBgDljAC/TkdLKALE5/gHtUs4AETJeAMGzZHPkc+zSFkAROVfSYBTVk9Ryt9gugSQ5e9i2AYat9HL/uf4BLs0hMsC4PrIgc2y5uoG/AE1TngEUBHgBZHLYOWyFvDJ8BNqz4wHOstc5QDnnMd4ABIAV0mhyEHPAco8BjbhX+RfM8RnjAAwEaZJGsm84oLl+AY1AYBUZGQs

BZHOuON5A3vV/+QsAMdMOOSpBNgGLsqC50bMeQFg5ZHKYhBQEJlNNQT5BcVLMcsABobMOsx25VUBOgFg5ZgFqc/g4dgDgOFf45kENMtKTKkF+OSfZgFIGU16S7rPqeXpyqNP+QHFT/zmqcr0BRnL32LYBHkEgOeZBdbl+AWZyZICwBeYADLLeAVVAVnJPObwzY5MLAQkBVUFqcp14+lN72TYAcVLDuYJyxwDGcrazmDmKcnaxLnO/sqqzQZPsclJ

5vQD2cgZTVgGOcl5yznPecnxzPnI/s0GSoEGp4iyzYdMUgYA5PgGGs55zTnL6U85y75Nkc7GyADmEgN6yYBTeQE2TWnOgeVfSHDL5UHwF8dHRcjW5QDmDuYHTtrLYeUZzMDiUcttTzzNzAAGzaHK+co8Ap9O9APEZ3gG8MkhBYrKhs425rgGAOdc5GRheARA5mXJicnBy2XLXOBKzmlPTszMyaUCxs6uyLbimAdfYyNPiAWRz6sHxOZVy75M9UuB

ynnMVc2uyVXMMM9Vyn0y1c5C5D7Jac04An6KVcuuzVXLtk8VyVbJfydWz9NIRALWyGSGAoXWzAgH1s9mTEQC46Y2yeZLNsygoRwA1cm45TXMBQc1yDwEtcuBADXOIBI1ylbLdMiyT7jIPTHgBsIG5ATthEDmt4cmZ1wBRAR8BNCBSAdcAukD0ABCzAgDdAZCzvJKcMu/YYHLIcoBSw7mt0yAAUEE5c7B5nBgIBE1AWDkLssJAHDhUuIC4wenaga/

TONJOgZu5NHK2cr8ABIH+QRuz2LK5MlsyydJCM+kyO7MZMiIzE1KHs9kzU1IkswAylEEXcnszhzN6k0ezqFPHsxSzJ7OUsjWyE1NnMyzT57NwAXsAl7OnkleyYwCOsz3QiDK3sw65TLM10mcwY7gWc6yyejLHUiAB7LNvMqdSb7KiQVyyMnOeAa/TPLLnMvgybjJfs8oBAZPfs2azf7Pgcl2zYnM6AcGSwZM/oyfSx3hfOGPNtnPBc6DySbOuOXG

ywUGoM9YAvgDeANRy5HJBs5VBnqxmgYatPgFqcxYE5FL4ud45hDm2ATzSkHLZ4vlQWLItUnE4dgFqc5IAWMHeAQsBtzJgQNqzzrMsBPfSTbl+OH/Y0XMw8sKyxDgEUqBB/kBrcjozubPkc/5B9q3x0HW5ufjFcuDyJXNZspc4DDiYsuf43gD+AQkyynMysbMyZIDBQIxyTrNqc5W4iEER03W4o3Ioclpy9gDKuNA4ETKYsiE4pgCs8jtzAHP0Mnt

yynPbeQdzFgGHct5B/kE88sfZO3J88mZA/PIHc9qAh3P+QYLzonJds5WyIPMdc+yhD3L8gV1yY7RS8qDTq9LEgQ2zfXO5k5Ngs5MDckmyAUS9ubzyETgMMtRz+3JbcmLzAvLi89xzwIDdsn8zsIGPwY/BFSES7CYymgDWAf+AsqFmAZ8AOAHwATNgKkEC7GMzY7JLc+Oyy3Nn09/ZvwH6sqYBOnKh0nvZGTmJc1VA0zKgQPlQ23MBMHAE8wEA8sH

pCwBfOCsza9PKcwsAZePmQelykTLHcoNSJ3M4s1uzkkCjU2dyadMEs0eThLIZ0jdyxLOOUgAypLJe8+aSt3LksqAyJzKyMqcznXLyMxAz1LPF0m/TagAvc/9Sr3INAOMBEDgycoyyQVIP2R9z9pMBQB1TvwFKck+y6DIN0i+yjdKvskDyUwF/cu0yoLkJM0kzgPOdM5+ykVIg8t+zzbNZchDzYPMQcgqzMrDOAXW4pkGeOT+SwAUqQXOSfDLX2YL

ywegLAJhyNPIZ8kmyA/QHuSyyIQACuIxzOfOgQE6AefO6gPnz8EDQOWpz4nnWk54BF1NH2Pi4pfLPzeFzefL2cplzanJH2XW5DPNgQd4B9qzOBTXzufILAOXzdfMV8yTzgbLrUFbz+1PQckKTkZIUBOA5jZN2AVOyBlMeQLjzvrId8yuTfgGd8z6zUgATAMuyxlO/AeGzBfPg8soBSbPt864BHfID87YBKkDjuVVAyHI9UywylgAS8oXzWbN6BC5

y4/P98jYBE/LHALE4/gBNQFjTt9jxGUFB9fPSLKC55gFOspHSdbm5sxQ5HkGm5OjSPfI8823ydZJruL8AADmNQFtzYwGeANayxDlOs4352oDVQNw5mbI78tlzzjlmBZvy61H28+VywAGX+VEztzPkgC3TfHL9+Y2TPdFOAQ6zHLiV09RzMSzX03xEToGEgPYB7rMn8zoB3swhAJiyvfK4MvfzF/IP8mXzCQGP8riAz/JZciFz6HJ4BBMAGq2/Odz

S1rJ087fYWMHmQHvZ5gAn8t/ysPM6ARy5s7MEuAVyCbI4Mhfyb3nQcxjy07NO8sFywAsCsqYBUgAtU4A4+VBfOagza3Ipk4uzGRmnubwFPgG/OWRyrkEosqszpgHx0BqztICD8hWyB7mJ8+GycpMz8qPyRrIPOJmTbnKOOIg4oTIysyizPgD0MxjzTeN72cgK5fjeABHTcwCWAUBTgbPs0435PgHP0j4AZkFECs/MbNMXU4RSvbk58//zLVNPuTM

yGZNf8+1zafNpkh55rUwWQSwzdbicMznySHlgBZwYsLMWAXW59HILeVYAwnMAU8JB87ksC+Jz3NIyczJyjUFQCgwL3/IgCy/ZpoFB0hE5XzhmgFpyGmwPODxzzDOVQOA4S7P0c0P5IDk9QEVzsAvwCsAAGm2vuUFBszOIQYSAcc1ACvwLwAvUcgFEw7je9XMy6fhJMznz2oAx0c6AuoHXONVA7zl8cgt4EASiswFAknMM8ioL4c2qCqhykrMj8rT

zzHPyosLzwpO2AU4BFgHaCqoKcpK6C+oLz/PUcxO4wUDP0/85fjlLuUYLCQE6C2oLugt8cmYKuDKcCl4AT9OGs67AOgvGC1YLJgrjcvwKIAFVssXBUvKB8gyAMvOKqD1zsvINsjmT8vJNszWTOQBp86YKy81mC9XztgsWCkcA9grGCg2TDgtJc44LXtJ/MjKhFSAFACgBMwAWAH+B0QCaABYBbaEfAUsBuQEfAErTftOjsycBELPG8vuSjVJjAF8

5rkAAOPQyErN0M61TWlObueKFVUC2soS5kTNdUx5AqNLH8g/ZPUCcMsFTkpKe0xSBTqzY0tbyoEFP8i7yKTLe8kNT+EC4siNTbvMp08TS53K7M9dympPiM/syuTO7M77zc1O3czTSFLP+8pSzsjOnso9zZ7Px809zNjIcQL5SLNOXs3Syy7L4uPGSGjOkga/SjzKfcqZBzZPmAI5zMfKvMynzx1Nx8ocAf3KOM83T/3I9Ur1BDFPfMsDy7QtOC6n

zivMMCoKzS5Lyc1fZPkGP8ijyXzNHcwMKpgp5s/y4ZvM2ANh56azI0+ny2AtvTR5AmXNU82zTTUDv8rBz8gsCs5iAvQG2AVEyS/Iecthzf7MIOeuzzzLbU3W5WAt6CuRzwTIhOX25bnNucrKTRnJVuevz19iX2WTy1XKjC+RycrPecquTPVPgCxYE0PPuQHwzorLyCzTy6HNxkjhyvHPesve459lSc7QzWPMROZ44oEAHucgLRrPXOW5BgdOUc7m

ynPIocnYA8Rg9U/at9AonC/0K0DnQQIhzdgFOAHcLpbOgQUfzzzP5smB5uoH0c9z5Vbhv8k4zlPJac/4BZ9lV80/zVAoh6eIK35OGrUA5DTIisypAp9MzCxMBgAsWsrYBfHIn+dUyApMNMz4BwIttUxmzUwsNQN0LfHPyo+MBmlJf8mzSF/O2ARtipgBYwL4AirPmQKvygUHhsqsyCwsyCypB0pNMMjZztrLOBSaBanKzee5ANgFY03fymPNBk1k

LyrKYeHW5szIWQTzyelKwBa4B99mBQVILWQpT8iEzEgqgQOA5PPPwQGBzk6NFc90KeIu8ucQKHws5C1KyowucAUryyPL3uMTyWMBacqSKtzM0i6jSTwqz86PyH7nR84jSa3OwORSA6IvUi9kKa3PMi/RzIa2b8ihz/kAorE6SxwBMijSKOQtciqMLerghMmXihgoMs7qBHIrZCr8AAotP88gLrjkQOAZzH5M90RkZjIuSAI2SyguYOfZzyArsMww

zJoF+OerB0zN8i7+4T/MJMmIK0zPRc7W5U7kNMjiK0LgIizt5H7m9Uxy4UzNkcmmyJlImgWC4MnIUgOiK+rKtky1S5ItOAXwLTwv8CsoB6LN/+fasRICAcpkLOgHeARfY+VFCudYBWfIGUlqKA/QBc8PyYEGmc1JyZooR0wsABIs0cnoLJwpGilaLGRjWiivz/gE2i6+4ungQBZwYrgGWi1sLjou8BU6L1vLHALaLLotgQa6LPkDtcoaLTgqy81U

L0vJKMN1ysvK9cyCAHgoMAP1zCvIDc8mT6LIE8+EzzZI2ig8AXouwOK6KZoBuioELG9J/MowAYADgOOAAc2E0IPwBmAHRACVAYAGfgH+BbwGvwOYyi3LjsrEL8NINAdYBk/OmgD1ShgtrciABxzIwCo441nLYgM4A5fI28jVzQDirCs4zaAtIM5MBONLikhDMspNV87e5r9MbMgIyW7KnctszNlI7M0UKu7MHM2IyOTI+8gczpLNAM2Sy2UDHswt

SJ7IMQKey0vJF0pAy2FI0smqBATO1C+tTL3KbUvRTlPJSeY0KcEB3ss0LXNN+OOZA4rigQN9yzTN80/ozjdPx85yy/3I4Mx5AoLg8sj6T8fO8s70LIPNeCgoKAwt/svJyKgpxs9iAfjmxU/4AgwsOcnIL6sFB6TYBAUDoCgqz2LkzCmuyB7hOgPYBBossikjysXJW81fTzAtcs7mzrsHji/OLAAskC4uK2AtP0jrMagt72bKSZvLjigpyE4oLihu

L1/Lvo9HyXzgSiuwLO4rziw1B64qLi/Rz3kGQuE1Bdbiis0A47/JriruK64qTixuKawvkcgizlIDQ8q6KF/MKsmaB45IkcuZABlPjk9FyZ3hOgNzSa7Nuc4aywNn2c7VzDZOOi7SK0ArHAeizPDLcuHwFhDh2AQfz2Dhrs70ACbLBQahAWorsM+MAITIy41HyFwuNucKTJoE90Ff5vwFkc78KCDOTouHS5POGsq5AjIqGcuoLILjn+WBKkLgXU4S

BVgBDCtMzRnNDuSaAETPihY/z3gFgS7+44EC8M4A4drI/2aWzegT+AI0zvVKAUiyK2ArgOb/YNTK6UuZA7rJLCuJyY6wWQUwy4wrerKjyowu/AMvNUXMPiv+LpAu+ctJ4vIu6gCHoQIsUgcgLpPLzAT1T8oqAU8CKH7kPCrlytgAAOfRzfvkYSuSAE4vkS4az0pNN42E5oVIasnpygov4OARKKXN0c7tzzAroi0C5LDLKsjc599nHCkuKSHmoMpc

KWHL2AAuzfIsYBSBKE5LHeeMBPEuTC645jopoSlRzK5I18wJLzbnTs+1S+gTcM3xzLARMStw4WHILAbmz1gDmAPKKOoC4gEPNZHM5LfBBGnLLi2TznEtySvlR8krqMs4AiktiuM4BspMXoxEzrQp4igxKqkvX2GpLnbKGiqOKbszLs1YBODMW84iKKkvsM+dTMnghMrpKS4uuAC4BSAo48yC459kkitpLRkoKS2pLREsWBLKzDjh/ueuy5kGGSvJ

KOkvGSlqLOS0MMj4B6aw983gLWksqSpZLOktgS3c55nOeAJYAiDkgOYjyckpGS6pL9kqjCpjTf/gQQVHzhIELAHZL2krGSwpL3kus8r0AirIgStfZIDjoih54vfMHipmyykHCSmsKNXIIsjCzF6NRMqaKKZONuAO5/gFhimxzV4oOisAAeYoWi344drDysp5LtbixY3mL3NJreY1ya7gBcnMyknNjANFL8UrJS4Q4KUqVciZKm4vJcx+ShXLo85s

LfIuZSth4ForZS2RyZoofk7hKj4t6zdfS+UuuQclLBUoSsiZKkvMgAM4KnXPgMyKBrgp1stWzPXMyU3LyQYq5kp4KivPJkkVLJDiLMn+LspLMc9c5pUpZS2VKqUtRi78zL4A6QFEAK6l7AEwAhAAFAP7TWkFBMjVyunP8S8az+1JEUyOBsbJs8ogLS7hKskRSbDKacoCKmZMC8pwLkIqFip7SWMG5Cu/SB7L5CtFBaTLbsmdyFYoe8pkynvPp02T

Sl3MlCySz1Yq+8tRA5Qt+8xUKsoGVCwHyVUtUs/Iy57OlM58BIfNVM0Q4+1PrshHytUE3sTezdpLMskTzJDg2km0L9dPA8+0LvYrx8yzS/YqJ8jHSBlMfs6eSw4tssmLSqgA32CAUT4CcUyxT50ue0wbT/VE7UuPSd40jgDlTJtIFU6bS31Nm0pbh5tLzgKOyltN/U42LNNIL0iQAV0s7ecvTZVLuCmDTFVPegZVSmvLtSqoAxjImMqYyZjMIAOY

yFjKWMlYy1jI2Mv7Te5ITsy5Aw7gGuV85OXM6smNKxIF4gOK4MdGiihu4fgCZisNL8dBPOUFAe7mQuEy4DvLx0r+48EFNSzEyxlNhQMkzCdKbM5FBJ3NTSoULeLJFCzNL53JZMjWLXvN7s97yEjOlC8ULi0tHMnWLedKVC/dyVQsNi49yG1NPczQglzMwMmMzfAobkqzTHpMMsmAVwUvti+NKO0ohUsyyWDmm5Cuy+0rPsj9yv3MtMkdLCfKfM1Y

AEXKZisnyn7K9CmdKfQsrIfyzhoujio8Bswu6S3MK0MrFiuZAsMqmUywKLrItuLKTCMvyeeVK3/M+WHVKwYtNsl4L8fOn0Z2S7hMJUp2ShZMGAUoyFTI9k9yB4uxEMsQyJDNwAKQyZDLkMhQylDJUMwgBZwDlkiOSEUDxCgVyiQHTsjxyNgGTk2rTZFKf+BqyuoGAOGEzcwHXMtqSm5M8k1uTJTI7k2rLu5LFQXYzAQAHk2pB33IQAV9KRjPQAOA

AUgFEAa/ATgAoASrSRvJjs91KEzMfOV4ADDjH2C5zrVMM89g4czORi+HT/gA28sC4VbmvCttS3kC6gDHzIUFr0uTKxICli5uz39IFC8nSqMvbsjNLP9Np0oSyc0vZ0xjLVYpYypNKZQvYytIyd3N1ivdz9YoPcy4KZ7JrSjULpTNXABtKqjMekt0KvIuAOe2KJoGR82EB/zjus084PYvoMr2KHLJ9irTLnQsfM1yzJ9LX2YOLbdNDiinzjMpDQdA

ATUGM7T5Al0rPUvHLIPAJyuPT10ukgERSfFIjgR9SvdOPS4JTD0vcAY9Ls9KiU5bTCVMA03HLRBhJyuaSbtPvSoGLcvP3gJ7SX0vjcneS30okAegAHPkciaoAJUAFAQ+QKADvMQOye2F7OfXgtLLRC5rKQIFAy3gATjPxM4nzYgtIC61SpAox0Gxy94sGcneybDLeARfZYAub8mozT7krshTAQTmtTOf4aApGCtizLvKTSijLuLNOy9NL+EE7sq7

LHsuDUvsyC0tYy5WLNYpHs0tLd3O4y97LeMs+ytULvspPc6UyukGEylcz3JLEyxtKA7lk8xQL7YuIyp2LYQDbi+RKx/Jhy7HzB0vhy4dKG1NHSp8ze/JzMydL/1OnS8+zFUt9C8mTArMQ8qzKS4oaUi3LgUCtywjLhrJNQe3Ksks5CiyzPooM0vLzQYoK83zL3oBl0p3RAstdk/HyQspdksLLJTOn0TuS6suny2kBF8qayoCAWsv7khEJ2sqi0rr

LilMVIL2y5/mIABoAZZNegUbyxsuxC8KA5vNhY2+STblBQI2TrVPVM2fYCEDQOLJKNTI28+YBiuKZk7fSnDN4S2iy40vmUpuyrvJliyjK7LLu887KBLKzS9WTnvNzS27Ll3P7s3kK/cuzUkcznsoVC8PLy0p4yytLcjOrSkHza0tNim/S9VIti3UKrYsWgApzNTPZ4zPLW0o10/aSLbgBcokLVMo6ym8zNMtLy7TKUcuScsHoq8r9QIzLa8q905v

TCQCaAQnLYtM90fgqycr0kinLN0t8nGnKguxT0+nLN7Lm0rPTFtLEgaJS2cqvSjnKUgGEKvKS0lMr0rVKbdNg02vShcq/MhNz3bIgAdcBr8Fd4LKh0QB/gThTVcoNUgHSL8s1yxQ4sDlwChpKbbOfkxiAgHk34tbzt/PGs7HSNvI2AOLMTAseQBYLMzNtyvbLygAOyoAqjspu80ArhQoZM2jKxQuDy2Ar80tXcpUBECs3cktLtYpeyrjL0CsjyzA

rxTPVCuPK8CtwAY/B/so3M2KSDAQFs2aB5MrbSzfZwcvcQAmzdvM6My8z+0u9CjTLHLN9ilgrg7keQbayOCuuM36TC8p4KiQA8coEK3gr0ABEKuVS9gHEKhPTxtM3U3dKQuwkAGQqNUDkKvdKT0pz0kLo89K0QFQrxXlEGO9K7tO0K76SBcr0KsyThcqEMqoAQEH/gH+B8AAqUt1K8NMgAaqAqApPgMt5FvPx0G6ymYpQQDg49LjKynKy19JByoZ

TXVI/yhu4v8oNuH/KRFM400Iq4UHJMxNLeQvdywULoiuoy2IqLsse8qArrspAMxIq+7LVioPKGMtlCjjLMiqFMt7KlZINi6PLDNPyKgTLpTOqAEoqGECbUvnyK8s1FKorE4F72WorgwD30tmsLzNNM2HK+jOLyx0Lr7KRy2+ytnK+AArKPQq8srHLuCpxyrYq1CuGKwYq+CrXS0QqcEEpysbTSECkK5YqFisPMjPSmcoUK8oAlCslM9nLRSvUK/b

LNCulKkeSdCqfSvJT69OBC0XL1QAmAZgB4gGfAbkB1wESAKu5iABjgK9JSwGUAR8B1wBOAIwBcAGAyjfK7CrRsmd5Z1Ln2agyP9iCk8YB5zAJ0UjTITOekiKLfioksrJyFHPjATNjYTm3uHDKKEDSeO+TCPLDuR5KSEAAK8dy3cuu82WK00vbM73LFYt9ytjL/cpZ0wtKYCqxKlAqBpLLS7lACSqrSr7KcCp+yworFSETyhUzyjJTygHLZFPkSuK

5KCvBAKaLd7M7Sp9z2oFO86t4TTNPshgq4cu/crkq06GOMlHKBlMcuERSDMqnSoUqP3Ijiv0LzMqby6jy5zjB0hMqEdO+CnOLZotguNbzMyucGAfLvXKNskfLngrHy5eyJ8tCy5QBgstpASfK58v8yp3RV8ux4R8qHSsayj8r18vVy1rKt8qHkgQzjisTcngAmwH0AY/BMACyoJoAT5OsK3DTbCupijUZZICZc61N7nIci1wq+IDgQIe44TPfiw2

5dXJ/kiSzlgAJ+eMBGRkyCkBTs4uZC3bLsytdyqEq8ypAK0kAwCqLKuIqlYsxKvBSA8uSKweyEiqrKgUzOMtxKiPL8So+yhsqY8qbKgoqwfNwAW8BySt+UxaAJrJm89graStVkhkrnmiWAT4ByQsaK1kr+itaKhHLmCu5K/2KKPPNk/TKQ4vJ8rgqP3JFK0nLmUFpUonLucuj08nKZSsmK6nLE9JmK5PTFSrT0xYqj0vkK09LFCtZyzUrNitMq3U

qINK0K+VT+cqMkwXKjioMKkXLusogATQg9gDgATQhsIFvAMkrYKp4KifSnkHYOGB5BXN284jLeIAD87/YY7jX2ezKsrJWy8pyD9mSczMLfwvIqv/LKKoTSsjL+NNoqj3LYSrOyxiqESsgKhNT6MqLSssqV3M+8ysqnsu4qnErxzOyK/iqo8sEqokrY8pJKwoqFYAkqiTLVZL3uC/SIUrkqmHyFKqEUtGyCPPHKrHyB0s/cy+zOSvaK7Sq7TJuOEi

qeity81crPdJFKhYAmgEEPcUr0AGOq06qxithACYro9K3SyQrOVLmK/dKIjIi7NyrViolYdYrL0vx2arTzqpOq6AUdiv8qg0r9iqCqw4qXtLRis0qIAFmALpBagEKwLKgUgFXAAgqRsrPk+CrbivBAA2Tg/OC8mB40dI3s2DLBoB72IO4kgHLcie5rDNdUw44yDjerCZTLDO9UkIqqKp5CpjLk0vgU/MrPcsLK6nTGqroy6AqbsolCtEr7soQK0s

qkCp+8jIrUCteyviqS1MJKo2LQfOqgG/THwHGq6Hz2Rn/OIRTKivBU6oqByuzy1WTR/NaUh+z6Cs9i9krpys2q2cqXQrj8iS4oLj2qkxS+itWqkyqeAHjYM6rxXgtq0YqrKulKm6rRtIfU+yqyVMcqx6rU9IPS2QrXKuWK5nKz0tz0i9K4lK+qyxTPkEtqnnLdioCqw0rgqpe08AACoBv09ch05nCyroB6fEnAQmgYIAOABgAzvgYq/EBXQFdAEY

AIAGmWJsACeh0iKkzIisZqyAB86oJ6H1QYivu81mrc6vLqrIBm0nZqlEra6qIKAuqsgCLq5jKpQrLqlurC6qAMlirigDzq7uqsgHvAZAruqq7q8SZW6vAq2sq06rrq/QB0aCdq6Yqx6uEQCeq56pj0jdL+6pnqjOg6cucqxeqosp7qmrKQICXy4xSB6vHqgnpreC/Kg+q18o8kv8r16sHq/QBO5N4ZXoAewFzqvbQsQHwAapTGICKs2kLAyuACo0

y06pfq/kALwEgQLE4fgCUyp3LlnP7qowBZeHgs+UzDzPkYrLLA4oR07eTj6qXqgnph6u4U+0AwDLsgXOqqQBIAayqRtP1i3BrMwCFU52r8SpIAQHYpBS1qghq+LOXMsSBWoip0XoBNpjA3Jhq41AghLkBpKFKMfhAH0M+QdwZuGsTMp5pWGqQanZZkUC3xJeShqqWCJBsE6sgAfU4+cvVKogBiGvDqyAARmRkagaSV4DH6fWykGr4pOeSbeDgAch

qcpxkam/Tcgm/ULEBJGsVMmOzlBNooAuAttIMAB+rBSqMqz3ThvG+wixr+isNs5ZUbE2MazeT8IHAABpBIjIRcEWTdwBAAXcAgAA
```
%%