![[claude code 2025-07-27 10.31.57.excalidraw]]


Swarm 群
Hive-Mind 蜂群 写调成
  Quean 代理 Mash策略
  

Claude CODE 集成 MCP

mota GLM ms-128a29e5-2096-4e0d-87ed-80bc151c5c0d


















Skip to content
Navigation Menu
ruvnet
claude-flow

Code
Issues
103
Pull requests
13
Discussions
Actions
Claude-Flow v2.0.0 Alpha represents a revolutionary leap in AI-powered development orchestration. Built from the ground up with enterprise-grade architecture, advanced swarm intelligence, and seamless Claude Code integration.

License
 MIT license
 4.7k stars
 630 forks
 78 watching
 78 Branches
 71 Tags
 Activity
Public repository
ruvnet/claude-flow
Name		
ruvnet
ruvnet
Merge pull request #555 from ruvnet/alpha-81
ae25336
 · 
6 hours ago
.claude-flow
🔖 Bump version to alpha.82 for npm publish
6 hours ago
.claude
🔖 Bump version to alpha.82 for npm publish
6 hours ago
.github
🚀 Release: Claude Flow v2.0.0-alpha.62
2 weeks ago
.hive-mind
feat: Release v2.0.0-alpha.80 - GitHub hooks separation
11 hours ago
.roo
🚀 Release: Claude Flow v2.0.0-alpha.61
2 weeks ago
.ruv-swarm/benchmarks
feat: implement critical hook safety system & resolve all init issues (…
3 weeks ago
analysis-reports
feat: Release v2.0.0-alpha.80 - GitHub hooks separation
11 hours ago
archive
🚀 Fix headless mode for swarm command - Enable Claude CLI in non-inte…
2 days ago
assets
Add comprehensive test suite for Claude-Flow Console
2 months ago
benchmark
feat: implement critical hook safety system & resolve all init issues (…
3 weeks ago
Repository files navigation
README
MIT license
🌊 Claude-Flow v2.0.0 Alpha: Revolutionary AI Orchestration Platform
Claude-Flow v2.0.0 Alpha：革命AI编排平台
🌟 Star on GitHub 📦 Alpha Release ⚡ Claude Code 🏛️ Agentics Foundation 🐝 Hive-Mind 🧠 Neural 🛡️ MIT License

🌟 Overview
🌟概述
Claude-Flow v2 Alpha is an enterprise-grade AI orchestration platform that revolutionizes how developers build with AI. By combining hive-mind swarm intelligence, neural pattern recognition, and 87 advanced MCP tools, Claude-Flow enables unprecedented AI-powered development workflows.
Claude-Flow V2 Alpha是一个企业级的AI编排平台，彻底改变了开发人员使用AI的构建方式。通过结合蜂巢群智能，神经模式识别和87种高级MCP工具，Claude-Flow可以实现前所未有的AI驱动开发工作流程。

🎯 Key Features
🎯关键功能
🐝 Hive-Mind Intelligence: Queen-led AI coordination with specialized worker agents
🐝蜂巢智能：由女王领导的AI协调与专业工人的特工
🧠 Neural Networks: 27+ cognitive models with WASM SIMD acceleration
🧠神经网络：27多个具有WASM SIMD加速的认知模型
🔧 87 MCP Tools: Comprehensive toolkit for swarm orchestration, memory, and automation
🔧87MCP工具：用于群编排，内存和自动化的综合工具包
🔄 Dynamic Agent Architecture (DAA): Self-organizing agents with fault tolerance
🔄动态剂体系结构（DAA）：具有容错性的自组织剂
💾 SQLite Memory System: Persistent .swarm/memory.db with 12 specialized tables
💾sqlite内存系统：persistent .swarm/memory.db带12个专用表
🪝 Advanced Hooks System: Automated workflows with pre/post operation hooks
🪝高级挂钩系统：带有预/后操作钩的自动化工作流程
📊 GitHub Integration: 6 specialized modes for repository management
📊github集成：6种用于存储库管理的专用模式
⚡ Performance: 84.8% SWE-Bench solve rate, 2.8-4.4x speed improvement
⚡绩效：84.8％的SWE板凳求解率，2.8-4.4 x提高速度
🔥 Revolutionary AI Coordination: Build faster, smarter, and more efficiently with AI-powered development orchestration
🔥革命性的AI协调：通过AI驱动的开发编排，建立更快，更聪明，更有效

⚡ Try v2.0.0 Alpha in 4 Commands
⚡尝试4个命令中的v2.0.0 alpha
📋 Prerequisites
📋先决条件
Node.js 18+ (LTS recommended)
Node.js 18+（推荐LTS）
npm 9+ or equivalent package manager
NPM 9+或同等软件包管理器
Windows users: See Windows Installation Guide for special instructions
Windows用户：有关特殊说明，请参阅Windows安装指南
⚠️ IMPORTANT: Claude Code must be installed first:
⚠️Mimpimtant：必须首先安装Claude代码：

# 1. Install Claude Code globally
npm install -g @anthropic-ai/claude-code

# 2. (Optional) Skip permissions check for faster setup
# Only use if you understand the security implications
# claude --dangerously-skip-permissions
💡 Windows Note: If you encounter SQLite errors, Claude Flow will automatically use in-memory storage. For persistent storage options, see our Windows guide.
💡Windows注意：如果遇到SQLite错误，Claude Flow将自动使用内存存储。有关持久存储选项，请参见我们的Windows指南。

🎯 Instant Alpha Testing
🎯即时α测试
# 1. Initialize Claude Flow with enhanced MCP setup (auto-configures permissions!)
npx claude-flow@alpha init --force

# 2. Explore all revolutionary capabilities  
npx claude-flow@alpha --help

# 3a. Quick AI coordination (recommended for most tasks)
npx claude-flow@alpha swarm "build me a REST API"

# 3b. OR launch the full hive-mind system (for complex projects)
npx claude-flow@alpha hive-mind wizard
npx claude-flow@alpha hive-mind spawn "build enterprise system" --claude
🤔 Swarm vs Hive-Mind: Which to Use?
🤔群vs蜂巢：要使用哪个？
Feature	swarm Command	hive-mind Command
Best For最好的	Quick tasks, single objectives
快速任务，单一目标	Complex projects, persistent sessions
复杂的项目，持续的会议
Setup设置	Instant - no configuration needed
即时 - 无需配置	Interactive wizard setup交互式向导设置
Session会议	Temporary coordination临时协调	Persistent with resume capability
持续具有简历能力
Memory记忆	Task-scoped任务分组	Project-wide with SQLite storage
带有SQLite存储的项目范围
Agents代理商	Auto-spawned for task自动产生任务	Manual control with specializations
手动控制与专业
Use When使用何时	"Build X", "Fix Y", "Analyze Z"
“构建X”，“ FIX Y”，“分析Z”	Multi-feature projects, team coordination
多功能项目，团队协调
Quick Rule: Start with swarm for most tasks. Use hive-mind when you need persistent sessions or complex multi-agent coordination.

🎯 Typical Workflows - Your "Happy Path" Guide
New to Claude-Flow? Start Here!
Confused about .hive-mind and .swarm directories? Not sure when to create new hives? Here are the most common workflow patterns:

🚀 Pattern 1: Single Feature Development
# Initialize once per feature/task
npx claude-flow@alpha init --force
npx claude-flow@alpha hive-mind spawn "Implement user authentication" --claude

# Continue working on SAME feature (reuse existing hive)
npx claude-flow@alpha hive-mind status
npx claude-flow@alpha memory query "authentication" --recent
npx claude-flow@alpha swarm "Add password reset functionality" --continue-session
🏗️ Pattern 2: Multi-Feature Project
# Project-level initialization (once per project)
npx claude-flow@alpha init --force --project-name "my-app"

# Feature 1: Authentication (new hive)
npx claude-flow@alpha hive-mind spawn "auth-system" --namespace auth --claude

# Feature 2: User management (separate hive)  
npx claude-flow@alpha hive-mind spawn "user-management" --namespace users --claude

# Resume Feature 1 later (use session ID from spawn output)
npx claude-flow@alpha hive-mind resume session-xxxxx-xxxxx
🔍 Pattern 3: Research & Analysis
# Start research session
npx claude-flow@alpha hive-mind spawn "Research microservices patterns" --agents researcher,analyst --claude

# Continue research in SAME session
npx claude-flow@alpha memory stats  # See what's been learned
npx claude-flow@alpha swarm "Deep dive into API gateway patterns" --continue-session
🤔 When Should I Create a New Hive?
Situation	Action	Command
Same objective/feature	Continue existing hive	npx claude-flow@alpha hive-mind resume <session-id>
New feature in same project	Create new hive with namespace	npx claude-flow@alpha hive-mind spawn "new-feature" --namespace feature-name
Completely different project	New directory + init	mkdir new-project && cd new-project && npx claude-flow@alpha init
Experimenting/testing	Temporary hive	npx claude-flow@alpha hive-mind spawn "experiment" --temp
📁 Understanding "Empty" Directories
Don't panic if directories seem empty! Claude-Flow uses SQLite databases that may not show files in directory listings:

# Check what's actually stored (even if directories look empty)
npx claude-flow@alpha memory stats        # See memory data
npx claude-flow@alpha memory list         # List all namespaces  
npx claude-flow@alpha hive-mind status    # See active hives

# Your project structure after initialization:
# .hive-mind/     <- Contains config.json + SQLite session data
# .swarm/         <- Contains memory.db (SQLite database)
# memory/         <- Agent-specific memories (created when agents spawn)
# coordination/   <- Active workflow files (created during tasks)
🔄 Continuing Previous Work
# See what you were working on
npx claude-flow@alpha hive-mind status
npx claude-flow@alpha memory query --recent --limit 5

# List all sessions to find the one you want
npx claude-flow@alpha hive-mind sessions

# Resume specific session by ID
npx claude-flow@alpha hive-mind resume session-xxxxx-xxxxx
🪝 Advanced Hooks System
Automated Workflow Enhancement
Claude-Flow v2.0.0 introduces a powerful hooks system that automates coordination and enhances every operation:

# Hooks automatically trigger on operations
npx claude-flow@alpha init --force  # Auto-configures MCP servers & hooks
Available Hooks
Pre-Operation Hooks
pre-task: Auto-assigns agents based on task complexity
pre-search: Caches searches for improved performance
pre-edit: Validates files and prepares resources
pre-command: Security validation before execution
Post-Operation Hooks
post-edit: Auto-formats code using language-specific tools
post-task: Trains neural patterns from successful operations
post-command: Updates memory with operation context
notification: Real-time progress updates
Session Hooks
session-start: Restores previous context automatically
session-end: Generates summaries and persists state
session-restore: Loads memory from previous sessions
Hook Configuration
// .claude/settings.json (auto-configured)
{
  "hooks": {
    "preEditHook": {
      "command": "npx",
      "args": ["claude-flow", "hooks", "pre-edit", "--file", "${file}", "--auto-assign-agents", "true"],
      "alwaysRun": false
    },
    "postEditHook": {
      "command": "npx", 
      "args": ["claude-flow", "hooks", "post-edit", "--file", "${file}", "--format", "true"],
      "alwaysRun": true
    },
    "sessionEndHook": {
      "command": "npx",
      "args": ["claude-flow", "hooks", "session-end", "--generate-summary", "true"],
      "alwaysRun": true
    }
  }
}
Using Hooks in Claude Code
Hooks integrate seamlessly with Claude Code's workflow:

Automatic Triggering: Hooks fire automatically during Claude Code operations
Context Awareness: Each hook receives relevant context (file paths, commands, etc.)
Non-Blocking: Hooks run asynchronously to maintain performance
Configurable: Enable/disable specific hooks as needed
Hook Examples
# Manual hook execution
npx claude-flow hooks pre-task --description "Build REST API" --auto-spawn-agents

# Post-edit with formatting
npx claude-flow hooks post-edit --file "src/api.js" --format --train-neural

# Session management
npx claude-flow hooks session-end --generate-summary --persist-state
Fixing Hook Variable Interpolation
If you're experiencing issues with ${file} or ${command} variables not working in your hooks (common with Claude Code 1.0.51+), use the fix-hook-variables command:

# Fix all found settings.json files
npx claude-flow@alpha fix-hook-variables

# Fix specific file
npx claude-flow@alpha fix-hook-variables .claude/settings.json

# Create test configuration
npx claude-flow@alpha fix-hook-variables --test
This command automatically transforms legacy variable syntax to working environment variables:

${file} → $CLAUDE_EDITED_FILE
${command} → $CLAUDE_COMMAND
${tool} → $CLAUDE_TOOL
🐝 Revolutionary Hive-Mind Intelligence
Queen-Led AI Coordination
Claude-Flow v2.0.0 introduces groundbreaking hive-mind architecture where a Queen AI coordinates specialized worker agents in perfect harmony.

# Deploy intelligent swarm coordination
npx claude-flow@alpha swarm "Build a full-stack application" --strategy development

# Launch hive-mind with specific specializations
npx claude-flow@alpha hive-mind spawn "Create microservices architecture" --agents 8 --claude
🤖 Intelligent Agent Types
👑 Queen Agent: Master coordinator and decision maker
🏗️ Architect Agents: System design and technical architecture
💻 Coder Agents: Implementation and development
🧪 Tester Agents: Quality assurance and validation
📊 Analyst Agents: Data analysis and insights
🔍 Researcher Agents: Information gathering and analysis
🛡️ Security Agents: Security auditing and compliance
🚀 DevOps Agents: Deployment and infrastructure
⚡ 87 Advanced MCP Tools
🧠 Neural & Cognitive Tools
# Neural pattern recognition and training
npx claude-flow@alpha neural train --pattern coordination --epochs 50
npx claude-flow@alpha neural predict --model cognitive-analysis
npx claude-flow@alpha cognitive analyze --behavior "development workflow"
💾 SQLite Memory Systems
# Cross-session memory management with SQLite persistence
npx claude-flow@alpha memory store "project-context" "Full-stack app requirements"
npx claude-flow@alpha memory query "authentication" --namespace sparc
npx claude-flow@alpha memory stats  # Shows 12 specialized tables
npx claude-flow@alpha memory export backup.json --namespace default
npx claude-flow@alpha memory import project-memory.json
🪟 Windows Users: SQLite will automatically fallback to in-memory storage if native modules fail. All features work normally, but data won't persist between sessions. See Windows guide for persistent storage options.

🔄 Workflow Orchestration
# Advanced workflow automation
npx claude-flow@alpha workflow create --name "CI/CD Pipeline" --parallel
npx claude-flow@alpha batch process --items "test,build,deploy" --concurrent
npx claude-flow@alpha pipeline create --config advanced-deployment.json
🧠 Neural Network Capabilities
Cognitive Computing Engine
Powered by 27+ neural models optimized with WASM SIMD acceleration:

# Train coordination patterns
npx claude-flow@alpha neural train --pattern coordination --data "workflow.json"

# Real-time predictions
npx claude-flow@alpha neural predict --model task-optimizer --input "current-state.json"

# Analyze cognitive behavior
npx claude-flow@alpha cognitive analyze --behavior "development-patterns"
Neural Features
Pattern Recognition: Learns from successful operations
Adaptive Learning: Improves performance over time
Transfer Learning: Apply knowledge across domains
Model Compression: Efficient storage and execution
Ensemble Models: Combine multiple neural networks
Explainable AI: Understand decision-making process
🔧 DAA MCP Endpoints
Dynamic Agent Architecture
Complete programmatic control over agent lifecycle and coordination:

# Create specialized agents
npx claude-flow@alpha daa agent-create --type "specialized-researcher" \
  --capabilities "[\"deep-analysis\", \"pattern-recognition\"]" \
  --resources "{\"memory\": 2048, \"compute\": \"high\"}"

# Match capabilities to tasks
npx claude-flow@alpha daa capability-match \
  --task-requirements "[\"security-analysis\", \"performance-optimization\"]"

# Manage agent lifecycle
npx claude-flow@alpha daa lifecycle-manage --agentId "agent-123" --action "scale-up"
DAA Features
Resource Allocation: Dynamic CPU/memory management
Inter-Agent Communication: Message passing and coordination
Consensus Mechanisms: Democratic decision making
Fault Tolerance: Self-healing with automatic recovery
Performance Optimization: Real-time bottleneck resolution
MCP Tool Categories
🐝 Swarm Orchestration (15 tools)
swarm_init, agent_spawn, task_orchestrate
swarm_monitor, topology_optimize, load_balance
coordination_sync, swarm_scale, swarm_destroy
🧠 Neural & Cognitive (12 tools)
neural_train, neural_predict, pattern_recognize
cognitive_analyze, learning_adapt, neural_compress
ensemble_create, transfer_learn, neural_explain
💾 Memory Management (10 tools)
memory_usage, memory_search, memory_persist
memory_namespace, memory_backup, memory_restore
memory_compress, memory_sync, memory_analytics
SQLite Backend: Persistent .swarm/memory.db with 12 specialized tables
📊 Performance & Monitoring (10 tools)
performance_report, bottleneck_analyze, token_usage
benchmark_run, metrics_collect, trend_analysis
health_check, diagnostic_run, usage_stats
🔄 Workflow Automation (10 tools)
workflow_create, workflow_execute, workflow_export
automation_setup, pipeline_create, scheduler_manage
trigger_setup, batch_process, parallel_execute
📦 GitHub Integration (6 tools)
github_repo_analyze, github_pr_manage, github_issue_track
github_release_coord, github_workflow_auto, github_code_review
🤖 Dynamic Agents (6 tools)
daa_agent_create, daa_capability_match, daa_resource_alloc
daa_lifecycle_manage, daa_communication, daa_consensus
🛡️ System & Security (8 tools)
security_scan, backup_create, restore_system
config_manage, features_detect, log_analysis
📊 GitHub Integration
# GitHub workflow orchestration and coordination
npx claude-flow@alpha github gh-coordinator analyze --analysis-type security
npx claude-flow@alpha github pr-manager review --multi-reviewer --ai-powered
npx claude-flow@alpha github release-manager coord --version 2.0.0 --auto-changelog
npx claude-flow@alpha github repo-architect optimize --structure-analysis
npx claude-flow@alpha github issue-tracker manage --project-coordination
npx claude-flow@alpha github sync-coordinator align --multi-package
🛡️ Seamless Claude Code Integration
Auto-MCP Server Setup
v2.0.0 Alpha automatically configures MCP servers for seamless Claude Code integration:

# Automatic MCP integration (happens during init)
✅ claude-flow MCP server configured
✅ ruv-swarm MCP server configured  
✅ 87 tools available in Claude Code
✅ --dangerously-skip-permissions set as default
Enhanced SPARC Workflows
# Advanced SPARC development with neural enhancement
npx claude-flow@alpha sparc mode --type "neural-tdd" --auto-learn
npx claude-flow@alpha sparc workflow --phases "all" --ai-guided --memory-enhanced
🧠 Cognitive Computing Features
🎯 Neural Pattern Recognition
27+ Cognitive Models: Adaptive learning from successful operations
Pattern Analysis: Real-time behavior analysis and optimization
Decision Tracking: Complete audit trail of AI decisions
Performance Learning: Continuous improvement from past executions
🔄 Self-Healing Systems
# Automatic error recovery and optimization
npx claude-flow@alpha health check --components all --auto-heal
npx claude-flow@alpha fault tolerance --strategy retry-with-learning
npx claude-flow@alpha bottleneck analyze --auto-optimize
💾 Advanced Memory Architecture
SQLite Persistence: Robust .swarm/memory.db storage with 12 specialized tables
Cross-Session Persistence: Remember context across Claude Code sessions
Namespace Management: Organized memory with hierarchical access
Enhanced Schema: Agent interactions, training data, performance metrics, and more
Memory Compression: Efficient storage of large coordination contexts
Distributed Sync: Share memory across multiple AI instances
📊 Performance Metrics
🏆 Industry-Leading Results
✅ 84.8% SWE-Bench Solve Rate: Superior problem-solving through hive-mind coordination
✅ 32.3% Token Reduction: Efficient task breakdown reduces costs significantly
✅ 2.8-4.4x Speed Improvement: Parallel coordination maximizes throughput
✅ 87 MCP Tools: Most comprehensive AI tool suite available
✅ Zero-Config Setup: Automatic MCP integration with Claude Code
🚀 Available Capabilities
# Check memory system performance
npx claude-flow@alpha memory stats
npx claude-flow@alpha memory list

# Test GitHub coordination modes
npx claude-flow@alpha github gh-coordinator --help
npx claude-flow@alpha github pr-manager --help

# Workflow orchestration
npx claude-flow@alpha workflow create --name "Development Pipeline" --parallel
🎮 Advanced Usage Examples
🏗️ Full-Stack Development
# Deploy complete development swarm
npx claude-flow@alpha hive-mind spawn "Build e-commerce platform with React, Node.js, and PostgreSQL" \
  --agents 10 \
  --strategy parallel \
  --memory-namespace ecommerce

# Monitor progress in real-time
npx claude-flow@alpha swarm monitor --dashboard --real-time
🔬 Research & Analysis
# Deploy research swarm with neural enhancement
npx claude-flow@alpha swarm "Research AI safety in autonomous systems" \
  --strategy research \
  --neural-patterns enabled \
  --memory-compression high

# Analyze results with cognitive computing
npx claude-flow@alpha cognitive analyze --target research-results
🛡️ Security & Compliance
# Automated security analysis with AI coordination
npx claude-flow@alpha github gh-coordinator analyze --analysis-type security --target ./src
npx claude-flow@alpha github repo-architect optimize --security-focused --compliance SOC2
npx claude-flow@alpha hive-mind spawn "security audit and compliance review" --claude
🏗️ Alpha Architecture Overview
🐝 Hive-Mind Coordination Layer
┌─────────────────────────────────────────────────────────┐
│                    👑 Queen Agent                       │
│              (Master Coordinator)                      │
├─────────────────────────────────────────────────────────┤
│  🏗️ Architect │ 💻 Coder │ 🧪 Tester │ 🔍 Research │ 🛡️ Security │
│      Agent    │   Agent  │   Agent   │    Agent    │    Agent    │
├─────────────────────────────────────────────────────────┤
│           🧠 Neural Pattern Recognition Layer           │
├─────────────────────────────────────────────────────────┤
│              💾 Distributed Memory System               │
├─────────────────────────────────────────────────────────┤
│            ⚡ 87 MCP Tools Integration Layer            │
├─────────────────────────────────────────────────────────┤
│              🛡️ Claude Code Integration                 │
└─────────────────────────────────────────────────────────┘
🔄 Coordination Strategies
Hierarchical: Queen-led with specialized worker agents
Mesh: Peer-to-peer coordination for complex tasks
Hybrid: Dynamic strategy selection based on task complexity
Neural-Enhanced: AI-optimized coordination patterns
🛠️ Alpha Installation & Setup
🚀 Quick Alpha Installation
# Global installation (recommended for testing)
npm install -g claude-flow@alpha

# Or use NPX for instant testing
npx claude-flow@alpha init --force

# Verify installation
claude-flow --version  # Should show 2.0.0-alpha.53
🔧 Enhanced Configuration
# Initialize with full alpha features
npx claude-flow@alpha init --force --hive-mind --neural-enhanced

# Configure Claude Code integration
npx claude-flow@alpha mcp setup --auto-permissions --87-tools

# Test hive-mind coordination
npx claude-flow@alpha hive-mind test --agents 5 --coordination-test
📋 Alpha Command Reference
For complete command documentation, run:

npx claude-flow@alpha --help          # Main help
npx claude-flow@alpha help <command>  # Detailed command help
Key Commands:

Hive-Mind: hive-mind wizard, hive-mind spawn, hive-mind status
Neural: neural train, neural predict, cognitive analyze
Memory: memory store, memory query, memory stats, memory export/import
GitHub: github <mode> (6 specialized modes available)
Workflows: workflow create, batch process, pipeline create
🧪 Alpha Testing & Development
🐛 Bug Reports & Feedback
Found issues with the alpha? We want to hear from you!

🐛 Report Bugs: GitHub Issues
💡 Feature Requests: Use the "Alpha Feedback" label
🛠️ Development: Check the claude-flow-v2.0.0 branch
📋 Alpha Testing: Join our alpha testing program
🔬 Testing the Alpha
# Test available GitHub modes
npx claude-flow@alpha github gh-coordinator --help
npx claude-flow@alpha github pr-manager --help  
npx claude-flow@alpha github issue-tracker --help
npx claude-flow@alpha github release-manager --help
npx claude-flow@alpha github repo-architect --help
npx claude-flow@alpha github sync-coordinator --help

# Test memory functionality
npx claude-flow@alpha memory stats
npx claude-flow@alpha memory store "test" "alpha testing data"
npx claude-flow@alpha memory query "test"

# Test workflow execution
npx claude-flow@alpha workflow create --name "Test Pipeline" --parallel
📊 Alpha Metrics Dashboard
# Check memory usage and statistics
npx claude-flow@alpha memory stats

# View available GitHub coordination modes
npx claude-flow@alpha github --help

# Test workflow capabilities
npx claude-flow@alpha workflow --help
🚀 Roadmap to Stable v2.0.0
🎯 Alpha Phase (Current)
✅ Hive-mind coordination system
✅ 87 MCP tools integration
✅ Neural pattern recognition
✅ Distributed memory architecture
✅ Auto-MCP setup for Claude Code
🔄 Beta Phase (Coming Soon)
🔜 Enhanced swarm intelligence algorithms
🔜 Advanced cognitive computing features
🔜 Enterprise security and compliance
🔜 Multi-cloud deployment automation
🔜 Real-time collaboration features
🏆 Stable v2.0.0 (Q2 2025)
🎯 Production-ready hive-mind orchestration
🎯 Complete neural computing suite
🎯 Enterprise-grade security and monitoring
🎯 Comprehensive documentation and tutorials
🎯 Professional support and training
🤝 Contributing to Alpha
🛠️ Alpha Development Setup
# Clone the alpha development branch
git clone https://github.com/ruvnet/claude-flow.git
cd claude-flow
git checkout claude-flow-v2.0.0

# Install alpha dependencies
npm install

# Build alpha version
npm run build:alpha

# Test alpha features
npm run test:alpha
🔬 Alpha Testing Guidelines
Focus on hive-mind coordination testing
Test neural pattern recognition accuracy
Validate memory system persistence
Verify Claude Code MCP integration
Report performance metrics and bottlenecks
🛡️ Enhanced Safety & Security Features
Enterprise-Grade Security in v2.0.0 Alpha
Claude-Flow v2.0.0 introduces revolutionary safety features that ensure secure, reliable AI orchestration at scale:

🔐 Auto-Configured MCP Permissions
# Automatic settings.local.json creation during init
# Pre-approves trusted MCP tools - no more permission prompts!
{
  "permissions": {
    "allow": [
      "mcp__ruv-swarm",
      "mcp__claude-flow"
    ],
    "deny": []
  }
}
🌐 Quantum-Resistant Security Architecture
QuDag Networks: Future-proof encryption for global communications
Byzantine Fault Tolerance: Consensus protocols prevent malicious agents
Zero-Trust Agent Communication: Every inter-agent message is validated
Encrypted Memory Storage: Cross-session persistence with AES-256 encryption
🛡️ Multi-Layer Safety Mechanisms
1. Hook-Based Validation System
# Pre-execution safety checks
npx claude-flow hooks pre-command --validate-security
npx claude-flow hooks pre-edit --check-permissions
2. Agent Isolation & Sandboxing
Each agent runs in isolated context
Resource limits prevent runaway processes
Automatic timeout on long-running operations
Memory usage caps per agent
3. Audit Trail & Compliance
# Complete audit logging
npx claude-flow security audit --full-trace
npx claude-flow security compliance --standard SOC2
4. Real-Time Threat Detection
Pattern recognition for anomalous behavior
Automatic agent suspension on security violations
Neural network-based threat prediction
Self-healing security responses
🔒 Secure Communication Protocols
Cross-Boundary Security
End-to-end encryption for all agent communications
<1ms latency with full encryption
Secure WebSocket connections with TLS 1.3
Certificate pinning for MCP servers
DAA Security Features
# Secure agent creation with resource limits
npx claude-flow daa agent-create \
  --security-level high \
  --resource-limits "cpu:50%,memory:2GB" \
  --sandbox enabled
🚨 Safety Guardrails
Automatic Safety Checks
Code Injection Prevention: Sanitizes all inputs
Path Traversal Protection: Validates file operations
Command Injection Blocking: Secure command execution
Memory Overflow Protection: Prevents buffer attacks
Rollback & Recovery
# Instant rollback on security issues
npx claude-flow init --rollback --security-breach
npx claude-flow recovery --point last-safe-state
📊 Security Monitoring Dashboard
# Real-time security monitoring
npx claude-flow security monitor --dashboard
npx claude-flow security scan --deep --report

# Security metrics and alerts
npx claude-flow security metrics --last-24h
npx claude-flow security alerts --configure
🔧 Auto-Configured Security
Claude-Flow v2.0.0 Alpha automatically configures secure defaults:

Agent isolation and resource limits
Encrypted memory storage with AES-256
Complete audit logging for all operations
Neural threat detection and prevention
Auto-approved MCP permissions for trusted tools
🛡️ Defense-in-Depth Architecture
┌─────────────────────────────────────────────────────────┐
│                 🔐 Security Gateway                     │
├─────────────────────────────────────────────────────────┤
│     🛡️ Hook Validation │ 🔒 Permission Layer            │
├─────────────────────────────────────────────────────────┤
│          🚨 Threat Detection & Response                 │
├─────────────────────────────────────────────────────────┤
│     🔐 Encrypted Communication │ 📊 Audit Logging       │
├─────────────────────────────────────────────────────────┤
│            🐝 Isolated Agent Sandboxes                  │
└─────────────────────────────────────────────────────────┘
✅ Security Best Practices
Regular security scans with npx claude-flow security scan
Enable audit logging for production environments
Use high security level for sensitive operations
Configure resource limits for all agents
Regular backup and recovery testing
📚 Additional Documentation
Core Documentation
Complete API Reference - Comprehensive API documentation
CLI Commands Guide - All command-line options
Architecture Overview - System design and internals
Examples Directory - Code examples and demos
New Features (v2.0.0)
🔒 Git Checkpoint System - Automatic Git checkpoints for Claude Code sessions
📊 Real Performance Tracking - Actual metrics collection and analysis
🪟 Windows Installation - Special instructions for Windows users
Integration Guides
MCP Server Setup - Model Context Protocol configuration
Claude Code Integration - Enhanced AI workflows
GitHub Workflows - Repository automation
📄 License
MIT License - see LICENSE for details.

Alpha Disclaimer: This is an alpha release intended for testing and feedback. Use in production environments is not recommended.

🎉 Alpha Credits
🧠 Hive-Mind Architecture: Inspired by natural swarm intelligence
⚡ Neural Computing: Advanced AI coordination patterns
🛡️ Claude Code Integration: Seamless AI development workflow
🚀 Performance Optimization: 2.8-4.4x speed improvements through parallel coordination
🚀 Ready to experience the future of AI development?
npx claude-flow@alpha init --force
Join the alpha testing revolution!

GitHub NPM Alpha Discord

Star History
Star History Chart
Built with ❤️ by rUv | Powered by Revolutionary AI

v2.0.0 Alpha - The Future of AI Orchestration

Releases
 71 tags
Packages
No packages published
Contributors
9
@ruvnet
@claude
@alexx-ftw
@lanemc
@tommy-ca
@6farer
@michaelozery
@coygeek
@mosdehcom
Languages
TypeScript
48.6%
 
JavaScript
38.8%
 
Python
9.5%
 
Shell
1.5%
 
HTML
0.8%
 
CSS
0.7%
 
Other
0.1%
Footer
© 2025 GitHub, Inc.
Footer navigation
Terms
Privacy
Security
Status
Docs
Contact
Manage cookies
Do not share my personal information
