/*
Description: This script resizes the selected Excalidraw elements to a width of 200px while maintaining their original aspect ratio. The height will be adjusted proportionally.

How to use:
1. Select one or more elements on your Excalidraw canvas.
2. Run this script.
3. The selected elements will be resized.

```javascript
*/
if(!ea.verifyMinimumPluginVersion || !ea.verifyMinimumPluginVersion("1.5.0")) { // Assuming a base version for API compatibility
  new Notice("This script requires a newer version of Excalidraw. Please install the latest version.");
  return;
}

const selectedElements = ea.getViewSelectedElements();
if (!selectedElements || selectedElements.length === 0) {
  new Notice("Please select at least one element to resize.");
  return;
}

const targetWidth = 500;
const elementsToUpdate = [];

for (const el of selectedElements) {
  // Skip elements that are deleted, don't have valid width/height, or have a width of 0 (to prevent division by zero)
  if (el.isDeleted || typeof el.width === 'undefined' || typeof el.height === 'undefined' || el.width === 0) {
    // console.log("Skipping element: ", el.id, " (deleted, no width/height, or width is 0)");
    continue;
  }

  const originalWidth = el.width;
  const originalHeight = el.height;
  
  // Aspect ratio is height / width if we are setting width and want to calculate height
  const aspectRatio = originalHeight / originalWidth; 

  const newWidth = targetWidth;
  const newHeight = Math.round(newWidth * aspectRatio); // Round to nearest pixel

  // Create a new object for the updated element.
  const updatedEl = { ...el }; 
  updatedEl.height = newHeight;
  updatedEl.width = newWidth;
  
  elementsToUpdate.push(updatedEl);
}

if (elementsToUpdate.length > 0) {
  ea.copyViewElementsToEAforEditing(elementsToUpdate);
  await ea.addElementsToView(false, true); 
  new Notice(`Resized ${elementsToUpdate.length} element(s) to width ${targetWidth}px proportionally.`);
} else {
  new Notice("No applicable elements were selected or could be resized (e.g., already deleted or invalid dimensions).");
}

/*
```
*/