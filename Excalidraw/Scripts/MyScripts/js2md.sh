#!/bin/bash

# 检查是否提供了文件名参数
if [ -z "$1" ]; then
    echo "错误: 请提供文件名作为参数 (例如: 'Add Box ↓ Down')"
    exit 1
fi

# 定义文件所在的目录
base_dir="/Users/<USER>/Library/Mobile Documents/iCloud~md~obsidian/Documents/Moc/Excalidraw/Scripts/MyScripts"

# 从参数构建文件名
filename_base="$1"
md_file="$base_dir/$filename_base.md"
js_file="$base_dir/$filename_base.js"
temp_file=$(mktemp) # 创建一个临时文件

# 检查文件是否存在
# 使用双引号确保路径正确处理
if [ ! -f "$md_file" ]; then
    echo "错误: Markdown 文件不存在: \"$md_file\""
    rm "$temp_file" # 删除临时文件
    exit 1
fi

if [ ! -f "$js_file" ]; then
    echo "错误: JavaScript 文件不存在: \"$js_file\""
    rm "$temp_file" # 删除临时文件
    exit 1
fi

# --- 修改开始 ---
# 1. 查找包含第一个 '*/' 的行号
marker='*/'
marker_line=$(grep -n -F -m 1 "$marker" "$md_file" | cut -d: -f1)

# 检查是否找到了标记行
if [ -z "$marker_line" ]; then
    echo "错误: 在 \"$md_file\" 中未找到标记: '$marker'"
    rm "$temp_file"
    exit 1
fi

echo "在 \"$md_file\" 的第 $marker_line 行找到标记 '$marker'。将保留此行及之前的所有内容。"

# 2. 提取 MD 文件从第 1 行到标记行（包含）的内容到临时文件
head -n "$marker_line" "$md_file" > "$temp_file"
if [ $? -ne 0 ]; then
    echo "错误: 无法读取 \"$md_file\" 的前 $marker_line 行"
    rm "$temp_file"
    exit 1
fi
# --- 修改结束 ---


# 3. 将 JS 文件的内容追加到临时文件
# 使用双引号确保路径正确处理
cat "$js_file" >> "$temp_file"
if [ $? -ne 0 ]; then
    echo "错误: 无法读取或追加 \"$js_file\""
    rm "$temp_file"
    exit 1
fi

# 4. 用临时文件的内容覆盖原始 MD 文件
# 使用双引号确保路径正确处理
mv "$temp_file" "$md_file"
if [ $? -ne 0 ]; then
    echo "错误: 无法移动临时文件到 \"$md_file\""
    # 尝试恢复临时文件以便检查
    echo "临时文件保留在: $temp_file"
    exit 1
fi


echo "操作完成: \"$md_file\" 的内容已更新。"

# 注意：mktemp 创建的临时文件在成功 mv 后会自动处理，无需手动删除
# 如果 mv 失败，临时文件会被保留