/*

选中当前节点连出去的所有节点

```javascript
*/
/*
@title Select Outgoing Sub-Tree
@description 从当前节点开始，选中所有“沿箭头向外”连接的节点（递归），以及它们之间的箭头
@hotkey Mod+Shift+A
*/

(async () => {
  const tell = msg => new Notice(msg);

  /* —— 1. 版本检查 —— */
  if (!ea.verifyMinimumPluginVersion?.("1.5.21")) {
    tell("请将 Excalidraw 更新到 ≥ 1.5.21"); return;
  }

  /* —— 2. 起点 —— */
  const start = ea.getViewSelectedElements()[0];
  if (!start) { tell("⚠️ 先选一个起始节点"); return; }

  const all = ea.getViewElements();

  /* —— 3. BFS 收集所有下游节点 —— */
  const nodeIds = new Set([start.id]);
  const q = [start];
  while (q.length) {
    const node = q.shift();

    // 找所有从 node 出发的箭头
    const outArrows = all.filter(el =>
      el.type === "arrow" && el.startBinding?.elementId === node.id
    );

    outArrows.forEach(arrow => {
      const tgtId = arrow.endBinding?.elementId;
      if (!tgtId) return;

      const tgtNode = all.find(el => el.id === tgtId);
      if (!tgtNode || nodeIds.has(tgtId)) return;

      nodeIds.add(tgtId);
      q.push(tgtNode);
    });
  }

  /* —— 4. 收集节点 + 两节点间的箭头 —— */
  const nodes  = all.filter(el => nodeIds.has(el.id));
  const arrows = all.filter(el =>
    el.type === "arrow" &&
    nodeIds.has(el.startBinding?.elementId) &&
    nodeIds.has(el.endBinding?.elementId)
  );

  const toSelect = [...nodes, ...arrows];

  /* —— 5. 在视图中选中 —— */
  ea.selectElementsInView(toSelect);

  tell(`✅ 已选中 ${nodes.length} 个节点和 ${arrows.length} 条箭头`);
})();
