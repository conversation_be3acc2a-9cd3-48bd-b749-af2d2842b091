/*
Description: This script resizes the selected Excalidraw elements to a height of 50px while maintaining their original aspect ratio. The width will be adjusted proportionally.

How to use:
1. Select one or more elements on your Excalidraw canvas.
2. Run this script.
3. The selected elements will be resized.

```javascript
*/
if(!ea.verifyMinimumPluginVersion || !ea.verifyMinimumPluginVersion("1.5.0")) { // Assuming a base version for API compatibility
  new Notice("This script requires a newer version of Excalidraw. Please install the latest version.");
  return;
}

const selectedElements = ea.getViewSelectedElements();
if (!selectedElements || selectedElements.length === 0) {
  new Notice("Please select at least one element to resize.");
  return;
}

const targetHeight = 500;
const elementsToUpdate = [];

for (const el of selectedElements) {
  // Skip elements that are deleted, don't have valid width/height, or have a height of 0 (to prevent division by zero)
  if (el.isDeleted || typeof el.width === 'undefined' || typeof el.height === 'undefined' || el.height === 0) {
    // console.log("Skipping element: ", el.id, " (deleted, no width/height, or height is 0)");
    continue;
  }

  const originalWidth = el.width;
  const originalHeight = el.height;
  
  const aspectRatio = originalWidth / originalHeight;

  const newHeight = targetHeight;
  const newWidth = Math.round(newHeight * aspectRatio); // Round to nearest pixel

  // Create a new object for the updated element.
  // This is a common pattern in Excalidraw scripting to provide a list of modified elements.
  const updatedEl = { ...el }; // Shallow copy is usually sufficient for top-level properties like width/height
  updatedEl.height = newHeight;
  updatedEl.width = newWidth;
  
  elementsToUpdate.push(updatedEl);
}

if (elementsToUpdate.length > 0) {
  ea.copyViewElementsToEAforEditing(elementsToUpdate);
  // The second boolean parameter for addElementsToView often controls history creation.
  // Setting it to 'true' typically creates a new undo/redo step.
  await ea.addElementsToView(false, true); 
  new Notice(`Resized ${elementsToUpdate.length} element(s) to height ${targetHeight}px proportionally.`);
} else {
  new Notice("No applicable elements were selected or could be resized (e.g., already deleted or invalid dimensions).");
}

/*
```
*/