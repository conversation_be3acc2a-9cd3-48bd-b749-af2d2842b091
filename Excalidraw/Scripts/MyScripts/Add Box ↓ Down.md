/*
![](https://raw.githubusercontent.com/zsviczian/obsidian-excalidraw-plugin/master/images/scripts-normalize-selected-arrows.png)

This script will reset the start and end positions of the selected arrows. The arrow will point to the center of the connected box and will have a gap of 8px from the box.

Tips: If you are drawing a flowchart, you can use `Normalize Selected Arrows` script to correct the position of the start and end points of the arrows, then use `Elbow connectors` script, and you will get the perfect connecting line!

```javascript
*/

/*
@title Add Box ↓ & Re-layout Tree (3-Section Layout) v6.2 — 添加子节点并重排树（三区布局, 支持编组, 优先布局在最深父节点下）
@description 选中节点下方添加新子节点，然后对整个所属树进行重新布局。支持将 Excalidraw 编组作为整体移动。当节点有多个父节点时，尝试将其布局在层级最深的父节点下。
@hotkey Mod+ArrowDown
*/

await(async () => {
    const info = t => console.log(t);
    const bail = t => { info(t); return null; };

    /* ── 版本检查 ── */
    if (!ea.verifyMinimumPluginVersion?.("1.5.21")) return bail("请将 Excalidraw 更新到 ≥1.5.21");

    /* ── 选中元素 & 基础数据 ── */
    const selected = ea.getViewSelectedElements();
    if (!selected || selected.length === 0) return bail("⚠️ 请先选一个节点");
    const sel = selected[0];
    if (!sel || !["rectangle", "ellipse", "diamond", "text"].includes(sel.type)) return bail("⚠️ 请选择一个有效的节点元素");

    /* ── 获取父节点文本 (用于新节点命名) ── */
    let parentRawText = "";
    const currentElementsForTextFind = ea.getViewElements(); // 获取一次，用于查找文本和后续布局
    const boundTextElement = currentElementsForTextFind.find(e => e.type === "text" && e.containerId === sel.id && e.rawText);
    if (boundTextElement) { parentRawText = boundTextElement.rawText; }
    else if (sel.type === "text" && sel.rawText) { parentRawText = sel.rawText; }
    else { parentRawText = "节点"; }

    /* ── 布局参数 (可在此处调整以改变间距) ── */
    const H_GAP = 40;
    const V_GAP = 60;
    const LEAF_COLS = 3;
    const MOVE_THRESHOLD = 0.1;
    /* ── 新节点宽度估算参数 (可调整) ── */
    const MIN_NODE_WIDTH = 60; // 新节点最小宽度
    const AVG_CHAR_WIDTH_FACTOR = 0.65; // 估算的平均字符宽度系数 (基于字号) - 需要根据实际字体调整
    const NODE_H_PADDING = 15; // 节点左右两边的总内边距估算值

    /* ── 样式助手函数 & 连接函数 ── */
    const copyStyle = s => ({
        angle: s.angle, strokeColor: s.strokeColor, backgroundColor: s.backgroundColor,
        strokeWidth: s.strokeWidth, strokeStyle: s.strokeStyle, strokeSharpness: s.strokeSharpness,
        fillStyle: s.fillStyle, roughness: s.roughness, opacity: s.opacity,
        roundness: s.roundness, fontSize: s.fontSize, fontFamily: s.fontFamily,
        textAlign: s.textAlign, verticalAlign: s.verticalAlign,
    });
    const using = (st, fn) => {
        const bk = { ...ea.style }; Object.assign(ea.style, st);
        const r = fn(); Object.assign(ea.style, bk); return r;
    };
    // 强化版连接函数，更严格地清除重复
    const connect = (f, t) => {
        if (!f || !t || !f.id || !t.id) return null;
        
        let newArrow = null;
        
        // 1. 找到并清除所有从 f 到 t 的箭头 (所有当前视图中已有的)
        const existingArrows = ea.getViewElements().filter(e => 
            e.type === "arrow" && 
            e.startBinding?.elementId === f.id && 
            e.endBinding?.elementId === t.id
        );
        
        if (existingArrows.length > 0) {
            // 即使只找到一个，也显示一下有删除操作
            info(`connect. 删除 ${existingArrows.length} 个从 ${f.id} 到 ${t.id} 的旧箭头`);
            ea.deleteViewElements(existingArrows.map(e => e.id));
        }
        
        // 2. 使用起点节点的样式创建新箭头
        try {
            using({ 
                strokeColor: f.strokeColor, 
                strokeWidth: f.strokeWidth || 2, 
                strokeStyle: f.strokeStyle || "solid", 
                strokeSharpness: f.strokeSharpness 
            }, () => {
                // 尝试连接元素的底部中间到顶部中间
                const arrowId = ea.connectObjects(
                    f.id, "bottom", 
                    t.id, "top", 
                    { numberOfPoints: 0, startArrowHead: null, endArrowHead: "triangle", padding: 0 }
                );
                
                if (arrowId) {
                    newArrow = ea.getElement(arrowId);
                    if (newArrow) {
                        // 默认设置透明度
                        newArrow.opacity = 100;
                        info(`connect. 创建新箭头成功: ID=${arrowId} from ${f.id} to ${t.id}`);
                    } else {
                        console.warn(`connect. 获取新箭头失败: ID=${arrowId} from ${f.id} to ${t.id}`);
                    }
                } else {
                    console.warn(`connect. 创建箭头失败: 从 ${f.id} 到 ${t.id}`);
                }
            });
        } catch (err) {
            console.error(`连接箭头异常: 从 ${f.id} 到 ${t.id}`, err);
        }
        
        return newArrow;
    };

    // =============================================
    //     树重新布局核心功能区域 (Tree Re-layout Core)
    // =============================================
    const getElementById = (id, map) => map.get(id);

    // 获取节点的直接子节点 ID 列表 (按 X 坐标排序)
    const getDirectChildrenIds = (parentId, arrows, elementMap) => {
        const children = [];
        arrows.forEach(arrow => {
            if (arrow.startBinding?.elementId === parentId && arrow.endBinding?.elementId) {
                const child = getElementById(arrow.endBinding.elementId, elementMap);
                if (child && ["rectangle", "ellipse", "diamond", "text"].includes(child.type)) {
                    children.push(child.id);
                }
            }
        });
        children.sort((idA, idB) => {
            const nodeA = getElementById(idA, elementMap);
            const nodeB = getElementById(idB, elementMap);
            return (nodeA?.x ?? 0) - (nodeB?.x ?? 0);
        });
        return children;
    };

    // 获取节点的直接父节点 ID 列表
    const getDirectParentIds = (childId, arrows, elementMap) => {
        const parents = [];
        arrows.forEach(arrow => {
            if (arrow.endBinding?.elementId === childId && arrow.startBinding?.elementId) {
                const parent = getElementById(arrow.startBinding.elementId, elementMap);
                if (parent && ["rectangle", "ellipse", "diamond", "text"].includes(parent.type)) {
                    parents.push(parent.id);
                }
            }
        });
        return parents;
    };

    /**
     * [新增 V6.2] 计算从根节点出发的所有节点深度 (使用 BFS)
     * @param {string} rootId - 起始根节点 ID
     * @param {Array} arrows - 所有箭头元素
     * @param {Map} elementMap - ID 到元素的映射
     * @returns {Map<string, number>} - 节点 ID 到其深度的映射
     */
    const calculateAllNodeDepths = (rootId, arrows, elementMap) => {
        const depths = new Map();
        if (!rootId || !elementMap.has(rootId)) {
            return depths; // 如果根无效则返回空 Map
        }
        const queue = [[rootId, 0]]; // [nodeId, level]
        const visited = new Set([rootId]);
        depths.set(rootId, 0);

        let head = 0;
        while (head < queue.length) {
            const [currentId, level] = queue[head++]; // Dequeue

            // 查找当前节点的子节点
            const childrenIds = getDirectChildrenIds(currentId, arrows, elementMap);

            for (const childId of childrenIds) {
                // 检查子节点是否存在且未被访问过 (BFS保证找到最短路径即最小深度)
                if (elementMap.has(childId) && !visited.has(childId)) {
                    visited.add(childId);
                    depths.set(childId, level + 1);
                    queue.push([childId, level + 1]); // Enqueue
                }
            }
        }
        return depths;
    };


    /**
     * [修改 V6.2] 查找树的根节点，优先沿层级最深的父节点路径回溯
     * @param {string} startNodeId - 起始节点 ID
     * @param {Array} arrows - 所有箭头元素
     * @param {Map} elementMap - ID 到元素的映射
     * @param {Map<string, number>} depthsMap - 节点 ID 到深度的映射
     * @returns {string} - 找到的根节点 ID
     */
    const findTreeRoot = (startNodeId, arrows, elementMap, depthsMap) => {
        let currentId = startNodeId;
        const visitedUpward = new Set([currentId]); // 用于检测向上查找时的循环
        const maxDepth = elementMap.size + 5;

        while (visitedUpward.size <= maxDepth) {
            const parents = getDirectParentIds(currentId, arrows, elementMap);

            if (parents.length === 0) {
                return currentId; // 没有父节点，是根节点
            }

            let nextParentId = null;

            if (parents.length === 1) {
                nextParentId = parents[0];
            } else {
                // --- V6.2 核心修改：选择最深的父节点 ---
                let deepestParentId = null;
                let maxDepthFound = -1;

                for (const parentId of parents) {
                    const parentDepth = depthsMap.get(parentId) ?? -1; // 获取父节点深度，未找到则为-1
                    if (parentDepth > maxDepthFound) {
                        maxDepthFound = parentDepth;
                        deepestParentId = parentId;
                    }
                    // 可选的次要排序：如果深度相同，可以选择 X 坐标最小的等
                }

                if (deepestParentId) {
                    nextParentId = deepestParentId;
                    // info(`Node ${currentId.substring(0,4)} has multiple parents, chose deepest ${nextParentId.substring(0,4)} (Depth ${maxDepthFound})`);
                } else {
                    nextParentId = parents[0]; // 如果所有父节点深度都未知或为-1，则退回默认选择第一个
                    // info(`Node ${currentId.substring(0,4)} has multiple parents, depths unknown, defaulting to ${nextParentId.substring(0,4)}`);
                }
                // --- 结束 V6.2 修改 ---
            }

            // 循环检测
            if (visitedUpward.has(nextParentId)) {
                console.warn(`Cycle detected during upward traversal to find root, stopping at ${currentId}, treating it as subtree root.`);
                return currentId;
            }
            // 节点连接自身检测
            if (nextParentId === currentId) {
                console.warn("Node connected to itself detected:", currentId);
                return currentId;
            }

            visitedUpward.add(nextParentId);
            currentId = nextParentId;
        }

        console.error("Exceeded maximum depth while searching for root. Possible complex structure or deep cycle. Returning start node.");
        return startNodeId;
    };

    /**
     * [核心函数 V11 - 无需修改] 递归计算节点及其子树的布局信息 (混合布局)
     */
    const calculateLayout = (nodeId, nodeMap, arrows, elementMap) => {
        if (nodeMap.has(nodeId)) return nodeMap.get(nodeId);

        const node = getElementById(nodeId, elementMap);
        if (!node) return null;

        const childrenIds = getDirectChildrenIds(nodeId, arrows, elementMap);

        if (childrenIds.length === 0) {
            const layout = {
                elementId: nodeId, relX: 0, relY: 0, isLeaf: true,
                subtreeWidth: node.width, subtreeHeight: node.height,
                childrenLayouts: []
            };
            nodeMap.set(nodeId, layout);
            return layout;
        }

        const childLayouts = childrenIds.map(id => calculateLayout(id, nodeMap, arrows, elementMap)).filter(Boolean);

        const leafChildrenLayouts = childLayouts.filter(l => l.isLeaf);
        const nonLeafChildrenLayouts = childLayouts.filter(l => !l.isLeaf);

        let nonLeafTotalWidth = 0;
        let maxNonLeafSubtreeHeight = 0;
        for (let i = 0; i < nonLeafChildrenLayouts.length; i++) {
            const layout = nonLeafChildrenLayouts[i];
            nonLeafTotalWidth += layout.subtreeWidth;
            if (i < nonLeafChildrenLayouts.length - 1) { nonLeafTotalWidth += H_GAP; }
            maxNonLeafSubtreeHeight = Math.max(maxNonLeafSubtreeHeight, layout.subtreeHeight);
        }

        let leafGridWidth = 0;
        let leafGridHeight = 0;
        let maxLeafWidth = 0;
        let maxLeafHeight = 0;
        if (leafChildrenLayouts.length > 0) {
            leafChildrenLayouts.forEach(layout => {
                const leafNode = getElementById(layout.elementId, elementMap);
                if (leafNode) {
                    maxLeafWidth = Math.max(maxLeafWidth, leafNode.width);
                    maxLeafHeight = Math.max(maxLeafHeight, leafNode.height);
                }
            });
            const numCols = Math.min(leafChildrenLayouts.length, LEAF_COLS);
            const numRows = Math.ceil(leafChildrenLayouts.length / numCols);
            leafGridWidth = numCols * maxLeafWidth + (numCols > 1 ? (numCols - 1) * H_GAP : 0);
            leafGridHeight = numRows * maxLeafHeight + (numRows > 1 ? (numRows - 1) * V_GAP : 0);
        }

        const layoutBlocks = [];
        nonLeafChildrenLayouts.forEach(layout => {
            layoutBlocks.push({ type: 'non-leaf', layout: layout, width: layout.subtreeWidth, height: layout.subtreeHeight });
        });
        if (leafChildrenLayouts.length > 0) {
            layoutBlocks.push({ type: 'leaf-grid', layouts: leafChildrenLayouts, width: leafGridWidth, height: leafGridHeight, maxLeafW: maxLeafWidth, maxLeafH: maxLeafHeight });
        }

        let totalCombinedWidth = 0;
        for (let i = 0; i < layoutBlocks.length; i++) {
            totalCombinedWidth += layoutBlocks[i].width;
            if (i < layoutBlocks.length - 1) { totalCombinedWidth += H_GAP; }
        }

        const finalSubtreeWidth = Math.max(totalCombinedWidth, node.width);
        let maxChildBlockHeight = 0;
        let currentXCenterOffset = -totalCombinedWidth / 2;

        for (const block of layoutBlocks) {
            const blockCenterRelX = currentXCenterOffset + block.width / 2;
            maxChildBlockHeight = Math.max(maxChildBlockHeight, block.height);

            if (block.type === 'non-leaf') {
                const layout = block.layout;
                const childNode = getElementById(layout.elementId, elementMap);
                if (childNode) {
                    layout.relX = blockCenterRelX;
                    layout.relY = node.height / 2 + V_GAP + childNode.height / 2;
                }
            } else if (block.type === 'leaf-grid') {
                const gridTopLeftRelX = currentXCenterOffset;
                const gridTopRelY = node.height / 2 + V_GAP;
                const numCols = Math.min(block.layouts.length, LEAF_COLS);
                const cellWidth = block.maxLeafW;
                const cellHeight = block.maxLeafH;

                block.layouts.forEach((leafLayout, index) => {
                    const rowIndex = Math.floor(index / numCols);
                    const colIndex = index % numCols;
                    const leafNode = getElementById(leafLayout.elementId, elementMap);
                    if (leafNode) {
                        leafLayout.relX = gridTopLeftRelX + (colIndex * (cellWidth + H_GAP)) + cellWidth / 2;
                        leafLayout.relY = gridTopRelY + (rowIndex * (cellHeight + V_GAP)) + cellHeight / 2;
                    }
                });
            }
            currentXCenterOffset += block.width + H_GAP;
        }

        const finalSubtreeHeight = node.height + (childLayouts.length > 0 ? V_GAP : 0) + maxChildBlockHeight;

        const finalLayout = {
            elementId: nodeId, relX: 0, relY: 0, isLeaf: false,
            subtreeWidth: finalSubtreeWidth, subtreeHeight: finalSubtreeHeight,
            childrenLayouts: childLayouts
        };
        nodeMap.set(nodeId, finalLayout);
        return finalLayout;
    };

    /**
     * [核心修改 V6 - 无需修改] 应用布局计算结果，移动节点 (支持编组)
     */
    const applyLayout = (layoutInfo, parentAbsoluteX, parentAbsoluteY, elementsToUpdate, elementMap, groupMap, processedGroupIds) => {
        const node = getElementById(layoutInfo.elementId, elementMap);
        if (!node) return;

        const targetAbsoluteX = parentAbsoluteX + layoutInfo.relX;
        const targetAbsoluteY = parentAbsoluteY + layoutInfo.relY;

        if (node.groupIds && node.groupIds.length > 0) {
            const groupId = node.groupIds[0];
            if (!processedGroupIds.has(groupId)) {
                processedGroupIds.add(groupId);
                const currentCenterX = node.x + node.width / 2;
                const currentCenterY = node.y + node.height / 2;
                const deltaX = targetAbsoluteX - currentCenterX;
                const deltaY = targetAbsoluteY - currentCenterY;
                const memberIds = groupMap.get(groupId);

                if (memberIds && memberIds.length > 0) {
                    for (const memberId of memberIds) {
                        const memberElement = getElementById(memberId, elementMap);
                        if (memberElement) {
                            if (Math.abs(deltaX) > MOVE_THRESHOLD || Math.abs(deltaY) > MOVE_THRESHOLD) {
                                elementsToUpdate.push({ ...memberElement, x: memberElement.x + deltaX, y: memberElement.y + deltaY });
                            }
                        }
                    }
                } else {
                    console.warn(`Group ${groupId} not found in groupMap, moving node ${node.id} individually.`);
                    const nodeDeltaX = targetAbsoluteX - (node.x + node.width / 2);
                    const nodeDeltaY = targetAbsoluteY - (node.y + node.height / 2);
                    if (Math.abs(nodeDeltaX) > MOVE_THRESHOLD || Math.abs(nodeDeltaY) > MOVE_THRESHOLD) {
                        elementsToUpdate.push({ ...node, x: node.x + nodeDeltaX, y: node.y + nodeDeltaY });
                    }
                }
            }
        } else {
            const currentCenterX = node.x + node.width / 2;
            const currentCenterY = node.y + node.height / 2;
            const deltaX = targetAbsoluteX - currentCenterX;
            const deltaY = targetAbsoluteY - currentCenterY;
            if (Math.abs(deltaX) > MOVE_THRESHOLD || Math.abs(deltaY) > MOVE_THRESHOLD) {
                elementsToUpdate.push({ ...node, x: node.x + deltaX, y: node.y + deltaY });
            }
        }

        for (const childLayout of layoutInfo.childrenLayouts) {
            applyLayout(childLayout, targetAbsoluteX, targetAbsoluteY, elementsToUpdate, elementMap, groupMap, processedGroupIds);
        }
    };

    /**
     * [修改 V6.2] 触发树的重新布局，加入深度计算逻辑
     */
    const triggerRelayout = async (startNode) => {
        try {
            info("triggerRelayout. 重新布局树结构 (V6.2 优先最深父节点)...");
            const currentElements = ea.getViewElements();
            const currentElementMap = new Map(currentElements.map(el => [el.id, el]));
            const currentArrows = currentElements.filter(el => el.type === "arrow");

            // --- 构建 groupMap (同 V6.1) ---
            const groupMap = new Map();
            currentElements.forEach(el => {
                if (el.groupIds && el.groupIds.length > 0) {
                    const groupId = el.groupIds[0];
                    if (!groupMap.has(groupId)) { groupMap.set(groupId, []); }
                    if (!groupMap.get(groupId).includes(el.id)) { groupMap.get(groupId).push(el.id); }
                }
            });

            // --- V6.2 新增：计算节点深度 ---
            // 1. 找到一个用于深度计算的起始根节点
            const tempRootFinder = (startId, arrs, map) => {
                let curr = startId; let visited = new Set([curr]); let count = 0;
                while (count++ < map.size + 5) {
                    const p = getDirectParentIds(curr, arrs, map);
                    if (p.length === 0) return curr;
                    const nextP = p[0];
                    if (visited.has(nextP)) { return curr; } // Cycle
                    if (nextP === curr) return curr; // Self-link
                    visited.add(nextP); curr = nextP;
                }
                return startId; // Timeout
            };
            const calculationRootId = tempRootFinder(startNode.id, currentArrows, currentElementMap);
            if (!calculationRootId || !currentElementMap.has(calculationRootId)) {
                info("triggerRelayout.1 无法确定深度计算的根节点，跳过重排"); return false;
            }
            // info(`Calculating node depths starting from ${calculationRootId.substring(0,6)}...`); // Optional Debug Info
            const depthsMap = calculateAllNodeDepths(calculationRootId, currentArrows, currentElementMap);
            // info("Calculated Depths:", depthsMap); // Debug: View calculated depths
            // --- 结束 V6.2 新增 ---

            // 2. 查找真正的布局根节点 (使用修改后的 findTreeRoot 和深度信息)
            const rootId = findTreeRoot(startNode.id, currentArrows, currentElementMap, depthsMap);
            if (!rootId) { info("triggerRelayout.2 未找到布局根节点，跳过重排"); return false; }
            const rootNode = getElementById(rootId, currentElementMap);
            if (!rootNode) { info("triggerRelayout.2 根节点元素丢失，跳过重排"); return false; }

            // --- 获取根节点文本 (同 V6.1) ---
            let rootNodeText = "根节点";
            if (rootNode.type === 'text' && rootNode.rawText) {
                rootNodeText = rootNode.rawText;
            } else {
                const rootBoundText = currentElements.find(e => e.type === "text" && e.containerId === rootNode.id && e.rawText);
                if (rootBoundText) rootNodeText = rootBoundText.rawText;
                else rootNodeText = rootId.substring(0, 6) + "..."; // Fallback to ID prefix
            }
            info(`triggerRelayout.2 以 '${rootNodeText}' (${rootId.substring(0, 6)}...) 为根进行布局计算 (优先最深父)...`);

            // 3. 计算布局 (使用原始 calculateLayout)
            const layoutMap = new Map();
            const treeLayout = calculateLayout(rootId, layoutMap, currentArrows, currentElementMap);

            if (!treeLayout) { info("triggerRelayout.3 布局计算失败，跳过重排"); return false; }

            // 4. 应用布局 (使用原始 applyLayout)
            const elementsToUpdate = [];
            const processedGroupIds = new Set();
            const rootCurrentCenterX = rootNode.x + rootNode.width / 2;
            const rootCurrentCenterY = rootNode.y + rootNode.height / 2;
            applyLayout(treeLayout, rootCurrentCenterX, rootCurrentCenterY, elementsToUpdate, currentElementMap, groupMap, processedGroupIds);

            // 5. 更新视图与重新连接
            if (elementsToUpdate.length === 0) {
                info("triggerRelayout.5 无需更新节点位置");
                return false;
            }
            
            // 处理需要更新的元素
            const uniqueUpdatesMap = new Map();
            for (const update of elementsToUpdate) { uniqueUpdatesMap.set(update.id, update); }
            const uniqueUpdates = Array.from(uniqueUpdatesMap.values());
            
            if (uniqueUpdates.length === 0) {
                info("triggerRelayout.5 计算出的位置变化均小于阈值，无需更新");
                return false;
            }
            
            // 更新位置
            info(`triggerRelayout.5 准备更新 ${uniqueUpdates.length} 个元素的位置...`);
            ea.copyViewElementsToEAforEditing(uniqueUpdates);
            await ea.addElementsToView(false, true, true); // 更新视图，保留选择状态
            
            // 获取更新后的元素
            const updatedElements = ea.getViewElements();
            const updatedElementMap = new Map();
            updatedElements.forEach(el => updatedElementMap.set(el.id, el));
            
            // 重建所有箭头 (完全重写) triggerRelayout.rebuildArrows
            info("triggerRelayout.rebuildArrows 准备重建所有箭头连接...");
            
            // 1. 先彻底删除所有箭头 - 确保不会有重复
            const allArrows = updatedElements.filter(e => e.type === "arrow");
            if (allArrows.length > 0) {
                const allArrowIds = allArrows.map(arrow => arrow.id);
                info(`triggerRelayout.rebuildArrows 删除当前视图中的所有 ${allArrowIds.length} 个箭头...`);
                ea.deleteViewElements(allArrowIds);
                
                // 强制更新视图，确保箭头删除生效
                await ea.addElementsToView(false, true, true);
            }
            
            // 2. 重新获取元素，确保没有箭头
            const elementsWithoutArrows = ea.getViewElements();
            if (elementsWithoutArrows.some(e => e.type === "arrow")) {
                info("triggerRelayout.rebuildArrows 警告: 箭头删除可能不完全，尝试继续重建...");
            }
            
            // 3. 创建一个新的元素映射
            const cleanElementMap = new Map();
            elementsWithoutArrows.forEach(el => cleanElementMap.set(el.id, el));
            
            // 4. 收集需要重建的连接
            info("triggerRelayout.rebuildArrows 分析需要重建的连接关系...");
            
            // 使用Set结构移除重复连接
            const connectionSet = new Set();
            
            // 在这停顿
            bail("triggerRelayout.rebuildArrows 停顿 ");
            // 从原始箭头收集连接信息
            currentArrows.forEach(arrow => {
                if (arrow.type === "arrow" && arrow.startBinding?.elementId && arrow.endBinding?.elementId) {
                    const startId = arrow.startBinding.elementId;
                    const endId = arrow.endBinding.elementId;
                    
                    // 只处理两端都还存在的连接
                    if (cleanElementMap.has(startId) && cleanElementMap.has(endId)) {
                        const key = `${startId}->${endId}`;
                        // 如果这个连接还没有被添加到Set中
                        if (!connectionSet.has(key)) {
                            connectionSet.add(key);
                        }
                    }
                }
            });
            
            info(`triggerRelayout.rebuildArrows 发现 ${connectionSet.size} 个即将重建的连接关系`);
            
            // 5. 重建连接
            let successCount = 0;
            let failedCount = 0;
            
            for (const connKey of connectionSet) {
                const [startId, endId] = connKey.split("->");
                
                // 获取起点和终点元素
                const startElement = cleanElementMap.get(startId);
                const endElement = cleanElementMap.get(endId);
                
                if (startElement && endElement) {
                    try {
                        // 使用强化版connect函数
                        const arrow = connect(startElement, endElement);
                        
                        if (arrow) {
                            // 成功创建箭头并设置属性
                            arrow.opacity = 70; // 设置一个适当的透明度
                            ea.moveViewElementToZIndex(arrow.id, 0); // 移动到底层
                            successCount++;
                        } else {
                            failedCount++;
                        }
                    } catch (err) {
                        failedCount++;
                        console.error(`连接异常: ${startId} -> ${endId}`, err);
                    }
                } else {
                    failedCount++;
                    info(`triggerRelayout.rebuildArrows 跳过缺失元素的连接: ${startId} -> ${endId}`);
                }
            }
            
            // 显示结果和更新视图
            let resultMessage = "";
            if (successCount > 0) {
                resultMessage = `重建了 ${successCount} 个连接`;
                if (failedCount > 0) {
                    resultMessage += `, 但有 ${failedCount} 个失败 (详情见控制台)`;
                }
                info(resultMessage);
                // 再次更新视图以显示箭头
                await ea.addElementsToView(false, true, true);
            } else if (failedCount > 0) {
                info(`triggerRelayout.rebuildArrows 警告: 所有 ${failedCount} 个连接重建均失败 (详情见控制台)`);
            } else {
                info("triggerRelayout.rebuildArrows 没有需要重建的连接");
            }
            
            info(`triggerRelayout.rebuildArrows ✅ 树布局更新完成 (优先最深父)`);
            return true;
        } catch (error) {
            console.error("树重新布局错误 (V6.2):", error);
            info("❌ 布局脚本出错 (优先最深父逻辑)");
            return false;
        }
    };

    // =============================================
    //     脚本主逻辑 Main Script Logic (同 V6.1)
    // =============================================
    try {
        // 1. 复制选中元素到编辑区
        ea.copyViewElementsToEAforEditing([sel]);
        const parentElement = ea.getElement(sel.id);

        // 2. 准备新节点信息
        const initialElements = ea.getViewElements();
        const initialElementMap = new Map(initialElements.map(el => [el.id, el]));
        const initialArrows = initialElements.filter(el => el.type === "arrow");
        const childrenCount = getDirectChildrenIds(parentElement.id, initialArrows, initialElementMap).length;
        const newNodeText = `${parentRawText}.${childrenCount + 1}`;

        // 4. 确定新节点的文本样式 (需要先确定样式以获取字号)
        const tempTxtStyle = (sel.type === 'text' ? copyStyle(sel) : null) || (boundTextElement ? copyStyle(boundTextElement) : null) || { fontSize: 16, fontFamily: 1 };
        tempTxtStyle.textAlign = "center";
        tempTxtStyle.verticalAlign = "middle";
        const fontSize = tempTxtStyle.fontSize || 16; // 获取字号，提供默认值

        // [修改 V6.2.1] -> 计算估算宽度
        const estimatedTextWidth = newNodeText.length * fontSize * AVG_CHAR_WIDTH_FACTOR;
        const estimatedWidth = estimatedTextWidth + NODE_H_PADDING * 2;
        const finalWidth = Math.max(MIN_NODE_WIDTH, estimatedWidth); // 取估算宽度和最小宽度中的较大者
        // const finalWidth = Math.max(MIN_NODE_WIDTH, parentElement.width, estimatedWidth); // 或者也考虑父节点宽度？可以试试

        // 3. 设定新节点的初始位置和尺寸 (使用计算出的 finalWidth)
        const initialPosition = {
            x: parentElement.x + (parentElement.width / 2) - (finalWidth / 2), // 保持中心对齐父节点
            y: parentElement.y + parentElement.height + V_GAP,
            width: finalWidth, // <<< 应用计算出的宽度
            // height: parentElement.height, // 高度可以继续继承，或也设置最小值/估算值
            // 可选：如果也想让高度自适应单行文本，可以设置一个基于字号的固定高度
            height: Math.max(35, fontSize + NODE_H_PADDING * 1.5) // 保证最小高度，并基于字号增加一点上下边距
        };


        // 5. 创建新节点 (带边框的文本) - 使用更新后的 initialPosition
        let newElementId = null;
        using(tempTxtStyle, () => { // 应用文本样式
            newElementId = ea.addText(
                initialPosition.x,
                initialPosition.y,
                newNodeText,
                {
                    box: true,
                    width: initialPosition.width,   // 使用计算的宽度
                    height: initialPosition.height, // 使用计算/继承的高度
                    // ... 其他样式继承 ...
                    strokeColor: parentElement.strokeColor, backgroundColor: parentElement.backgroundColor,
                    strokeWidth: parentElement.strokeWidth, strokeStyle: parentElement.strokeStyle,
                    strokeSharpness: parentElement.strokeSharpness, fillStyle: parentElement.fillStyle,
                    roughness: parentElement.roughness, opacity: parentElement.opacity,
                    roundness: parentElement.roundness, angle: parentElement.angle,
                    fontFamily: tempTxtStyle.fontFamily, fontSize: tempTxtStyle.fontSize,
                    textAlign: tempTxtStyle.textAlign, verticalAlign: tempTxtStyle.verticalAlign,
                    boxPadding: 5 // Excalidraw 内部的 boxPadding 似乎影响不大，我们主要靠外部尺寸控制
                }
            );
        });

        const newElement = ea.getElement(newElementId);
        
        info(`Main.5 newElement... ${JSON.stringify(newElement)}`);
/*
    {
    "id": "JgLGsply",
    "type": "rectangle",
    "x": 14346.18553735993,
    "y": -1713.0244355162731,
    "width": 31.199966430664062,
    "height": 30,
    "angle": 0,
    "strokeColor": "#1e1e1e",
    "backgroundColor": "#ffffff",
    "fillStyle": "solid",
    "strokeWidth": 2,
    "strokeStyle": "solid",
    "opacity": 1,
    "roundness": 0,
    "seed": 37500,
    "version": 1,
    "versionNonce": 537577331,
    "updated": 1746334585193,
    "isDeleted": false,
    "groups": [],
    "boundElements": [
        {
            "type": "text",
            "id": "VcTxgfps"
        }
    ],
    "link": null,
    "locked": false
}
    */
        
        //获取关联的 text 元素
        const newElementBoundElements = newElement.boundElements.find(e => e.type === "text");
        info(`Main.5 newElementBoundElements... ${JSON.stringify(newElementBoundElements)}`);
        const associatedTextElement = ea.getElement(newElementBoundElements.id);
        info(`Main.5 associatedTextElement... ${JSON.stringify(associatedTextElement)}`);
        
        // 修改文本元素内容为容器ID
        if (associatedTextElement) {
            associatedTextElement.text = associatedTextElement.containerId;
            associatedTextElement.rawText = associatedTextElement.containerId;
            associatedTextElement.originalText = associatedTextElement.containerId;
             ea.copyViewElementsToEAforEditing([associatedTextElement]);
            await ea.addElementsToView(false, false, true);
        }
        // 查找与新节点关联的文本元素

        if (!newElement) throw new Error("创建新元素失败");

        // 6. 创建箭头
        // info(`✅ 开始创建箭头: ${newNodeText}`);
        // info(`parentElement...${parentElement.id}`);
        // info(`newElement...${newElement.id}`);
        // const newArrow1 = connect(parentElement, newElement);
        // info(`newArrow...${newArrow1.id}`);
        const newArrow = connect(parentElement, newElement);
        info(`Main.6 newArrow...${newArrow.id}`);


        if (!newArrow) console.warn("创建连接箭头失败...");
        else{
              newArrow.opacity = 40; // 设置透明度
              info(`Main.6 ✅ 已创建连接箭头: ${JSON.stringify(newArrow)}`);
        }

        // 7. 添加到视图
        await ea.addElementsToView(false, false, true);
        
        // 移动箭头到底层 (在添加到视图后执行)
        if (newArrow) {
            ea.moveViewElementToZIndex(newArrow.id, 0);
        }

        info(`Main.7 ✅ 已添加子节点: ${newNodeText}`);

        // 8. 触发重排
        await triggerRelayout(parentElement);


    } catch (error) {
        console.error("脚本主逻辑错误:", error);
        info("Main.end ❌ 脚本主逻辑执行出错");
    }

})();
