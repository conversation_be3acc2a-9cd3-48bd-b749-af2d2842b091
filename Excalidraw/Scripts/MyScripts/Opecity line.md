/*

选中当前节点连出去的所有节点

```javascript
*/
/*
@title Connections Opacity 60 % ＋ Send to Back
@description 将所有连线(arrow/line)及其标签统一透明 60 %，再一起置于底层
@hotkey Mod+Shift+O
*/

(async () => {
  const tell = (msg) => new Notice(msg);

  // 1. 版本检查──建议 ≥1.8.8，才能保证 moveViewElementToZIndex 可用
  if (!ea.verifyMinimumPluginVersion?.("1.8.8")) {
    tell("请先把 Excalidraw 升级到 ≥ 1.8.8"); return;
  }

  // 2. 拿全量元素
  const all = ea.getViewElements();
  if (!all?.length) { tell("⚠️ 画布为空"); return; }

  // 3. 找连线
  const conns = all.filter(el => el.type === "arrow" || el.type === "line");
  if (!conns.length) { tell("⚠️ 未找到连线"); return; }

  // 4. 找标签（含内置 label 与 containerId 绑定的 text）
  const labelIds = new Set();
  conns.forEach(c => { if (c.label) labelIds.add(c.label.id); });
  all.forEach(el => {
    if (el.type === "text" && el.containerId && conns.some(c => c.id === el.containerId)) {
      labelIds.add(el.id);
    }
  });
  const labels = all.filter(el => labelIds.has(el.id));

  // 5. 改透明度
  [...conns, ...labels].forEach(el => el.opacity = 60);



  // 7. 真正“置底”：把连线和标签的 zIndex 全设为 0
  [...conns, ...labels].forEach(el => ea.moveViewElementToZIndex(el.id, 0));
  // 6. 先写回元素内容（保持修改一致）
  await ea.setViewElements(all);
  tell(`✅ 已处理 ${conns.length} 条连线 + ${labels.length} 个标签：透明 60 %，并置于底层`);
})();
