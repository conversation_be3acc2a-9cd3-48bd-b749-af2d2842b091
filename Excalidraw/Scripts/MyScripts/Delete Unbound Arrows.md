/*
Tips: If you are drawing a flowchart, you can use `Normalize Selected Arrows` script to correct the position of the start and end points of the arrows, then use `Elbow connectors` script, and you will get the perfect connecting line!

```javascript
*/

/*
@title Delete Unbound Arrows — 删除未连接的箭头
@description 查找并删除所有起点或终点未连接到任何元素的箭头。
@hotkey Ctrl+Shift+D (你可以设置一个不同的快捷键)
*/

await (async () => {
    const info = t => new Notice(t);
    const bail = t => { info(t); return null; };

    // 可选的版本检查
    if (!ea.verifyMinimumPluginVersion?.("1.5.0")) // 对版本要求可能不高
        return bail("请将 Excalidraw 更新到较新版本");

    const allElements = ea.getViewElements();
    const arrows = allElements.filter(el => el.type === "arrow");

    const unboundArrowIds = [];

    info(`正在检查 ${arrows.length} 条箭头...`);

    arrows.forEach(arrow => {
        // 检查起点绑定是否存在且有 elementId
        const isStartBound = arrow.startBinding?.elementId;
        // 检查终点绑定是否存在且有 elementId
        const isEndBound = arrow.endBinding?.elementId;

        // 如果起点 *或* 终点没有有效绑定，则认为是未连接的箭头
        if (!isStartBound || !isEndBound) {
            unboundArrowIds.push(arrow.id);
        }
        // 更严格的检查 (可选): 检查绑定的 elementId 是否真的存在于 elementMap 中
        // const elementMap = new Map(allElements.map(el => [el.id, el]));
        // const startElementExists = isStartBound ? elementMap.has(arrow.startBinding.elementId) : false;
        // const endElementExists = isEndBound ? elementMap.has(arrow.endBinding.elementId) : false;
        // if (!startElementExists || !endElementExists) {
        //    unboundArrowIds.push(arrow.id);
        //}
    });

    if (unboundArrowIds.length > 0) {
        info(`找到 ${unboundArrowIds.length} 条未完全连接的箭头，正在删除...`);
        try {
            // Excalidraw API 需要元素对象数组进行删除，至少包含 id
            const elementsToDelete = unboundArrowIds.map(id => ({ id: id }));

            // 执行删除
            ea.deleteViewElements(elementsToDelete);

            // 删除后通常不需要调用 addElementsToView
            // Excalidraw 应该会自动处理视图更新

            // 延迟一小段时间确保UI更新（非必需，且不是最佳实践）
            // await new Promise(resolve => setTimeout(resolve, 50));

            info(`✅ 已成功删除 ${unboundArrowIds.length} 条未连接的箭头。`);

        } catch (error) {
            console.error("删除未连接箭头时出错:", error);
            bail("❌ 删除箭头时发生错误，详情请查看控制台。");
        }
    } else {
        info("✅ 未找到需要删除的未连接箭头。");
    }

})();