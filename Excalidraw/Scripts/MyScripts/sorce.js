/*
@title Add Box ↓ & Connect (Stacked with H-Gap) — 冲突侧让路 + 样式继承 + 自动显示 ID
@description 选中节点下方按 3 列网格新增子节点；如与祖宗【非本路径】后代交叉面积 >20%，只移动真正冲突的那一侧子树让路；新矩形继承样式，并在内部自动写入自身 ID。
@hotkey Mod+ArrowDown
*/

await (async () => {
  const info = (t) => new Notice(t);
  const bail = (t) => {
    info(t);
    return null;
  };

  /* ── 版本检查 ── */
  if (!ea.verifyMinimumPluginVersion?.("1.5.21"))
    return bail("请将 Excalidraw 更新到 ≥1.5.21");

  /* ── 选中元素 & 基础数据 ── */
  const sel = ea.getViewSelectedElements()[0];
  if (!sel) return bail("⚠️ 请先选一个节点");
  const all = ea.getViewElements();

  /* ── 布局参数 ── */
  const H = 20,
    V = 40,
    G = 120,
    COL = 3;
  const col = [0, -1, 1];
  const slot = (i) => ({
    x: sel.x + col[i % COL] * (sel.width + H),
    y: sel.y + sel.height + G + Math.floor(i / COL) * (sel.height + V),
    width: sel.width,
    height: sel.height,
  });
  const overlap = (a, b) => {
    const w = Math.max(
      0,
      Math.min(a.x + a.width / 2, b.x + b.width / 2) -
        Math.max(a.x - a.width / 2, b.x - b.width / 2)
    );
    const h = Math.max(
      0,
      Math.min(a.y + a.height / 2, b.y + b.height / 2) -
        Math.max(a.y - a.height / 2, b.y - b.height / 2)
    );
    return w * h > a.width * a.height * 0.2;
  };

  /* ── 父 / 子 ── */
  const pA = all.find(
    (e) => e.type === "arrow" && e.endBinding?.elementId === sel.id
  );
  const parent = pA
    ? all.find((e) => e.id === pA.startBinding?.elementId)
    : null;
  const kids = all
    .filter((e) => e.type === "arrow" && e.startBinding?.elementId === sel.id)
    .map((a) => all.find((e) => e.id === a.endBinding?.elementId))
    .filter(Boolean);

  /* ── 样式助手 ── */
  const copyStyle = (s) => ({
    angle: s.angle,
    strokeColor: s.strokeColor,
    backgroundColor: s.backgroundColor,
    strokeWidth: s.strokeWidth,
    strokeStyle: s.strokeStyle,
    strokeSharpness: s.strokeSharpness,
    fillStyle: s.fillStyle,
    roughness: s.roughness,
    opacity: s.opacity,
    roundness: s.roundness,
  });
  const using = (st, fn) => {
    const bk = { ...ea.style };
    Object.assign(ea.style, st);
    const r = fn();
    Object.assign(ea.style, bk);
    return r;
  };
  const connect = (f, t) => {
    ea.getViewElements()
      .filter(
        (e) =>
          e.type === "arrow" &&
          e.startBinding?.elementId === f.id &&
          e.endBinding?.elementId === t.id
      )
      .forEach((e) => ea.deleteViewElements([e]));
    using(
      {
        strokeColor: sel.strokeColor,
        strokeWidth: sel.strokeWidth,
        strokeStyle: sel.strokeStyle,
        strokeSharpness: sel.strokeSharpness,
      },
      () => {
        ea.connectObjects(f.id, "bottom", t.id, "top", {
          numberOfPoints: 0,
          startArrowHead: null,
          endArrowHead: "triangle",
          padding: 0,
        });
      }
    );
  };

  /* ── 冲突检测（仅移动冲突侧） ── */
  if (parent) {
    const path = new Set([sel.id]);
    for (let n = parent; n; ) {
      path.add(n.id);
      const up = all.find(
        (e) => e.type === "arrow" && e.endBinding?.elementId === n.id
      );
      n = up ? all.find((e) => e.id === up.startBinding?.elementId) : null;
    }
    const conf = [];
    for (let anc = parent; anc; ) {
      const q = [anc];
      while (q.length) {
        const v = q.shift();
        if (v.id !== anc.id && !path.has(v.id)) conf.push(v);
        all.forEach((e) => {
          if (e.type === "arrow" && e.startBinding?.elementId === v.id) {
            const c = all.find((x) => x.id === e.endBinding?.elementId);
            if (c) q.push(c);
          }
        });
      }
      const up = all.find(
        (e) => e.type === "arrow" && e.endBinding?.elementId === anc.id
      );
      anc = up ? all.find((e) => e.id === up.startBinding?.elementId) : null;
    }

    const hit =
      kids.some((k) => conf.some((c) => overlap(k, c))) ||
      conf.some((c) => overlap(slot(kids.length), c));
    if (hit) {
      const left = conf.filter((n) => n.x < sel.x && overlap(n, sel));
      const right = conf.filter((n) => n.x > sel.x && overlap(n, sel));
      if (left.length ^ right.length) {
        const mv = left.length ? left : right;
        const dx = (left.length ? -1 : 1) * (sel.width + H);
        const ids = new Set(mv.map((n) => n.id));
        const arrows = all.filter(
          (e) =>
            e.type === "arrow" &&
            ids.has(e.startBinding?.elementId) &&
            ids.has(e.endBinding?.elementId)
        );
        ea.copyViewElementsToEAforEditing([...mv, ...arrows]);
        ea.getElements().forEach((e) => (e.x += dx));
        await ea.addElementsToView(false, false, true);
        return info("⚡️ 冲突侧已让路");
      }
    }
  }

  /* ── 新增节点 ── */
  const used = [];
  kids.forEach((c) => {
    for (let i = 0; i < kids.length; i++)
      if (overlap(slot(i), c)) {
        used.push(i);
        break;
      }
  });
  let idx = 0;
  while (used.includes(idx)) idx++;
  const pos = slot(idx);

  ea.copyViewElementsToEAforEditing([sel, ...kids]);

  /* ① 新矩形 */
  // const rect = using(copyStyle(sel), () =>
  //   ea.getElement(ea.addRect(pos.x, pos.y, sel.width, sel.height))
  // );

  // /* ② 写入自身 ID */

  const txtStyle = {
    ...copyStyle(sel),
    fontSize: 20,
    textAlign: "center",
    verticalAlign: "middle", // 旧版用 textVerticalAlign
  };
  // new Notice("rect.id:"+JSON.stringify(rect.id));
  // using(txtStyle, () =>
  //   ea.addText(
  //     rect.x, rect.y,                      // x, y
  //     rect.id,                             // containerId
  //     String(rect.id),                             // rawText
  //   ));
  const id = using(txtStyle, () =>
    ea.addText(pos.x, pos.y, "String(this.id)", {
      wrapAt: 100,
      textAlign: "center",
      textVerticalAlign: "middle",
      box: "rectangle",
      boxPadding: 20,
    })
  );

  /* ② 取回文字元素并改内容 */
  /* rectId = 你从 addText 得到的 id */
  const rectEl = ea.getElement(id);
  new Notice("rectEl:" + JSON.stringify(rectEl));

  /* 1️⃣ 找到 boundElements 里 type === "text" 的那条 */
  const txtRef = rectEl.boundElements?.find((b) => b.type === "text");
  if (!txtRef) {
    new Notice("矩形里没有绑定文字！");
    return;
  }

  /* 2️⃣ 取出真正的文字元素 */
  const textEl = ea.getElement(txtRef.id);

  /* 3️⃣ 修改文字内容（记得三个字段一起改） */
  const newContent = String(id);
  textEl.rawText = textEl.text = textEl.originalText = newContent;

  /* ③ 选中节点 → 新矩形 连线 */
  // const rectId = txtEl.containerId;        // 通过 containerId 拿到矩形
  // ea.connectObjects(sel.id, null, rectId, null, {
  //   startArrowHead: null,
  //   endArrowHead: "triangle",
  //   numberOfPoints: 1
  // });

  connect(sel, rectEl);

  await ea.addElementsToView(false, false, true);
  info("✅ 新增子节点并写入 ID");
})();
