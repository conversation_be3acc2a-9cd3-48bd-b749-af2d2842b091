/*
![](https://raw.githubusercontent.com/zsviczian/obsidian-excalidraw-plugin/master/images/scripts-normalize-selected-arrows.png)

This script will reset the start and end positions of the selected arrows. The arrow will point to the center of the connected box and will have a gap of 8px from the box.

Tips: If you are drawing a flowchart, you can use `Normalize Selected Arrows` script to correct the position of the start and end points of the arrows, then use `Elbow connectors` script, and you will get the perfect connecting line!

```javascript
*/


/*
@title Add Box ↓ & Re-layout Tree (3-Section Layout) v6 — 添加子节点并重排树（三区布局, 支持编组）
@description 选中节点下方添加新子节点，然后对整个所属树进行重新布局。支持将 Excalidraw 编组作为整体移动。
@hotkey Mod+ArrowDown
*/

await (async () => {
    const info = t => new Notice(t);
    const bail = t => { info(t); return null; };

    /* ── 版本检查 ── */
    if (!ea.verifyMinimumPluginVersion?.("1.5.21")) return bail("请将 Excalidraw 更新到 ≥1.5.21");

    /* ── 选中元素 & 基础数据 ── */
    const selected = ea.getViewSelectedElements();
    if (!selected || selected.length === 0) return bail("⚠️ 请先选一个节点");
    const sel = selected[0];
    if (!sel || !["rectangle", "ellipse", "diamond", "text"].includes(sel.type)) return bail("⚠️ 请选择一个有效的节点元素");

    /* ── 获取父节点文本 (用于新节点命名) ── */
    let parentRawText = "";
    const currentElementsForTextFind = ea.getViewElements(); // 获取一次，用于查找文本和后续布局
    const boundTextElement = currentElementsForTextFind.find(e => e.type === "text" && e.containerId === sel.id && e.rawText);
    if (boundTextElement) { parentRawText = boundTextElement.rawText; }
    else if (sel.type === "text" && sel.rawText) { parentRawText = sel.rawText; }
    else { parentRawText = "节点"; }

    /* ── 布局参数 ── */
    const H_GAP = 40; // 水平间距
    const V_GAP = 60; // 垂直间距
    const LEAF_COLS = 3; // 中区叶子节点 >=3 个时的列数
    const MOVE_THRESHOLD = 0.1; // 移动距离阈值，小于此值不视为移动

    /* ── 样式助手函数 & 连接函数 (保持不变) ── */
    const copyStyle = s => ({
        angle: s.angle, strokeColor: s.strokeColor, backgroundColor: s.backgroundColor,
        strokeWidth: s.strokeWidth, strokeStyle: s.strokeStyle, strokeSharpness: s.strokeSharpness,
        fillStyle: s.fillStyle, roughness: s.roughness, opacity: s.opacity,
        roundness: s.roundness, fontSize: s.fontSize, fontFamily: s.fontFamily,
        textAlign: s.textAlign, verticalAlign: s.verticalAlign,
       });
    const using = (st, fn) => {
        const bk = {...ea.style}; Object.assign(ea.style, st);
        const r = fn(); Object.assign(ea.style, bk); return r;
       };
    const connect = (f, t) => {
        let newArrow = null;
        // 查找并删除已有的从 f 指向 t 的箭头
        ea.getViewElements().filter(e => e.type === "arrow" && e.startBinding?.elementId === f.id && e.endBinding?.elementId === t.id).forEach(e => ea.deleteViewElements([e]));
        // 使用父节点样式创建新箭头
        using({ strokeColor: f.strokeColor, strokeWidth: f.strokeWidth, strokeStyle: f.strokeStyle, strokeSharpness: f.strokeSharpness }, () => {
            const arrowId = ea.connectObjects(f.id,"bottom", t.id, "top", { numberOfPoints: 0, startArrowHead: null, endArrowHead: "triangle", padding: 0 });
            newArrow = ea.getElement(arrowId);
            if (newArrow) {
                newArrow.opacity = 40; // 设置透明度
                ea.moveViewElementToZIndex(newArrow.id, 0); // 置于底层
                // 可以选择性添加箭头标签等逻辑
            }
        });
        return newArrow;
       };

    // =============================================
    //      树重新布局核心功能区域 (Tree Re-layout Core)
    // =============================================
    const getElementById = (id, map) => map.get(id);

    const getDirectChildrenIds = (parentId, arrows, elementMap) => {
        const children = [];
        arrows.forEach(arrow => {
            if (arrow.startBinding?.elementId === parentId && arrow.endBinding?.elementId) {
                const child = getElementById(arrow.endBinding.elementId, elementMap);
                // 确保子节点是有效的节点类型
                if (child && ["rectangle", "ellipse", "diamond", "text"].includes(child.type)) {
                    children.push(child.id);
                }
            }
        });
        // 按子节点当前 X 坐标排序
        children.sort((idA, idB) => {
            const nodeA = getElementById(idA, elementMap);
            const nodeB = getElementById(idB, elementMap);
            return (nodeA?.x ?? 0) - (nodeB?.x ?? 0);
        });
        return children;
    };

    const getDirectParentIds = (childId, arrows, elementMap) => {
        const parents = [];
        arrows.forEach(arrow => {
            if (arrow.endBinding?.elementId === childId && arrow.startBinding?.elementId) {
                const parent = getElementById(arrow.startBinding.elementId, elementMap);
                // 确保父节点是有效的节点类型
                if (parent && ["rectangle", "ellipse", "diamond", "text"].includes(parent.type)) {
                    parents.push(parent.id);
                }
            }
        });
        return parents;
    };

    const findTreeRoot = (startNodeId, arrows, elementMap) => {
        let currentId = startNodeId;
        const visited = new Set([currentId]);
        const maxDepth = elementMap.size + 5; // 防止无限循环的保护措施

        while (visited.size <= maxDepth) {
            const parents = getDirectParentIds(currentId, arrows, elementMap);
            if (parents.length === 0) {
                return currentId; // 没有父节点，是根节点
            }

            // 简单处理：只考虑第一个父节点（假设是树状结构）
            const nextParentId = parents[0];

            // 循环检测
            if (visited.has(nextParentId)) {
                console.warn(`检测到循环或多个父节点指向同一个祖先，停止在 ${currentId}，将其视为子树根`);
                return currentId; // 出现循环，将当前节点视为根
            }
             // 节点连接自身检测 (虽然 getDirectParentIds 理论上会避免，但以防万一)
            if (nextParentId === currentId) {
                console.warn("节点连接自身", currentId);
                return currentId;
            }

            visited.add(nextParentId);
            currentId = nextParentId;
        }

        console.error("查找根节点访问次数过多，可能存在非常复杂的结构或深层循环。返回初始节点。");
        return startNodeId; // 超出深度限制，返回起始节点
    };

    /**
     * [核心函数 V11 - 保持不变] 递归计算节点及其子树的布局信息 (混合布局)
     * 计算每个节点相对于其父节点的理想 *中心点* 偏移 (relX, relY)
     * 以及该节点作为根的子树的总宽度和高度 (subtreeWidth, subtreeHeight)
     */
    const calculateLayout = (nodeId, nodeMap, arrows, elementMap) => {
        // 如果已计算过，直接返回缓存结果
        if (nodeMap.has(nodeId)) return nodeMap.get(nodeId);

        const node = getElementById(nodeId, elementMap);
        if (!node) return null; // 节点不存在

        const childrenIds = getDirectChildrenIds(nodeId, arrows, elementMap);

        // Base Case: 叶子节点
        if (childrenIds.length === 0) {
            const layout = {
                elementId: nodeId,
                relX: 0, // 相对于父节点的 X 偏移 (由父节点设置)
                relY: 0, // 相对于父节点的 Y 偏移 (由父节点设置)
                isLeaf: true,
                subtreeWidth: node.width, // 子树宽度就是节点本身宽度
                subtreeHeight: node.height, // 子树高度就是节点本身高度
                childrenLayouts: []
            };
            nodeMap.set(nodeId, layout); // 缓存结果
            return layout;
        }

        // Recursive step: 先计算所有子节点的布局信息
        const childLayouts = childrenIds.map(id => calculateLayout(id, nodeMap, arrows, elementMap)).filter(Boolean);

        // --- 分离叶子节点和非叶子 (有子树的) 节点 ---
        const leafChildrenLayouts = childLayouts.filter(l => l.isLeaf);
        const nonLeafChildrenLayouts = childLayouts.filter(l => !l.isLeaf);

        // --- 计算非叶子节点的布局指标 (水平排列) ---
        let nonLeafTotalWidth = 0;
        let maxNonLeafSubtreeHeight = 0;
        for (let i = 0; i < nonLeafChildrenLayouts.length; i++) {
            const layout = nonLeafChildrenLayouts[i];
            nonLeafTotalWidth += layout.subtreeWidth;
            if (i < nonLeafChildrenLayouts.length - 1) {
                nonLeafTotalWidth += H_GAP; // 子树间的水平间距
            }
            maxNonLeafSubtreeHeight = Math.max(maxNonLeafSubtreeHeight, layout.subtreeHeight);
        }

        // --- 计算叶子节点网格的布局指标 ---
        let leafGridWidth = 0;
        let leafGridHeight = 0;
        let maxLeafWidth = 0;
        let maxLeafHeight = 0;
        if (leafChildrenLayouts.length > 0) {
            leafChildrenLayouts.forEach(layout => {
                const leafNode = getElementById(layout.elementId, elementMap);
                if (leafNode) {
                    maxLeafWidth = Math.max(maxLeafWidth, leafNode.width);
                    maxLeafHeight = Math.max(maxLeafHeight, leafNode.height);
                }
            });

            // 计算网格的列数和行数
            const numCols = Math.min(leafChildrenLayouts.length, LEAF_COLS);
            const numRows = Math.ceil(leafChildrenLayouts.length / numCols);

            // 计算网格总宽度和高度 (包含间距)
            leafGridWidth = numCols * maxLeafWidth + (numCols > 1 ? (numCols - 1) * H_GAP : 0);
            leafGridHeight = numRows * maxLeafHeight + (numRows > 1 ? (numRows - 1) * V_GAP : 0);
        }

        // --- 创建布局块 (非叶子子树 + 叶子网格) 用于计算总宽度和定位 ---
        const layoutBlocks = [];
        // 先添加非叶子节点块
        nonLeafChildrenLayouts.forEach(layout => {
            layoutBlocks.push({ type: 'non-leaf', layout: layout, width: layout.subtreeWidth, height: layout.subtreeHeight });
        });
        // 再添加叶子节点网格块 (如果存在)
        if (leafChildrenLayouts.length > 0) {
            layoutBlocks.push({ type: 'leaf-grid', layouts: leafChildrenLayouts, width: leafGridWidth, height: leafGridHeight, maxLeafW: maxLeafWidth, maxLeafH: maxLeafHeight });
        }

        // --- 计算所有块组合后的总宽度 ---
        let totalCombinedWidth = 0;
        for (let i = 0; i < layoutBlocks.length; i++) {
            totalCombinedWidth += layoutBlocks[i].width;
            if (i < layoutBlocks.length - 1) {
                totalCombinedWidth += H_GAP; // 块之间的水平间距
            }
        }

        // --- 计算本节点作为根的子树最终尺寸 ---
        const finalSubtreeWidth = Math.max(totalCombinedWidth, node.width); // 子树宽度至少是节点自身宽度
        let maxChildBlockHeight = 0; // 用于计算本节点下方的最大垂直延伸距离

        // --- 定位所有子节点/子块 (计算 relX, relY) ---
        // 所有子节点/块的 X 坐标是相对于父节点中心点计算的
        let currentXCenterOffset = -totalCombinedWidth / 2; // 从总宽度的左边开始，用于水平居中

        for (const block of layoutBlocks) {
            const blockCenterRelX = currentXCenterOffset + block.width / 2; // 当前块的中心 X 相对于父节点中心
            maxChildBlockHeight = Math.max(maxChildBlockHeight, block.height); // 记录最高的块

            if (block.type === 'non-leaf') {
                const layout = block.layout;
                const childNode = getElementById(layout.elementId, elementMap);
                if (childNode) {
                    // 非叶子子树的根节点中心 X = 块中心 X
                    layout.relX = blockCenterRelX;
                    // 非叶子子树的根节点中心 Y = 父节点底边 + V_GAP + 子节点高度一半
                    // (相对于父节点中心)
                    layout.relY = node.height / 2 + V_GAP + childNode.height / 2;
                }
            } else if (block.type === 'leaf-grid') {
                // 叶子网格区域左上角 X (相对于父节点中心)
                const gridTopLeftRelX = currentXCenterOffset;
                // 叶子网格区域顶边 Y (相对于父节点中心) = 父节点底边 + V_GAP
                const gridTopRelY = node.height / 2 + V_GAP;
                const numCols = Math.min(block.layouts.length, LEAF_COLS);
                const cellWidth = block.maxLeafW;
                const cellHeight = block.maxLeafH;

                block.layouts.forEach((leafLayout, index) => {
                    const rowIndex = Math.floor(index / numCols);
                    const colIndex = index % numCols;
                    const leafNode = getElementById(leafLayout.elementId, elementMap);
                    if (leafNode) {
                        // 叶子节点中心 X = 网格左边界 + 列偏移 + 单元格宽度一半
                        leafLayout.relX = gridTopLeftRelX + (colIndex * (cellWidth + H_GAP)) + cellWidth / 2;
                        // 叶子节点中心 Y = 网格顶边 + 行偏移 + 单元格高度一半
                        leafLayout.relY = gridTopRelY + (rowIndex * (cellHeight + V_GAP)) + cellHeight / 2;
                    }
                });
            }

            // 更新下一个块的起始 X 偏移
            currentXCenterOffset += block.width + H_GAP;
        }

        // --- 计算本节点子树的总高度 ---
        // 总高度 = 节点高度 + 垂直间距 + 最高子块的高度
        const finalSubtreeHeight = node.height + (childLayouts.length > 0 ? V_GAP : 0) + maxChildBlockHeight;

        // --- 构建并缓存当前节点的布局信息 ---
        const finalLayout = {
            elementId: nodeId,
            relX: 0, // 根节点的相对位置由调用者决定或为0
            relY: 0,
            isLeaf: false,
            subtreeWidth: finalSubtreeWidth,
            subtreeHeight: finalSubtreeHeight,
            childrenLayouts: childLayouts // 包含所有子节点，它们的 relX/relY 已被设置
        };
        nodeMap.set(nodeId, finalLayout);
        return finalLayout;
    };

    /**
     * [核心修改 V6] 应用布局计算结果，移动节点 (支持编组)
     * @param layoutInfo 当前节点的布局信息
     * @param parentAbsoluteX 父节点的绝对中心 X
     * @param parentAbsoluteY 父节点的绝对中心 Y
     * @param elementsToUpdate 收集需要更新位置的元素数组
     * @param elementMap 全局元素 ID 到元素的映射
     * @param groupMap groupId 到其成员 ID 列表的映射
     * @param processedGroupIds 已处理过的 groupId 集合，防止重复移动
     */
    const applyLayout = (layoutInfo, parentAbsoluteX, parentAbsoluteY, elementsToUpdate, elementMap, groupMap, processedGroupIds) => {
        const node = getElementById(layoutInfo.elementId, elementMap);
        if (!node) return;

        // 计算节点的理论目标 *中心* 位置
        const targetAbsoluteX = parentAbsoluteX + layoutInfo.relX;
        const targetAbsoluteY = parentAbsoluteY + layoutInfo.relY;

        // 检查节点是否属于编组
        if (node.groupIds && node.groupIds.length > 0) {
            const groupId = node.groupIds[0]; // 通常取第一个 group id

            // 如果这个编组已经处理过，则跳过移动逻辑，但仍需递归子节点
            if (!processedGroupIds.has(groupId)) {
                // 标记该编组为已处理
                processedGroupIds.add(groupId);

                // 计算需要移动的偏移量 (delta)，基于当前节点的目标位置和当前位置
                // 注意：Excalidraw 元素的 x, y 是左上角坐标，我们需要中心点来计算 delta
                const currentCenterX = node.x + node.width / 2;
                const currentCenterY = node.y + node.height / 2;
                const deltaX = targetAbsoluteX - currentCenterX;
                const deltaY = targetAbsoluteY - currentCenterY;

                 // 获取该编组的所有成员
                 const memberIds = groupMap.get(groupId); // 从预先建立的 groupMap 获取

                 if (memberIds && memberIds.length > 0) {
                     // console.log(`移动编组 ${groupId} (包含 ${memberIds.length} 个元素), delta: (${deltaX.toFixed(1)}, ${deltaY.toFixed(1)})`);
                     // 遍历所有成员并应用相同的偏移量
                     for (const memberId of memberIds) {
                         const memberElement = getElementById(memberId, elementMap);
                         if (memberElement) {
                             // 只有当移动距离大于阈值时才添加更新
                             if (Math.abs(deltaX) > MOVE_THRESHOLD || Math.abs(deltaY) > MOVE_THRESHOLD) {
                                 // 应用 delta 到当前成员的左上角位置
                                 elementsToUpdate.push({
                                     ...memberElement,
                                     x: memberElement.x + deltaX,
                                     y: memberElement.y + deltaY
                                 });
                             }
                             // else {
                             //   console.log(`编组 ${groupId} 成员 ${memberId} 移动距离 (${deltaX.toFixed(1)}, ${deltaY.toFixed(1)}) 小于阈值，跳过更新`);
                             // }
                         }
                     }
                 } else {
                    // 如果 groupMap 中没有找到成员，按单个元素处理 (理论上不应该发生)
                    console.warn(`编组 ${groupId} 在 groupMap 中未找到成员，节点 ${node.id} 将被单独移动`);
                    const nodeDeltaX = targetAbsoluteX - (node.x + node.width / 2);
                    const nodeDeltaY = targetAbsoluteY - (node.y + node.height / 2);
                    if (Math.abs(nodeDeltaX) > MOVE_THRESHOLD || Math.abs(nodeDeltaY) > MOVE_THRESHOLD) {
                        elementsToUpdate.push({ ...node, x: node.x + nodeDeltaX, y: node.y + nodeDeltaY });
                    }
                }
            }
            // else {
            //     console.log(`编组 ${groupId} 已处理，跳过节点 ${node.id} 的移动逻辑`);
            // }

        } else {
            // 不属于任何编组，按原逻辑处理单个节点
            // 计算当前中心点与目标中心点的差异
            const currentCenterX = node.x + node.width / 2;
            const currentCenterY = node.y + node.height / 2;
            const deltaX = targetAbsoluteX - currentCenterX;
            const deltaY = targetAbsoluteY - currentCenterY;

            if (Math.abs(deltaX) > MOVE_THRESHOLD || Math.abs(deltaY) > MOVE_THRESHOLD) {
                // console.log(`移动独立节点 ${node.id}, delta: (${deltaX.toFixed(1)}, ${deltaY.toFixed(1)})`);
                elementsToUpdate.push({ ...node, x: node.x + deltaX, y: node.y + deltaY });
            }
            // else {
            //    console.log(`独立节点 ${node.id} 移动距离 (${deltaX.toFixed(1)}, ${deltaY.toFixed(1)}) 小于阈值，跳过更新`);
            // }
        }

        // 递归处理子节点布局
        // 子节点的父级绝对位置是当前节点 *移动后* 的目标中心位置
        for (const childLayout of layoutInfo.childrenLayouts) {
            applyLayout(childLayout, targetAbsoluteX, targetAbsoluteY, elementsToUpdate, elementMap, groupMap, processedGroupIds);
        }
    };

    /**
     * [核心修改 V6] 触发树的重新布局 (支持编组)
     * @param startNode 触发布局的起始节点 (通常是新添加节点的父节点)
     */
    const triggerRelayout = async (startNode) => {
        try {
            info("重新布局树结构 (混合布局 V11, 支持编组)...");
            const currentElements = ea.getViewElements(); // 获取当前所有元素
            const currentElementMap = new Map(currentElements.map(el => [el.id, el]));
            const currentArrows = currentElements.filter(el => el.type === "arrow");

            // --- 新增：构建 groupMap ---
            const groupMap = new Map(); // groupId -> [elementId1, elementId2, ...]
            currentElements.forEach(el => {
                if (el.groupIds && el.groupIds.length > 0) {
                    // 假设一个元素主要属于第一个 group (Excalidraw 常见用法)
                    const groupId = el.groupIds[0];
                    if (!groupMap.has(groupId)) {
                        groupMap.set(groupId, []);
                    }
                    // 确保不重复添加同一个元素ID到分组列表
                    if (!groupMap.get(groupId).includes(el.id)) {
                        groupMap.get(groupId).push(el.id);
                    }
                }
            });
            // console.log("构建的 Group Map:", groupMap);
            // --- 结束新增 ---

            // 查找树的根节点
            const rootId = findTreeRoot(startNode.id, currentArrows, currentElementMap);
            if (!rootId) { info("未找到根节点，跳过重排"); return false; }
            const rootNode = getElementById(rootId, currentElementMap);
            if (!rootNode) { info("根节点元素丢失，跳过重排"); return false; }
            const rootNodeText = getElementById(rootId, currentElementMap)?.rawText || rootId.substring(0,6)+"...";
            info(`以 '${rootNodeText}' (${rootId.substring(0,6)}...) 为根进行布局计算...`);

            // 计算布局
            const layoutMap = new Map(); // 存储计算好的布局信息 {elementId: layout}
            const treeLayout = calculateLayout(rootId, layoutMap, currentArrows, currentElementMap);

            if (!treeLayout) { info("布局计算失败，跳过重排"); return false; }

            // 应用布局
            const elementsToUpdate = []; // 存储需要更新的元素对象
            const processedGroupIds = new Set(); // --- 新增：用于跟踪已处理的编组 ---

            // 调用修改后的 applyLayout
            // 根节点的布局应用：其父节点位置可以认为是它自己的当前中心位置，因为根节点本身通常不移动
            const rootCurrentCenterX = rootNode.x + rootNode.width / 2;
            const rootCurrentCenterY = rootNode.y + rootNode.height / 2;
            // applyLayout 计算的是目标位置，所以以根节点的当前中心为基准开始递归
            applyLayout(treeLayout, rootCurrentCenterX, rootCurrentCenterY, elementsToUpdate, currentElementMap, groupMap, processedGroupIds);


            if (elementsToUpdate.length > 0) {
                // --- 新增：去重 elementsToUpdate ---
                // 因为移动编组时，多个成员可能最终指向同一个元素更新对象（如果 applyLayout 被多次触发）
                // 使用 Map 来确保每个元素 ID 只保留最后一次的更新状态
                const uniqueUpdatesMap = new Map();
                for (const update of elementsToUpdate) {
                    uniqueUpdatesMap.set(update.id, update); // 后面的会覆盖前面的，保留最新的更新
                }
                const uniqueUpdates = Array.from(uniqueUpdatesMap.values());
                // --- 结束去重 ---

                if (uniqueUpdates.length > 0) {
                    info(`准备更新 ${uniqueUpdates.length} 个元素的位置...`);
                    ea.copyViewElementsToEAforEditing(uniqueUpdates); // 传递去重后的更新列表
                    await ea.addElementsToView(false, true, true); // 更新视图，保留选择状态
                    info(`✅ 树布局更新完成 (含编组)`);
                    return true;
                } else {
                    info("ℹ️ 计算出的位置变化均小于阈值，无需更新");
                    return false;
                }

            } else {
                info("ℹ️ 无需更新节点位置");
                return false;
            }
        } catch (error) {
            console.error("树重新布局错误:", error);
            new Notice("❌ 布局脚本出错 (含编组逻辑)");
            return false;
        }
    };

    // =============================================
    //      脚本主逻辑 Main Script Logic
    // =============================================
    try {
        // 1. 复制选中元素到编辑区，以便获取最新状态 (特别是样式)
        ea.copyViewElementsToEAforEditing([sel]);
        const parentElement = ea.getElement(sel.id); // 获取编辑区中的父元素副本

        // 2. 准备新节点信息
        const initialElements = ea.getViewElements(); // 获取当前视图元素用于计算子节点数量
        const initialElementMap = new Map(initialElements.map(el => [el.id, el]));
        const initialArrows = initialElements.filter(el => el.type === "arrow");
        const childrenCount = getDirectChildrenIds(parentElement.id, initialArrows, initialElementMap).length;
        const newNodeText = `${parentRawText}.${childrenCount + 1}`;

        // 3. 设定新节点的初始位置 (大致在父节点下方) 和尺寸
        const initialPosition = {
            x: parentElement.x, // 与父节点 X 对齐
            y: parentElement.y + parentElement.height + V_GAP, // 在父节点下方 V_GAP 处
            width: parentElement.width, // 继承父节点宽度
            height: parentElement.height, // 继承父节点高度
        };

        // 4. 确定新节点的文本样式 (优先继承绑定的文本，其次是父节点本身是文本，最后是默认)
        const tempTxtStyle = (sel.type === 'text' ? copyStyle(sel) : null) || (boundTextElement ? copyStyle(boundTextElement) : null) || { fontSize: 16, fontFamily: 1 };
        tempTxtStyle.textAlign = "center"; // 强制居中
        tempTxtStyle.verticalAlign = "middle";

        // 5. 创建新节点 (带边框的文本)
        let newElementId = null;
        using(tempTxtStyle, () => { // 应用文本样式
            newElementId = ea.addText(
                initialPosition.x,
                initialPosition.y,
                newNodeText,
                {
                    box: true, // 创建为带边框的文本
                    width: initialPosition.width,
                    height: initialPosition.height,
                    // 继承父节点的视觉样式
                    strokeColor: parentElement.strokeColor,
                    backgroundColor: parentElement.backgroundColor,
                    strokeWidth: parentElement.strokeWidth,
                    strokeStyle: parentElement.strokeStyle,
                    strokeSharpness: parentElement.strokeSharpness,
                    fillStyle: parentElement.fillStyle,
                    roughness: parentElement.roughness,
                    opacity: parentElement.opacity,
                    roundness: parentElement.roundness,
                    angle: parentElement.angle,
                    // 应用确定的文本样式
                    fontFamily: tempTxtStyle.fontFamily,
                    fontSize: tempTxtStyle.fontSize,
                    textAlign: tempTxtStyle.textAlign,
                    verticalAlign: tempTxtStyle.verticalAlign,
                    boxPadding: 5 // 给文本框一些内边距
                }
            );
        });

        const newElement = ea.getElement(newElementId);
        if (!newElement) throw new Error("创建新元素失败");

        // 6. 创建从父节点到新节点的箭头
        const newArrow = connect(parentElement, newElement);
        if (!newArrow) console.warn("创建连接箭头失败，但不中断流程");

        // 7. 将新节点和箭头添加到视图中
        // false: 不要切换到编辑模式, false: 不要选中新元素, true: 提交更改
        await ea.addElementsToView(false, false, true);
        info(`✅ 已添加子节点: ${newNodeText}`);

        // 8. 触发以父节点为起点的树重新布局
        await triggerRelayout(parentElement);

        // 9. [可选] 选中新创建的节点
        // ea.selectElements([newElement]);

    } catch (error) {
        console.error("脚本执行错误:", error);
        new Notice("❌ 脚本执行出错");
    }

})();